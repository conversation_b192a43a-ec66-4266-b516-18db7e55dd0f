# SQLFluff Configuration for Lifeboard Project
#
# S<PERSON>Fluff is a SQL linter and formatter for PostgreSQL.
# This configuration ensures consistent SQL code style and detects common issues.
#
# Features:
# - PostgreSQL dialect support
# - Code formatting and style enforcement
# - Anti-pattern detection
# - Performance hint detection
#
# Usage:
#   sqlfluff lint migrations/
#   sqlfluff fix migrations/
#   sqlfluff format migrations/

[sqlfluff]
dialect = postgres
templater = jinja
sql_file_exts = .sql,.SQL

# Rule exclusions (when needed)
exclude_rules = None

# Maximum line length
max_line_length = 120

[sqlfluff:indentation]
# Indentation configuration
tab_space_size = 2
indent_unit = space
allow_implicit_indents = false

[sqlfluff:layout]
# Layout configuration
type.comma = trailing
type.binary_operator = leading
type.assignment_operator = leading

[sqlfluff:templater:jinja]
apply_dbt_builtins = true

[sqlfluff:rules]
# Enforce consistent capitalization
capitalisation.keywords = lower
capitalisation.identifiers = lower
capitalisation.functions = lower
capitalisation.literals = lower

# Layout rules
layout.select_targets = single_line
layout.long_lines = trailing
layout.operators = after
layout.commas = trailing

# Naming conventions
references.consistent = true
references.from_clause = qualified
references.qualification = unqualified

# Structure rules
structure.subquery = true
structure.union = consistent
structure.unused_cte = true

# Specific rule configurations
[sqlfluff:rules:L010]
# Keywords should be consistently lower case
capitalisation_policy = lower

[sqlfluff:rules:L014]
# Unquoted identifiers should be consistently lower case
capitalisation_policy = lower

[sqlfluff:rules:L030]
# Function names should be consistently lower case
capitalisation_policy = lower

[sqlfluff:rules:L040]
# Null & Boolean literals should be consistently lower case
capitalisation_policy = lower

[sqlfluff:rules:L063]
# Data types should be consistently lower case
capitalisation_policy = lower

[sqlfluff:rules:ambiguous.join]
# Fully qualify columns in joins
fully_qualify_join_on = outer

[sqlfluff:rules:ambiguous.column_references]
# Qualify column references in ambiguous cases
group_by_and_order_by_style = explicit
