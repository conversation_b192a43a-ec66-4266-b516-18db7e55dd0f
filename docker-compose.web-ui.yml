# Lifeboard Web UI Service Configuration
# Phase 8: Minimal Web UI Infrastructure
# Purpose: Serves static web UI on port 9820 with full logging and security

version: '3.8'

services:
  webui:
    image: nginx:alpine
    container_name: lifeboard_webui
    profiles: ["webui"]

    # Port mapping - using high port 9820 per project rules
    ports:
      - "9820:80"

    # Volume mounts
    volumes:
      - ./webui:/usr/share/nginx/html:ro
      - ./logs/webui:/var/log/nginx
      - ./config/nginx/nginx.conf:/etc/nginx/nginx.conf:ro

    # Network isolation
    networks:
      - lifeboard_frontend

    # Logging configuration - JSON format with rotation
    logging:
      driver: json-file
      options:
        max-size: "50m"
        max-file: "3"
        labels: "component=webui,environment=development"

    # Resource limits
    deploy:
      resources:
        limits:
          cpus: "0.25"
          memory: "128M"
        reservations:
          cpus: "0.1"
          memory: "64M"

    # Security hardening
    security_opt:
      - no-new-privileges:true
    cap_drop:
      - ALL
    cap_add:
      - CHOWN
      - SETUID
      - SETGID
    read_only: true
    tmpfs:
      - /var/cache/nginx:noexec,nosuid,size=50m
      - /var/run:noexec,nosuid,size=10m
      - /tmp:noexec,nosuid,size=20m

    # Health check
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:80/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s

    # Environment variables
    environment:
      - NGINX_HOST=localhost
      - NGINX_PORT=80
      - TZ=UTC

    # Restart policy
    restart: unless-stopped

    # Labels for identification
    labels:
      - "lifeboard.component=webui"
      - "lifeboard.version=1.0.0"
      - "lifeboard.phase=8"
      - "lifeboard.description=Minimal Web UI serving welcome page"

networks:
  lifeboard_frontend:
    name: lifeboard_frontend
    driver: bridge
    labels:
      - "lifeboard.network=frontend"
      - "lifeboard.isolation=ui-only"
