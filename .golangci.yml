# GolangCI-Lint Configuration for Lifeboard Project
#
# GolangCI-<PERSON><PERSON> is a fast Go linters runner. The configuration here is tailored
# to enforce code quality in Lifeboard's Go codebase, including aggregation of tools.
#
# Features:
# - Aggregates multiple Go linters
# - Enables fast running of checks in CI/CD
# - Provides detailed issue insights
#
# Usage:
#   golangci-lint run
---
runtime:
  max-threads: 4

linters-settings:
  govet:
    check-shadowing: true

  gocyclo:
    min-complexity: 15

  golint:
    min-confidence: 0.8
    enable-all: true

  staticcheck:
    checks: [ "all" ]

  revive:
    severity: warning

output:
  format: json
  sort-results: true

issues:
  max-issues-per-linter: 0
  max-same-issues: 0
  exclude-use-default: false

linters:
  enable:
    - govet
    - errcheck
    - staticcheck
    - structcheck
    - varcheck
    - unused
    - deadcode
    - gocyclo
    - golint
    - revive

disable-all: false

build-tags:
  - integration
