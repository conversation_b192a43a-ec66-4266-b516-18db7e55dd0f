module.exports = {
    testEnvironment: 'node',
    testMatch: ['<rootDir>/tests/**/*.test.js'],
    testPathIgnorePatterns: ['/node_modules/'],
    clearMocks: true,
    resetMocks: true,
    restoreMocks: true,
    verbose: true,
    collectCoverage: false,
    coverageDirectory: 'coverage',
    collectCoverageFrom: [
        'desktop/**/*.js',
        '!desktop/node_modules/**',
        '!desktop/dist/**',
        '!desktop/build/**'
    ],
    moduleNameMapper: {
        '^@/(.*)$': '<rootDir>/desktop/$1'
    },
    setupFilesAfterEnv: [],
    testTimeout: 10000
};
