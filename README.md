# Lifeboard - Supabase Infrastructure

![CI/CD Status](https://github.com/bbookman/lifeboard-supabase/actions/workflows/ci-cd.yml/badge.svg)

A production-ready, hardened Supabase infrastructure implementation with comprehensive CI/CD, security, observability, and multi-profile deployment capabilities.

## 🚀 Quick Start

```bash
# Clone the repository
git clone https://github.com/bbookman/lifeboard-supabase.git
cd lifeboard-supabase

# Copy environment template
cp .env.example .env.local

# Start minimal database for development
./scripts/deploy.sh --detach minimal

# Start full development environment
./scripts/deploy.sh --health-check base

# Start with Supabase Studio UI
./scripts/deploy.sh --profile studio
```

## 📋 Table of Contents

- [Features](#features)
- [Architecture](#architecture)
- [Prerequisites](#prerequisites)
- [Installation](#installation)
- [Deployment Profiles](#deployment-profiles)
- [Development](#development)
- [Production Deployment](#production-deployment)
- [Monitoring & Observability](#monitoring--observability)
- [Security](#security)
- [Testing](#testing)
- [Contributing](#contributing)
- [Documentation](#documentation)

## ✨ Features

### Infrastructure
- **Multi-Profile Deployments**: 6 deployment profiles for different use cases
- **Container Security**: Hardened containers with capability restrictions and privilege dropping
- **Network Isolation**: Dedicated project networks with controlled access
- **Health Monitoring**: Comprehensive health checks and service dependency management
- **Volume Management**: Ephemeral and persistent storage strategies

### CI/CD & Quality
- **GitHub Actions Pipeline**: 9-stage automated quality gates
- **Pre-commit Hooks**: Automated code quality enforcement
- **Security Scanning**: Container and secret vulnerability detection
- **Comprehensive Testing**: Unit, integration, performance, and health tests
- **Code Quality**: SonarQube integration with quality metrics

### Observability
- **Structured Logging**: JSON-formatted logs with component tracking
- **Log Aggregation**: Loki and Grafana integration ready
- **Performance Monitoring**: Automated performance testing and validation
- **Health Dashboards**: Real-time service health and dependency monitoring

### Security
- **Secret Management**: Centralized secrets in `.env.local`
- **Database Hardening**: PostgreSQL with advanced security configuration
- **Role-Based Access**: Proper authentication and authorization
- **Container Isolation**: No-new-privileges and capability restrictions

## 🏗️ Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    Lifeboard Infrastructure                 │
├─────────────────────────────────────────────────────────────┤
│  Frontend (Studio) │  Auth Service  │  REST API  │  Storage │
├─────────────────────────────────────────────────────────────┤
│              Realtime Engine │ Health Monitoring            │
├─────────────────────────────────────────────────────────────┤
│                    PostgreSQL Database                      │
├─────────────────────────────────────────────────────────────┤
│     Observability Stack (Grafana, Loki, Promtail)         │
└─────────────────────────────────────────────────────────────┘
```

## 📋 Prerequisites

- **Docker**: Version 20.10+ with Docker Compose
- **Git**: For repository management
- **Make**: For build automation (optional)
- **curl/wget**: For health checks and testing

### System Requirements
- **RAM**: Minimum 4GB, recommended 8GB+
- **Storage**: Minimum 10GB free space
- **Network**: Internet access for image downloads

## 🛠️ Installation

### 1. Clone Repository
```bash
git clone https://github.com/bbookman/lifeboard-supabase.git
cd lifeboard-supabase
```

### 2. Environment Setup
```bash
# Copy environment template
cp .env.example .env.local

# Edit environment variables (required)
nano .env.local
```

### 3. Install Pre-commit Hooks (Optional)
```bash
# Install pre-commit
pip install pre-commit

# Install hooks
pre-commit install
```

## 🎯 Deployment Profiles

Choose the appropriate profile for your use case:

| Profile | Services | Use Case |
|---------|----------|----------|
| **minimal** | Database only | Development/testing |
| **base** | Core services (db, auth, rest, realtime, storage) | API development |
| **studio** | Base + Supabase Studio UI | Full development |
| **observability** | Full logging and monitoring | Production monitoring |
| **production** | Production-ready with observability | Production deployment |
| **full** | All services and features | Complete development environment |

### Deployment Commands

```bash
# Development workflows
./scripts/deploy.sh --detach minimal
./scripts/deploy.sh --health-check base
./scripts/deploy.sh --clean studio

# Production deployment
./scripts/deploy.sh --profile production --health-check --verbose

# Observability and monitoring
./scripts/deploy.sh --profile observability --detach
```

## 💻 Development

### Local Development Setup
1. **Start Development Environment**:
   ```bash
   ./scripts/deploy.sh --health-check base
   ```

2. **Access Services**:
   - **Database**: `localhost:5543`
   - **REST API**: `localhost:8810`
   - **Auth Service**: Internal network only
   - **Realtime**: Internal network only

3. **Supabase Studio** (optional):
   ```bash
   ./scripts/deploy.sh --profile studio
   # Access at http://localhost:3333
   ```

### Database Migrations
```bash
# Apply migrations
docker exec -it lifeboard-db-1 psql -U supabase_admin -d postgres -f /docker-entrypoint-initdb.d/migrations/001_initial_schema.sql

# Run seeds
docker exec -it lifeboard-db-1 psql -U supabase_admin -d postgres -f /docker-entrypoint-initdb.d/seeds/001_sample_data.sql
```

### Testing
```bash
# Run all tests
./tests/run_all_tests.sh

# Run specific test suites
./tests/test_health_checks.sh
./tests/test_security_scan.sh
./tests/test_observability_logging.sh
```

## 🚀 Production Deployment

### Production Checklist
- [ ] Environment variables configured
- [ ] SSL certificates ready
- [ ] Backup strategy implemented
- [ ] Monitoring configured
- [ ] Security hardening applied

### Production Commands
```bash
# Production deployment
./scripts/deploy.sh --profile production --health-check --verbose

# Monitor deployment
./scripts/health_monitor.sh --continuous

# View logs
docker compose -p lifeboard logs -f
```

## 📊 Monitoring & Observability

### Health Monitoring
```bash
# Real-time health monitoring
./scripts/health_monitor.sh --continuous

# One-time health check
./tests/test_health_checks.sh
```

### Logs
- **Location**: `/logs/` directory with timestamped files
- **Format**: Structured JSON with component tracking
- **Retention**: Configurable rotation and compression

### Grafana Dashboard (when using observability profile)
- **URL**: `http://localhost:9811`
- **Default Login**: admin/admin
- **Features**: Service health, logs, performance metrics

## 🔒 Security

### Security Features
- **Container Hardening**: No-new-privileges, capability restrictions
- **Network Isolation**: Dedicated networks, no bridge exposure
- **Secret Management**: Centralized in `.env.local`
- **Database Security**: Role-based access, encrypted connections
- **Automated Scanning**: Container and secret vulnerability detection

### Security Testing
```bash
# Run security scans
./tests/test_security_scan.sh

# Container security validation
./tests/test_container_security.sh

# Secret leak detection
./tests/test_secrets_validation.sh
```

## 🧪 Testing

### Test Suites
- **Unit Tests**: Component-level validation
- **Integration Tests**: Service interaction testing
- **Security Tests**: Vulnerability and configuration validation
- **Performance Tests**: Load and stress testing
- **Health Tests**: Service health and dependency validation

### Running Tests
```bash
# All tests
./tests/run_all_tests.sh

# Specific phases
./tests/run_phase1_tests.sh  # Database tests
./tests/test_health_checks.sh  # Health monitoring
./tests/test_observability_logging.sh  # Logging tests
```

## 🤝 Contributing

### Development Workflow
1. Fork the repository
2. Create a feature branch
3. Make changes with tests
4. Run quality checks: `pre-commit run --all-files`
5. Submit pull request

### Code Quality
- **Pre-commit Hooks**: Automated linting, formatting, security checks
- **CI/CD Pipeline**: Comprehensive quality gates on all PRs
- **Code Review**: Required before merging

## 📚 Documentation

### Available Documentation
- **[API Documentation](supporting_documents/API_Documentation.md)**: Complete API reference
- **[Deployment Guide](supporting_documents/Production_Deployment_Guide.md)**: Production deployment procedures
- **[Developer Guide](supporting_documents/Developer_Guide.md)**: Local development setup
- **[Operations Manual](supporting_documents/Operations_Manual.md)**: Monitoring and maintenance
- **[Security Guide](supporting_documents/Security_Guide.md)**: Security configuration and best practices
- **[Architecture Overview](supporting_documents/Architecture_Documentation.md)**: System architecture and design

### Quick Links
- [Executive Summary](supporting_documents/1-Executive_Summary_Vision.md)
- [MVP Definition](supporting_documents/2-mvp-definition-prioritization.md)
- [Core Features](supporting_documents/3-core_features_user_stories.md)
- [Development Phases](supporting_documents/Development_phasing.md)

## 🆘 Support

### Getting Help
- **Issues**: [GitHub Issues](https://github.com/bbookman/lifeboard-supabase/issues)
- **Discussions**: [GitHub Discussions](https://github.com/bbookman/lifeboard-supabase/discussions)
- **Documentation**: `supporting_documents/` directory

### Common Issues
- **Port Conflicts**: Use `--clean` flag to reset
- **Permission Issues**: Check Docker daemon permissions
- **Health Check Failures**: Review service logs with `docker logs`

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Supabase team for the excellent self-hosting foundation
- Docker community for containerization best practices
- GitHub Actions for CI/CD automation

---

**Status**: Production Ready ✅
**Last Updated**: 2025-07-02
**Version**: 1.0.0
