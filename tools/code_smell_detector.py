#!/usr/bin/env python3
"""
Code Smell Detection Infrastructure

This module provides comprehensive code smell detection across multiple languages
and file types. It implements automated quality gates and generates detailed reports.

Features:
- Multi-language support (Python, JavaScript/TypeScript, Go, Shell, SQL, YAML, Docker)
- Configurable quality gates with thresholds
- Detailed logging and reporting
- Integration with CI/CD pipelines
- Baseline support for legacy code

Author: Lifeboard Development Team
Date: 2025-07-03
"""

import argparse
import asyncio
import json
import logging
import subprocess
import sys
from dataclasses import asdict
from datetime import datetime
from pathlib import Path
from typing import Any

# Add the tools directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

from code_smell_detector.core.config import Config
from code_smell_detector.core.models import (
    Code<PERSON>mell,
    ScanResult,
    SeverityLevel,
)


class CodeSmellDetector:
    """
    Main code smell detection engine

    This class orchestrates the code smell detection process across multiple
    languages and file types, applies quality gates, and generates reports.
    """

    def __init__(self, config_path: str | None = None):
        """
        Initialize the code smell detector

        Args:
            config_path: Path to configuration file
        """
        self.config = Config.load(config_path)
        self.logger = self._setup_logging()
        self.quality_gates = self.config.get_quality_gates()
        self.baseline = self._load_baseline()

    def _setup_logging(self) -> logging.Logger:
        """Set up comprehensive logging for code smell detection"""
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)

        timestamp = datetime.now().strftime("%Y-%m-%d")
        log_file = log_dir / f"{timestamp}_code_smell.log"

        logging.basicConfig(
            level=logging.DEBUG,
            format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
            handlers=[logging.FileHandler(log_file), logging.StreamHandler(sys.stdout)],
        )

        logger = logging.getLogger("CodeSmellDetector")
        logger.info("Initialized code smell detector with log file: %s", log_file)
        return logger


    def _load_baseline(self) -> dict[str, Any]:
        """Load baseline for existing code smells"""
        baseline_path = self.config.baseline_path
        if Path(baseline_path).exists():
            with open(baseline_path, "r", encoding="utf-8") as f:
                return json.load(f)
        return {"smells": []}

    async def scan_project(self, project_path: str = ".") -> ScanResult:
        """
        Scan entire project for code smells

        Args:
            project_path: Path to project root

        Returns:
            ScanResult with comprehensive analysis
        """
        start_time = datetime.now()
        self.logger.info("Starting code smell scan for project: %s", project_path)

        # Discover files to scan
        files_to_scan = self._discover_files(project_path)
        self.logger.info("Discovered %d files to scan", len(files_to_scan))

        # Run parallel scans
        all_smells = []
        if self.config.parallel_execution:
            all_smells = await self._run_parallel_scans(files_to_scan)
        else:
            all_smells = await self._run_sequential_scans(files_to_scan)

        # Filter against baseline
        filtered_smells = self._filter_baseline_smells(all_smells)

        # Calculate metrics
        metrics = self._calculate_metrics(filtered_smells, files_to_scan)

        # Apply quality gates
        quality_gate_results = self._apply_quality_gates(metrics)

        # Create scan result
        end_time = datetime.now()
        scan_duration = (end_time - start_time).total_seconds()

        result = ScanResult(
            total_files=len(files_to_scan),
            total_smells=len(filtered_smells),
            smells_by_severity=self._group_by_severity(filtered_smells),
            smells_by_category=self._group_by_category(filtered_smells),
            quality_gates=quality_gate_results,
            files_scanned=files_to_scan,
            smells=filtered_smells,
            scan_duration=scan_duration,
            timestamp=end_time,
        )

        self.logger.info("Scan completed in %.2f seconds", scan_duration)
        self.logger.info("Found %d code smells", len(filtered_smells))

        return result

    def _discover_files(self, project_path: str) -> list[str]:
        """Discover files to scan based on configured patterns"""
        files = []
        project_path = Path(project_path)

        # Get all patterns
        all_patterns = []
        for patterns in self.config.file_patterns.values():
            all_patterns.extend(patterns)

        # Find matching files
        for pattern in all_patterns:
            files.extend(project_path.rglob(pattern))

        # Convert to strings and filter excludes
        file_paths = [str(f.relative_to(project_path)) for f in files]

        # Apply exclude patterns
        filtered_files = []
        for file_path in file_paths:
            excluded = False
            for exclude_pattern in self.config.exclude_patterns:
                if exclude_pattern in file_path:
                    excluded = True
                    break
            if not excluded:
                filtered_files.append(file_path)

        return filtered_files

    async def _run_parallel_scans(self, files: list[str]) -> list[CodeSmell]:
        """Run code smell detection in parallel"""
        self.logger.info("Running parallel code smell detection")

        # Group files by language
        language_groups = self._group_files_by_language(files)

        # Create tasks for each language
        tasks = []
        for language, file_list in language_groups.items():
            if file_list:
                task = self._scan_language_files(language, file_list)
                tasks.append(task)

        # Run tasks concurrently
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # Collect all smells
        all_smells = []
        for result in results:
            if isinstance(result, list):
                all_smells.extend(result)
            else:
                self.logger.error("Scan task failed: %s", result)

        return all_smells

    async def _run_sequential_scans(self, files: list[str]) -> list[CodeSmell]:
        """Run code smell detection sequentially"""
        self.logger.info("Running sequential code smell detection")

        language_groups = self._group_files_by_language(files)
        all_smells = []

        for language, file_list in language_groups.items():
            if file_list:
                smells = await self._scan_language_files(language, file_list)
                all_smells.extend(smells)

        return all_smells

    def _group_files_by_language(self, files: list[str]) -> dict[str, list[str]]:
        """Group files by programming language"""
        groups = {lang: [] for lang in self.config.file_patterns}

        for file_path in files:
            for language, patterns in self.config.file_patterns.items():
                for pattern in patterns:
                    # Simple pattern matching - could be enhanced
                    if file_path.endswith(pattern.replace("*", "")):
                        groups[language].append(file_path)
                        break

        return groups

    async def _scan_language_files(
        self, language: str, files: list[str]
    ) -> list[CodeSmell]:
        """Scan files for a specific language"""
        self.logger.info("Scanning %d %s files", len(files), language)

        # Map language to scanner method
        scanner_map = {
            "javascript": self._scan_javascript_files,
            "python": self._scan_python_files,
            "go": self._scan_go_files,
            "shell": self._scan_shell_files,
            "sql": self._scan_sql_files,
            "docker": self._scan_docker_files,
            "yaml": self._scan_yaml_files,
            "markdown": self._scan_markdown_files,
        }

        if (scanner := scanner_map.get(language)):
            return await scanner(files)

        self.logger.warning("No scanner implemented for language: %s", language)
        return []

    async def _scan_javascript_files(self, files: list[str]) -> list[CodeSmell]:
        """Scan JavaScript/TypeScript files using ESLint and SonarJS"""
        smells = []

        if self.config.enabled_tools["eslint"]:
            eslint_smells = await self._run_eslint(files)
            smells.extend(eslint_smells)

        if self.config.enabled_tools["sonarjs"]:
            sonarjs_smells = await self._run_sonarjs(files)
            smells.extend(sonarjs_smells)

        return smells

    async def _scan_python_files(self, files: list[str]) -> list[CodeSmell]:
        """Scan Python files using pylint and bandit"""
        smells = []

        if self.config.enabled_tools["pylint"]:
            pylint_smells = await self._run_pylint(files)
            smells.extend(pylint_smells)

        if self.config.enabled_tools["bandit"]:
            bandit_smells = await self._run_bandit(files)
            smells.extend(bandit_smells)

        return smells

    async def _scan_go_files(self, files: list[str]) -> list[CodeSmell]:
        """Scan Go files using golangci-lint"""
        smells = []

        if self.config.enabled_tools["golangci-lint"]:
            golangci_smells = await self._run_golangci_lint(files)
            smells.extend(golangci_smells)

        return smells

    async def _scan_shell_files(self, files: list[str]) -> list[CodeSmell]:
        """Scan shell files using shellcheck"""
        smells = []

        if self.config.enabled_tools["shellcheck"]:
            shellcheck_smells = await self._run_shellcheck(files)
            smells.extend(shellcheck_smells)

        return smells

    async def _scan_sql_files(self, files: list[str]) -> list[CodeSmell]:
        """Scan SQL files using sqlfluff"""
        smells = []

        if self.config.enabled_tools["sqlfluff"]:
            sqlfluff_smells = await self._run_sqlfluff(files)
            smells.extend(sqlfluff_smells)

        return smells

    async def _scan_docker_files(self, files: list[str]) -> list[CodeSmell]:
        """Scan Docker files using hadolint"""
        smells = []

        if self.config.enabled_tools["hadolint"]:
            hadolint_smells = await self._run_hadolint(files)
            smells.extend(hadolint_smells)

        return smells

    async def _scan_yaml_files(self, files: list[str]) -> list[CodeSmell]:
        """Scan YAML files using yamllint"""
        smells = []

        if self.config.enabled_tools["yamllint"]:
            yamllint_smells = await self._run_yamllint(files)
            smells.extend(yamllint_smells)

        return smells

    async def _scan_markdown_files(self, files: list[str]) -> list[CodeSmell]:
        """Scan Markdown files using markdownlint"""
        smells = []

        if self.config.enabled_tools["markdownlint"]:
            markdownlint_smells = await self._run_markdownlint(files)
            smells.extend(markdownlint_smells)

        return smells

    async def _run_eslint(self, files: list[str]) -> list[CodeSmell]:
        """Run ESLint on JavaScript/TypeScript files"""
        if not files:
            return []

        self.logger.debug("Running ESLint on %d files", len(files))

        try:
            cmd = [
                "npx",
                "eslint",
                "--format",
                "json",
                "--no-error-on-unmatched-pattern",
            ] + files

            result = await self._run_command(cmd)

            if result.returncode != 0 and result.stdout:
                # ESLint returns non-zero even for warnings
                pass

            return self._parse_eslint_output(result.stdout)

        except Exception as e:
            self.logger.error("ESLint execution failed: %s", e)
            return []

    async def _run_pylint(self, files: list[str]) -> list[CodeSmell]:
        """Run pylint on Python files"""
        if not files:
            return []

        self.logger.debug("Running pylint on %d files", len(files))

        try:
            cmd = [
                "pylint",
                "--output-format=json",
                "--disable=C0114,C0115,C0116",  # Disable docstring warnings for now
            ] + files

            result = await self._run_command(cmd)
            return self._parse_pylint_output(result.stdout)

        except Exception as e:
            self.logger.error("Pylint execution failed: %s", e)
            return []

    async def _run_golangci_lint(self, files: list[str]) -> list[CodeSmell]:
        """Run golangci-lint on Go files"""
        if not files:
            return []

        self.logger.debug("Running golangci-lint on %d files", len(files))

        try:
            cmd = ["golangci-lint", "run", "--out-format=json", "--timeout=5m"] + files

            result = await self._run_command(cmd)
            return self._parse_golangci_output(result.stdout)

        except Exception as e:
            self.logger.error("golangci-lint execution failed: %s", e)
            return []

    async def _run_shellcheck(self, files: list[str]) -> list[CodeSmell]:
        """Run shellcheck on shell files"""
        if not files:
            return []

        self.logger.debug("Running shellcheck on %d files", len(files))

        try:
            cmd = ["shellcheck", "--format=json"] + files

            result = await self._run_command(cmd)
            return self._parse_shellcheck_output(result.stdout)

        except Exception as e:
            self.logger.error("Shellcheck execution failed: %s", e)
            return []

    async def _run_sqlfluff(self, files: list[str]) -> list[CodeSmell]:
        """Run sqlfluff on SQL files"""
        if not files:
            return []

        self.logger.debug("Running sqlfluff on %d files", len(files))

        try:
            cmd = ["sqlfluff", "lint", "--format=json", "--dialect=postgres"] + files

            result = await self._run_command(cmd)
            return self._parse_sqlfluff_output(result.stdout)

        except Exception as e:
            self.logger.error("sqlfluff execution failed: %s", e)
            return []

    async def _run_hadolint(self, files: list[str]) -> list[CodeSmell]:
        """Run hadolint on Docker files"""
        if not files:
            return []

        self.logger.debug("Running hadolint on %d files", len(files))

        smells = []
        for file_path in files:
            try:
                cmd = ["hadolint", "--format=json", file_path]

                result = await self._run_command(cmd)
                file_smells = self._parse_hadolint_output(result.stdout, file_path)
                smells.extend(file_smells)

            except Exception as e:
                self.logger.error("Hadolint execution failed for %s: %s", file_path, e)

        return smells

    async def _run_yamllint(self, files: list[str]) -> list[CodeSmell]:
        """Run yamllint on YAML files"""
        if not files:
            return []

        self.logger.debug("Running yamllint on %d files", len(files))

        try:
            cmd = ["yamllint", "--format=parsable"] + files

            result = await self._run_command(cmd)
            return self._parse_yamllint_output(result.stdout)

        except Exception as e:
            self.logger.error("yamllint execution failed: %s", e)
            return []

    async def _run_markdownlint(self, files: list[str]) -> list[CodeSmell]:
        """Run markdownlint on Markdown files"""
        if not files:
            return []

        self.logger.debug("Running markdownlint on %d files", len(files))

        try:
            cmd = ["markdownlint-cli2", "--json"] + files

            result = await self._run_command(cmd)
            return self._parse_markdownlint_output(result.stdout)

        except Exception as e:
            self.logger.error("markdownlint execution failed: %s", e)
            return []

    async def _run_sonarjs(self, files: list[str]) -> list[CodeSmell]:
        """Run SonarJS analysis (placeholder implementation)"""
        # This would typically integrate with SonarQube/SonarCloud
        # The files parameter would be used when implementing actual SonarJS integration
        _ = files  # Suppress unused argument warning
        self.logger.debug("SonarJS analysis not implemented yet")
        return []

    async def _run_bandit(self, files: list[str]) -> list[CodeSmell]:
        """Run bandit security analysis on Python files"""
        if not files:
            return []

        self.logger.debug("Running bandit on %d files", len(files))

        try:
            cmd = ["bandit", "--format=json", "--recursive"] + files

            result = await self._run_command(cmd)
            return self._parse_bandit_output(result.stdout)

        except Exception as e:
            self.logger.error("Bandit execution failed: %s", e)
            return []

    async def _run_command(self, cmd: list[str]) -> subprocess.CompletedProcess:
        """Run a command asynchronously"""
        process = await asyncio.create_subprocess_exec(
            *cmd, stdout=asyncio.subprocess.PIPE, stderr=asyncio.subprocess.PIPE
        )

        stdout, stderr = await process.communicate()

        return subprocess.CompletedProcess(cmd, process.returncode, stdout, stderr)

    def _parse_eslint_output(self, output: bytes) -> list[CodeSmell]:
        """Parse ESLint JSON output"""
        smells = []

        try:
            if not output:
                return smells

            data = json.loads(output.decode("utf-8"))

            for file_result in data:
                file_path = file_result.get("filePath", "")

                for message in file_result.get("messages", []):
                    severity = self._map_eslint_severity(message.get("severity", 1))

                    smell = CodeSmell(
                        file_path=file_path,
                        line_number=message.get("line", 0),
                        column=message.get("column", 0),
                        rule_id=message.get("ruleId", "unknown"),
                        severity=severity,
                        message=message.get("message", ""),
                        category="style",
                        tool="eslint",
                        fixable=message.get("fix") is not None,
                    )
                    smells.append(smell)

        except json.JSONDecodeError as e:
            self.logger.error("Failed to parse ESLint output: %s", e)

        return smells

    def _parse_pylint_output(self, output: bytes) -> list[CodeSmell]:
        """Parse pylint JSON output"""
        smells = []

        try:
            if not output:
                return smells

            data = json.loads(output.decode("utf-8"))

            for item in data:
                severity = self._map_pylint_severity(item.get("type", "info"))

                smell = CodeSmell(
                    file_path=item.get("path", ""),
                    line_number=item.get("line", 0),
                    column=item.get("column", 0),
                    rule_id=item.get("message-id", "unknown"),
                    severity=severity,
                    message=item.get("message", ""),
                    category=item.get("category", "unknown"),
                    tool="pylint",
                )
                smells.append(smell)

        except json.JSONDecodeError as e:
            self.logger.error("Failed to parse pylint output: %s", e)

        return smells

    def _parse_golangci_output(self, output: bytes) -> list[CodeSmell]:
        """Parse golangci-lint JSON output"""
        smells = []

        try:
            if not output:
                return smells

            data = json.loads(output.decode("utf-8"))

            for issue in data.get("Issues", []):
                severity = self._map_golangci_severity(issue.get("Severity", "info"))

                smell = CodeSmell(
                    file_path=issue.get("Pos", {}).get("Filename", ""),
                    line_number=issue.get("Pos", {}).get("Line", 0),
                    column=issue.get("Pos", {}).get("Column", 0),
                    rule_id=issue.get("FromLinter", "unknown"),
                    severity=severity,
                    message=issue.get("Text", ""),
                    category="code-quality",
                    tool="golangci-lint",
                )
                smells.append(smell)

        except json.JSONDecodeError as e:
            self.logger.error("Failed to parse golangci-lint output: %s", e)

        return smells

    def _parse_shellcheck_output(self, output: bytes) -> list[CodeSmell]:
        """Parse shellcheck JSON output"""
        smells = []

        try:
            if not output:
                return smells

            data = json.loads(output.decode("utf-8"))

            for item in data:
                severity = self._map_shellcheck_severity(item.get("level", "info"))

                smell = CodeSmell(
                    file_path=item.get("file", ""),
                    line_number=item.get("line", 0),
                    column=item.get("column", 0),
                    rule_id=f"SC{item.get('code', 0)}",
                    severity=severity,
                    message=item.get("message", ""),
                    category="shell",
                    tool="shellcheck",
                )
                smells.append(smell)

        except json.JSONDecodeError as e:
            self.logger.error("Failed to parse shellcheck output: %s", e)

        return smells

    def _parse_sqlfluff_output(self, output: bytes) -> list[CodeSmell]:
        """Parse sqlfluff JSON output"""
        smells = []

        try:
            if not output:
                return smells

            data = json.loads(output.decode("utf-8"))

            for file_result in data:
                file_path = file_result.get("filepath", "")

                for violation in file_result.get("violations", []):
                    severity = self._map_sqlfluff_severity(
                        violation.get("warning", False)
                    )

                    smell = CodeSmell(
                        file_path=file_path,
                        line_number=violation.get("line_no", 0),
                        column=violation.get("line_pos", 0),
                        rule_id=violation.get("code", "unknown"),
                        severity=severity,
                        message=violation.get("description", ""),
                        category="sql",
                        tool="sqlfluff",
                    )
                    smells.append(smell)

        except json.JSONDecodeError as e:
            self.logger.error("Failed to parse sqlfluff output: %s", e)

        return smells

    def _parse_hadolint_output(self, output: bytes, file_path: str) -> list[CodeSmell]:
        """Parse hadolint JSON output"""
        smells = []

        try:
            if not output:
                return smells

            data = json.loads(output.decode("utf-8"))

            for item in data:
                severity = self._map_hadolint_severity(item.get("level", "info"))

                smell = CodeSmell(
                    file_path=file_path,
                    line_number=item.get("line", 0),
                    column=item.get("column", 0),
                    rule_id=item.get("code", "unknown"),
                    severity=severity,
                    message=item.get("message", ""),
                    category="docker",
                    tool="hadolint",
                )
                smells.append(smell)

        except json.JSONDecodeError as e:
            self.logger.error("Failed to parse hadolint output: %s", e)

        return smells

    def _parse_yamllint_output(self, output: bytes) -> list[CodeSmell]:
        """Parse yamllint parsable output"""
        smells = []

        try:
            if not output:
                return smells

            lines = output.decode("utf-8").strip().split("\n")

            for line in lines:
                if ":" in line:
                    parts = line.split(":", 4)
                    if len(parts) >= 5:
                        file_path = parts[0]
                        line_number = int(parts[1]) if parts[1].isdigit() else 0
                        column = int(parts[2]) if parts[2].isdigit() else 0
                        level = parts[3].strip()
                        message = parts[4].strip()

                        severity = self._map_yamllint_severity(level)

                        smell = CodeSmell(
                            file_path=file_path,
                            line_number=line_number,
                            column=column,
                            rule_id="yaml-lint",
                            severity=severity,
                            message=message,
                            category="yaml",
                            tool="yamllint",
                        )
                        smells.append(smell)

        except Exception as e:
            self.logger.error("Failed to parse yamllint output: %s", e)

        return smells

    def _parse_markdownlint_output(self, output: bytes) -> list[CodeSmell]:
        """Parse markdownlint JSON output"""
        smells = []

        try:
            if not output:
                return smells

            data = json.loads(output.decode("utf-8"))

            for file_path, file_issues in data.items():
                for issue in file_issues:
                    severity = (
                        SeverityLevel.MINOR
                    )  # markdownlint issues are typically minor

                    smell = CodeSmell(
                        file_path=file_path,
                        line_number=issue.get("lineNumber", 0),
                        column=issue.get("columnNumber", 0),
                        rule_id=issue.get("ruleNames", ["unknown"])[0],
                        severity=severity,
                        message=issue.get("ruleDescription", ""),
                        category="documentation",
                        tool="markdownlint",
                    )
                    smells.append(smell)

        except json.JSONDecodeError as e:
            self.logger.error("Failed to parse markdownlint output: %s", e)

        return smells

    def _parse_bandit_output(self, output: bytes) -> list[CodeSmell]:
        """Parse bandit JSON output"""
        smells = []

        try:
            if not output:
                return smells

            data = json.loads(output.decode("utf-8"))

            for result in data.get("results", []):
                severity = self._map_bandit_severity(
                    result.get("issue_severity", "LOW")
                )

                smell = CodeSmell(
                    file_path=result.get("filename", ""),
                    line_number=result.get("line_number", 0),
                    column=0,
                    rule_id=result.get("test_id", "unknown"),
                    severity=severity,
                    message=result.get("issue_text", ""),
                    category="security",
                    tool="bandit",
                )
                smells.append(smell)

        except json.JSONDecodeError as e:
            self.logger.error("Failed to parse bandit output: %s", e)

        return smells

    def _map_eslint_severity(self, severity: int) -> SeverityLevel:
        """Map ESLint severity to SeverityLevel"""
        if severity == 2:
            return SeverityLevel.MAJOR
        elif severity == 1:
            return SeverityLevel.MINOR
        else:
            return SeverityLevel.INFO

    def _map_pylint_severity(self, msg_type: str) -> SeverityLevel:
        """Map pylint message type to SeverityLevel"""
        mapping = {
            "error": SeverityLevel.CRITICAL,
            "warning": SeverityLevel.MAJOR,
            "refactor": SeverityLevel.MINOR,
            "convention": SeverityLevel.MINOR,
            "info": SeverityLevel.INFO,
        }
        return mapping.get(msg_type.lower(), SeverityLevel.INFO)

    def _map_golangci_severity(self, severity: str) -> SeverityLevel:
        """Map golangci-lint severity to SeverityLevel"""
        mapping = {
            "error": SeverityLevel.CRITICAL,
            "warning": SeverityLevel.MAJOR,
            "info": SeverityLevel.MINOR,
        }
        return mapping.get(severity.lower(), SeverityLevel.INFO)

    def _map_shellcheck_severity(self, level: str) -> SeverityLevel:
        """Map shellcheck level to SeverityLevel"""
        mapping = {
            "error": SeverityLevel.CRITICAL,
            "warning": SeverityLevel.MAJOR,
            "info": SeverityLevel.MINOR,
            "style": SeverityLevel.MINOR,
        }
        return mapping.get(level.lower(), SeverityLevel.INFO)

    def _map_sqlfluff_severity(self, is_warning: bool) -> SeverityLevel:
        """Map sqlfluff warning flag to SeverityLevel"""
        return SeverityLevel.MINOR if is_warning else SeverityLevel.MAJOR

    def _map_hadolint_severity(self, level: str) -> SeverityLevel:
        """Map hadolint level to SeverityLevel"""
        mapping = {
            "error": SeverityLevel.CRITICAL,
            "warning": SeverityLevel.MAJOR,
            "info": SeverityLevel.MINOR,
            "style": SeverityLevel.MINOR,
        }
        return mapping.get(level.lower(), SeverityLevel.INFO)

    def _map_yamllint_severity(self, level: str) -> SeverityLevel:
        """Map yamllint level to SeverityLevel"""
        mapping = {"error": SeverityLevel.MAJOR, "warning": SeverityLevel.MINOR}
        return mapping.get(level.lower(), SeverityLevel.INFO)

    def _map_bandit_severity(self, severity: str) -> SeverityLevel:
        """Map bandit severity to SeverityLevel"""
        mapping = {
            "HIGH": SeverityLevel.CRITICAL,
            "MEDIUM": SeverityLevel.MAJOR,
            "LOW": SeverityLevel.MINOR,
        }
        return mapping.get(severity.upper(), SeverityLevel.INFO)

    def _filter_baseline_smells(self, smells: list[CodeSmell]) -> list[CodeSmell]:
        """Filter out smells that exist in the baseline"""
        if not self.baseline.get("smells"):
            return smells

        baseline_smells = set()
        for baseline_smell in self.baseline["smells"]:
            key = f"{baseline_smell['file_path']}:{baseline_smell['line_number']}:{baseline_smell['rule_id']}"
            baseline_smells.add(key)

        filtered_smells = []
        for smell in smells:
            if (key := f"{smell.file_path}:{smell.line_number}:{smell.rule_id}") not in baseline_smells:
                filtered_smells.append(smell)

        return filtered_smells

    def _calculate_metrics(
        self, smells: list[CodeSmell], files: list[str]
    ) -> dict[str, float]:
        """Calculate metrics for quality gates"""
        metrics = {
            "blocker_count": sum(
                1 for s in smells if s.severity == SeverityLevel.BLOCKER
            ),
            "critical_count": sum(
                1 for s in smells if s.severity == SeverityLevel.CRITICAL
            ),
            "major_count": sum(1 for s in smells if s.severity == SeverityLevel.MAJOR),
            "minor_count": sum(1 for s in smells if s.severity == SeverityLevel.MINOR),
            "info_count": sum(1 for s in smells if s.severity == SeverityLevel.INFO),
            "security_count": sum(1 for s in smells if s.category == "security"),
            "total_files": len(files),
            "total_smells": len(smells),
        }

        # Calculate ratios
        if metrics["total_files"] > 0:
            metrics["smells_per_file"] = (
                metrics["total_smells"] / metrics["total_files"]
            )
        else:
            metrics["smells_per_file"] = 0.0

        # Placeholder for other metrics (would need more sophisticated analysis)
        metrics["duplication_percentage"] = 0.0
        metrics["avg_complexity"] = 0.0
        metrics["coverage_percentage"] = 0.0
        metrics["maintainability"] = 1.0

        return metrics

    def _apply_quality_gates(self, metrics: dict[str, float]) -> dict[str, bool]:
        """Apply quality gates to metrics"""
        results = {}

        for gate in self.quality_gates:
            metric_value = metrics.get(gate.metric, 0.0)

            if gate.operator == "lt":
                passed = metric_value < gate.threshold
            elif gate.operator == "lte":
                passed = metric_value <= gate.threshold
            elif gate.operator == "gt":
                passed = metric_value > gate.threshold
            elif gate.operator == "gte":
                passed = metric_value >= gate.threshold
            elif gate.operator == "eq":
                passed = metric_value == gate.threshold
            else:
                passed = False

            results[gate.name] = passed

            if gate.blocker and not passed:
                self.logger.warning("Blocker quality gate failed: %s", gate.name)

        return results

    def _group_by_severity(self, smells: list[CodeSmell]) -> dict[str, int]:
        """Group smells by severity level"""
        groups = {}
        for smell in smells:
            severity = smell.severity.value
            groups[severity] = groups.get(severity, 0) + 1
        return groups

    def _group_by_category(self, smells: list[CodeSmell]) -> dict[str, int]:
        """Group smells by category"""
        groups = {}
        for smell in smells:
            category = smell.category
            groups[category] = groups.get(category, 0) + 1
        return groups

    def generate_report(self, result: ScanResult, output_path: str = None) -> str:
        """
        Generate a comprehensive code smell report

        Args:
            result: Scan result to report on
            output_path: Optional path to save report

        Returns:
            Report content as string
        """
        self.logger.info("Generating code smell report")

        # Generate report content
        report_content = self._generate_report_content(result)

        # Save to file if path provided
        if output_path:
            with open(output_path, "w", encoding="utf-8") as f:
                f.write(report_content)
            self.logger.info("Report saved to: %s", output_path)

        return report_content

    def _generate_report_content(self, result: ScanResult) -> str:
        """Generate the actual report content"""
        report = []

        # Header
        report.append("# Code Smell Analysis Report")
        report.append(
            f"**Generated:** {result.timestamp.strftime('%Y-%m-%d %H:%M:%S')}"
        )
        report.append(f"**Scan Duration:** {result.scan_duration:.2f} seconds")
        report.append("")

        # Summary
        report.append("## Summary")
        report.append(f"- **Total Files Scanned:** {result.total_files}")
        report.append(f"- **Total Code Smells:** {result.total_smells}")
        report.append(
            f"- **Smells per File:** {result.total_smells / max(result.total_files, 1):.2f}"
        )
        report.append("")

        # Severity breakdown
        report.append("## Severity Breakdown")
        for severity, count in result.smells_by_severity.items():
            report.append(f"- **{severity.title()}:** {count}")
        report.append("")

        # Category breakdown
        report.append("## Category Breakdown")
        for category, count in result.smells_by_category.items():
            report.append(f"- **{category.title()}:** {count}")
        report.append("")

        # Quality gates
        report.append("## Quality Gates")
        for gate_name, passed in result.quality_gates.items():
            status = "✅ PASSED" if passed else "❌ FAILED"
            report.append(f"- **{gate_name}:** {status}")
        report.append("")

        # Top issues
        if result.smells:
            report.append("## Top Issues")

            # Group by file
            files_with_smells = {}
            for smell in result.smells:
                if smell.file_path not in files_with_smells:
                    files_with_smells[smell.file_path] = []
                files_with_smells[smell.file_path].append(smell)

            # Sort by number of smells
            sorted_files = sorted(
                files_with_smells.items(), key=lambda x: len(x[1]), reverse=True
            )

            for file_path, smells in sorted_files[:10]:  # Top 10 files
                report.append(f"### {file_path} ({len(smells)} issues)")

                # Sort smells by severity
                sorted_smells = sorted(smells, key=lambda x: x.severity.value)

                for smell in sorted_smells[:5]:  # Top 5 issues per file
                    report.append(
                        f"- Line {smell.line_number}: **{smell.severity.value}** - {smell.message} ({smell.rule_id})"
                    )

                if len(smells) > 5:
                    report.append(f"- ... and {len(smells) - 5} more issues")
                report.append("")

        return "\n".join(report)

    def save_baseline(self, smells: list[CodeSmell]) -> None:
        """Save current smells as baseline"""
        self.logger.info("Saving baseline with %d smells", len(smells))

        baseline_data = {
            "created_at": datetime.now().isoformat(),
            "total_smells": len(smells),
            "smells": [asdict(smell) for smell in smells],
        }

        # Ensure directory exists
        baseline_path = Path(self.config.baseline_path)
        baseline_path.parent.mkdir(parents=True, exist_ok=True)

        with open(baseline_path, "w", encoding="utf-8") as f:
            json.dump(baseline_data, f, indent=2, default=str)

        self.logger.info("Baseline saved to: %s", baseline_path)


async def main():
    """Main entry point for command-line usage"""

    parser = argparse.ArgumentParser(description="Code Smell Detection Tool")
    parser.add_argument("--config", help="Path to configuration file")
    parser.add_argument("--project", default=".", help="Project path to scan")
    parser.add_argument("--output", help="Output report path")
    parser.add_argument(
        "--baseline", action="store_true", help="Save current scan as baseline"
    )
    parser.add_argument(
        "--format",
        choices=["json", "markdown"],
        default="markdown",
        help="Output format",
    )

    args = parser.parse_args()

    # Initialize detector
    detector = CodeSmellDetector(args.config)

    # Run scan
    result = await detector.scan_project(args.project)

    # Generate report
    if args.format == "json":
        report_content = json.dumps(asdict(result), indent=2, default=str)
    else:
        report_content = detector.generate_report(result)

    # Save or print report
    if args.output:
        with open(args.output, "w", encoding="utf-8") as f:
            f.write(report_content)
        print(f"Report saved to: {args.output}")
    else:
        print(report_content)

    # Save baseline if requested
    if args.baseline:
        detector.save_baseline(result.smells)

    # Exit with appropriate code
    if (failed_gates := [name for name, passed in result.quality_gates.items() if not passed]):
        print(f"Quality gates failed: {', '.join(failed_gates)}")
        sys.exit(1)
    else:
        print("All quality gates passed!")
        sys.exit(0)


if __name__ == "__main__":
    asyncio.run(main())
