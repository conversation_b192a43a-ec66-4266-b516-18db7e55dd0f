"""
Configuration management for the code smell detection system.

This module handles loading and managing configuration settings from files
and provides default configurations.
"""

import json
import logging
from pathlib import Path
from typing import Any

from .models import QualityGate


class Config:
    """Configuration manager for the code smell detection system"""

    def __init__(self, config_path: str | None = None):
        """
        Initialize configuration manager.

        Args:
            config_path: Optional path to configuration file
        """
        self.config_data = self._load_config(config_path)
        self.logger = logging.getLogger(__name__)

    @classmethod
    def load(cls, config_path: str | None = None) -> "Config":
        """
        Load configuration from file or use defaults.

        Args:
            config_path: Optional path to configuration file

        Returns:
            Configured Config instance
        """
        return cls(config_path)

    def _load_config(self, config_path: str | None) -> dict[str, Any]:
        """Load configuration from file or use defaults"""
        default_config = {
            "enabled_tools": {
                "eslint": True,
                "sonarjs": True,
                "golangci-lint": True,
                "shellcheck": True,
                "sqlfluff": True,
                "hadolint": True,
                "yamllint": True,
                "markdownlint": True,
                "gitleaks": True,
                "bandit": True,
                "pylint": True,
            },
            "file_patterns": {
                "javascript": ["*.js", "*.jsx", "*.ts", "*.tsx"],
                "python": ["*.py"],
                "go": ["*.go"],
                "shell": ["*.sh", "*.bash"],
                "sql": ["*.sql"],
                "docker": ["Dockerfile", "*.dockerfile", "docker-compose*.yml"],
                "yaml": ["*.yml", "*.yaml"],
                "markdown": ["*.md"],
            },
            "exclude_patterns": [
                "node_modules/",
                ".git/",
                "volumes/",
                "logs/",
                "*.min.js",
                "*.min.css",
            ],
            "output_format": "json",
            "parallel_execution": True,
            "max_workers": 4,
            "baseline_path": ".github/linters/baseline.json",
        }

        if config_path and Path(config_path).exists():
            with open(config_path, "r", encoding="utf-8") as f:
                user_config = json.load(f)
                default_config.update(user_config)

        return default_config

    def get(self, key: str, default: Any = None) -> Any:
        """
        Get configuration value.

        Args:
            key: Configuration key
            default: Default value if key not found

        Returns:
            Configuration value
        """
        return self.config_data.get(key, default)

    def get_quality_gates(self) -> list[QualityGate]:
        """Load quality gate configurations"""
        return [
            QualityGate(
                "duplicated_lines", 3.0, "lt", "duplication_percentage", blocker=True
            ),
            QualityGate(
                "cyclomatic_complexity", 15.0, "lte", "avg_complexity", blocker=True
            ),
            QualityGate("blocker_issues", 0.0, "eq", "blocker_count", blocker=True),
            QualityGate("critical_issues", 0.0, "eq", "critical_count", blocker=True),
            QualityGate("security_hotspots", 0.0, "eq", "security_count", blocker=True),
            QualityGate(
                "test_coverage", 90.0, "gte", "coverage_percentage", blocker=False
            ),
            QualityGate(
                "maintainability_rating", 1.0, "eq", "maintainability", blocker=False
            ),
        ]

    @property
    def baseline_path(self) -> str:
        """Get baseline file path"""
        return self.get("baseline_path", ".github/linters/baseline.json")

    @property
    def parallel_execution(self) -> bool:
        """Get parallel execution setting"""
        return self.get("parallel_execution", True)

    @property
    def enabled_tools(self) -> dict[str, bool]:
        """Get enabled tools configuration"""
        return self.get("enabled_tools", {})

    @property
    def file_patterns(self) -> dict[str, list[str]]:
        """Get file patterns configuration"""
        return self.get("file_patterns", {})

    @property
    def exclude_patterns(self) -> list[str]:
        """Get exclude patterns configuration"""
        return self.get("exclude_patterns", [])
