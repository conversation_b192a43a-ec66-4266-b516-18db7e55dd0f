"""
Core data models for the code smell detection system.

This module contains all the data classes and enums used throughout the
code smell detection infrastructure.
"""

from dataclasses import dataclass
from datetime import datetime
from enum import Enum


class SeverityLevel(Enum):
    """Severity levels for code smells"""

    BLOCKER = "blocker"
    CRITICAL = "critical"
    MAJOR = "major"
    MINOR = "minor"
    INFO = "info"


@dataclass
class CodeSmell:
    """Represents a detected code smell"""

    file_path: str
    line_number: int
    column: int
    rule_id: str
    severity: SeverityLevel
    message: str
    category: str
    tool: str
    fixable: bool = False
    fix_suggestion: str | None = None


@dataclass
class QualityGate:
    """Quality gate configuration"""

    name: str
    threshold: float
    operator: str  # 'lt', 'lte', 'gt', 'gte', 'eq'
    metric: str
    blocker: bool = False


@dataclass
class ScanResult:
    """Results of a code smell scan"""

    total_files: int
    total_smells: int
    smells_by_severity: dict[str, int]
    smells_by_category: dict[str, int]
    quality_gates: dict[str, bool]
    files_scanned: list[str]
    smells: list[CodeSmell]
    scan_duration: float
    timestamp: datetime
