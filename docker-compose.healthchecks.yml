# Docker Compose Health Check Configuration
# Purpose: Add comprehensive health monitoring to all Lifeboard services
# Usage: Include this file for health check capabilities

services:
  db:
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U $${POSTGRES_USER:-supabase_admin} -d $${POSTGRES_DB:-postgres}"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    environment:
      - POSTGRES_USER=supabase_admin

  auth:
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:9999/health"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 60s
    depends_on:
      db:
        condition: service_healthy

  realtime:
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:4000/api/health"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 60s
    depends_on:
      db:
        condition: service_healthy
      auth:
        condition: service_healthy

  rest:
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000/"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 60s
    depends_on:
      db:
        condition: service_healthy

  storage:
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:5000/status"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 60s
    depends_on:
      db:
        condition: service_healthy
      rest:
        condition: service_healthy

  studio:
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 120s
    depends_on:
      db:
        condition: service_healthy
      auth:
        condition: service_healthy
      realtime:
        condition: service_healthy

  # Health monitoring service
  health_monitor:
    image: alpine/curl:latest
    container_name: lifeboard_health_monitor
    volumes:
      - ./scripts:/scripts:ro
      - ./logs:/logs
    networks:
      - lifeboard_net
    security_opt:
      - "no-new-privileges:true"
    cap_drop:
      - ALL
    profiles:
      - "health"
    restart: unless-stopped
    command: >
      sh -c "
        while true; do
          echo '{\"timestamp\":\"'$(date -u +%Y-%m-%dT%H:%M:%S.%3NZ)'\"\"level\":\"INFO\",\"component\":\"health_monitor\",\"message\":\"Health check cycle starting\"}' >> /logs/health_monitor_$(date +%Y%m%d).log
          sleep 60
        done
      "
    depends_on:
      - db
      - auth
      - realtime
      - rest
      - storage
