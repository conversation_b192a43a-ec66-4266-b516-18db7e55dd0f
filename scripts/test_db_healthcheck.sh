#!/bin/bash

# Test Database Health Check Integration
# Description: Test script to validate the database health check configuration
# Author: AI Assistant
# Created: 2025-07-07

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Logging function
log_test() {
    local level="$1"
    local message="$2"
    local timestamp
    timestamp=$(date '+%Y-%m-%dT%H:%M:%S.000Z')

    case "$level" in
        "PASS")
            echo -e "${GREEN}[PASS]${NC} $timestamp: $message"
            ;;
        "FAIL")
            echo -e "${RED}[FAIL]${NC} $timestamp: $message"
            ;;
        "INFO")
            echo -e "${YELLOW}[INFO]${NC} $timestamp: $message"
            ;;
    esac
}

# Test 1: Verify health check script exists and is executable
test_script_exists() {
    log_test "INFO" "Testing health check script existence and permissions"

    if [[ ! -f "scripts/db_healthcheck.sh" ]]; then
        log_test "FAIL" "Health check script not found at scripts/db_healthcheck.sh"
        return 1
    fi

    if [[ ! -x "scripts/db_healthcheck.sh" ]]; then
        log_test "FAIL" "Health check script is not executable"
        return 1
    fi

    log_test "PASS" "Health check script exists and is executable"
    return 0
}

# Test 2: Verify script syntax
test_script_syntax() {
    log_test "INFO" "Testing health check script syntax"

    if ! bash -n "scripts/db_healthcheck.sh"; then
        log_test "FAIL" "Health check script has syntax errors"
        return 1
    fi

    log_test "PASS" "Health check script syntax is valid"
    return 0
}

# Test 3: Verify Docker Compose configuration
test_docker_compose_config() {
    log_test "INFO" "Testing Docker Compose configuration"

    if ! docker compose config --quiet; then
        log_test "FAIL" "Docker Compose configuration is invalid"
        return 1
    fi

    log_test "PASS" "Docker Compose configuration is valid"
    return 0
}

# Test 4: Verify auth service configuration
test_auth_service_config() {
    log_test "INFO" "Testing auth service configuration"

    # Check if the auth service has the health check script volume mount
    if ! docker compose config | grep -q "db_healthcheck.sh"; then
        log_test "FAIL" "Auth service does not have health check script volume mount"
        return 1
    fi

    # Check if the auth service has the command configuration
    if ! docker compose config | grep -q "db_healthcheck.sh"; then
        log_test "FAIL" "Auth service does not have health check command"
        return 1
    fi

    log_test "PASS" "Auth service configuration includes health check script"
    return 0
}

# Test 5: Verify environment variables are set
test_environment_variables() {
    log_test "INFO" "Testing environment variables"

    if [[ ! -f ".env.local" ]]; then
        log_test "FAIL" "Environment file .env.local not found"
        return 1
    fi

    # Check required variables
    local required_vars=("POSTGRES_HOST" "POSTGRES_DB" "POSTGRES_PASSWORD")

    for var in "${required_vars[@]}"; do
        if ! grep -q "^$var=" .env.local; then
            log_test "FAIL" "Required environment variable $var not found in .env.local"
            return 1
        fi
    done

    log_test "PASS" "Required environment variables are configured"
    return 0
}

# Test 6: Verify logging directory structure
test_logging_directory() {
    log_test "INFO" "Testing logging directory structure"

    if [[ ! -d "logs" ]]; then
        log_test "FAIL" "Logs directory not found"
        return 1
    fi

    if [[ ! -d "logs/auth" ]]; then
        log_test "FAIL" "Auth logs directory not found"
        return 1
    fi

    log_test "PASS" "Logging directory structure is correct"
    return 0
}

# Main test execution
main() {
    log_test "INFO" "Starting database health check integration tests"

    local total_tests=0
    local passed_tests=0

    # Run all tests
    tests=(
        "test_script_exists"
        "test_script_syntax"
        "test_docker_compose_config"
        "test_auth_service_config"
        "test_environment_variables"
        "test_logging_directory"
    )

    for test in "${tests[@]}"; do
        total_tests=$((total_tests + 1))
        if $test; then
            passed_tests=$((passed_tests + 1))
        fi
    done

    # Summary
    echo ""
    log_test "INFO" "Test Summary: $passed_tests/$total_tests tests passed"

    if [[ $passed_tests -eq $total_tests ]]; then
        log_test "PASS" "All tests passed - health check integration is ready"
        return 0
    else
        log_test "FAIL" "Some tests failed - please fix issues before deployment"
        return 1
    fi
}

# Execute main function
main "$@"
