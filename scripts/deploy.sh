#!/bin/bash

# Lifeboard Deployment Script
# Supports multiple deployment profiles and environments
# Phase 6: CI/CD & Profiles implementation

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/.." && pwd)"
LOG_DIR="${PROJECT_ROOT}/logs"
LOG_FILE="${LOG_DIR}/deployment_$(date +%Y%m%d_%H%M%S).log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Ensure log directory exists
mkdir -p "${LOG_DIR}"

# Logging function with structured format
log() {
    local level="$1"
    shift
    local message="$*"
    local timestamp
    timestamp=$(date '+%Y-%m-%dT%H:%M:%S.000Z')
    local component="deployment"
    local session_id
    session_id="$(date +%s)_$$"

    # Structured JSON log format
    echo "{\"timestamp\":\"${timestamp}\",\"level\":\"${level}\",\"component\":\"${component}\",\"session_id\":\"${session_id}\",\"message\":\"${message}\"}" | tee -a "${LOG_FILE}"
}

log_info() { log "INFO" "$@"; }
log_warn() { log "WARN" "$@"; }
log_error() { log "ERROR" "$@"; }
log_success() { log "SUCCESS" "$@"; }

# Print colored output
print_status() {
    local status="$1"
    shift
    local message="$*"

    case "$status" in
        "INFO")
            echo -e "${BLUE}ℹ${NC}  $message"
            ;;
        "SUCCESS")
            echo -e "${GREEN}✓${NC}  $message"
            ;;
        "WARN")
            echo -e "${YELLOW}⚠${NC}  $message"
            ;;
        "ERROR")
            echo -e "${RED}✗${NC}  $message"
            ;;
    esac
}

# Usage information
usage() {
    cat << EOF
Lifeboard Deployment Script

Usage: $0 [OPTIONS] PROFILE

PROFILES:
    minimal     - Database only (for development/testing)
    base        - Core services (db, auth, rest, realtime, storage)
    studio      - Base + Studio UI
    s3          - Base + S3 storage services
    observability - Base + Logging and monitoring
    full        - All services including studio and observability
    production  - Production-ready configuration

OPTIONS:
    -e, --env FILE      Environment file (default: .env.local)
    -d, --detach        Run in detached mode
    -f, --force         Force rebuild containers
    -h, --help          Show this help message
    -v, --verbose       Verbose output
    --dry-run           Show what would be deployed without executing
    --health-check      Run health checks after deployment
    --clean             Clean up before deployment

EXAMPLES:
    $0 base                 # Deploy core services
    $0 --env .env.prod production  # Production deployment
    $0 --dry-run full       # Preview full deployment
    $0 --health-check studio  # Deploy with health checks

EOF
}

# Parse command line arguments
PROFILE=""
ENV_FILE=".env.local"
DETACH_MODE=""
FORCE_REBUILD=""
VERBOSE=""
DRY_RUN=""
HEALTH_CHECK=""
CLEAN=""

while [[ $# -gt 0 ]]; do
    case $1 in
        -e|--env)
            ENV_FILE="$2"
            shift 2
            ;;
        -d|--detach)
            DETACH_MODE="-d"
            shift
            ;;
        -f|--force)
            FORCE_REBUILD="--build"
            shift
            ;;
        -h|--help)
            usage
            exit 0
            ;;
        -v|--verbose)
            VERBOSE="--verbose"
            export VERBOSE  # Export for potential use in sub-scripts
            shift
            ;;
        --dry-run)
            DRY_RUN="true"
            shift
            ;;
        --health-check)
            HEALTH_CHECK="true"
            shift
            ;;
        --clean)
            CLEAN="true"
            shift
            ;;
        *)
            if [[ -z "$PROFILE" ]]; then
                PROFILE="$1"
            else
                echo "Unknown option: $1"
                usage
                exit 1
            fi
            shift
            ;;
    esac
done

# Validate profile
if [[ -z "$PROFILE" ]]; then
    echo "Error: Profile is required"
    usage
    exit 1
fi

# Validate environment file
if [[ ! -f "$ENV_FILE" ]]; then
    log_error "Environment file not found: $ENV_FILE"
    print_status "ERROR" "Environment file not found: $ENV_FILE"
    exit 1
fi

# Start deployment
print_status "INFO" "Starting Lifeboard deployment..."
log_info "Starting deployment with profile: $PROFILE"

# Load environment variables
log_info "Loading environment from: $ENV_FILE"
set -a
# shellcheck source=/dev/null
source "$ENV_FILE"
set +a

# Clean up if requested
if [[ "$CLEAN" == "true" ]]; then
    print_status "INFO" "Cleaning up existing containers..."
    if [[ "$DRY_RUN" != "true" ]]; then
        docker compose -p lifeboard down --volumes || true
        log_info "Cleanup completed"
    else
        echo "DRY RUN: docker compose -p lifeboard down --volumes"
    fi
fi

# Determine docker-compose command based on profile
get_compose_command() {
    local profile="$1"
    local base_cmd="docker compose -p lifeboard"

    case "$profile" in
        "minimal")
            echo "$base_cmd -f docker-compose.yml up ${DETACH_MODE} ${FORCE_REBUILD} db"
            ;;
        "base")
            echo "$base_cmd -f docker-compose.yml up ${DETACH_MODE} ${FORCE_REBUILD} db auth rest realtime storage"
            ;;
        "studio")
            echo "$base_cmd -f docker-compose.yml --profile studio up ${DETACH_MODE} ${FORCE_REBUILD}"
            ;;
        "s3")
            echo "$base_cmd -f docker-compose.yml -f docker-compose.s3.yml up ${DETACH_MODE} ${FORCE_REBUILD}"
            ;;
        "observability")
            echo "$base_cmd -f docker-compose.yml -f docker-compose.logging.yml --profile observability up ${DETACH_MODE} ${FORCE_REBUILD}"
            ;;
        "full")
            echo "$base_cmd -f docker-compose.yml -f docker-compose.logging.yml -f docker-compose.s3.yml --profile studio --profile observability up ${DETACH_MODE} ${FORCE_REBUILD}"
            ;;
        "production")
            echo "$base_cmd -f docker-compose.yml -f docker-compose.logging.yml --profile observability up ${DETACH_MODE} ${FORCE_REBUILD}"
            ;;
        *)
            log_error "Unknown profile: $profile"
            print_status "ERROR" "Unknown profile: $profile"
            exit 1
            ;;
    esac
}

# Get the compose command
COMPOSE_CMD=$(get_compose_command "$PROFILE")

print_status "INFO" "Deployment profile: $PROFILE"
print_status "INFO" "Environment file: $ENV_FILE"

if [[ "$DRY_RUN" == "true" ]]; then
    print_status "INFO" "DRY RUN MODE - Commands that would be executed:"
    echo "Command: $COMPOSE_CMD"
    log_info "Dry run completed for profile: $PROFILE"
    exit 0
fi

# Execute deployment
print_status "INFO" "Executing deployment..."
log_info "Executing command: $COMPOSE_CMD"

if eval "$COMPOSE_CMD"; then
    print_status "SUCCESS" "Deployment completed successfully"
    log_success "Deployment completed for profile: $PROFILE"
else
    print_status "ERROR" "Deployment failed"
    log_error "Deployment failed for profile: $PROFILE"
    exit 1
fi

# Wait for services to be ready
if [[ "$DETACH_MODE" == "-d" ]]; then
    print_status "INFO" "Waiting for services to be ready..."
    sleep 10

    # Show service status
    print_status "INFO" "Service status:"
    docker compose -p lifeboard ps
fi

# Run health checks if requested
if [[ "$HEALTH_CHECK" == "true" ]]; then
    print_status "INFO" "Running health checks..."

    if [[ -x "${PROJECT_ROOT}/tests/test_health_checks.sh" ]]; then
        if "${PROJECT_ROOT}/tests/test_health_checks.sh"; then
            print_status "SUCCESS" "Health checks passed"
            log_success "Health checks completed successfully"
        else
            print_status "WARN" "Some health checks failed"
            log_warn "Health checks completed with failures"
        fi
    else
        print_status "WARN" "Health check script not found or not executable"
        log_warn "Health check script not available"
    fi
fi

# Deployment summary
print_status "SUCCESS" "Lifeboard deployment completed!"
print_status "INFO" "Profile: $PROFILE"
print_status "INFO" "Environment: $ENV_FILE"
print_status "INFO" "Logs: $LOG_FILE"

if [[ "$PROFILE" == "studio" || "$PROFILE" == "full" ]]; then
    print_status "INFO" "Studio UI available at: http://localhost:${STUDIO_PORT:-3000}"
fi

if [[ "$PROFILE" == "observability" || "$PROFILE" == "full" ]]; then
    print_status "INFO" "Grafana available at: http://localhost:9811"
fi

log_success "Deployment script completed successfully"
