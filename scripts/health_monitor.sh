#!/bin/bash

# Health Monitoring Script
# Description: Continuous health monitoring for Lifeboard services
# Author: AI Assistant
# Created: 2025-07-02
# Phase: 5 - Health & Tests

set -euo pipefail

# Configuration
LOG_DIR="/logs"
HEALTH_LOG="$LOG_DIR/health_monitor_$(date +%Y%m%d).log"
CHECK_INTERVAL=60  # seconds
ALERT_THRESHOLD=3  # consecutive failures before alert

# Services to monitor
SERVICES=("db" "auth" "realtime" "rest" "storage" "studio")

# Counters for failed health checks
declare -A failure_counts

# Initialize failure counters
for service in "${SERVICES[@]}"; do
    failure_counts[$service]=0
done

# Logging function
log_health() {
    local level="$1"
    local service="$2"
    local message="$3"
    local timestamp
    timestamp=$(date '+%Y-%m-%dT%H:%M:%S.000Z')

    echo "{\"timestamp\":\"$timestamp\",\"level\":\"$level\",\"component\":\"health_monitor\",\"service\":\"$service\",\"message\":\"$message\"}" >> "$HEALTH_LOG"
}

# Check individual service health
check_service_health() {
    local service="$1"
    local container_name="lifeboard-${service}-1"

    # Check if container is running
    if ! docker ps --format "{{.Names}}" | grep -q "^${container_name}$"; then
        log_health "ERROR" "$service" "Container not running"
        return 1
    fi

    # Check health status
    local health_status
    health_status=$(docker inspect --format='{{.State.Health.Status}}' "$container_name" 2>/dev/null || echo "unknown")

    case "$health_status" in
        "healthy")
            log_health "INFO" "$service" "Service is healthy"
            failure_counts[$service]=0
            return 0
            ;;
        "unhealthy")
            log_health "WARN" "$service" "Service is unhealthy"
            failure_counts[$service]=$((failure_counts[$service] + 1))
            return 1
            ;;
        "starting")
            log_health "INFO" "$service" "Service is starting"
            return 0
            ;;
        *)
            log_health "WARN" "$service" "Unknown health status: $health_status"
            failure_counts[$service]=$((failure_counts[$service] + 1))
            return 1
            ;;
    esac
}

# Check all services
check_all_services() {
    local unhealthy_services=()

    for service in "${SERVICES[@]}"; do
        if ! check_service_health "$service"; then
            unhealthy_services+=("$service")

            # Check if we've reached alert threshold
            if [[ ${failure_counts[$service]} -ge $ALERT_THRESHOLD ]]; then
                log_health "CRITICAL" "$service" "Service has failed $ALERT_THRESHOLD consecutive health checks"
            fi
        fi
    done

    # Log overall health status
    if [[ ${#unhealthy_services[@]} -eq 0 ]]; then
        log_health "INFO" "system" "All services are healthy"
    else
        log_health "WARN" "system" "Unhealthy services: ${unhealthy_services[*]}"
    fi
}

# Main monitoring loop
main() {
    log_health "INFO" "system" "Health monitoring started"

    while true; do
        check_all_services
        sleep $CHECK_INTERVAL
    done
}

# Handle signals
trap 'log_health "INFO" "system" "Health monitoring stopped"; exit 0' SIGTERM SIGINT

# Execute main function
main "$@"
