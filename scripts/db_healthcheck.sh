#!/bin/bash

# Database Health Check Script
# Description: Lightweight health check for PostgreSQL database connectivity
# Author: AI Assistant
# Created: 2025-07-07
# Usage: Executed by auth container CMD to verify database connectivity

set -euo pipefail

# Configuration
DB_HOST="${POSTGRES_HOST:-db}"
DB_PORT="${POSTGRES_PORT:-5432}"
DB_NAME="${POSTGRES_DB:-postgres}"
DB_USER="${POSTGRES_USER:-authenticator}"
DB_PASSWORD="${POSTGRES_PASSWORD:-}"

# Logging configuration
LOG_DIR="/app/logs"
LOG_FILE="$LOG_DIR/db_healthcheck_$(date +%Y%m%d_%H%M%S).log"

# Ensure log directory exists
mkdir -p "$LOG_DIR"

# Logging function with structured JSON output
log_health() {
    local level="$1"
    local message="$2"
    local timestamp
    timestamp=$(date '+%Y-%m-%dT%H:%M:%S.000Z')

    # Create structured log entry
    local log_entry="{\"timestamp\":\"$timestamp\",\"level\":\"$level\",\"component\":\"db_healthcheck\",\"service\":\"auth\",\"message\":\"$message\"}"

    # Write to log file
    echo "$log_entry" >> "$LOG_FILE"

    # Also output to stdout for container logs
    echo "$log_entry"
}

# Database connectivity check function
check_database_connectivity() {
    local connection_string="postgresql://${DB_USER}:${DB_PASSWORD}@${DB_HOST}:${DB_PORT}/${DB_NAME}"

    log_health "INFO" "Starting database connectivity check for authenticator role"

    # Check 1: Basic connection test
    if ! psql "$connection_string" -c "SELECT 1;" &>/dev/null; then
        log_health "ERROR" "Failed to connect to database using authenticator role"
        return 1
    fi

    log_health "INFO" "Database connection successful"

    # Check 2: Test authenticator role permissions
    if ! psql "$connection_string" -c "SELECT current_user, current_database();" &>/dev/null; then
        log_health "ERROR" "Failed to query database with authenticator role"
        return 1
    fi

    log_health "INFO" "Authenticator role permissions verified"

    # Check 3: Test basic read access to system tables
    if ! psql "$connection_string" -c "SELECT version();" &>/dev/null; then
        log_health "ERROR" "Failed to read system information"
        return 1
    fi

    log_health "INFO" "Database system information accessible"

    # Check 4: Test schema access
    if ! psql "$connection_string" -c "SELECT schema_name FROM information_schema.schemata WHERE schema_name = 'public';" &>/dev/null; then
        log_health "ERROR" "Failed to access public schema"
        return 1
    fi

    log_health "INFO" "Public schema access verified"

    return 0
}

# Main health check function
main() {
    log_health "INFO" "Database health check initiated"

    # Validate required environment variables
    if [[ -z "$DB_PASSWORD" ]]; then
        log_health "ERROR" "POSTGRES_PASSWORD environment variable is required"
        exit 1
    fi

    # Check if psql is available
    if ! command -v psql &>/dev/null; then
        log_health "ERROR" "psql command not found - PostgreSQL client not installed"
        exit 1
    fi

    # Perform database connectivity check
    if check_database_connectivity; then
        log_health "INFO" "Database health check completed successfully"
        exit 0
    else
        log_health "ERROR" "Database health check failed"
        exit 1
    fi
}

# Handle signals gracefully
trap 'log_health "WARN" "Health check interrupted"; exit 130' SIGINT SIGTERM

# Execute main function
main "$@"
