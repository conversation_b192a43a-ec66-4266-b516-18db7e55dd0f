services:
  db:
    image: public.ecr.aws/supabase/postgres:15.6.1.124
    env_file:
      - ./.env.local
    ports:
      - "5543:5432"  # Map external port 5543 to internal port 5432 for testing
    volumes:
      - ./volumes/db:/var/lib/postgresql/data
      - ./docker-entrypoint-initdb.d:/docker-entrypoint-initdb.d
      - ./db/migrations:/docker-entrypoint-initdb.d/migrations
      - ./logs/postgres:/var/log/postgresql
    environment:
      - POSTGRES_LOG_DESTINATION=stderr,csvlog
      - POSTGRES_LOG_DIRECTORY=/var/log/postgresql
      - POSTGRES_LOG_FILENAME=postgresql-%Y%m%d_%H%M%S.log
      - POSTGRES_LOG_ROTATION_AGE=1d
      - POSTGRES_LOG_ROTATION_SIZE=100MB
      - POSTGRES_LOG_MIN_MESSAGES=info
      - POSTGRES_LOG_MIN_DURATION_STATEMENT=1000
      - POSTGRES_LOG_CONNECTIONS=on
      - POSTGRES_LOG_DISCONNECTIONS=on
      - POSTGRES_LOG_STATEMENT=ddl
      - custom.db_password_authenticator=${POSTGRES_PASSWORD}
      - custom.db_password_anon=${POSTGRES_PASSWORD}
      - custom.db_password_authenticated=${POSTGRES_PASSWORD}
      - custom.db_password_service_role=${POSTGRES_PASSWORD}
      - custom.db_password_postgres=${POSTGRES_PASSWORD}
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
        max-file: "5"
        labels: "service=postgres,component=database,environment=development"
    networks:
      - supabase_internal
    security_opt:
      - "no-new-privileges:true"
    cap_drop:
      - ALL
    cap_add:
      - SETGID
      - SETUID
      - DAC_OVERRIDE
      - CHOWN
      - FOWNER
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U supabase_admin -d postgres"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  auth:
    image: public.ecr.aws/supabase/gotrue:v2.158.1
    env_file:
      - ./.env.local
    volumes:
      - ./logs/auth:/app/logs
      - ./scripts:/app/scripts:ro
    environment:
      - LOG_LEVEL=debug
      - LOG_FILE=/app/logs/gotrue-%Y%m%d_%H%M%S.log
      - POSTGRES_HOST=db
      - POSTGRES_USER=authenticator
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - DB_ANON_ROLE=anon
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
        max-file: "5"
        labels: "service=gotrue,component=auth,environment=development"
    depends_on:
      db:
        condition: service_healthy
    networks:
      - supabase_internal
    security_opt:
      - "no-new-privileges:true"
    cap_drop:
      - ALL
    restart: unless-stopped
    # command: [
    #   "sh", "-c",
    #   "chmod +x /app/scripts/db_healthcheck.sh && /app/scripts/db_healthcheck.sh && exec gotrue"
    # ]
    # Healthcheck removed to avoid conflicts

  realtime:
    image: public.ecr.aws/supabase/realtime:v2.30.34
    env_file:
      - ./.env.local
    volumes:
      - ./logs/realtime:/app/logs
    environment:
      - RLIMIT_NOFILE=1048576
      - LOG_LEVEL=debug
      - LOG_FILE=/app/logs/realtime-%Y%m%d_%H%M%S.log
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
        max-file: "5"
        labels: "service=realtime,component=websocket,environment=development"
    depends_on:
      db:
        condition: service_healthy
    networks:
      - supabase_internal
    security_opt:
      - "no-new-privileges:true"
    cap_drop:
      - ALL
    cap_add:
      - SETUID
      - SETGID
    restart: unless-stopped
    # Healthcheck removed to avoid conflicts

  rest:
    build:
      context: .
      dockerfile: Dockerfile.postgrest
    image: lifeboard/postgrest:dev
    env_file:
      - ./.env.local
    volumes:
      - ./logs/rest:/app/logs
    environment:
      - PGRST_LOG_LEVEL=info
      - LOG_FILE=/app/logs/postgrest-%Y%m%d_%H%M%S.log
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
        max-file: "5"
        labels: "service=postgrest,component=api,environment=development"
    depends_on:
      db:
        condition: service_healthy
    ports:
      - "${SUPABASE_PORT}:3000"
    networks:
      - supabase_internal
    security_opt:
      - "no-new-privileges:true"
    cap_drop:
      - ALL
    restart: unless-stopped
    # Healthcheck removed to avoid conflicts

  storage:
    image: public.ecr.aws/supabase/storage-api:v1.11.8
    env_file:
      - ./.env.local
    volumes:
      - ./logs/storage:/app/logs
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - LOG_LEVEL=debug
      - LOG_FILE=/app/logs/storage-%Y%m%d_%H%M%S.log
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
        max-file: "5"
        labels: "service=storage,component=api,environment=development"
    depends_on:
      db:
        condition: service_healthy
    networks:
      - supabase_internal
    security_opt:
      - "no-new-privileges:true"
    cap_drop:
      - ALL
    restart: unless-stopped
    # Healthcheck removed to avoid conflicts

  studio:
    image: public.ecr.aws/supabase/studio:latest
    env_file:
      - ./.env.local
    volumes:
      - ./logs/studio:/app/logs
    environment:
      - LOG_LEVEL=info
      - LOG_FILE=/app/logs/studio-%Y%m%d_%H%M%S.log
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
        max-file: "5"
        labels: "service=studio,component=ui,environment=development"
    depends_on:
      db:
        condition: service_healthy
    ports:
      - "${STUDIO_PORT}:3000"
    networks:
      - supabase_internal
    security_opt:
      - "no-new-privileges:true"
    cap_drop:
      - ALL
    restart: unless-stopped
    profiles:
      - "studio"
      - "full"
    # Healthcheck removed to avoid conflicts

networks:
  supabase_internal:
    name: supabase_internal
