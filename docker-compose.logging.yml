# Docker Compose Logging Configuration
# Purpose: Enhance observability with structured logging and centralized log management
# Usage: Include this file for comprehensive logging setup

services:
  db:
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
        max-file: "5"
        labels: "service=postgres,component=database,environment=development"
    volumes:
      - ./logs/postgres:/var/log/postgresql
    environment:
      - POSTGRES_LOG_DESTINATION=stderr,csvlog
      - POSTGRES_LOG_DIRECTORY=/var/log/postgresql
      - POSTGRES_LOG_FILENAME=postgresql-%Y%m%d_%H%M%S.log
      - POSTGRES_LOG_ROTATION_AGE=1d
      - POSTGRES_LOG_ROTATION_SIZE=100MB
      - POSTGRES_LOG_MIN_MESSAGES=info
      - POSTGRES_LOG_MIN_DURATION_STATEMENT=1000
      - POSTGRES_LOG_CONNECTIONS=on
      - POSTGRES_LOG_DISCONNECTIONS=on
      - POSTGRES_LOG_STATEMENT=ddl

  auth:
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
        max-file: "5"
        labels: "service=gotrue,component=auth,environment=development"
    volumes:
      - ./logs/auth:/app/logs
    environment:
      - LOG_LEVEL=debug
      - LOG_FILE=/app/logs/gotrue-%Y%m%d_%H%M%S.log

  realtime:
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
        max-file: "5"
        labels: "service=realtime,component=websocket,environment=development"
    volumes:
      - ./logs/realtime:/app/logs
    environment:
      - LOG_LEVEL=debug
      - LOG_FILE=/app/logs/realtime-%Y%m%d_%H%M%S.log

  rest:
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
        max-file: "5"
        labels: "service=postgrest,component=api,environment=development"
    volumes:
      - ./logs/rest:/app/logs
    environment:
      - PGRST_LOG_LEVEL=info
      - LOG_FILE=/app/logs/postgrest-%Y%m%d_%H%M%S.log

  storage:
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
        max-file: "5"
        labels: "service=storage,component=api,environment=development"
    volumes:
      - ./logs/storage:/app/logs
    environment:
      - LOG_LEVEL=debug
      - LOG_FILE=/app/logs/storage-%Y%m%d_%H%M%S.log

  studio:
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
        max-file: "5"
        labels: "service=studio,component=ui,environment=development"
    volumes:
      - ./logs/studio:/app/logs
    environment:
      - LOG_LEVEL=info
      - LOG_FILE=/app/logs/studio-%Y%m%d_%H%M%S.log

  # Log aggregation and management service
  log_aggregator:
    image: grafana/promtail:latest
    container_name: lifeboard_log_aggregator
    volumes:
      - ./logs:/var/log/lifeboard:ro
      - ./config/promtail:/etc/promtail
    networks:
      - lifeboard_net
    security_opt:
      - "no-new-privileges:true"
    cap_drop:
      - ALL
    cap_add:
      - DAC_OVERRIDE  # Allow reading log files
    profiles:
      - "observability"
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:9080/ready"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
        labels: "service=promtail,component=log-aggregator,environment=development"

  # Optional: Loki for log storage and analysis
  loki:
    image: grafana/loki:latest
    container_name: lifeboard_loki
    ports:
      - "9810:3100"  # High port per user rules
    volumes:
      - ./volumes/loki:/loki
      - ./config/loki:/etc/loki
    networks:
      - lifeboard_net
    security_opt:
      - "no-new-privileges:true"
    cap_drop:
      - ALL
    profiles:
      - "observability"
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3100/ready"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
        labels: "service=loki,component=log-storage,environment=development"

  # Optional: Grafana for log visualization
  grafana:
    image: grafana/grafana:latest
    container_name: lifeboard_grafana
    ports:
      - "9811:3000"  # High port per user rules
    volumes:
      - ./volumes/grafana:/var/lib/grafana
      - ./config/grafana:/etc/grafana
    networks:
      - lifeboard_net
    security_opt:
      - "no-new-privileges:true"
    cap_drop:
      - ALL
    profiles:
      - "observability"
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=lifeboard_dev_admin
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_INSTALL_PLUGINS=grafana-clock-panel,grafana-simple-json-datasource
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
        labels: "service=grafana,component=visualization,environment=development"
