-- API Keys Functions and Security Policies
-- This file contains encryption functions, stored procedures, and RLS policies for API keys

-- Insert Vault secret placeholder for API encryption key
INSERT INTO vault.secrets (id, secret) VALUES ('API_ENC_KEY', 'default_api_encryption_key_replace_in_prod') ON CONFLICT (id) DO NOTHING;

-- Encryption helper function
CREATE OR REPLACE FUNCTION encrypt_api_key(raw_key text)
RETURNS text
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    secret text;
BEGIN
    -- Get encryption key from vault
    SELECT vault.decrypt_secret('API_ENC_KEY') INTO secret;

    -- Encrypt the API key using pgcrypto
    RETURN pgp_sym_encrypt(raw_key, secret);
END;
$$;

-- Decryption helper function
CREATE OR REPLACE FUNCTION decrypt_api_key(enc_key text)
RETURNS text
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    secret text;
BEGIN
    -- Get encryption key from vault
    SELECT vault.decrypt_secret('API_ENC_KEY') INTO secret;

    -- Decrypt the API key using pgcrypto
    RETURN pgp_sym_decrypt(enc_key, secret);
END;
$$;

-- Stored procedure to create API key
CREATE OR REPLACE FUNCTION sp_create_api_key(
    p_plugin_id TEXT,
    p_key_name TEXT,
    p_raw_key TEXT
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_user_id UUID;
    v_api_key_id UUID;
    v_encrypted_key TEXT;
BEGIN
    -- Get authenticated user ID
    SELECT auth.uid() INTO v_user_id;

    -- Validate user is authenticated
    IF v_user_id IS NULL THEN
        RAISE EXCEPTION 'Authentication required';
    END IF;

    -- Encrypt the API key
    v_encrypted_key := encrypt_api_key(p_raw_key);

    -- Insert the API key
    INSERT INTO api_keys (plugin_id, key_name, encrypted_key, user_id)
    VALUES (p_plugin_id, p_key_name, v_encrypted_key, v_user_id)
    RETURNING id INTO v_api_key_id;

    -- Audit the creation
    INSERT INTO audit_api_key_access (api_key_id, action, performed_by)
    VALUES (v_api_key_id, 'CREATE', v_user_id);

    RETURN v_api_key_id;
END;
$$;

-- Stored procedure to get API keys for a plugin
CREATE OR REPLACE FUNCTION sp_get_api_keys(p_plugin_id TEXT)
RETURNS TABLE(
    id UUID,
    plugin_id TEXT,
    key_name TEXT,
    decrypted_key TEXT,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_user_id UUID;
    v_api_key_record RECORD;
BEGIN
    -- Get authenticated user ID
    SELECT auth.uid() INTO v_user_id;

    -- Validate user is authenticated
    IF v_user_id IS NULL THEN
        RAISE EXCEPTION 'Authentication required';
    END IF;

    -- Return decrypted API keys for the user and plugin
    FOR v_api_key_record IN
        SELECT ak.id, ak.plugin_id, ak.key_name, ak.encrypted_key, ak.created_at, ak.updated_at
        FROM api_keys ak
        WHERE ak.user_id = v_user_id AND ak.plugin_id = p_plugin_id
    LOOP
        -- Audit the access
        INSERT INTO audit_api_key_access (api_key_id, action, performed_by)
        VALUES (v_api_key_record.id, 'READ', v_user_id);

        -- Return the decrypted key
        RETURN QUERY SELECT
            v_api_key_record.id,
            v_api_key_record.plugin_id,
            v_api_key_record.key_name,
            decrypt_api_key(v_api_key_record.encrypted_key),
            v_api_key_record.created_at,
            v_api_key_record.updated_at;
    END LOOP;

    RETURN;
END;
$$;

-- Stored procedure to update API key
CREATE OR REPLACE FUNCTION sp_update_api_key(
    p_api_key_id UUID,
    p_raw_key TEXT
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_user_id UUID;
    v_encrypted_key TEXT;
    v_updated_count INT;
BEGIN
    -- Get authenticated user ID
    SELECT auth.uid() INTO v_user_id;

    -- Validate user is authenticated
    IF v_user_id IS NULL THEN
        RAISE EXCEPTION 'Authentication required';
    END IF;

    -- Encrypt the new API key
    v_encrypted_key := encrypt_api_key(p_raw_key);

    -- Update the API key (only if owned by user)
    UPDATE api_keys
    SET encrypted_key = v_encrypted_key, updated_at = NOW()
    WHERE id = p_api_key_id AND user_id = v_user_id;

    GET DIAGNOSTICS v_updated_count = ROW_COUNT;

    -- Audit the update if successful
    IF v_updated_count > 0 THEN
        INSERT INTO audit_api_key_access (api_key_id, action, performed_by)
        VALUES (p_api_key_id, 'UPDATE', v_user_id);
        RETURN TRUE;
    END IF;

    RETURN FALSE;
END;
$$;

-- Stored procedure to delete API key
CREATE OR REPLACE FUNCTION sp_delete_api_key(p_api_key_id UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_user_id UUID;
    v_deleted_count INT;
BEGIN
    -- Get authenticated user ID
    SELECT auth.uid() INTO v_user_id;

    -- Validate user is authenticated
    IF v_user_id IS NULL THEN
        RAISE EXCEPTION 'Authentication required';
    END IF;

    -- Audit the deletion before deleting
    INSERT INTO audit_api_key_access (api_key_id, action, performed_by)
    VALUES (p_api_key_id, 'DELETE', v_user_id);

    -- Delete the API key (only if owned by user)
    DELETE FROM api_keys
    WHERE id = p_api_key_id AND user_id = v_user_id;

    GET DIAGNOSTICS v_deleted_count = ROW_COUNT;

    RETURN v_deleted_count > 0;
END;
$$;

-- Enable Row Level Security
ALTER TABLE api_keys ENABLE ROW LEVEL SECURITY;
ALTER TABLE audit_api_key_access ENABLE ROW LEVEL SECURITY;

-- RLS Policy: Users can only access their own API keys
CREATE POLICY api_keys_user_isolation ON api_keys
    FOR ALL
    TO authenticated
    USING (user_id = auth.uid())
    WITH CHECK (user_id = auth.uid());

-- RLS Policy: Users can only access their own audit records
CREATE POLICY audit_api_key_access_user_isolation ON audit_api_key_access
    FOR ALL
    TO authenticated
    USING (performed_by = auth.uid())
    WITH CHECK (performed_by = auth.uid());

-- Grant permissions to authenticated users
GRANT SELECT, INSERT, UPDATE, DELETE ON api_keys TO authenticated;
GRANT SELECT, INSERT ON audit_api_key_access TO authenticated;

-- Grant execute permissions on functions
GRANT EXECUTE ON FUNCTION sp_create_api_key(TEXT, TEXT, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION sp_get_api_keys(TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION sp_update_api_key(UUID, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION sp_delete_api_key(UUID) TO authenticated;
