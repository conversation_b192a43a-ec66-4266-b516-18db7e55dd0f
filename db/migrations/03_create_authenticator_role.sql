-- Create authenticator role and grant privileges
-- Extracted from volumes/db/01_roles.sql relevant portion

DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'authenticator') THEN
        CREATE ROLE authenticator LOGIN NOINHERIT;
    END IF;
END
$$;

-- Grant anon & authenticated roles to authenticator (these should exist already)
GRANT anon, authenticated TO authenticator;
