-- Fix Database Issues for Migration Smoke Test
-- Address missing functions, RLS, and table structure issues

-- 1. <PERSON>reate missing update_updated_at_column function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 2. Create triggers for updated_at columns
DO $$
BEGIN
    -- Drop existing triggers if they exist
    DROP TRIGGER IF EXISTS update_posts_updated_at ON posts;
    DROP TRIGGER IF EXISTS update_plugins_updated_at ON plugins;
    DROP TRIGGER IF EXISTS update_user_preferences_updated_at ON user_preferences;

    -- Create triggers
    CREATE TRIGGER update_posts_updated_at
        BEFORE UPDATE ON posts
        FOR EACH ROW
        EXECUTE FUNCTION update_updated_at_column();

    CREATE TRIGGER update_plugins_updated_at
        BEFORE UPDATE ON plugins
        FOR EACH ROW
        EXECUTE FUNCTION update_updated_at_column();

    CREATE TRIGGER update_user_preferences_updated_at
        BEFORE UPDATE ON user_preferences
        FOR EACH ROW
        EXECUTE FUNCTION update_updated_at_column();
END $$;

-- 3. Enable RLS on all tables
ALTER TABLE posts ENABLE ROW LEVEL SECURITY;
ALTER TABLE tags ENABLE ROW LEVEL SECURITY;
ALTER TABLE post_tags ENABLE ROW LEVEL SECURITY;
ALTER TABLE plugins ENABLE ROW LEVEL SECURITY;
ALTER TABLE plugin_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_preferences ENABLE ROW LEVEL SECURITY;

-- 4. Create missing indexes
CREATE INDEX IF NOT EXISTS idx_app_logs_created_at ON app_logs(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_app_logs_level ON app_logs(level);

-- 5. Fix app_logs table structure if needed
DO $$
BEGIN
    -- Check if context column exists and has correct type
    IF EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'app_logs' AND column_name = 'context' AND data_type != 'jsonb'
    ) THEN
        -- Convert context column to JSONB if it's not already
        ALTER TABLE app_logs ALTER COLUMN context TYPE JSONB USING context::JSONB;
    END IF;

    -- Add context column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'app_logs' AND column_name = 'context'
    ) THEN
        ALTER TABLE app_logs ADD COLUMN context JSONB DEFAULT '{}'::jsonb;
    END IF;
END $$;

-- 6. Ensure plugin_logs table exists with correct structure
CREATE TABLE IF NOT EXISTS plugin_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    plugin_id UUID REFERENCES plugins(id) ON DELETE CASCADE,
    level VARCHAR(20) NOT NULL,
    message TEXT NOT NULL,
    context JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for plugin_logs if they don't exist
CREATE INDEX IF NOT EXISTS idx_plugin_logs_plugin_id ON plugin_logs(plugin_id);
CREATE INDEX IF NOT EXISTS idx_plugin_logs_created_at ON plugin_logs(created_at DESC);

-- Enable RLS on plugin_logs
ALTER TABLE plugin_logs ENABLE ROW LEVEL SECURITY;
