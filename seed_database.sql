-- Seed Database with Essential Data
-- This script inserts the minimum required data for tests to pass

-- 1. Insert essential tags
INSERT INTO tags (name, color, description, ai_generated) VALUES
    ('reflection', '#6366f1', 'Personal reflection and introspection', false),
    ('growth', '#10b981', 'Personal development and growth', false),
    ('gratitude', '#f59e0b', 'Things to be grateful for', false),
    ('relationships', '#ef4444', 'Family, friends, and personal connections', false),
    ('health', '#8b5cf6', 'Physical and mental health', false),
    ('work', '#3b82f6', 'Professional and career-related', false),
    ('creativity', '#f97316', 'Creative projects and inspiration', false),
    ('learning', '#06b6d4', 'Education and skill development', false),
    ('travel', '#84cc16', 'Travel experiences and adventures', false),
    ('goals', '#ec4899', 'Personal and professional goals', false)
ON CONFLICT (name) DO NOTHING;

-- 2. Insert essential plugins
INSERT INTO plugins (name, version, enabled, configuration, sync_frequency_minutes) VALUES
    ('manual-journal', '1.0.0', true,
     '{"description": "Manual journaling plugin for personal reflection"}', 60),
    ('location-tracker', '1.0.0', true,
     '{"description": "Track location data for life mapping", "privacy_level": "high"}', 30),
    ('spotify-integration', '1.0.0', true,
     '{"description": "Integrate with Spotify for music-based life tracking", "api_enabled": false}', 120),
    ('health-tracker', '1.0.0', true,
     '{"description": "Basic health and wellness tracking", "metrics": ["mood", "energy", "sleep"]}', 480),
    ('photo-journal', '1.0.0', false,
     '{"description": "Photo-based journaling and memory capture"}', 1440)
ON CONFLICT (name, user_id) DO NOTHING;

-- 3. Insert default user preferences template
-- Note: Since user_id is required and references auth.users, we'll create a template record
-- that can be used as a reference for new users
INSERT INTO user_preferences (user_id, ai_prompts, feed_settings, theme_settings)
SELECT
    '00000000-0000-0000-0000-000000000000'::uuid, -- Template UUID
    '{"reflection_style": "thoughtful", "tone": "encouraging", "focus": "growth"}'::jsonb,
    '{"posts_per_page": 20, "auto_refresh": true, "show_ai_suggestions": true}'::jsonb,
    '{"color_scheme": "auto", "font_size": "medium", "compact_mode": false}'::jsonb
WHERE NOT EXISTS (SELECT 1 FROM user_preferences)
ON CONFLICT (user_id) DO NOTHING;

-- 4. Insert seeding log entries
INSERT INTO app_logs (level, service, message, context) VALUES
    ('INFO', 'database', 'Database seeding started - Essential tags inserted',
     jsonb_build_object('seed_type', 'tags', 'count', 10, 'timestamp', NOW()::text)),
    ('INFO', 'database', 'Database seeding continued - Essential plugins inserted',
     jsonb_build_object('seed_type', 'plugins', 'count', 5, 'timestamp', NOW()::text)),
    ('INFO', 'database', 'Database seeding continued - Default user preferences template created',
     jsonb_build_object('seed_type', 'user_preferences', 'template_id', '00000000-0000-0000-0000-000000000000', 'timestamp', NOW()::text)),
    ('INFO', 'database', 'Database seeding completed successfully - All essential data loaded',
     jsonb_build_object('seed_type', 'complete', 'status', 'success', 'timestamp', NOW()::text));
