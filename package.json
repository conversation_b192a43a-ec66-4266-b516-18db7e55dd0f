{"name": "lifeboard-supabase", "version": "1.0.0", "description": "![CI/CD Status](https://github.com/bbookman/lifeboard-supabase/actions/workflows/ci-cd.yml/badge.svg)", "main": ".eslintrc.js", "directories": {"test": "tests"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://github.com/bbookman/lifeboard-supabase.git"}, "keywords": [], "author": "", "license": "ISC", "bugs": {"url": "https://github.com/bbookman/lifeboard-supabase/issues"}, "homepage": "https://github.com/bbookman/lifeboard-supabase#readme", "devDependencies": {"jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jsdom": "^26.1.0"}}