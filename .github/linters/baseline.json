{"created_at": "2025-07-03T10:50:30.627849", "smells": [{"category": "unknown", "column": 0, "file_path": ".pyl<PERSON><PERSON>", "fix_suggestion": null, "fixable": false, "line_number": 1, "message": "Unrecognized option found: include-ids, symbols, files-output", "rule_id": "E0015", "severity": "SeverityLevel.CRITICAL", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": ".pyl<PERSON><PERSON>", "fix_suggestion": null, "fixable": false, "line_number": 1, "message": "Useless option value for '--enable', 'C0326' was removed from pylint, see https://github.com/pylint-dev/pylint/pull/3577.", "rule_id": "R0022", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 87, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 100, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 105, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 108, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 117, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 121, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 160, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 165, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 167, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 179, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 186, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 199, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 203, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 210, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 213, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 216, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 219, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 223, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 235, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 238, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 240, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 245, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 250, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 254, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 257, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 268, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 270, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 274, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 277, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 284, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 287, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 295, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 297, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 301, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 304, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 309, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 311, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 315, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 323, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 325, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 329, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 349, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 353, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 357, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 361, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 363, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 367, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 371, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 375, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 377, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 381, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 385, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 387, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 391, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 395, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 397, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 401, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 405, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 407, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 411, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 415, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 417, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 421, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 425, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 427, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 431, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 435, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 437, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 442, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 444, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 451, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 453, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 457, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 459, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 463, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 468, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 470, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 477, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 480, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 484, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 489, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 491, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 498, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 501, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 505, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 510, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 512, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 518, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 521, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 525, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 530, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 532, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 539, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 542, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 546, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 551, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 553, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 562, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 566, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 569, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 571, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 576, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 578, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 584, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 587, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 591, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 596, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 598, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 604, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 607, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 611, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 617, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 622, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 624, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 631, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 634, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 638, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 646, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 648, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 652, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 656, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 660, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 662, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 665, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 668, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 681, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 684, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 686, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 690, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 694, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 696, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 699, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 711, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 714, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 716, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 720, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 724, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 726, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 729, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 741, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 744, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 746, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 750, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 754, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 756, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 759, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 771, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 774, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 776, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 780, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 784, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 786, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 789, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 792, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 804, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 807, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 809, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 813, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 817, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 819, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 822, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 834, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 837, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 839, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 843, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 847, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 849, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 859, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 861, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 873, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 876, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 878, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 882, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 886, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 888, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 892, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 904, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 907, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 909, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 913, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 917, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 919, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 922, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 934, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 937, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 939, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 948, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 959, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 968, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 978, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 982, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 992, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 1000, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 1009, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 1014, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 1019, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 1025, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 1027, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 1040, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 1046, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 1052, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 1054, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 1058, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 1061, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 1074, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 1076, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 1079, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 1081, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 1089, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 1097, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 1110, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 1113, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 1119, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 1121, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 1125, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 1131, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 1138, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 1144, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 1150, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 1157, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 1161, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 1168, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 1171, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 1174, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 1177, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 1179, "message": "Line too long (128/120)", "rule_id": "C0301", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 1180, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 1184, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 1186, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 1190, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 1196, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 1200, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 1203, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 1210, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 1217, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 1219, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 1222, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 1225, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 1231, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 1239, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 1243, "message": "Trailing whitespace", "rule_id": "C0303", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 1, "message": "Too many lines in module (1255/1000)", "rule_id": "C0302", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 20, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 53, "message": "Consider using alternative Union syntax instead of 'Optional'", "rule_id": "R6003", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 20, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 53, "message": "Consider using alternative Union syntax instead of 'Optional'", "rule_id": "R6003", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 36, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 88, "message": "Consider using alternative Union syntax instead of 'Optional'", "rule_id": "R6003", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 36, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 88, "message": "Consider using alternative Union syntax instead of 'Optional'", "rule_id": "R6003", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 8, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 119, "message": "Use lazy % formatting in logging functions", "rule_id": "W1203", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 40, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 122, "message": "Consider using alternative Union syntax instead of 'Optional'", "rule_id": "R6003", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 40, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 122, "message": "Consider using alternative Union syntax instead of 'Optional'", "rule_id": "R6003", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 17, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 162, "message": "Using open without explicitly specifying an encoding", "rule_id": "W1514", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 17, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 183, "message": "Using open without explicitly specifying an encoding", "rule_id": "W1514", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 8, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 198, "message": "Use lazy % formatting in logging functions", "rule_id": "W1203", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 8, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 202, "message": "Use lazy % formatting in logging functions", "rule_id": "W1203", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 8, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 236, "message": "Use lazy % formatting in logging functions", "rule_id": "W1203", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 8, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 237, "message": "Use lazy % formatting in logging functions", "rule_id": "W1203", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 16, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 294, "message": "Use lazy % formatting in logging functions", "rule_id": "W1203", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 39, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 314, "message": "Consider iterating the dictionary directly instead of calling .keys()", "rule_id": "C0201", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 8, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 328, "message": "Use lazy % formatting in logging functions", "rule_id": "W1203", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 12, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 347, "message": "Use lazy % formatting in logging functions", "rule_id": "W1203", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 4, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 326, "message": "Too many return statements (9/6)", "rule_id": "R0911", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 8, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 443, "message": "Use lazy % formatting in logging functions", "rule_id": "W1203", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 12, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 461, "message": "Use lazy % formatting in logging functions", "rule_id": "W1203", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 8, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 469, "message": "Use lazy % formatting in logging functions", "rule_id": "W1203", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 12, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 482, "message": "Use lazy % formatting in logging functions", "rule_id": "W1203", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 8, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 490, "message": "Use lazy % formatting in logging functions", "rule_id": "W1203", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 12, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 503, "message": "Use lazy % formatting in logging functions", "rule_id": "W1203", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 8, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 511, "message": "Use lazy % formatting in logging functions", "rule_id": "W1203", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 12, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 523, "message": "Use lazy % formatting in logging functions", "rule_id": "W1203", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 8, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 531, "message": "Use lazy % formatting in logging functions", "rule_id": "W1203", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 12, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 544, "message": "Use lazy % formatting in logging functions", "rule_id": "W1203", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 8, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 552, "message": "Use lazy % formatting in logging functions", "rule_id": "W1203", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 16, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 568, "message": "Use lazy % formatting in logging functions", "rule_id": "W1203", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 8, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 577, "message": "Use lazy % formatting in logging functions", "rule_id": "W1203", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 12, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 589, "message": "Use lazy % formatting in logging functions", "rule_id": "W1203", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 8, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 597, "message": "Use lazy % formatting in logging functions", "rule_id": "W1203", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 12, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 609, "message": "Use lazy % formatting in logging functions", "rule_id": "W1203", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 33, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 612, "message": "Unused argument 'files'", "rule_id": "W0613", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 8, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 623, "message": "Use lazy % formatting in logging functions", "rule_id": "W1203", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 12, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 636, "message": "Use lazy % formatting in logging functions", "rule_id": "W1203", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 12, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 683, "message": "Use lazy % formatting in logging functions", "rule_id": "W1203", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 12, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 713, "message": "Use lazy % formatting in logging functions", "rule_id": "W1203", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 12, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 743, "message": "Use lazy % formatting in logging functions", "rule_id": "W1203", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 12, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 773, "message": "Use lazy % formatting in logging functions", "rule_id": "W1203", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 12, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 806, "message": "Use lazy % formatting in logging functions", "rule_id": "W1203", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 12, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 836, "message": "Use lazy % formatting in logging functions", "rule_id": "W1203", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 12, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 875, "message": "Use lazy % formatting in logging functions", "rule_id": "W1203", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 12, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 906, "message": "Use lazy % formatting in logging functions", "rule_id": "W1203", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 12, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 936, "message": "Use lazy % formatting in logging functions", "rule_id": "W1203", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 15, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 1023, "message": "Use 'if (key := f'{smell.file_path}:{smell.line_number}:{smell.rule_id}') not in baseline_smells:' instead", "rule_id": "R6103", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 15, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 1023, "message": "Use 'if (key := f'{smell.file_path}:{smell.line_number}:{smell.rule_id}') not in baseline_smells:' instead", "rule_id": "R6103", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 16, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 1078, "message": "Use lazy % formatting in logging functions", "rule_id": "W1203", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 17, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 1116, "message": "Using open without explicitly specifying an encoding", "rule_id": "W1514", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 12, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 1118, "message": "Use lazy % formatting in logging functions", "rule_id": "W1203", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 8, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 1189, "message": "Use lazy % formatting in logging functions", "rule_id": "W1203", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 13, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 1201, "message": "Using open without explicitly specifying an encoding", "rule_id": "W1514", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 8, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 1204, "message": "Use lazy % formatting in logging functions", "rule_id": "W1203", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 4, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 1209, "message": "Import outside toplevel (argparse)", "rule_id": "C0415", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 13, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 1234, "message": "Using open without explicitly specifying an encoding", "rule_id": "W1514", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 7, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 1246, "message": "Use 'if (failed_gates := [name for (name, passed) in result.quality_gates.items() if not passed]):' instead", "rule_id": "R6103", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 7, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 1246, "message": "Use 'if (failed_gates := [name for (name, passed) in result.quality_gates.items() if not passed]):' instead", "rule_id": "R6103", "severity": "SeverityLevel.MINOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 22, "message": "Unused import os", "rule_id": "W0611", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 0, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 27, "message": "Unused Tuple imported from typing", "rule_id": "W0611", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 24, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 71, "message": "'typing.Dict' is deprecated, use 'dict' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 24, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 72, "message": "'typing.Dict' is deprecated, use 'dict' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 19, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 73, "message": "'typing.Dict' is deprecated, use 'dict' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 19, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 74, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 12, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 75, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 58, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 122, "message": "'typing.Dict' is deprecated, use 'dict' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 37, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 168, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 32, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 180, "message": "'typing.Dict' is deprecated, use 'dict' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 52, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 241, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 47, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 271, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 61, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 271, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 49, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 298, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 63, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 298, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 46, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 312, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 60, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 312, "message": "'typing.Dict' is deprecated, use 'dict' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 70, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 312, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 63, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 326, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 77, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 326, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 50, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 350, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 64, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 350, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 46, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 364, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 60, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 364, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 42, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 378, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 56, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 378, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 45, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 388, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 59, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 388, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 43, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 398, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 57, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 398, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 46, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 408, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 60, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 408, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 44, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 418, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 58, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 418, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 48, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 428, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 62, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 428, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 39, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 438, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 53, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 438, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 39, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 464, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 53, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 464, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 46, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 485, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 60, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 485, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 43, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 506, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 57, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 506, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 41, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 526, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 55, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 526, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 41, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 547, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 55, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 547, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 41, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 572, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 55, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 572, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 45, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 592, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 59, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 592, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 40, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 612, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 54, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 612, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 39, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 618, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 53, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 618, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 38, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 639, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 53, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 653, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 53, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 687, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 55, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 717, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 57, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 747, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 55, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 777, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 71, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 810, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 55, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 840, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 59, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 879, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 53, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 910, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 46, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 1010, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 66, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 1010, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 41, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 1028, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 65, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 1028, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 79, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 1028, "message": "'typing.Dict' is deprecated, use 'dict' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 44, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 1055, "message": "'typing.Dict' is deprecated, use 'dict' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 65, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 1055, "message": "'typing.Dict' is deprecated, use 'dict' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 41, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 1082, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 61, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 1082, "message": "'typing.Dict' is deprecated, use 'dict' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 41, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 1090, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 61, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 1090, "message": "'typing.Dict' is deprecated, use 'dict' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 36, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 1187, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 24, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 71, "message": "'typing.Dict' is deprecated, use 'dict' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 24, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 72, "message": "'typing.Dict' is deprecated, use 'dict' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 19, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 73, "message": "'typing.Dict' is deprecated, use 'dict' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 19, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 74, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 12, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 75, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 58, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 122, "message": "'typing.Dict' is deprecated, use 'dict' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 37, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 168, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 32, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 180, "message": "'typing.Dict' is deprecated, use 'dict' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 52, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 241, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 47, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 271, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 61, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 271, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 49, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 298, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 63, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 298, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 46, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 312, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 60, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 312, "message": "'typing.Dict' is deprecated, use 'dict' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 70, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 312, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 63, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 326, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 77, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 326, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 50, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 350, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 64, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 350, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 46, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 364, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 60, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 364, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 42, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 378, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 56, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 378, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 45, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 388, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 59, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 388, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 43, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 398, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 57, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 398, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 46, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 408, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 60, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 408, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 44, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 418, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 58, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 418, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 48, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 428, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 62, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 428, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 39, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 438, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 53, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 438, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 39, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 464, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 53, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 464, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 46, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 485, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 60, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 485, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 43, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 506, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 57, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 506, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 41, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 526, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 55, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 526, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 41, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 547, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 55, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 547, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 41, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 572, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 55, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 572, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 45, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 592, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 59, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 592, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 40, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 612, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 54, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 612, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 39, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 618, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 53, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 618, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 38, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 639, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 53, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 653, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 53, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 687, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 55, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 717, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 57, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 747, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 55, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 777, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 71, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 810, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 55, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 840, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 59, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 879, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 53, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 910, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 46, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 1010, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 66, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 1010, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 41, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 1028, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 65, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 1028, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 79, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 1028, "message": "'typing.Dict' is deprecated, use 'dict' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 44, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 1055, "message": "'typing.Dict' is deprecated, use 'dict' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 65, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 1055, "message": "'typing.Dict' is deprecated, use 'dict' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 41, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 1082, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 61, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 1082, "message": "'typing.Dict' is deprecated, use 'dict' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 41, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 1090, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 61, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 1090, "message": "'typing.Dict' is deprecated, use 'dict' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "unknown", "column": 36, "file_path": "tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 1187, "message": "'typing.List' is deprecated, use 'list' instead", "rule_id": "W6001", "severity": "SeverityLevel.MAJOR", "tool": "pylint"}, {"category": "security", "column": 0, "file_path": "./tools/code_smell_detector.py", "fix_suggestion": null, "fixable": false, "line_number": 23, "message": "Consider possible security implications associated with the subprocess module.", "rule_id": "B404", "severity": "SeverityLevel.MINOR", "tool": "bandit"}, {"category": "shell", "column": 12, "file_path": "tests/test_health_checks.sh", "fix_suggestion": null, "fixable": false, "line_number": 20, "message": "Not following: ./.env.local was not specified as input (see shellcheck -x).", "rule_id": "SC1091", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 11, "file_path": "tests/test_health_checks.sh", "fix_suggestion": null, "fixable": false, "line_number": 39, "message": "Declare and assign separately to avoid masking return values.", "rule_id": "SC2155", "severity": "SeverityLevel.MAJOR", "tool": "shellcheck"}, {"category": "shell", "column": 11, "file_path": "tests/test_health_checks.sh", "fix_suggestion": null, "fixable": false, "line_number": 41, "message": "Declare and assign separately to avoid masking return values.", "rule_id": "SC2155", "severity": "SeverityLevel.MAJOR", "tool": "shellcheck"}, {"category": "shell", "column": 11, "file_path": "tests/test_health_checks.sh", "fix_suggestion": null, "fixable": false, "line_number": 80, "message": "Declare and assign separately to avoid masking return values.", "rule_id": "SC2155", "severity": "SeverityLevel.MAJOR", "tool": "shellcheck"}, {"category": "shell", "column": 11, "file_path": "tests/test_health_checks.sh", "fix_suggestion": null, "fixable": false, "line_number": 83, "message": "Declare and assign separately to avoid masking return values.", "rule_id": "SC2155", "severity": "SeverityLevel.MAJOR", "tool": "shellcheck"}, {"category": "shell", "column": 15, "file_path": "tests/test_health_checks.sh", "fix_suggestion": null, "fixable": false, "line_number": 104, "message": "Declare and assign separately to avoid masking return values.", "rule_id": "SC2155", "severity": "SeverityLevel.MAJOR", "tool": "shellcheck"}, {"category": "shell", "column": 15, "file_path": "tests/test_health_checks.sh", "fix_suggestion": null, "fixable": false, "line_number": 230, "message": "Declare and assign separately to avoid masking return values.", "rule_id": "SC2155", "severity": "SeverityLevel.MAJOR", "tool": "shellcheck"}, {"category": "shell", "column": 11, "file_path": "tests/test_health_checks.sh", "fix_suggestion": null, "fixable": false, "line_number": 241, "message": "Declare and assign separately to avoid masking return values.", "rule_id": "SC2155", "severity": "SeverityLevel.MAJOR", "tool": "shellcheck"}, {"category": "shell", "column": 19, "file_path": "tests/test_health_checks.sh", "fix_suggestion": null, "fixable": false, "line_number": 262, "message": "Declare and assign separately to avoid masking return values.", "rule_id": "SC2155", "severity": "SeverityLevel.MAJOR", "tool": "shellcheck"}, {"category": "shell", "column": 19, "file_path": "tests/test_health_checks.sh", "fix_suggestion": null, "fixable": false, "line_number": 263, "message": "Declare and assign separately to avoid masking return values.", "rule_id": "SC2155", "severity": "SeverityLevel.MAJOR", "tool": "shellcheck"}, {"category": "shell", "column": 15, "file_path": "tests/test_health_checks.sh", "fix_suggestion": null, "fixable": false, "line_number": 271, "message": "Declare and assign separately to avoid masking return values.", "rule_id": "SC2155", "severity": "SeverityLevel.MAJOR", "tool": "shellcheck"}, {"category": "shell", "column": 15, "file_path": "tests/test_health_checks.sh", "fix_suggestion": null, "fixable": false, "line_number": 290, "message": "Declare and assign separately to avoid masking return values.", "rule_id": "SC2155", "severity": "SeverityLevel.MAJOR", "tool": "shellcheck"}, {"category": "shell", "column": 15, "file_path": "tests/test_health_checks.sh", "fix_suggestion": null, "fixable": false, "line_number": 319, "message": "Declare and assign separately to avoid masking return values.", "rule_id": "SC2155", "severity": "SeverityLevel.MAJOR", "tool": "shellcheck"}, {"category": "shell", "column": 1, "file_path": "tests/test_observability_logging.sh", "fix_suggestion": null, "fixable": false, "line_number": 19, "message": "YELLOW appears unused. Verify use (or export if used externally).", "rule_id": "SC2034", "severity": "SeverityLevel.MAJOR", "tool": "shellcheck"}, {"category": "shell", "column": 11, "file_path": "tests/test_observability_logging.sh", "fix_suggestion": null, "fixable": false, "line_number": 31, "message": "Declare and assign separately to avoid masking return values.", "rule_id": "SC2155", "severity": "SeverityLevel.MAJOR", "tool": "shellcheck"}, {"category": "shell", "column": 11, "file_path": "tests/test_observability_logging.sh", "fix_suggestion": null, "fixable": false, "line_number": 33, "message": "Declare and assign separately to avoid masking return values.", "rule_id": "SC2155", "severity": "SeverityLevel.MAJOR", "tool": "shellcheck"}, {"category": "shell", "column": 11, "file_path": "tests/test_observability_logging.sh", "fix_suggestion": null, "fixable": false, "line_number": 103, "message": "Declare and assign separately to avoid masking return values.", "rule_id": "SC2155", "severity": "SeverityLevel.MAJOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_observability_logging.sh", "fix_suggestion": null, "fixable": false, "line_number": 106, "message": "Consider using { cmd1; cmd2; } >> file instead of individual redirects.", "rule_id": "SC2129", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 15, "file_path": "tests/test_observability_logging.sh", "fix_suggestion": null, "fixable": false, "line_number": 123, "message": "Declare and assign separately to avoid masking return values.", "rule_id": "SC2155", "severity": "SeverityLevel.MAJOR", "tool": "shellcheck"}, {"category": "shell", "column": 15, "file_path": "tests/test_observability_logging.sh", "fix_suggestion": null, "fixable": false, "line_number": 124, "message": "Declare and assign separately to avoid masking return values.", "rule_id": "SC2155", "severity": "SeverityLevel.MAJOR", "tool": "shellcheck"}, {"category": "shell", "column": 15, "file_path": "tests/test_observability_logging.sh", "fix_suggestion": null, "fixable": false, "line_number": 125, "message": "Declare and assign separately to avoid masking return values.", "rule_id": "SC2155", "severity": "SeverityLevel.MAJOR", "tool": "shellcheck"}, {"category": "shell", "column": 15, "file_path": "tests/test_observability_logging.sh", "fix_suggestion": null, "fixable": false, "line_number": 126, "message": "Declare and assign separately to avoid masking return values.", "rule_id": "SC2155", "severity": "SeverityLevel.MAJOR", "tool": "shellcheck"}, {"category": "shell", "column": 11, "file_path": "tests/test_observability_logging.sh", "fix_suggestion": null, "fixable": false, "line_number": 149, "message": "Declare and assign separately to avoid masking return values.", "rule_id": "SC2155", "severity": "SeverityLevel.MAJOR", "tool": "shellcheck"}, {"category": "shell", "column": 15, "file_path": "tests/test_observability_logging.sh", "fix_suggestion": null, "fixable": false, "line_number": 173, "message": "Declare and assign separately to avoid masking return values.", "rule_id": "SC2155", "severity": "SeverityLevel.MAJOR", "tool": "shellcheck"}, {"category": "shell", "column": 15, "file_path": "tests/test_observability_logging.sh", "fix_suggestion": null, "fixable": false, "line_number": 174, "message": "Declare and assign separately to avoid masking return values.", "rule_id": "SC2155", "severity": "SeverityLevel.MAJOR", "tool": "shellcheck"}, {"category": "shell", "column": 11, "file_path": "tests/test_observability_logging.sh", "fix_suggestion": null, "fixable": false, "line_number": 286, "message": "Declare and assign separately to avoid masking return values.", "rule_id": "SC2155", "severity": "SeverityLevel.MAJOR", "tool": "shellcheck"}, {"category": "shell", "column": 84, "file_path": "tests/test_observability_logging.sh", "fix_suggestion": null, "fixable": false, "line_number": 286, "message": "Consider using 'grep -c' instead of 'grep|wc -l'.", "rule_id": "SC2126", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 27, "file_path": "tests/test_observability_logging.sh", "fix_suggestion": null, "fixable": false, "line_number": 320, "message": "Declare and assign separately to avoid masking return values.", "rule_id": "SC2155", "severity": "SeverityLevel.MAJOR", "tool": "shellcheck"}, {"category": "shell", "column": 11, "file_path": "tests/test_observability_logging.sh", "fix_suggestion": null, "fixable": false, "line_number": 350, "message": "Declare and assign separately to avoid masking return values.", "rule_id": "SC2155", "severity": "SeverityLevel.MAJOR", "tool": "shellcheck"}, {"category": "shell", "column": 11, "file_path": "tests/test_observability_logging.sh", "fix_suggestion": null, "fixable": false, "line_number": 351, "message": "Declare and assign separately to avoid masking return values.", "rule_id": "SC2155", "severity": "SeverityLevel.MAJOR", "tool": "shellcheck"}, {"category": "shell", "column": 15, "file_path": "tests/test_observability_logging.sh", "fix_suggestion": null, "fixable": false, "line_number": 356, "message": "Declare and assign separately to avoid masking return values.", "rule_id": "SC2155", "severity": "SeverityLevel.MAJOR", "tool": "shellcheck"}, {"category": "shell", "column": 11, "file_path": "tests/test_observability_logging.sh", "fix_suggestion": null, "fixable": false, "line_number": 360, "message": "Declare and assign separately to avoid masking return values.", "rule_id": "SC2155", "severity": "SeverityLevel.MAJOR", "tool": "shellcheck"}, {"category": "shell", "column": 11, "file_path": "tests/test_observability_logging.sh", "fix_suggestion": null, "fixable": false, "line_number": 372, "message": "Declare and assign separately to avoid masking return values.", "rule_id": "SC2155", "severity": "SeverityLevel.MAJOR", "tool": "shellcheck"}, {"category": "shell", "column": 11, "file_path": "tests/test_observability_logging.sh", "fix_suggestion": null, "fixable": false, "line_number": 407, "message": "Declare and assign separately to avoid masking return values.", "rule_id": "SC2155", "severity": "SeverityLevel.MAJOR", "tool": "shellcheck"}, {"category": "shell", "column": 11, "file_path": "tests/test_observability_logging.sh", "fix_suggestion": null, "fixable": false, "line_number": 408, "message": "Declare and assign separately to avoid masking return values.", "rule_id": "SC2155", "severity": "SeverityLevel.MAJOR", "tool": "shellcheck"}, {"category": "shell", "column": 11, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 34, "message": "Declare and assign separately to avoid masking return values.", "rule_id": "SC2155", "severity": "SeverityLevel.MAJOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 75, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 77, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 79, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 8, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 79, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 80, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 81, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 85, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 10, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 85, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 86, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 87, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 91, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 10, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 91, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 92, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 93, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 96, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 10, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 96, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 97, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 101, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 10, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 101, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 102, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 106, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 10, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 106, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 44, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 106, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 107, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 108, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 111, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 10, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 111, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 45, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 111, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 112, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 113, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 116, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 10, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 116, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 45, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 116, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 117, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 118, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 121, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 122, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 127, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 129, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 131, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 8, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 131, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 132, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 133, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 137, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 8, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 137, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 138, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 14, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 138, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 13, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 139, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 13, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 140, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 142, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 144, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 148, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 10, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 148, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 149, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 150, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 154, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 10, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 154, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 155, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 156, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 160, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 170, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 171, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 14, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 171, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 13, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 172, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 13, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 173, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 177, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 178, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 183, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 185, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 187, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 8, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 187, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 188, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 189, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 193, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 10, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 193, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 194, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 195, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 198, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 10, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 198, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 199, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 200, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 203, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 10, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 203, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 204, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 205, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 209, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 218, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 219, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 14, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 219, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 13, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 220, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 13, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 221, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 226, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 10, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 226, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 227, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 228, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 232, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 239, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 240, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 14, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 240, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 13, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 241, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 246, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 8, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 246, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 247, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 12, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 247, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 27, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 247, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 13, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 248, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 13, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 250, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 253, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 256, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 261, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 263, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 266, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 274, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 275, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 277, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 278, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 12, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 278, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 13, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 279, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 13, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 280, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 13, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 282, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 287, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 8, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 287, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 48, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 287, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 288, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 289, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 290, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 292, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 293, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 297, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 299, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 8, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 299, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 10, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 299, "message": "Double quote to prevent globbing and word splitting.", "rule_id": "SC2086", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 30, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 299, "message": "Double quote to prevent globbing and word splitting.", "rule_id": "SC2086", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 300, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 301, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 303, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 304, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 310, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 312, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 313, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 316, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 10, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 316, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 317, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 318, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 321, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 10, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 321, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 322, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 323, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 326, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 10, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 326, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 327, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 330, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 10, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 330, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 331, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 335, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 10, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 335, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 336, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 337, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 340, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 10, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 340, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 341, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 342, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 346, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 10, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 346, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 347, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 348, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 351, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 10, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 351, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 352, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 353, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 356, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 10, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 356, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 357, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 358, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 361, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 362, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 367, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 369, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 372, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 10, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 372, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 373, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 374, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 378, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 8, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 378, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 379, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 382, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 383, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 388, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 390, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 393, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 10, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 393, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 394, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 395, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 398, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 10, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 398, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 399, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 400, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 403, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 10, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 403, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 404, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 405, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 409, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 10, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 409, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 410, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 413, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 414, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 419, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 421, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 424, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 10, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 424, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 425, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 426, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 429, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 10, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 429, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 430, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 431, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 435, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 10, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 435, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 436, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 440, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 10, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 440, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 441, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 444, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_config_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 445, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 1, "file_path": "tests/test_webui_health.sh", "fix_suggestion": null, "fixable": false, "line_number": 18, "message": "WEBUI_HEALTH_URL appears unused. Verify use (or export if used externally).", "rule_id": "SC2034", "severity": "SeverityLevel.MAJOR", "tool": "shellcheck"}, {"category": "shell", "column": 11, "file_path": "tests/test_webui_health.sh", "fix_suggestion": null, "fixable": false, "line_number": 37, "message": "Declare and assign separately to avoid masking return values.", "rule_id": "SC2155", "severity": "SeverityLevel.MAJOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_health.sh", "fix_suggestion": null, "fixable": false, "line_number": 78, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_health.sh", "fix_suggestion": null, "fixable": false, "line_number": 80, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 8, "file_path": "tests/test_webui_health.sh", "fix_suggestion": null, "fixable": false, "line_number": 80, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_health.sh", "fix_suggestion": null, "fixable": false, "line_number": 81, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_health.sh", "fix_suggestion": null, "fixable": false, "line_number": 82, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_health.sh", "fix_suggestion": null, "fixable": false, "line_number": 85, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 8, "file_path": "tests/test_webui_health.sh", "fix_suggestion": null, "fixable": false, "line_number": 85, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_health.sh", "fix_suggestion": null, "fixable": false, "line_number": 86, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_health.sh", "fix_suggestion": null, "fixable": false, "line_number": 87, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_health.sh", "fix_suggestion": null, "fixable": false, "line_number": 90, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_health.sh", "fix_suggestion": null, "fixable": false, "line_number": 91, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_health.sh", "fix_suggestion": null, "fixable": false, "line_number": 96, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_health.sh", "fix_suggestion": null, "fixable": false, "line_number": 98, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 8, "file_path": "tests/test_webui_health.sh", "fix_suggestion": null, "fixable": false, "line_number": 98, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_health.sh", "fix_suggestion": null, "fixable": false, "line_number": 99, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_health.sh", "fix_suggestion": null, "fixable": false, "line_number": 100, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_health.sh", "fix_suggestion": null, "fixable": false, "line_number": 104, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 10, "file_path": "tests/test_webui_health.sh", "fix_suggestion": null, "fixable": false, "line_number": 104, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_health.sh", "fix_suggestion": null, "fixable": false, "line_number": 105, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_health.sh", "fix_suggestion": null, "fixable": false, "line_number": 106, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_health.sh", "fix_suggestion": null, "fixable": false, "line_number": 110, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 10, "file_path": "tests/test_webui_health.sh", "fix_suggestion": null, "fixable": false, "line_number": 110, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_health.sh", "fix_suggestion": null, "fixable": false, "line_number": 111, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_health.sh", "fix_suggestion": null, "fixable": false, "line_number": 112, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_health.sh", "fix_suggestion": null, "fixable": false, "line_number": 116, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 10, "file_path": "tests/test_webui_health.sh", "fix_suggestion": null, "fixable": false, "line_number": 116, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_health.sh", "fix_suggestion": null, "fixable": false, "line_number": 117, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_health.sh", "fix_suggestion": null, "fixable": false, "line_number": 118, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_health.sh", "fix_suggestion": null, "fixable": false, "line_number": 121, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_health.sh", "fix_suggestion": null, "fixable": false, "line_number": 122, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_health.sh", "fix_suggestion": null, "fixable": false, "line_number": 127, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_health.sh", "fix_suggestion": null, "fixable": false, "line_number": 129, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 8, "file_path": "tests/test_webui_health.sh", "fix_suggestion": null, "fixable": false, "line_number": 129, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_health.sh", "fix_suggestion": null, "fixable": false, "line_number": 130, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_health.sh", "fix_suggestion": null, "fixable": false, "line_number": 131, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_health.sh", "fix_suggestion": null, "fixable": false, "line_number": 135, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 10, "file_path": "tests/test_webui_health.sh", "fix_suggestion": null, "fixable": false, "line_number": 135, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_health.sh", "fix_suggestion": null, "fixable": false, "line_number": 136, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_health.sh", "fix_suggestion": null, "fixable": false, "line_number": 137, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_health.sh", "fix_suggestion": null, "fixable": false, "line_number": 140, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_health.sh", "fix_suggestion": null, "fixable": false, "line_number": 141, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_health.sh", "fix_suggestion": null, "fixable": false, "line_number": 146, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_health.sh", "fix_suggestion": null, "fixable": false, "line_number": 148, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 8, "file_path": "tests/test_webui_health.sh", "fix_suggestion": null, "fixable": false, "line_number": 148, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_health.sh", "fix_suggestion": null, "fixable": false, "line_number": 149, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_health.sh", "fix_suggestion": null, "fixable": false, "line_number": 150, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_health.sh", "fix_suggestion": null, "fixable": false, "line_number": 154, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 10, "file_path": "tests/test_webui_health.sh", "fix_suggestion": null, "fixable": false, "line_number": 154, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_health.sh", "fix_suggestion": null, "fixable": false, "line_number": 155, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_health.sh", "fix_suggestion": null, "fixable": false, "line_number": 156, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_health.sh", "fix_suggestion": null, "fixable": false, "line_number": 158, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_health.sh", "fix_suggestion": null, "fixable": false, "line_number": 160, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_health.sh", "fix_suggestion": null, "fixable": false, "line_number": 161, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_health.sh", "fix_suggestion": null, "fixable": false, "line_number": 166, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_health.sh", "fix_suggestion": null, "fixable": false, "line_number": 169, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 8, "file_path": "tests/test_webui_health.sh", "fix_suggestion": null, "fixable": false, "line_number": 169, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_health.sh", "fix_suggestion": null, "fixable": false, "line_number": 171, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 12, "file_path": "tests/test_webui_health.sh", "fix_suggestion": null, "fixable": false, "line_number": 171, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 13, "file_path": "tests/test_webui_health.sh", "fix_suggestion": null, "fixable": false, "line_number": 172, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 13, "file_path": "tests/test_webui_health.sh", "fix_suggestion": null, "fixable": false, "line_number": 173, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 13, "file_path": "tests/test_webui_health.sh", "fix_suggestion": null, "fixable": false, "line_number": 175, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 13, "file_path": "tests/test_webui_health.sh", "fix_suggestion": null, "fixable": false, "line_number": 177, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_health.sh", "fix_suggestion": null, "fixable": false, "line_number": 180, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_health.sh", "fix_suggestion": null, "fixable": false, "line_number": 181, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_health.sh", "fix_suggestion": null, "fixable": false, "line_number": 187, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_health.sh", "fix_suggestion": null, "fixable": false, "line_number": 189, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 8, "file_path": "tests/test_webui_health.sh", "fix_suggestion": null, "fixable": false, "line_number": 189, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 45, "file_path": "tests/test_webui_health.sh", "fix_suggestion": null, "fixable": false, "line_number": 189, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_health.sh", "fix_suggestion": null, "fixable": false, "line_number": 190, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 12, "file_path": "tests/test_webui_health.sh", "fix_suggestion": null, "fixable": false, "line_number": 190, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 13, "file_path": "tests/test_webui_health.sh", "fix_suggestion": null, "fixable": false, "line_number": 191, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 13, "file_path": "tests/test_webui_health.sh", "fix_suggestion": null, "fixable": false, "line_number": 192, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 13, "file_path": "tests/test_webui_health.sh", "fix_suggestion": null, "fixable": false, "line_number": 194, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 13, "file_path": "tests/test_webui_health.sh", "fix_suggestion": null, "fixable": false, "line_number": 195, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_health.sh", "fix_suggestion": null, "fixable": false, "line_number": 198, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_health.sh", "fix_suggestion": null, "fixable": false, "line_number": 199, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_health.sh", "fix_suggestion": null, "fixable": false, "line_number": 205, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_health.sh", "fix_suggestion": null, "fixable": false, "line_number": 208, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 10, "file_path": "tests/test_webui_health.sh", "fix_suggestion": null, "fixable": false, "line_number": 208, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_health.sh", "fix_suggestion": null, "fixable": false, "line_number": 209, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_health.sh", "fix_suggestion": null, "fixable": false, "line_number": 210, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_health.sh", "fix_suggestion": null, "fixable": false, "line_number": 214, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 10, "file_path": "tests/test_webui_health.sh", "fix_suggestion": null, "fixable": false, "line_number": 214, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_health.sh", "fix_suggestion": null, "fixable": false, "line_number": 215, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_health.sh", "fix_suggestion": null, "fixable": false, "line_number": 216, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_health.sh", "fix_suggestion": null, "fixable": false, "line_number": 220, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 10, "file_path": "tests/test_webui_health.sh", "fix_suggestion": null, "fixable": false, "line_number": 220, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_health.sh", "fix_suggestion": null, "fixable": false, "line_number": 221, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_health.sh", "fix_suggestion": null, "fixable": false, "line_number": 222, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_health.sh", "fix_suggestion": null, "fixable": false, "line_number": 225, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_health.sh", "fix_suggestion": null, "fixable": false, "line_number": 226, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 34, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 8, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 34, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 35, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 36, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 119, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 36, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 37, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 52, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 37, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 11, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 51, "message": "Declare and assign separately to avoid masking return values.", "rule_id": "SC2155", "severity": "SeverityLevel.MAJOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 92, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 95, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 8, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 95, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 96, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 97, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 100, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 101, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 103, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 15, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 103, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 17, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 103, "message": "Double quote to prevent globbing and word splitting.", "rule_id": "SC2086", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 30, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 103, "message": "Double quote to prevent globbing and word splitting.", "rule_id": "SC2086", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 13, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 104, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 16, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 104, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 17, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 105, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 17, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 106, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 13, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 108, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 13, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 109, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 112, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 113, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 115, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 116, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 122, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 124, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 125, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 127, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 11, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 127, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 13, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 127, "message": "Double quote to prevent globbing and word splitting.", "rule_id": "SC2086", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 26, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 127, "message": "Double quote to prevent globbing and word splitting.", "rule_id": "SC2086", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 128, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 12, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 128, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 13, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 129, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 13, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 130, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 132, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 133, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 136, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 137, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 142, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 144, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 145, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 8, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 145, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 19, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 145, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 147, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 12, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 147, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 13, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 148, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 13, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 151, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 16, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 151, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 17, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 152, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 17, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 155, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 20, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 155, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 21, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 156, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 21, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 157, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 21, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 159, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 21, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 160, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 17, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 163, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 17, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 164, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 13, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 167, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 13, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 168, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 171, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 172, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 178, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 180, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 181, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 183, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 8, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 183, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 19, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 183, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 184, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 21, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 184, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 185, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 19, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 185, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 187, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 12, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 187, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 13, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 188, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 13, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 191, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 16, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 191, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 17, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 192, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 17, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 195, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 20, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 195, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 21, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 196, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 21, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 197, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 21, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 199, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 21, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 200, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 17, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 203, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 17, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 204, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 13, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 207, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 13, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 208, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 211, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 212, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 218, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 220, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 221, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 8, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 221, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 18, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 221, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 222, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 223, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 226, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 12, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 226, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 13, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 227, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 13, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 228, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 13, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 230, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 234, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 12, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 234, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 13, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 235, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 13, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 236, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 13, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 238, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 242, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 12, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 242, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 13, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 243, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 13, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 244, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 13, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 246, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 250, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 12, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 250, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 13, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 251, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 13, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 252, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 13, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 254, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 258, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 12, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 258, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 13, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 259, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 13, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 261, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 13, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 262, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 266, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 12, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 266, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 14, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 266, "message": "Double quote to prevent globbing and word splitting.", "rule_id": "SC2086", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 13, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 267, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 13, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 268, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 13, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 270, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 13, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 271, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 274, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 275, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 281, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 283, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 284, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 8, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 284, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 25, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 284, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 285, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 12, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 285, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 13, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 287, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 13, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 288, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 16, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 288, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 32, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 288, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 17, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 289, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 20, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 289, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 21, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 290, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 21, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 292, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 21, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 293, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 13, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 298, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 13, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 299, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 16, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 299, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 28, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 299, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 17, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 300, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 20, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 300, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 21, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 301, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 21, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 303, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 13, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 308, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 13, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 309, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 16, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 309, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 32, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 309, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 17, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 310, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 20, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 310, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 21, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 311, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 21, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 313, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 13, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 317, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 13, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 319, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 13, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 320, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 323, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 324, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 330, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 333, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 336, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 339, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 340, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 8, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 340, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 25, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 340, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 341, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 12, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 341, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 13, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 343, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 13, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 344, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 16, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 344, "message": "access_logs appears unused. Verify use (or export if used externally).", "rule_id": "SC2034", "severity": "SeverityLevel.MAJOR", "tool": "shellcheck"}, {"category": "shell", "column": 16, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 344, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 30, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 344, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 17, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 345, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 17, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 348, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 17, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 349, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 20, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 349, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 34, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 349, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 21, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 350, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 24, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 350, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 25, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 351, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 25, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 352, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 25, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 354, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 25, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 355, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 21, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 358, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 21, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 359, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 17, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 362, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 17, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 363, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 13, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 366, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 13, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 367, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 370, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_webui_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 371, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 44, "file_path": "tests/run_all_tests.sh", "fix_suggestion": null, "fixable": false, "line_number": 161, "message": "This \\u will be a regular 'u' in this context.", "rule_id": "SC1001", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 50, "file_path": "tests/run_all_tests.sh", "fix_suggestion": null, "fixable": false, "line_number": 161, "message": "This \\u will be a regular 'u' in this context.", "rule_id": "SC1001", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 1, "file_path": "tests/test_secrets_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 19, "message": "YELLOW appears unused. Verify use (or export if used externally).", "rule_id": "SC2034", "severity": "SeverityLevel.MAJOR", "tool": "shellcheck"}, {"category": "shell", "column": 11, "file_path": "tests/test_secrets_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 31, "message": "Declare and assign separately to avoid masking return values.", "rule_id": "SC2155", "severity": "SeverityLevel.MAJOR", "tool": "shellcheck"}, {"category": "shell", "column": 11, "file_path": "tests/test_secrets_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 118, "message": "Declare and assign separately to avoid masking return values.", "rule_id": "SC2155", "severity": "SeverityLevel.MAJOR", "tool": "shellcheck"}, {"category": "shell", "column": 15, "file_path": "tests/test_secrets_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 130, "message": "Declare and assign separately to avoid masking return values.", "rule_id": "SC2155", "severity": "SeverityLevel.MAJOR", "tool": "shellcheck"}, {"category": "shell", "column": 16, "file_path": "tests/test_secrets_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 201, "message": "Not following: ./.env.local was not specified as input (see shellcheck -x).", "rule_id": "SC1091", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 16, "file_path": "tests/test_secrets_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 239, "message": "Not following: ./.env.local was not specified as input (see shellcheck -x).", "rule_id": "SC1091", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 19, "file_path": "tests/test_secrets_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 251, "message": "Declare and assign separately to avoid masking return values.", "rule_id": "SC2155", "severity": "SeverityLevel.MAJOR", "tool": "shellcheck"}, {"category": "shell", "column": 16, "file_path": "tests/test_secrets_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 271, "message": "Not following: ./.env.local was not specified as input (see shellcheck -x).", "rule_id": "SC1091", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 16, "file_path": "tests/test_secrets_validation.sh", "fix_suggestion": null, "fixable": false, "line_number": 301, "message": "Not following: ./.env.local was not specified as input (see shellcheck -x).", "rule_id": "SC1091", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 11, "file_path": "tests/test_security_scan.sh", "fix_suggestion": null, "fixable": false, "line_number": 32, "message": "Declare and assign separately to avoid masking return values.", "rule_id": "SC2155", "severity": "SeverityLevel.MAJOR", "tool": "shellcheck"}, {"category": "shell", "column": 19, "file_path": "tests/test_security_scan.sh", "fix_suggestion": null, "fixable": false, "line_number": 144, "message": "Declare and assign separately to avoid masking return values.", "rule_id": "SC2155", "severity": "SeverityLevel.MAJOR", "tool": "shellcheck"}, {"category": "shell", "column": 11, "file_path": "tests/test_security_scan.sh", "fix_suggestion": null, "fixable": false, "line_number": 166, "message": "Declare and assign separately to avoid masking return values.", "rule_id": "SC2155", "severity": "SeverityLevel.MAJOR", "tool": "shellcheck"}, {"category": "shell", "column": 11, "file_path": "tests/test_security_scan.sh", "fix_suggestion": null, "fixable": false, "line_number": 172, "message": "Declare and assign separately to avoid masking return values.", "rule_id": "SC2155", "severity": "SeverityLevel.MAJOR", "tool": "shellcheck"}, {"category": "shell", "column": 19, "file_path": "tests/test_security_scan.sh", "fix_suggestion": null, "fixable": false, "line_number": 175, "message": "Declare and assign separately to avoid masking return values.", "rule_id": "SC2155", "severity": "SeverityLevel.MAJOR", "tool": "shellcheck"}, {"category": "shell", "column": 11, "file_path": "tests/test_security_scan.sh", "fix_suggestion": null, "fixable": false, "line_number": 208, "message": "Declare and assign separately to avoid masking return values.", "rule_id": "SC2155", "severity": "SeverityLevel.MAJOR", "tool": "shellcheck"}, {"category": "shell", "column": 12, "file_path": "tests/test_crud_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 20, "message": "Not following: ./.env.local was not specified as input (see shellcheck -x).", "rule_id": "SC1091", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 1, "file_path": "tests/test_crud_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 26, "message": "This variable is assigned to itself, so the assignment does nothing.", "rule_id": "SC2269", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 1, "file_path": "tests/test_crud_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 27, "message": "This variable is assigned to itself, so the assignment does nothing.", "rule_id": "SC2269", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 11, "file_path": "tests/test_crud_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 44, "message": "Declare and assign separately to avoid masking return values.", "rule_id": "SC2155", "severity": "SeverityLevel.MAJOR", "tool": "shellcheck"}, {"category": "shell", "column": 11, "file_path": "tests/test_crud_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 46, "message": "Declare and assign separately to avoid masking return values.", "rule_id": "SC2155", "severity": "SeverityLevel.MAJOR", "tool": "shellcheck"}, {"category": "shell", "column": 14, "file_path": "tests/test_crud_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 53, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 11, "file_path": "tests/test_crud_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 85, "message": "Declare and assign separately to avoid masking return values.", "rule_id": "SC2155", "severity": "SeverityLevel.MAJOR", "tool": "shellcheck"}, {"category": "shell", "column": 11, "file_path": "tests/test_crud_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 88, "message": "Declare and assign separately to avoid masking return values.", "rule_id": "SC2155", "severity": "SeverityLevel.MAJOR", "tool": "shellcheck"}, {"category": "shell", "column": 11, "file_path": "tests/test_crud_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 154, "message": "Declare and assign separately to avoid masking return values.", "rule_id": "SC2155", "severity": "SeverityLevel.MAJOR", "tool": "shellcheck"}, {"category": "shell", "column": 23, "file_path": "tests/test_crud_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 192, "message": "Declare and assign separately to avoid masking return values.", "rule_id": "SC2155", "severity": "SeverityLevel.MAJOR", "tool": "shellcheck"}, {"category": "shell", "column": 11, "file_path": "tests/test_crud_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 224, "message": "Declare and assign separately to avoid masking return values.", "rule_id": "SC2155", "severity": "SeverityLevel.MAJOR", "tool": "shellcheck"}, {"category": "shell", "column": 23, "file_path": "tests/test_crud_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 262, "message": "Declare and assign separately to avoid masking return values.", "rule_id": "SC2155", "severity": "SeverityLevel.MAJOR", "tool": "shellcheck"}, {"category": "shell", "column": 11, "file_path": "tests/test_crud_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 310, "message": "Declare and assign separately to avoid masking return values.", "rule_id": "SC2155", "severity": "SeverityLevel.MAJOR", "tool": "shellcheck"}, {"category": "shell", "column": 23, "file_path": "tests/test_crud_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 370, "message": "Declare and assign separately to avoid masking return values.", "rule_id": "SC2155", "severity": "SeverityLevel.MAJOR", "tool": "shellcheck"}, {"category": "shell", "column": 11, "file_path": "tests/test_crud_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 423, "message": "Declare and assign separately to avoid masking return values.", "rule_id": "SC2155", "severity": "SeverityLevel.MAJOR", "tool": "shellcheck"}, {"category": "shell", "column": 23, "file_path": "tests/test_crud_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 460, "message": "Declare and assign separately to avoid masking return values.", "rule_id": "SC2155", "severity": "SeverityLevel.MAJOR", "tool": "shellcheck"}, {"category": "shell", "column": 11, "file_path": "tests/test_crud_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 514, "message": "Declare and assign separately to avoid masking return values.", "rule_id": "SC2155", "severity": "SeverityLevel.MAJOR", "tool": "shellcheck"}, {"category": "shell", "column": 11, "file_path": "tests/test_crud_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 541, "message": "Declare and assign separately to avoid masking return values.", "rule_id": "SC2155", "severity": "SeverityLevel.MAJOR", "tool": "shellcheck"}, {"category": "shell", "column": 19, "file_path": "tests/test_crud_integration.sh", "fix_suggestion": null, "fixable": false, "line_number": 588, "message": "Declare and assign separately to avoid masking return values.", "rule_id": "SC2155", "severity": "SeverityLevel.MAJOR", "tool": "shellcheck"}, {"category": "shell", "column": 1, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 12, "message": "YELLOW appears unused. Verify use (or export if used externally).", "rule_id": "SC2034", "severity": "SeverityLevel.MAJOR", "tool": "shellcheck"}, {"category": "shell", "column": 14, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 42, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 76, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 79, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 8, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 79, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 80, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 81, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 85, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 8, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 85, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 86, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 87, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 91, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 92, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 8, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 92, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 93, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 94, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 97, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 98, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 102, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 104, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 113, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 114, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 12, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 114, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 13, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 115, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 13, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 116, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 118, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 121, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 122, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 126, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 129, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 10, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 129, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 130, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 131, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 135, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 10, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 135, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 136, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 137, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 141, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 142, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 10, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 142, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 143, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 144, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 147, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 148, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 152, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 154, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 164, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 166, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 167, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 168, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 170, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 12, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 170, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 13, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 171, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 13, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 173, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 13, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 174, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 179, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 187, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 188, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 189, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 191, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 12, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 191, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 13, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 192, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 13, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 194, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 199, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 8, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 199, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 200, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 201, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 204, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 205, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 209, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 211, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 213, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 8, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 213, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 214, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 215, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 219, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 8, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 219, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 220, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 14, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 220, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 13, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 221, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 13, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 222, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 224, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 226, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 230, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 238, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 239, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 14, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 239, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 13, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 240, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 13, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 241, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 243, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 246, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 247, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 251, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 253, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 255, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 8, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 255, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 256, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 257, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 261, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 271, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 272, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 14, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 272, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 13, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 273, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 13, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 274, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 276, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 280, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 281, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 10, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 281, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 282, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 283, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 286, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 287, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 291, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 293, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 296, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 298, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 10, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 298, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 302, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 303, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 307, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 8, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 307, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 308, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 309, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 313, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 10, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 313, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 314, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 315, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 319, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 321, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 322, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 326, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 328, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 331, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 333, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 10, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 333, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 337, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 338, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 342, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 10, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 342, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 350, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 351, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 355, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 357, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 358, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 362, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 364, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 367, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 10, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 367, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 371, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 372, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 376, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 377, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 8, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 377, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 378, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 379, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 383, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 10, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 383, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 384, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 385, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 388, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 389, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 393, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 395, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 397, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 8, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 397, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 398, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 399, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 403, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 8, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 403, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 404, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 14, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 404, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 13, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 405, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 13, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 406, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 408, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 412, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 423, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 9, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 424, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 14, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 424, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 13, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 425, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 13, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 427, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 431, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 5, "file_path": "tests/test_code_smell_infrastructure.sh", "fix_suggestion": null, "fixable": false, "line_number": 432, "message": "Command appears to be unreachable. Check usage (or ignore if invoked indirectly).", "rule_id": "SC2317", "severity": "SeverityLevel.MINOR", "tool": "shellcheck"}, {"category": "shell", "column": 11, "file_path": "tests/test_security_scan_complex.sh", "fix_suggestion": null, "fixable": false, "line_number": 32, "message": "Declare and assign separately to avoid masking return values.", "rule_id": "SC2155", "severity": "SeverityLevel.MAJOR", "tool": "shellcheck"}, {"category": "shell", "column": 27, "file_path": "tests/test_security_scan_complex.sh", "fix_suggestion": null, "fixable": false, "line_number": 132, "message": "Declare and assign separately to avoid masking return values.", "rule_id": "SC2155", "severity": "SeverityLevel.MAJOR", "tool": "shellcheck"}, {"category": "shell", "column": 35, "file_path": "tests/test_security_scan_complex.sh", "fix_suggestion": null, "fixable": false, "line_number": 135, "message": "Declare and assign separately to avoid masking return values.", "rule_id": "SC2155", "severity": "SeverityLevel.MAJOR", "tool": "shellcheck"}, {"category": "shell", "column": 35, "file_path": "tests/test_security_scan_complex.sh", "fix_suggestion": null, "fixable": false, "line_number": 136, "message": "line_content appears unused. Verify use (or export if used externally).", "rule_id": "SC2034", "severity": "SeverityLevel.MAJOR", "tool": "shellcheck"}, {"category": "shell", "column": 35, "file_path": "tests/test_security_scan_complex.sh", "fix_suggestion": null, "fixable": false, "line_number": 136, "message": "Declare and assign separately to avoid masking return values.", "rule_id": "SC2155", "severity": "SeverityLevel.MAJOR", "tool": "shellcheck"}, {"category": "shell", "column": 19, "file_path": "tests/test_security_scan_complex.sh", "fix_suggestion": null, "fixable": false, "line_number": 224, "message": "Declare and assign separately to avoid masking return values.", "rule_id": "SC2155", "severity": "SeverityLevel.MAJOR", "tool": "shellcheck"}, {"category": "shell", "column": 19, "file_path": "tests/test_security_scan_complex.sh", "fix_suggestion": null, "fixable": false, "line_number": 234, "message": "Declare and assign separately to avoid masking return values.", "rule_id": "SC2155", "severity": "SeverityLevel.MAJOR", "tool": "shellcheck"}, {"category": "shell", "column": 19, "file_path": "tests/test_security_scan_complex.sh", "fix_suggestion": null, "fixable": false, "line_number": 259, "message": "Declare and assign separately to avoid masking return values.", "rule_id": "SC2155", "severity": "SeverityLevel.MAJOR", "tool": "shellcheck"}, {"category": "shell", "column": 23, "file_path": "tests/test_security_scan_complex.sh", "fix_suggestion": null, "fixable": false, "line_number": 280, "message": "Declare and assign separately to avoid masking return values.", "rule_id": "SC2155", "severity": "SeverityLevel.MAJOR", "tool": "shellcheck"}, {"category": "shell", "column": 27, "file_path": "tests/test_security_scan_complex.sh", "fix_suggestion": null, "fixable": false, "line_number": 329, "message": "Declare and assign separately to avoid masking return values.", "rule_id": "SC2155", "severity": "SeverityLevel.MAJOR", "tool": "shellcheck"}, {"category": "shell", "column": 23, "file_path": "tests/test_security_scan_complex.sh", "fix_suggestion": null, "fixable": false, "line_number": 361, "message": "Declare and assign separately to avoid masking return values.", "rule_id": "SC2155", "severity": "SeverityLevel.MAJOR", "tool": "shellcheck"}, {"category": "shell", "column": 27, "file_path": "tests/test_security_scan_complex.sh", "fix_suggestion": null, "fixable": false, "line_number": 363, "message": "Declare and assign separately to avoid masking return values.", "rule_id": "SC2155", "severity": "SeverityLevel.MAJOR", "tool": "shellcheck"}, {"category": "docker", "column": 1, "file_path": "docker-compose.yml", "fix_suggestion": null, "fixable": false, "line_number": 1, "message": "unexpected 's'\nexpecting '#', ADD, ARG, CMD, COPY, ENTRYPOINT, ENV, EXPOSE, FROM, HEALTHCHECK, LABEL, <PERSON>IN<PERSON>INER, ONB<PERSON>LD, RUN, SHELL, <PERSON><PERSON><PERSON>G<PERSON>L, USER, VOLUME, <PERSON><PERSON><PERSON><PERSON><PERSON>, a pragma, end of input, or whitespaces", "rule_id": "DL1000", "severity": "SeverityLevel.CRITICAL", "tool": "hadolint"}, {"category": "docker", "column": 1, "file_path": "docker-compose.yml", "fix_suggestion": null, "fixable": false, "line_number": 1, "message": "unexpected 's'\nexpecting '#', ADD, ARG, CMD, COPY, ENTRYPOINT, ENV, EXPOSE, FROM, HEALTHCHECK, LABEL, <PERSON>IN<PERSON>INER, ONB<PERSON>LD, RUN, SHELL, <PERSON><PERSON><PERSON>G<PERSON>L, USER, VOLUME, <PERSON><PERSON><PERSON><PERSON><PERSON>, a pragma, end of input, or whitespaces", "rule_id": "DL1000", "severity": "SeverityLevel.CRITICAL", "tool": "hadolint"}, {"category": "yaml", "column": 21, "file_path": "dev/docker-compose.dev.yml", "fix_suggestion": null, "fixable": false, "line_number": 15, "message": "expected 2 (comments)", "rule_id": "yaml-lint", "severity": "SeverityLevel.INFO", "tool": "yam<PERSON><PERSON>"}, {"category": "yaml", "column": 21, "file_path": "dev/docker-compose.dev.yml", "fix_suggestion": null, "fixable": false, "line_number": 16, "message": "expected 2 (comments)", "rule_id": "yaml-lint", "severity": "SeverityLevel.INFO", "tool": "yam<PERSON><PERSON>"}, {"category": "yaml", "column": 21, "file_path": "dev/docker-compose.dev.yml", "fix_suggestion": null, "fixable": false, "line_number": 17, "message": "expected 2 (comments)", "rule_id": "yaml-lint", "severity": "SeverityLevel.INFO", "tool": "yam<PERSON><PERSON>"}, {"category": "yaml", "column": 21, "file_path": "dev/docker-compose.dev.yml", "fix_suggestion": null, "fixable": false, "line_number": 15, "message": "expected 2 (comments)", "rule_id": "yaml-lint", "severity": "SeverityLevel.INFO", "tool": "yam<PERSON><PERSON>"}, {"category": "yaml", "column": 21, "file_path": "dev/docker-compose.dev.yml", "fix_suggestion": null, "fixable": false, "line_number": 16, "message": "expected 2 (comments)", "rule_id": "yaml-lint", "severity": "SeverityLevel.INFO", "tool": "yam<PERSON><PERSON>"}, {"category": "yaml", "column": 21, "file_path": "dev/docker-compose.dev.yml", "fix_suggestion": null, "fixable": false, "line_number": 17, "message": "expected 2 (comments)", "rule_id": "yaml-lint", "severity": "SeverityLevel.INFO", "tool": "yam<PERSON><PERSON>"}, {"category": "yaml", "column": 100, "file_path": ".pre-commit-config.yaml", "fix_suggestion": null, "fixable": false, "line_number": 78, "message": "mapping values are not allowed here (syntax)", "rule_id": "yaml-lint", "severity": "SeverityLevel.INFO", "tool": "yam<PERSON><PERSON>"}], "total_smells": 1255}