# GitHub Actions Workflow for Code Smell Detection
#
# This workflow implements the automated code quality pipeline as defined
# in the Lifeboard code smell automation plan. It runs on every push and PR,
# ensuring code quality gates are maintained.
#
# Features:
# - Multi-language linting (JS/TS, Python, Go, Shell, SQL, <PERSON>AM<PERSON>, Docker,
#   Markdown)
# - Quality gate enforcement
# - Parallel execution for performance
# - Report generation and artifact storage
# - Integration with pre-commit hooks
# - Baseline support for legacy code
#
# Based on the Code Smell Automation Plan document
---

name: Code Smell Detection

'on':
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]
  schedule:
    # Run nightly scan at 2 AM UTC
    - cron: '0 2 * * *'

env:
  # Common environment variables
  NODE_VERSION: '18'
  PYTHON_VERSION: '3.11'
  GO_VERSION: '1.19'

jobs:
  # Pre-commit hooks validation
  pre-commit:
    name: Pre-commit Hooks
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}

      - name: Install pre-commit
        run: |
          python -m pip install --upgrade pip
          pip install pre-commit

      - name: Cache pre-commit hooks
        uses: actions/cache@v3
        with:
          path: ~/.cache/pre-commit
          key: |
            pre-commit-${{ runner.os }}-${
              hashFiles('.pre-commit-config.yaml')
            }

      - name: Run pre-commit hooks
        run: pre-commit run --all-files --verbose

  # JavaScript/TypeScript linting
  javascript-linting:
    name: JavaScript/TypeScript Code Quality
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: |
          cd desktop && npm ci
          npm install -g eslint @typescript-eslint/parser \
            @typescript-eslint/eslint-plugin

      - name: Run ESLint
        run: |
          npx eslint . --ext .js,.jsx,.ts,.tsx --format json \
            --output-file eslint-report.json || true
          npx eslint . --ext .js,.jsx,.ts,.tsx --format unix

      - name: Upload ESLint report
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: eslint-report
          path: eslint-report.json

  # Python code quality
  python-linting:
    name: Python Code Quality
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install pylint bandit black isort flake8
          pip install -r requirements.txt || echo "No requirements.txt found"

      - name: Run Black (formatting check)
        run: black --check --diff .

      - name: Run isort (import sorting check)
        run: isort --check-only --diff .

      - name: Run flake8
        run: flake8 . --format=json --output-file flake8-report.json || true

      - name: Run pylint
        run: |
          find . -name "*.py" -not -path "./node_modules/*" \
            -not -path "./volumes/*" | xargs pylint --output-format=json \
            > pylint-report.json || true

      - name: Run bandit (security)
        run: |
          bandit -r . -f json -o bandit-report.json || true

      - name: Upload Python reports
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: python-reports
          path: |
            flake8-report.json
            pylint-report.json
            bandit-report.json

  # Go code quality
  go-linting:
    name: Go Code Quality
    runs-on: ubuntu-latest
    if: ${{ hashFiles('**/*.go') != '' }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Go
        uses: actions/setup-go@v4
        with:
          go-version: ${{ env.GO_VERSION }}

      - name: Install golangci-lint
        uses: golangci/golangci-lint-action@v3
        with:
          version: latest
          args: |
            --config=.golangci.yml --out-format=json
            --out-format=checkstyle:golangci-report.xml
          only-new-issues: false

      - name: Upload Go reports
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: go-reports
          path: golangci-report.xml

  # Shell script linting
  shell-linting:
    name: Shell Script Quality
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Install ShellCheck
        run: sudo apt-get install -y shellcheck

      - name: Run ShellCheck
        run: |
          find . -name "*.sh" -not -path "./node_modules/*" \
            -not -path "./volumes/*" | xargs shellcheck -f json \
            > shellcheck-report.json || true

      - name: Upload Shell reports
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: shell-reports
          path: shellcheck-report.json

  # SQL linting
  sql-linting:
    name: SQL Code Quality
    runs-on: ubuntu-latest
    if: ${{ hashFiles('**/*.sql') != '' }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}

      - name: Install SQLFluff
        run: pip install sqlfluff

      - name: Run SQLFluff
        run: |
          sqlfluff lint . --dialect postgres --format json \
            > sqlfluff-report.json || true

      - name: Upload SQL reports
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: sql-reports
          path: sqlfluff-report.json

  # Docker linting
  docker-linting:
    name: Docker Quality
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Install Hadolint
        run: |
          wget -O hadolint \
            https://github.com/hadolint/hadolint/releases/latest/download/hadolint-Linux-x86_64
          chmod +x hadolint
          sudo mv hadolint /usr/local/bin/

      - name: Run Hadolint
        run: |
          find . -name "Dockerfile*" -o -name "*.dockerfile" | \
            xargs -I {} hadolint {} --format json > hadolint-report.json || true

      - name: Upload Docker reports
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: docker-reports
          path: hadolint-report.json

  # YAML linting
  yaml-linting:
    name: YAML Quality
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}

      - name: Install yamllint
        run: pip install yamllint

      - name: Run yamllint
        run: |
          yamllint . --format parsable > yamllint-report.txt || true

      - name: Upload YAML reports
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: yaml-reports
          path: yamllint-report.txt

  # Markdown linting
  markdown-linting:
    name: Markdown Quality
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Install markdownlint-cli2
        run: npm install -g markdownlint-cli2

      - name: Run markdownlint
        run: |
          markdownlint-cli2 "**/*.md" \
            --output markdownlint-report.json || true

      - name: Upload Markdown reports
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: markdown-reports
          path: markdownlint-report.json

  # Comprehensive code smell analysis
  code-smell-analysis:
    name: Comprehensive Code Smell Analysis
    runs-on: ubuntu-latest
    needs:
      - javascript-linting
      - python-linting
      - shell-linting
      - sql-linting
      - docker-linting
      - yaml-linting
      - markdown-linting
    if: always()
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}

      - name: Install analysis tools
        run: |
          python -m pip install --upgrade pip
          pip install pylint bandit

      - name: Download all reports
        uses: actions/download-artifact@v3
        with:
          path: reports/

      - name: Run comprehensive code smell detector
        run: |
          python tools/code_smell_detector.py \
            --project . \
            --output code-smell-report.md \
            --format markdown

      - name: Generate quality gate summary
        run: |
          python tools/code_smell_detector.py \
            --project . \
            --output quality-gates.json \
            --format json

      - name: Check quality gates
        id: quality-gates
        run: |
          # Parse quality gates result and set appropriate exit code
          python -c "
          import json
          import sys

          with open('quality-gates.json', 'r') as f:
              result = json.load(f)

          failed_gates = [
            name for name, passed in result['quality_gates'].items()
            if not passed
          ]

          if failed_gates:
              print(f'Quality gates failed: {failed_gates}')
              print(f'::error::Quality gates failed: {failed_gates}')
              sys.exit(1)
          else:
              print('All quality gates passed!')
              print('::notice::All quality gates passed!')
          "

      - name: Upload comprehensive reports
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: code-smell-analysis
          path: |
            code-smell-report.md
            quality-gates.json

      - name: Comment PR with results
        if: github.event_name == 'pull_request'
        uses: actions/github-script@v6
        with:
          script: |
            const fs = require('fs');
            const path = require('path');

            // Read the code smell report
            const reportPath = 'code-smell-report.md';
            if (fs.existsSync(reportPath)) {
              const report = fs.readFileSync(reportPath, 'utf8');

              // Truncate if too long for GitHub comment
              const maxLength = 60000;
              const truncatedReport = report.length > maxLength
                ? report.substring(0, maxLength) + '\n\n... (report truncated)'
                : report;

              await github.rest.issues.createComment({
                issue_number: context.issue.number,
                owner: context.repo.owner,
                repo: context.repo.repo,
                body: `## Code Smell Analysis Report\n\n${truncatedReport}`
              });
            }

  # Security scan
  security-scan:
    name: Security Analysis
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'fs'
          scan-ref: '.'
          format: 'sarif'
          output: 'trivy-results.sarif'

      - name: Upload Trivy scan results to GitHub Security tab
        uses: github/codeql-action/upload-sarif@v2
        if: always()
        with:
          sarif_file: 'trivy-results.sarif'

      - name: Run GitLeaks
        uses: gitleaks/gitleaks-action@v2
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

  # Aggregate results and enforce quality gates
  quality-gate-enforcement:
    name: Quality Gate Enforcement
    runs-on: ubuntu-latest
    needs: [code-smell-analysis, security-scan]
    if: always()
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Download analysis results
        uses: actions/download-artifact@v3
        with:
          name: code-smell-analysis
          path: .

      - name: Enforce quality gates
        run: |
          # Check if quality gates file exists and enforce
          if [ -f "quality-gates.json" ]; then
            python -c "
            import json
            import sys

            with open('quality-gates.json', 'r') as f:
                result = json.load(f)

            failed_gates = [
              name for name, passed in result['quality_gates'].items()
              if not passed
            ]
            blocker_failed = any(
              gate.get('blocker', False) for gate in failed_gates
            )

            if failed_gates:
                print(f'Quality gates failed: {failed_gates}')
                if blocker_failed:
                    print('Blocker quality gates failed - build must fail')
                    sys.exit(1)
                else:
                    print('Non-blocker quality gates failed - warning issued')
            else:
                print('All quality gates passed!')
            "
          else
            echo "No quality gates file found"
            exit 1
          fi

      - name: Save baseline for future runs
        if: github.ref == 'refs/heads/main' && github.event_name == 'push'
        run: |
          # Save current state as baseline for future comparison
          mkdir -p .github/linters/
          python tools/code_smell_detector.py \
            --project . \
            --baseline \
            --format json

      - name: Commit baseline updates
        if: github.ref == 'refs/heads/main' && github.event_name == 'push'
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"
          git add .github/linters/baseline.json || true
          git commit -m "Update code smell baseline [skip ci]" || true
          git push || true
