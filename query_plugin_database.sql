-- Plugin State Database Verification Queries
-- Use these queries to check the current state of plugins in the database

-- 1. Check if the plugins table exists and view its structure
\d plugins;

-- 2. View all plugins in the database
SELECT 
    id,
    name,
    version,
    enabled,
    configuration,
    last_sync_at,
    sync_frequency_minutes,
    created_at,
    updated_at,
    user_id
FROM plugins
ORDER BY created_at DESC;

-- 3. Specifically check for Limitless plugin records
SELECT 
    id,
    name,
    version,
    enabled,
    configuration,
    created_at,
    updated_at,
    user_id
FROM plugins 
WHERE name = 'limitless' OR name LIKE '%limitless%'
ORDER BY created_at DESC;

-- 4. Count plugins by enabled status
SELECT 
    enabled,
    COUNT(*) as count
FROM plugins
GROUP BY enabled;

-- 5. Check for any plugins with default enabled=true (from schema)
SELECT 
    name,
    version,
    enabled,
    created_at
FROM plugins
WHERE enabled = true
ORDER BY name;

-- 6. Check for any plugins with enabled=false
SELECT 
    name,
    version,
    enabled,
    created_at
FROM plugins
WHERE enabled = false
ORDER BY name;

-- 7. View plugin configuration details for Limitless
SELECT 
    name,
    version,
    enabled,
    configuration,
    jsonb_pretty(configuration) as formatted_config
FROM plugins 
WHERE name = 'limitless';

-- 8. Check if there are any plugin_logs for Limitless
SELECT 
    pl.level,
    pl.message,
    pl.context,
    pl.created_at,
    p.name as plugin_name
FROM plugin_logs pl
JOIN plugins p ON pl.plugin_id = p.id
WHERE p.name = 'limitless'
ORDER BY pl.created_at DESC
LIMIT 10;

-- 9. Check user-specific plugin states (if you have a specific user)
-- Replace 'your-user-id' with an actual user ID
-- SELECT 
--     name,
--     version,
--     enabled,
--     user_id
-- FROM plugins 
-- WHERE user_id = 'your-user-id'
-- ORDER BY name;

-- 10. Insert a test Limitless plugin record (DISABLED by default)
-- Uncomment and run this if you want to test with a database record
-- INSERT INTO plugins (name, version, enabled, configuration, sync_frequency_minutes)
-- VALUES ('limitless', '0.1.0', false, '{"theme": "dark", "notifications": true, "apiKey": ""}', 360)
-- ON CONFLICT (name, user_id) DO UPDATE SET
--     version = EXCLUDED.version,
--     enabled = EXCLUDED.enabled,
--     configuration = EXCLUDED.configuration,
--     updated_at = NOW();

-- 11. Update existing Limitless plugin to be disabled (if it exists)
-- Uncomment and run this if you find enabled Limitless plugins that should be disabled
-- UPDATE plugins 
-- SET enabled = false, updated_at = NOW()
-- WHERE name = 'limitless' AND enabled = true;

-- 12. Check the default value for the enabled column in the plugins table
SELECT 
    column_name,
    column_default,
    is_nullable,
    data_type
FROM information_schema.columns 
WHERE table_name = 'plugins' 
    AND table_schema = 'public'
    AND column_name = 'enabled';
