#!/bin/bash

# Fix all user table references in test files to use auth.users

cd /Users/<USER>/code/lifeboard-supabase

# Fix edge cases test
sed -i '' 's/INSERT INTO users (id, email) VALUES (test_user_id, '\''concurrent\.test@lifeboard\.test'\'')/INSERT INTO auth.users (id, email, encrypted_password, created_at, updated_at, role, aud) VALUES (test_user_id, '\''<EMAIL>'\'', '\''test_password_hash'\'', NOW(), NOW(), '\''authenticated'\'', '\''authenticated'\'')/g' ./tests/test_edge_cases.sql

sed -i '' 's/INSERT INTO users (id, email) VALUES (test_user_id, '\''invalid\.test@lifeboard\.test'\'')/INSERT INTO auth.users (id, email, encrypted_password, created_at, updated_at, role, aud) VALUES (test_user_id, '\''<EMAIL>'\'', '\''test_password_hash'\'', NOW(), NOW(), '\''authenticated'\'', '\''authenticated'\'')/g' ./tests/test_edge_cases.sql

sed -i '' 's/INSERT INTO users (id, email) VALUES (test_user_id, '\''unicode\.test@lifeboard\.test'\'')/INSERT INTO auth.users (id, email, encrypted_password, created_at, updated_at, role, aud) VALUES (test_user_id, '\''<EMAIL>'\'', '\''test_password_hash'\'', NOW(), NOW(), '\''authenticated'\'', '\''authenticated'\'')/g' ./tests/test_edge_cases.sql

sed -i '' 's/INSERT INTO users (id, email) VALUES (test_user_id, '\''stress\.test@lifeboard\.test'\'')/INSERT INTO auth.users (id, email, encrypted_password, created_at, updated_at, role, aud) VALUES (test_user_id, '\''<EMAIL>'\'', '\''test_password_hash'\'', NOW(), NOW(), '\''authenticated'\'', '\''authenticated'\'')/g' ./tests/test_edge_cases.sql

sed -i '' 's/INSERT INTO users (id, email) VALUES (test_user_id, '\''transaction\.test@lifeboard\.test'\'')/INSERT INTO auth.users (id, email, encrypted_password, created_at, updated_at, role, aud) VALUES (test_user_id, '\''<EMAIL>'\'', '\''test_password_hash'\'', NOW(), NOW(), '\''authenticated'\'', '\''authenticated'\'')/g' ./tests/test_edge_cases.sql

sed -i '' 's/INSERT INTO users (id, email) VALUES (test_user_id, '\''constraint\.test@lifeboard\.test'\'')/INSERT INTO auth.users (id, email, encrypted_password, created_at, updated_at, role, aud) VALUES (test_user_id, '\''<EMAIL>'\'', '\''test_password_hash'\'', NOW(), NOW(), '\''authenticated'\'', '\''authenticated'\'')/g' ./tests/test_edge_cases.sql

# Fix all remaining DELETE FROM users references
sed -i '' 's/DELETE FROM users WHERE id = test_user_id;/DELETE FROM auth.users WHERE id = test_user_id;/g' ./tests/test_edge_cases.sql

echo "User table references fixed in edge cases test"
