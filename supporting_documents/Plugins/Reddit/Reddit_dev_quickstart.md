```markdown
# Listing a User’s Reddit Posts: High-Level Flow

CRITICAL: THIS IS NOT A REDDIT "APP" THAT IS ACCESSIBLE TO THE PUBLIC.  IT IS A LIFEBOARD PLUGIN THAT IS ONLY ACCESSIBLE TO LIFEBOARD USERS.

The following is the Reddit API reference: https://www.reddit.com/dev/api

Authentication details are here: https://github.com/reddit-archive/reddit/wiki/OAuth2

Also here: https://github.com/reddit-archive/reddit/wiki/OAuth2-Quick-Start-Example




NOTE:

I created an "app" in reddit
Name: lifeboard
Secret: Hbvv4ALjAs92FkSa30JSuQ


---

## 1. Register Your App and Store Credentials

1. Create a “script”-type app on Reddit’s developer portal.
2. Record the **Client ID** and **Client Secret**.
3. Securely store these (e.g., environment variables, secrets manager).

---

## 2. Obtain User Authorization

1. Redirect the user’s browser to Reddit’s OAuth URL:
```

https://www.reddit.com/api/v1/authorize? client_id=YOUR_ID& response_type=code& state=RANDOM_STRING& redirect_uri=YOUR_REDIRECT_URI& duration=temporary& scope=read

```

2. User logs in and approves your requested scope (read-only access).

3. Reddit redirects back with `code` and your `state` token.

---

## 3. Exchange Code for an Access Token

1. Send a POST request to:
```

https://www.reddit.com/api/v1/access_token

```

2. Include HTTP Basic auth using your **Client ID** and **Client Secret**.

3. In the request body, send:
- `grant_type=authorization_code`
- `code=THE_CODE_FROM_STEP_2`
- `redirect_uri=YOUR_REDIRECT_URI`

4. Receive a JSON response containing:
- `access_token`
- `token_type`
- `expires_in`
- `scope`

---

## 4. Call the Reddit API to List Posts

1. Choose the endpoint for retrieving user submissions:
```

GET https://oauth.reddit.com/user/{username}/submitted

```

2. Add headers:
- `Authorization: bearer YOUR_ACCESS_TOKEN`
- `User-Agent: your_app_name/0.1 by your_reddit_username`

3. Parse the returned JSON to extract each post’s title, URL, timestamp, etc.

---

## 5. Handle Pagination and Rate Limits

- Reddit pages results via `after` and `before` tokens.
- Loop requests, passing `?after=token` until no more items.
- Respect rate limits (usually 60 requests/minute).
- Implement retry/backoff for 429 (Too Many Requests).

---

## Security and Best Practices

- Never hard-code your **Client Secret** in source.
- Rotate tokens periodically.
- Use HTTPS for all communications.
- Log requests at a debug level without exposing secrets.

---

## Next Steps & Related Topics

- Use a refresh-token flow for long-lived access.
- Simplify OAuth and paging with libraries like PRAW (Python), Snoowrap (Node.js), or RedditSharp (C#).
- Cache results locally to minimize API calls and speed up your app.
- Expand scopes (e.g., `identity`, `vote`) for richer functionality.
```
