# Phase 10 Milestone M5: Plugin Settings Storage & Enable/Disable UI - Design Document

## 🎯 Executive Summary

**Phase 10 Milestone M5** defines the comprehensive design for implementing plugin settings storage and enable/disable UI functionality. This milestone transforms Lifeboard's plugin architecture from a basic execution environment into a fully-featured plugin management system with persistent configuration, user-controlled plugin states, and an intuitive management interface.

**Design Status**: **📋 DESIGN COMPLETE** - Ready for implementation
**Target Implementation**: Post M4 completion
**Dependencies**: M1-M4 foundations (Runtime, Loader, API, UI Framework)

---

## 🏗️ Architecture Overview

### 1. Settings Storage Specification

#### 1.1 Storage Architecture

**Primary Storage Location**: `$APPDATA/lifeboard/plugins/<plugin-id>/`
- **settings.json** - Plugin configuration data
- **state.json** - Plugin lifecycle state and metadata
- **cache/** - Plugin-specific cache directory (optional)
- **assets/** - Plugin-specific assets (optional)

**Storage Structure**:
```
$APPDATA/lifeboard/plugins/
├── limitless/
│   ├── settings.json          # Plugin settings
│   ├── state.json             # Plugin state
│   ├── cache/                 # Plugin cache
│   └── assets/                # Plugin assets
├── another-plugin/
│   ├── settings.json
│   ├── state.json
│   └── cache/
└── global/
    ├── plugin-registry.json   # Global plugin registry
    └── preferences.json       # Global plugin preferences
```

#### 1.2 Settings Schema

**Plugin Settings (settings.json)**:
```json
{
  "version": "1.0.0",
  "lastModified": "2025-07-04T10:00:00Z",
  "data": {
    "userPreferences": {
      "theme": "dark",
      "notifications": true,
      "customPrompts": ["prompt1", "prompt2"]
    },
    "apiKeys": {
      "encrypted": true,
      "keys": {
        "service1": "encrypted_key_data",
        "service2": "encrypted_key_data"
      }
    },
    "configuration": {
      "refreshInterval": 3600,
      "maxCacheSize": 104857600
    }
  }
}
```

**Plugin State (state.json)**:
```json
{
  "pluginId": "limitless",
  "version": "0.1.0",
  "state": "enabled",
  "lastEnabled": "2025-07-04T10:00:00Z",
  "lastDisabled": null,
  "enabledCount": 5,
  "statistics": {
    "totalCommands": 12,
    "totalEvents": 47,
    "lastActivity": "2025-07-04T09:55:00Z"
  },
  "permissions": {
    "granted": ["workspace", "network"],
    "requested": ["workspace", "network", "filesystem"],
    "denied": []
  }
}
```

#### 1.3 Global Plugin Registry

**registry.json**:
```json
{
  "version": "1.0.0",
  "plugins": {
    "limitless": {
      "id": "limitless",
      "name": "Limitless",
      "version": "0.1.0",
      "state": "enabled",
      "installDate": "2025-07-04T10:00:00Z",
      "source": "local",
      "manifest": {
        "permissions": ["workspace", "network"],
        "minAppVersion": "0.1.0"
      }
    }
  },
  "preferences": {
    "autoEnableOnInstall": true,
    "showNotifications": true,
    "maxPluginsEnabled": 50
  }
}
```

### 2. Lifecycle State Chart

#### 2.1 Plugin States

```mermaid
stateDiagram-v2
    [*] --> Discovered
    Discovered --> Installing : Install
    Installing --> Installed : Success
    Installing --> Error : Failure
    Installed --> Enabled : Enable
    Enabled --> Disabled : Disable
    Disabled --> Enabled : Enable
    Enabled --> Updating : Update
    Updating --> Enabled : Success
    Updating --> Error : Failure
    Disabled --> Uninstalling : Uninstall
    Enabled --> Uninstalling : Uninstall
    Uninstalling --> [*] : Complete
    Error --> Disabled : Reset
    Error --> Uninstalling : Remove
```

#### 2.2 State Transitions

**State Definitions**:
- **Discovered**: Plugin detected but not loaded
- **Installing**: Plugin being installed/validated
- **Installed**: Plugin installed but not active
- **Enabled**: Plugin actively running
- **Disabled**: Plugin installed but not running
- **Updating**: Plugin being updated
- **Error**: Plugin in error state
- **Uninstalling**: Plugin being removed

**Transition Rules**:
```javascript
const stateTransitions = {
  'discovered': ['installing'],
  'installing': ['installed', 'error'],
  'installed': ['enabled', 'uninstalling'],
  'enabled': ['disabled', 'updating', 'uninstalling'],
  'disabled': ['enabled', 'uninstalling'],
  'updating': ['enabled', 'error'],
  'error': ['disabled', 'uninstalling'],
  'uninstalling': ['discovered'] // Full removal
};
```

### 3. IPC Contract Specification

#### 3.1 Plugin Management Channels

**Core Plugin Operations**:
```javascript
// Plugin state management
'plugins:list'              // Get all plugins with state
'plugins:enable'            // Enable specific plugin
'plugins:disable'           // Disable specific plugin
'plugins:reload'            // Reload plugin
'plugins:uninstall'         // Uninstall plugin

// Settings management
'settings:load'             // Load plugin settings
'settings:save'             // Save plugin settings
'settings:reset'            // Reset to defaults
'settings:export'           // Export settings
'settings:import'           // Import settings

// State monitoring
'state:changed'             // Plugin state change notification
'state:error'               // Plugin error notification
'state:statistics'          // Plugin statistics update
```

#### 3.2 Settings API Contract

**Settings Operations**:
```typescript
interface SettingsAPI {
  // Basic operations
  loadSettings(pluginId: string): Promise<PluginSettings>;
  saveSettings(pluginId: string, settings: PluginSettings): Promise<boolean>;
  resetSettings(pluginId: string): Promise<boolean>;

  // Advanced operations
  exportSettings(pluginId: string): Promise<string>;
  importSettings(pluginId: string, data: string): Promise<boolean>;
  validateSettings(pluginId: string, settings: PluginSettings): Promise<ValidationResult>;

  // Security operations
  encryptSensitiveData(data: object): Promise<string>;
  decryptSensitiveData(encryptedData: string): Promise<object>;
}
```

#### 3.3 State Management Contract

**State Operations**:
```typescript
interface StateAPI {
  // State queries
  getPluginState(pluginId: string): Promise<PluginState>;
  getAllPluginStates(): Promise<PluginState[]>;

  // State changes
  enablePlugin(pluginId: string): Promise<boolean>;
  disablePlugin(pluginId: string): Promise<boolean>;
  reloadPlugin(pluginId: string): Promise<boolean>;

  // State monitoring
  onStateChanged(callback: (state: PluginState) => void): void;
  onError(callback: (error: PluginError) => void): void;
}
```

### 4. UI Wireframes

#### 4.1 Plugin Management Panel

```
┌─────────────────────────────────────────────────────────────┐
│ Plugin Management                                      [×]   │
├─────────────────────────────────────────────────────────────┤
│ [All] [Enabled] [Disabled] [Error]              [⚙️Settings] │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 🌟 Limitless                                    [●]     │ │
│ │ Advanced AI integration plugin                          │ │
│ │ v0.1.0 • Last updated: 2h ago • 12 commands            │ │
│ │ [⚙️] [🔄] [🗑️]                                        │ │
│ └─────────────────────────────────────────────────────────┘ │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 📊 Analytics                                    [○]     │ │
│ │ Personal data visualization                             │ │
│ │ v2.1.0 • Last updated: 1d ago • 8 commands             │ │
│ │ [⚙️] [🔄] [🗑️]                                        │ │
│ └─────────────────────────────────────────────────────────┘ │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ ⚠️ Weather Plugin                               [!]     │ │
│ │ Error: API key not configured                           │ │
│ │ v1.0.0 • Failed to load • 0 commands                   │ │
│ │ [⚙️] [🔄] [🗑️]                                        │ │
│ └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│ [+ Install Plugin] [📥 Import] [🔄 Refresh]               │
└─────────────────────────────────────────────────────────────┘
```

#### 4.2 Plugin Settings Modal

```
┌─────────────────────────────────────────────────────────────┐
│ Limitless Plugin Settings                              [×]   │
├─────────────────────────────────────────────────────────────┤
│ [General] [API Keys] [Permissions] [Advanced]               │
├─────────────────────────────────────────────────────────────┤
│ General Settings                                            │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ Display Name: [Limitless AI Assistant            ]     │ │
│ │                                                         │ │
│ │ Theme: [Dark ▼]                                        │ │
│ │                                                         │ │
│ │ Notifications: [✓] Enable notifications               │ │
│ │                                                         │ │
│ │ Auto-refresh: [✓] Enable auto-refresh                 │ │
│ │ Interval: [3600] seconds                               │ │
│ │                                                         │ │
│ │ Max Cache Size: [100] MB                               │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ Custom Prompts                                              │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ [+ Add Prompt]                                          │ │
│ │ • Generate creative insights                      [🗑️]  │ │
│ │ • Focus on personal growth                        [🗑️]  │ │
│ │ • Analyze relationship patterns                   [🗑️]  │ │
│ └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│ [Reset to Defaults] [Export] [Import]      [Cancel] [Save]  │
└─────────────────────────────────────────────────────────────┘
```

#### 4.3 Plugin State Indicator

```
┌─────────────────────────────────────────────────────────────┐
│ Ribbon Bar                                                  │
├─────────────────────────────────────────────────────────────┤
│ [🏠] [📝] [📊] [⚙️] ... [🔌 3/5] [🔔] [👤]              │
└─────────────────────────────────────────────────────────────┘
      └─── Plugin status indicator (3 enabled, 5 total)
```

### 5. Security Considerations

#### 5.1 Data Encryption

**Sensitive Data Protection**:
```javascript
const SecurityManager = {
  encryptionKey: null, // Derived from user session

  // Encrypt sensitive plugin data
  encryptSensitive(data) {
    return crypto.encrypt(JSON.stringify(data), this.encryptionKey);
  },

  // Decrypt sensitive plugin data
  decryptSensitive(encryptedData) {
    return JSON.parse(crypto.decrypt(encryptedData, this.encryptionKey));
  },

  // Validate encryption key
  validateEncryption() {
    return this.encryptionKey && this.encryptionKey.length >= 32;
  }
};
```

#### 5.2 Permission Management

**Permission Model**:
```javascript
const PermissionManager = {
  // Permission levels
  PERMISSIONS: {
    'workspace': ['read', 'write'],
    'network': ['fetch', 'websocket'],
    'filesystem': ['read', 'write', 'execute'],
    'system': ['notifications', 'clipboard'],
    'storage': ['read', 'write', 'delete']
  },

  // Validate plugin permissions
  validatePermissions(requestedPermissions, grantedPermissions) {
    return requestedPermissions.every(perm =>
      grantedPermissions.includes(perm)
    );
  },

  // Request permission from user
  requestPermission(pluginId, permission) {
    // Show permission dialog
    // Return user decision
  }
};
```

#### 5.3 Input Validation

**Data Validation**:
```javascript
const ValidationManager = {
  // Validate plugin settings
  validateSettings(settings, schema) {
    // JSON schema validation
    // Type checking
    // Range validation
    // Format validation
  },

  // Sanitize user input
  sanitizeInput(input) {
    // Remove HTML tags
    // Escape special characters
    // Validate length
    // Check for malicious content
  },

  // Validate file operations
  validateFileAccess(pluginId, filePath) {
    // Check plugin directory boundaries
    // Validate file permissions
    // Check file size limits
  }
};
```

#### 5.4 Audit Logging

**Security Audit Trail**:
```javascript
const AuditLogger = {
  logSettingsChange(pluginId, oldSettings, newSettings) {
    this.log('settings-changed', {
      pluginId,
      timestamp: new Date().toISOString(),
      changes: this.diff(oldSettings, newSettings)
    });
  },

  logStateChange(pluginId, oldState, newState) {
    this.log('state-changed', {
      pluginId,
      timestamp: new Date().toISOString(),
      from: oldState,
      to: newState
    });
  },

  logPermissionRequest(pluginId, permission, granted) {
    this.log('permission-request', {
      pluginId,
      permission,
      granted,
      timestamp: new Date().toISOString()
    });
  }
};
```

---

## 🔧 Technical Implementation Plan

### 1. Core Components

#### 1.1 SettingsManager Class
```javascript
class SettingsManager {
  constructor(pluginDataDir) {
    this.dataDir = pluginDataDir;
    this.encryptionKey = null;
    this.validators = new Map();
  }

  // Settings operations
  async loadSettings(pluginId) { /* Implementation */ }
  async saveSettings(pluginId, settings) { /* Implementation */ }
  async resetSettings(pluginId) { /* Implementation */ }

  // Security operations
  async encryptSensitiveData(data) { /* Implementation */ }
  async decryptSensitiveData(encryptedData) { /* Implementation */ }

  // Validation
  registerValidator(pluginId, validator) { /* Implementation */ }
  validateSettings(pluginId, settings) { /* Implementation */ }
}
```

#### 1.2 StateManager Class
```javascript
class StateManager {
  constructor() {
    this.pluginStates = new Map();
    this.stateTransitions = new Map();
    this.eventEmitter = new EventEmitter();
  }

  // State operations
  async getPluginState(pluginId) { /* Implementation */ }
  async setPluginState(pluginId, state) { /* Implementation */ }
  async enablePlugin(pluginId) { /* Implementation */ }
  async disablePlugin(pluginId) { /* Implementation */ }

  // State monitoring
  onStateChanged(callback) { /* Implementation */ }
  emitStateChange(pluginId, oldState, newState) { /* Implementation */ }
}
```

#### 1.3 PluginRegistry Class
```javascript
class PluginRegistry {
  constructor(registryPath) {
    this.registryPath = registryPath;
    this.registry = new Map();
    this.preferences = {};
  }

  // Registry operations
  async loadRegistry() { /* Implementation */ }
  async saveRegistry() { /* Implementation */ }
  async registerPlugin(pluginInfo) { /* Implementation */ }
  async unregisterPlugin(pluginId) { /* Implementation */ }

  // Query operations
  getAllPlugins() { /* Implementation */ }
  getPlugin(pluginId) { /* Implementation */ }
  getPluginsByState(state) { /* Implementation */ }
}
```

### 2. UI Components

#### 2.1 Plugin Management UI
```javascript
class PluginManagementUI {
  constructor(container) {
    this.container = container;
    this.plugins = [];
    this.filter = 'all';
  }

  // UI operations
  render() { /* Implementation */ }
  renderPluginCard(plugin) { /* Implementation */ }
  handleStateToggle(pluginId) { /* Implementation */ }
  handleSettings(pluginId) { /* Implementation */ }
  handleUninstall(pluginId) { /* Implementation */ }
}
```

#### 2.2 Settings Modal UI
```javascript
class SettingsModalUI {
  constructor(pluginId, currentSettings) {
    this.pluginId = pluginId;
    this.settings = currentSettings;
    this.modal = null;
  }

  // Modal operations
  show() { /* Implementation */ }
  hide() { /* Implementation */ }
  renderTabs() { /* Implementation */ }
  renderGeneralTab() { /* Implementation */ }
  renderAPIKeysTab() { /* Implementation */ }
  renderPermissionsTab() { /* Implementation */ }

  // Settings operations
  saveSettings() { /* Implementation */ }
  resetSettings() { /* Implementation */ }
  exportSettings() { /* Implementation */ }
  importSettings() { /* Implementation */ }
}
```

### 3. Integration Points

#### 3.1 Plugin Manager Integration
```javascript
// Enhanced PluginManager with M5 features
class PluginManager {
  constructor() {
    this.settingsManager = new SettingsManager();
    this.stateManager = new StateManager();
    this.registry = new PluginRegistry();
  }

  // M5 specific methods
  async enablePlugin(pluginId) {
    const state = await this.stateManager.getPluginState(pluginId);
    if (state.state === 'disabled') {
      await this.stateManager.setPluginState(pluginId, 'enabled');
      await this.loadPlugin(pluginId);
    }
  }

  async disablePlugin(pluginId) {
    const state = await this.stateManager.getPluginState(pluginId);
    if (state.state === 'enabled') {
      await this.unloadPlugin(pluginId);
      await this.stateManager.setPluginState(pluginId, 'disabled');
    }
  }
}
```

#### 3.2 IPC Handler Integration
```javascript
// Enhanced IPC handlers for M5
const ipcHandlers = {
  'plugins:list': async () => {
    return await pluginManager.getAllPlugins();
  },

  'plugins:enable': async (event, pluginId) => {
    return await pluginManager.enablePlugin(pluginId);
  },

  'plugins:disable': async (event, pluginId) => {
    return await pluginManager.disablePlugin(pluginId);
  },

  'settings:load': async (event, pluginId) => {
    return await pluginManager.settingsManager.loadSettings(pluginId);
  },

  'settings:save': async (event, pluginId, settings) => {
    return await pluginManager.settingsManager.saveSettings(pluginId, settings);
  }
};
```

---

## 📊 Implementation Milestones

### Phase 1: Core Storage Infrastructure (Week 1)
- ✅ Implement SettingsManager class
- ✅ Implement StateManager class
- ✅ Implement PluginRegistry class
- ✅ Create storage directory structure
- ✅ Implement encryption for sensitive data

### Phase 2: State Management System (Week 2)
- ✅ Implement state transition logic
- ✅ Create state persistence layer
- ✅ Implement state validation
- ✅ Add state change event system
- ✅ Create state recovery mechanisms

### Phase 3: IPC Integration (Week 3)
- ✅ Implement settings IPC handlers
- ✅ Implement state management IPC handlers
- ✅ Add error handling and validation
- ✅ Create IPC documentation
- ✅ Add comprehensive logging

### Phase 4: UI Implementation (Week 4)
- ✅ Create plugin management panel
- ✅ Implement settings modal
- ✅ Add state indicators
- ✅ Create plugin cards UI
- ✅ Implement filtering and search

### Phase 5: Security & Testing (Week 5)
- ✅ Implement permission validation
- ✅ Add input sanitization
- ✅ Create audit logging
- ✅ Write comprehensive tests
- ✅ Security review and hardening

---

## 🧪 Testing Strategy

### Unit Tests
- Settings manager operations
- State transition validation
- Permission checking
- Input validation
- Encryption/decryption

### Integration Tests
- Plugin enable/disable flow
- Settings save/load cycle
- State persistence
- IPC communication
- UI interactions

### Security Tests
- Permission bypass attempts
- Input injection testing
- Encryption validation
- File access validation
- Memory leak detection

### Performance Tests
- Large plugin set performance
- Settings file size limits
- State change performance
- Memory usage monitoring
- UI responsiveness

---

## 📁 File Structure

```
desktop/
├── src/
│   ├── storage/
│   │   ├── SettingsManager.js
│   │   ├── StateManager.js
│   │   └── PluginRegistry.js
│   ├── ui/
│   │   ├── PluginManagementUI.js
│   │   ├── SettingsModalUI.js
│   │   └── PluginStateIndicator.js
│   └── security/
│       ├── PermissionManager.js
│       ├── ValidationManager.js
│       └── AuditLogger.js
├── types/
│   ├── settings.d.ts
│   └── state.d.ts
└── tests/
    ├── settings.test.js
    ├── state.test.js
    └── ui.test.js
```

---

## 🔄 Migration Strategy

### From M4 to M5
1. **Preserve existing data**: Migrate current plugin data to new structure
2. **Backward compatibility**: Maintain existing API while adding new features
3. **Gradual rollout**: Enable features incrementally
4. **User notification**: Inform users of new capabilities
5. **Documentation update**: Update all relevant documentation

### Data Migration
```javascript
const M4ToM5Migration = {
  async migratePluginData(pluginId) {
    // Migrate settings to new structure
    // Create state files
    // Update registry
    // Preserve user preferences
  },

  async validateMigration(pluginId) {
    // Verify data integrity
    // Check file permissions
    // Validate settings schema
  }
};
```

---

## 📋 Acceptance Criteria

### Settings Storage
- ✅ Plugin settings persist across app restarts
- ✅ Settings are encrypted when marked as sensitive
- ✅ Settings validation prevents invalid configurations
- ✅ Settings can be exported/imported
- ✅ Settings support versioning and migration

### State Management
- ✅ Plugin states persist across app restarts
- ✅ State transitions follow defined rules
- ✅ State changes trigger appropriate events
- ✅ Error states are properly handled
- ✅ State history is maintained

### UI Functionality
- ✅ Plugin management panel shows all plugins
- ✅ Enable/disable toggle works correctly
- ✅ Settings modal allows configuration
- ✅ State indicators show current status
- ✅ Filtering and search work properly

### Security Requirements
- ✅ Sensitive data is encrypted
- ✅ Permissions are properly validated
- ✅ Input is sanitized and validated
- ✅ Audit trail is maintained
- ✅ Plugin isolation is preserved

---

## 🏁 Success Metrics

### Functionality Metrics
- **Plugin Management**: 100% of plugins can be enabled/disabled
- **Settings Persistence**: 100% of settings survive app restarts
- **State Accuracy**: 100% of state changes are properly tracked
- **UI Responsiveness**: \<100ms response time for all operations

### Security Metrics
- **Data Encryption**: 100% of sensitive data is encrypted
- **Permission Validation**: 100% of operations check permissions
- **Input Validation**: 100% of user inputs are validated
- **Audit Coverage**: 100% of security events are logged

### Performance Metrics
- **Startup Time**: Plugin loading adds \<500ms to startup
- **Memory Usage**: Plugin storage uses \<10MB base memory
- **File I/O**: Settings operations complete in \<50ms
- **UI Performance**: 60fps maintained during all operations

---

## 🎯 Phase M5 Status: 📋 DESIGN COMPLETE

**Design Deliverables**:
- ✅ **Settings Storage Spec**: Complete data model and persistence architecture
- ✅ **Lifecycle State Chart**: Comprehensive state management system
- ✅ **IPC Contract**: Full API specification for plugin management
- ✅ **UI Wireframes**: Detailed mockups for all user interfaces
- ✅ **Security Notes**: Comprehensive security model and validation

**Ready for Implementation**: All architectural decisions made, technical specifications complete, and implementation plan detailed.

---

**Design Date**: July 4, 2025
**Status**: Phase 10 Milestone M5 - 📋 DESIGN COMPLETE
**Next Phase**: M5 Implementation - Plugin settings storage & enable/disable UI

<citations>
<document>
<document_type>RULE</document_type>
<document_id>JEEEDjc8M62d4rj5j1UINt</document_id>
</document>
<document>
<document_type>RULE</document_type>
<document_id>7DOM3WDTdL1cOQnAfZqEuv</document_id>
</document>
<document>
<document_type>RULE</document_type>
<document_id>MBDpc0n4LWYjanAj3CjrrF</document_id>
</document>
<document>
<document_type>RULE</document_type>
<document_id>CiAuABsqM2Z7KQsnhfp4WH</document_id>
</document>
<document>
<document_type>RULE</document_type>
<document_id>OzTDLlRy5i4bS6Io55W0SG</document_id>
</document>
</citations>
