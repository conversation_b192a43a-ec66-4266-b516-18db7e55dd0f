# Logging Infrastructure Reboot

**Version:** 2025-07-05

This document supersedes prior logging notes and consolidates all tasks needed to deliver a unified, production-grade logging solution across the Lifeboard desktop application, plugins, web-UI, and backend services.

---
## 1. Goals
1. Single CoreLogger module used everywhere (desktop core, plugins, tests).
2. Universal JSON schema per log line to enable search & analytics.
3. Central on-disk location: `project_dir/logs/` with daily rotated files.
4. Configurable log-level filtering at runtime without code changes.
5. Safe, concurrent, and fault-tolerant writes on all supported OSs.
6. > 90 % test coverage and zero code-smell defects for CoreLogger.
7. Continuous integration gate that fails on missing DEBUG logs.
8. Forward-compatibility with Promtail/Loki aggregation.

---
## 2. Universal JSON Log Schema (final)
| Field | Type | Required | Notes |
|-------|------|----------|-------|
| `ts` | ISO-8601 string | ✅ | UTC timestamp |
| `level` | string (`DEBUG`\|`INFO`\|`WARN`\|`ERROR`\|`FATAL`) | ✅ | Upper-case |
| `component` | string | ✅ | `desktop`, `plugin:<name>`, `backend:<service>` |
| `msg` | string | ✅ | Human-readable message |
| `meta` | object | ⬜ | Free-form key/value pairs |
| `corrId` | string | ⬜ | Cross-service correlation/request ID |

Schema validation will be unit-tested; CoreLogger rejects or auto-maps unknown fields.

---
## 3. Implementation Roadmap

### 3.1 CoreLogger Module (Desktop → Shared)
1. **File:** `desktop/core/logger/CoreLogger.js`
2. **Singleton** exporting:
   - `logger` (global instance; default component `desktop`)
   - `factory(component)` → child logger with bound `component` field
3. **Log level control**
   - Env var `LIFEBOARD_LOG_LEVEL` (default `DEBUG`)
   - API: `setLevel(level)` on singleton
4. **Daily rotation & retention**
   - Name: `project_dir/logs/YYYY-MM-DD-<component>.log`
   - Keep 14 days then compress & delete after 30 days (cron job in CoreLogger)
5. **Safe writes**
   - Use [pino](https://github.com/pinojs/pino) with `pino.destination({ sync:false })` for async, thread-safe appends.
6. **Unhandled exception capture**
   - `process.on('uncaughtException'|'unhandledRejection')`
   - Electron renderer: `window.onerror` & `window.onunhandledrejection`
7. **Resilience**
   - If `project_dir/logs/` missing or unwritable → fallback to `stderr` with warning.
8. **Docstrings & DEBUG statements** for every public method per coding rules.
9. Add extensive unit tests (> 90 % lines) and benchmark.

### 3.2 PluginAPI Exposure
1. Modify `desktop/core/pluginAPI/index.js`:
   - `getLogger(pluginName)` → `CoreLogger.factory('plugin:'+pluginName)`
2. Update TypeScript typings & JSDoc.
3. Insert DEBUG logs around provisioning.

### 3.3 Refactor Existing Plugins
Limitless plugin as pilot:
1. Remove `desktop/plugins/limitless/src/logger.js` (re-export to preserve API until vNext).
2. Replace imports:
   ```js
   const logger = PluginAPI.getLogger('limitless');
   ```
3. Verify behaviour parity & add DEBUG statements.

### 3.4 Backend Services Alignment
1. **PostgreSQL, PostgREST, GoTrue, Realtime, Storage:**
   - Configure to emit JSON matching schema to `project_dir/logs/backend/<service>/YYYY-MM-DD-backend-<service>.log`.
2. Extend docker-compose:
   - Mount `./logs/backend` volumes.
   - No extra port exposure (rule compliance).
3. Provide fallback shell script to convert CSV/text to JSON during migration.

### 3.5 Web-UI Layer
Update Nginx & browser code to include `corrId` propagation and adopt schema.

### 3.6 Promtail/Loki Aggregation (Decision Tracker)
Create issue **OBS-42** with two options:
1. Sidecar Promtail per container.
2. Central agent scanning `project_dir/logs/`.
Include cost/perf matrix; await stakeholder vote.

### 3.7 Security & Compliance
1. Redact secrets/PII helper in CoreLogger (`sanitize(meta)` hook).
2. Set directory permissions `0700`; CI test asserts.
3. Docs: add guidelines for sensitive data.

### 3.8 Performance & Stress Tests
1. Benchmark 10 k writes/s; assert < 5 ms 99-percentile latency.
2. Include stress script in `tests/perf_logging_core.sh` and wire to optional CI job.

---
## 4. CI/CD Updates
1. **Shell test:** `tests/test_observability_logging_plugin.sh` (existing spec)
2. **CoreLogger unit tests** via Jest in `desktop/core/logger/__tests__/`.
3. **Coverage gate:** `--coverage --maxWorkers=2` must report ≥ 90 %.
4. **Lint & Code-smell**: run after tests; fail on any new smells.
5. Upload log artefacts on GitHub Actions failure for triage.

---
## 5. Documentation Tasks
1. `supporting_documents/Plugin_Developer_Guide.md` → add detailed CoreLogger usage section.
2. `supporting_documents/Phase4_Observability_and_Logging_Summary.md` → replace plugin-specific note with CoreLogger overview.
3. This reboot plan saved as **Logging Infra Reboot.md** (this file).

---
## 6. Timeline (T-shirt sizing)
| Week | Focus | Deliverables |
|------|-------|--------------|
| 1 | CoreLogger PoC | Module, basic tests, schema locked |
| 2 | Desktop & Plugin integration | Limitless migrated, template updated |
| 3 | Backend alignment | Docker compose changes, service configs |
| 4 | CI gates, stress tests | Coverage > 90 %, perf benchmarks green |
| 5 | Documentation & cleanup | Guides updated, deprecated code removed |
| 6 | Promtail/Loki decision | OBS-42 resolved, rollout plan locked |

---
## 7. Risk Register
| Risk | Mitigation |
|------|-----------|
| File-system contention on Windows | Async pino destination + tests |
| Log-spam performance hit | Runtime level filter + benchmarks |
| Migration breaks legacy parsing tools | Provide compatibility shims & comms |
| Oversized logs fill disk | Retention policy + CI check |

---
## 8. Acceptance Criteria
1. Running desktop app with sample plugin generates ≥ 10 `DEBUG` logs within 5 s.
2. All layers output JSON lines matching schema.
3. CI passes on code-smell, coverage, and log tests.
4. Legacy log files removed or archived after cut-over.
5. Stakeholders sign-off on OBS-42 decision.

---
### Maintainers
• **Logging Lead:** <tbd>
• **Dev-Ops:** <tbd>
• **QA:** <tbd>
