# Lifeboard Local Development Guide

A comprehensive reference capturing project setup, strict isolation, zero port conflicts, and deep logging principles for Lifeboard’s local environment.

---

## Development Principles

- Zero Port Conflicts
- Extreme Isolation & Privacy-First
- Deep, Structured Diagnostic Logging

---

## 1. Project Bootstrapping

Create your project directory and populate it with Supabase’s Docker setup:

```bash
mkdir ~/lifeboard-supabase
cp -r . ~/lifeboard-supabase
cd ~/lifeboard-supabase
```

---

## 2. Environment Configuration

### 2.1 Use an Explicit `.env.local`

Avoid OS-level exports by keeping all secrets in a project-scoped file named `.env.local`. Docker Compose will read from it directly:

```bash
cp .env.example .env.local
```

### 2.2 Curated `.env.local` Content

```env
# Authentication & Security
JWT_SECRET=fcadb837cf84de4a97765b6c72d1e3b97bd00ed474a6096e5a193a130789f031
SERVICE_ROLE_KEY=service_role_edc2c38827a7aef6b65e9371c9d2c6de
ANON_KEY=anon_6fc94b8e217e461e846bc3a9ff9823a5

# Database Credentials
POSTGRES_PASSWORD=db_ae8f36fc9b284572aa5b4c03e1ec2bce

# Port Isolation (high-range bindings)
SUPABASE_PORT=8810      # Supabase API & Realtime
POSTGRES_PORT=5543      # Postgres
STUDIO_PORT=8811        # Admin UI
API_PORT=9876           # Edge Functions or REST

# Optional Volume Paths
# DATA_DIR=./volumes/data
# STORAGE_DIR=./volumes/storage
```

---

## 3. Docker Compose Integration

In `docker-compose.yml`, reference only `.env.local`:

```yaml
version: "3.8"

services:
  db:
    image: supabase/postgres
    env_file:
      - .env.local
    networks:
      - lifeboard_net

  auth:
    image: supabase/gotrue
    env_file:
      - .env.local
    networks:
      - lifeboard_net

  realtime:
    image: supabase/realtime
    env_file:
      - .env.local
    networks:
      - lifeboard_net

  studio:
    image: supabase/studio
    env_file:
      - .env.local
    ports:
      - "${STUDIO_PORT}:3000"
    networks:
      - lifeboard_net

networks:
  lifeboard_net:
    name: lifeboard_net
```

---

## 4. Isolation Strategies

| Layer               | Strategy                                                                                  |
|---------------------|-------------------------------------------------------------------------------------------|
| Docker Network      | Dedicated network (`lifeboard_net`), no default bridge exposure                           |
| Volumes             | Ephemeral or project-scoped (`./volumes/*`), remove with `docker compose down --volumes`  |
| Secrets Management  | `.env.local` only; consider Docker secrets or Mozilla SOPS for encryption                 |
| Container Hardening | Flags: `--read-only`, `--cap-drop=ALL`, `--no-new-privileges`                             |
| AI Model Sandboxing | Run LLM containers in isolated subnets with no external access                           |
| Log Storage         | In-container volumes; avoid host syslogs                                                  |

---

## 5. Zero Port Conflicts

- No Port Publishing
  Expose only what you need. Omit `ports:` for purely internal services.

- High-Range Bindings
  Map services to high-number ports unlikely to clash:
  ```yaml
  ports:
    - "9810:8000"  # Supabase API & Realtime
    - "5543:5432"  # Postgres
  ```

- Compose Profiles
  Load only necessary services:
  ```bash
  docker compose --profile studio up
  ```

- Project Namespacing
  Isolate project containers and networks:
  ```bash
  docker compose -p lifeboard up
  ```

---

## 6. Deep Logging Practices

- Structured JSON Logs
  Configure each service to emit JSON with timestamps, severity, and context.

- Verbose & Debug Modes
  Enable debug flags during development:
  ```yaml
  environment:
    LOG_LEVEL: debug
  ```

- Key Instrumentation Points
  - User actions (post creation, edits)
  - AI decisions (embedding generation, tag assignment)
  - Workflow triggers (n8n nodes, function invocations)

- Local-Only Log Storage
  Mount logs to `./volumes/logs` and rotate or purge via scripts to prevent footprint accumulation.

---

## 7. Next Steps

1. Wire up Docker Compose using the updated `env_file` reference.
2. Bring the stack online with `docker compose up -d`.
3. Verify isolation: confirm no host ENV vars and correct port mappings.
4. Enable verbose logging in each service’s configuration.
5. Iterate data modeling and security rules, with logging hooks at each critical path.
