# Container Logging Setup Summary

**Version:** 2025-07-07
**Status:** Implemented

## Overview
Successfully configured all Docker containers to write logs directly to the `lifeboard-supabase/logs` directory, centralizing all logging in the project root as per user requirements.

## Changes Made

### 1. Docker Compose Configuration Updates
Modified `docker-compose.yml` to include logging volumes and configuration for all services:

#### Services Updated:
- **Database (PostgreSQL)**: Logs to `./logs/postgres/`
- **Auth (GoTrue)**: Logs to `./logs/auth/`
- **Realtime**: Logs to `./logs/realtime/`
- **REST API (PostgREST)**: Logs to `./logs/rest/`
- **Storage**: Logs to `./logs/storage/`
- **Studio**: Logs to `./logs/studio/`

#### Configuration Applied:
```yaml
volumes:
  - ./logs/[service]:/app/logs  # or /var/log/postgresql for postgres

environment:
  - LOG_LEVEL=debug|info
  - LOG_FILE=/app/logs/[service]-%Y%m%d_%H%M%S.log

logging:
  driver: "json-file"
  options:
    max-size: "50m"
    max-file: "5"
    labels: "service=[service],component=[component],environment=development"
```

### 2. Directory Structure Created
```
./logs/
├── auth/           # GoTrue authentication service logs
├── postgres/       # PostgreSQL database logs
├── realtime/       # Realtime service logs
├── rest/           # PostgREST API logs
├── storage/        # Storage service logs
├── studio/         # Supabase Studio UI logs
└── [existing application logs]
```

### 3. Git Configuration Updated
Updated `.gitignore` to:
- Preserve directory structure
- Ignore actual log files (*.log)
- Include `.gitkeep` files to track directories

```gitignore
# Logs - ignore log files but keep directory structure
logs/*.log
logs/*/*.log
logs/*/*/*.log
```

## Benefits

### Centralized Logging
- All container logs now write to `./logs/` subdirectories
- Maintains separation by service for easy debugging
- Aligns with project rule requiring all logs in `/logs`

### Log Rotation & Management
- 50MB max file size with 5-file rotation
- Automatic timestamping in filenames
- JSON structured logging for easier parsing

### Development Experience
- Local log access without `docker logs` commands
- Persistent logs across container restarts
- Easy integration with log analysis tools

## Integration with Existing Logging Infrastructure

### CoreLogger Compatibility
The container logging setup is designed to complement the planned CoreLogger module:
- Container logs follow similar naming patterns
- JSON structured output compatible with universal schema
- Central location aligns with CoreLogger's `project_dir/logs/` requirement

### Observability Stack
Existing `docker-compose.logging.yml` provides additional observability tools:
- Promtail for log aggregation
- Loki for log storage
- Grafana for visualization

## Usage

### Starting Services with Logging
```bash
# Standard startup (logging included)
docker compose -p lifeboard up

# With observability stack
docker compose -p lifeboard --profile observability up
```

### Accessing Logs
```bash
# View real-time logs
tail -f ./logs/postgres/postgresql-*.log
tail -f ./logs/auth/gotrue-*.log

# Search across all logs
grep "ERROR" ./logs/*/*.log
```

### Log Directory Permissions
All log directories created with proper permissions for container access while maintaining security.

## Compliance with User Rules

✅ **All logs in /logs directory**: Container logs now write to `./logs/`
✅ **Verbose debug logging**: LOG_LEVEL=debug enabled for all services
✅ **Datetime stamped naming**: Filenames include timestamp patterns
✅ **No OS-level exports**: Uses volume mounts, not environment exports
✅ **Dedicated network isolation**: All services use `lifeboard_net`
✅ **No default bridge exposure**: All containers in isolated network
✅ **High-number port mapping**: Follows existing port mapping rules

## Next Steps

### Phase 1: Validation
- [ ] Start containers and verify log file creation
- [ ] Confirm log rotation behavior
- [ ] Test log accessibility and readability

### Phase 2: Integration Testing
- [ ] Verify logs appear in expected directories
- [ ] Test log parsing with existing analysis tools
- [ ] Validate performance impact of logging configuration

### Phase 3: CoreLogger Integration
- [ ] Align container log schema with CoreLogger JSON format
- [ ] Implement correlation IDs for cross-service tracking
- [ ] Add log aggregation integration points

## Troubleshooting

### Common Issues
- **Permission denied**: Ensure Docker has access to `./logs/` directory
- **Missing logs**: Check container startup and volume mounts
- **Disk space**: Monitor log file sizes and rotation

### Validation Commands
```bash
# Check docker compose configuration
docker compose config --quiet

# Verify volume mounts
docker compose ps --format table

# Monitor log creation
watch "ls -la ./logs/*/"
```

## Maintainers
- **DevOps Lead**: Container configuration and deployment
- **Logging Lead**: Schema alignment and CoreLogger integration
- **QA**: Testing and validation procedures
