# Lifeboard Development Phasing & Quality Gates

This document outlines the staged plan for completing Lifeboard’s infrastructure, including built-in safeguards to minimize bugs: targeted tests and code-smell reviews at every step.

---

## ✅ PHASE 1 – Database Hardening

Purpose: make the database reliable, reproducible, and recoverable.

Quality gates
1. **Tests** ✅
   • Migration-smoke test: spin up `db`, apply migrations, verify all tables exist.
   • Seed-data test: run seed script, confirm row counts > 0.
2. **Code-smell review** ✅
   • Run SQL linter (e.g., `sqlfluff`) on migration files.
   • Inspect seed scripts for hard-coded secrets or non-deterministic data.

---

## ✅ PHASE 2 – Container & Network Isolation

Purpose: prevent port clashes and restrict unintended access.

Quality gates
1. **Tests** ✅
   • Compose-up test: launch stack with `ports:` removed, confirm intra-network connectivity and host isolation.
   • Security-flags test: inspect running containers for `CapEff=0000000000`.
2. **Code-smell review** ✅
   • Run `hadolint` on all Dockerfiles.
   • Review `docker-compose.yml` for accidental host binds or duplicate environment variables.

---

## ✅ PHASE 3 – Secrets & Configuration

Purpose: centralize secrets in `.env.local` and keep them out of the OS shell.

Quality gates
1. **Tests** ✅
   • Secret-leak scan (e.g., `truffleHog`, `git-secrets`) ensures no real secrets in repo.
   • Boot test: copy `.env.example` → `.env.local`, start stack, confirm all services read from file.
2. **Code-smell review** ✅
   • Validate variable names and scopes; flag unused or unclear keys.
   • Static analysis of shell scripts touching env files (`shellcheck`).

---

## ✅ PHASE 4 – Observability & Logging

Purpose: provide structured, actionable insight into runtime behavior.

Quality gates
1. **Tests** ✅
   • Log-format test: each service emits a sample log line; JSON schema validator passes.
   • Log-rotation test: simulate oversized log, verify rotation/compression triggers.
2. **Code-smell review** ✅
   • Scrub log statements for PII leakage.
   • Check logger configs for overly broad capture or default DEBUG in production.

---

## ✅ PHASE 5 – Health & Tests

Purpose: ensure the running stack stays healthy and basic workflows succeed.

Quality gates
1. **Tests** ✅
   • Healthcheck test: after `docker compose up`, each container reports `healthy` within defined timeout.
   • CRUD round-trip: create ⟴ read ⟴ delete a record via API, assert success.
2. **Code-smell review** ✅
   • Static analysis on API code touched by tests (e.g., `eslint`, `mypy`).
   • Ensure healthcheck commands are simple—no complex shell logic.

---

## ✅ PHASE 6 – CI/CD & Profiles (formal gateway)

Purpose: automate builds, tests, and deployments with strict quality enforcement.

Quality gates
1. **Tests (automated)** ✅
   • Unit tests, integration suite, and full end-to-end stack tests run on every PR.
   • Enforce mutation-testing or coverage ≥ preset threshold.
2. **Code-smell review (automated)** ✅
   • Linter & formatter configured to fail on warnings.
   • SonarQube (or equivalent) gate: complexity, duplication, and security hotspots.

---

## PHASE 7 – Documentation & Next Steps

Purpose: keep the development guide current and make extension straightforward.

Quality gates
1. **Tests**
   • Documentation link checker validates all Markdown links.
   • “New plugin” dry-run script follows docs to add dummy plugin; tests must still pass.
2. **Code-smell review**
   • Markdown linter enforces style and heading conventions.
   • Cross-check docs for outdated port numbers or env variable names.

---
