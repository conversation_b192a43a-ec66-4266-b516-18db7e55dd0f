# Code Smell Strategy and Planning

## Overview

This document defines the strategic approach to code smell detection and remediation for the Lifeboard-Supabase project. It operationalizes the project rule that "After every change, run a code smell review and recommend changes" through comprehensive automation, measurable quality gates, and actionable reporting.

## 1. Purpose and Scope

### Purpose
Continuously detect, report, and remediate code smells across all repositories (frontend, backend, edge-functions, infrastructure, documentation) to maintain high code quality and reduce technical debt.

### Scope
- **Languages**: TypeScript/JavaScript, Python, Go, Bash, SQL, Markdown, YAML, Docker, Terraform
- **Locations**: `/src`, `/desktop`, `/edge-functions`, `/tests`, `/scripts`, `supporting_documents`, CI workflows, Docker files
- **Stages**: Local pre-commit hooks, pull-request checks, nightly scheduled scans, release pipelines

## 2. Key Definitions

| Term | Meaning |
|------|---------|
| *Code smell* | Surface-level symptom indicating deeper problems—complexity, duplication, dead code, convention deviations, security risks, performance antipatterns |
| *Quality gate* | Configurable thresholds (e.g., <3% duplicated lines, Cyclomatic Complexity ≤ 15) that must pass before merging or releasing |
| *Baseline* | Initial set of accepted smells for legacy code, preventing new technical debt while existing issues are addressed |

## 3. Strategic Toolchain

### Selected Tools and Rationale

| Language/Artifact | Primary Tool | Secondary Tools | Rationale |
|-------------------|-------------|----------------|-----------|
| **JavaScript/TypeScript** | ESLint (Airbnb + TypeScript) | SonarJS, Prettier | Auto-fix support, VS Code integration, comprehensive rules |
| **Python** | Pylint | Flake8, Bandit, Black, isort | Security scanning, formatting, complexity analysis |
| **Go** | golangci-lint | staticcheck, revive, govet | Fast aggregation, single binary, comprehensive coverage |
| **Shell** | ShellCheck | - | Portability, security, best practices |
| **SQL** | SQLFluff | - | PostgreSQL dialect support, formatting |
| **Docker** | Hadolint | dockle | Best practices, CVE scanning |
| **YAML** | yamllint | kube-score | Syntax, Kubernetes security |
| **Markdown** | markdownlint-cli2 | - | Documentation quality, broken links |
| **Secrets** | gitleaks | - | Sensitive data prevention |
| **Aggregation** | SonarQube Community | - | Centralized reporting, quality gates |

> **Note**: All tools output JSON or SARIF format for automated processing and archival to `/logs/YYYY-MM-DD_code_smell.log`.

## 4. Quality Gates and Thresholds

### Core Quality Gates

| Metric | Threshold | Type | Rationale |
|--------|-----------|------|-----------|
| **Duplicated Lines** | < 3% | Blocker | Maintainability focus |
| **Cyclomatic Complexity** | ≤ 15 per function | Blocker | Readability first |
| **Blocker Issues** | = 0 | Blocker | Must fix before merge |
| **Critical Issues** | = 0 | Blocker | Must fix before merge |
| **Security Hotspots** | = 0 | Blocker | Shift-left security |
| **Test Coverage** | ≥ 90% for new code | Warning | Reliability assurance |
| **Maintainability Rating** | = 1.0 | Warning | Code health monitoring |

### Gate Configuration
- **SonarQube**: Quality Gate "lifeboard-default" with centralized configuration
- **GitHub**: Branch protection rules mirror SonarQube thresholds
- **Local**: Pre-commit hooks enforce subset of critical gates

## 5. Automation Strategy

### Multi-Stage Approach

#### Stage 1: Pre-commit (Developer Workstation)
- **Trigger**: `pre-commit run --all-files`
- **Tools**: ESLint (--fix), golangci-lint, shellcheck, markdownlint, yamllint, gitleaks
- **Behavior**: Blocks commit on failure
- **Goal**: Immediate feedback, auto-fix where possible

#### Stage 2: Pull Request CI (GitHub Actions)
- **Parallel Jobs**: Language-specific linters run concurrently
- **Quality Gates**: SonarQube integration with API-based gate checking
- **Feedback**: Line-specific PR annotations
- **Requirement**: Quality gate status must be "passed" to merge

#### Stage 3: Nightly Scans
- **Schedule**: Automated re-analysis of `main` branch
- **Detection**: New issues, dependency CVEs, security vulnerabilities
- **Tools**: dockle, npm audit, govulncheck
- **Reporting**: Trend analysis and degradation alerts

#### Stage 4: Release Pipeline
- **Validation**: All quality gates must pass
- **Artifacts**: HTML smell reports attached to GitHub Releases
- **Failure**: Automatic release blocking on gate failures

## 6. Baseline Management Strategy

### Legacy Code Approach
1. **Initial Baseline**: Run comprehensive scan to create `baseline.sarif`
2. **Storage**: Baseline file stored in `.github/linters/`
3. **Filtering**: PR checks ignore pre-existing baseline issues
4. **Backlog**: High-priority smells (>SEV-Medium) tracked as tickets
5. **Burn-down**: Technical Debt Sprint every 4 weeks

### Baseline Lifecycle
- **Creation**: `make smell-baseline` captures current state
- **Updates**: Quarterly review and refresh
- **Removal**: Issues resolved permanently removed from baseline

## 7. Governance and Ownership

### Responsibility Matrix

| Area | Owner | Review Cadence | Responsibilities |
|------|-------|----------------|------------------|
| **Lint Configurations** | Tech Lead (Frontend/Backend) | Monthly | Maintain `.eslintrc`, `.golangci.yml`, etc. |
| **SonarQube Infrastructure** | DevOps | Quarterly | Server maintenance, version updates |
| **Quality Gates** | Architecture Guild | Quarterly | Threshold review, gate adjustments |
| **Smell Backlog** | Engineering Manager | Sprint Planning | Prioritization, resource allocation |
| **Tool Updates** | Development Team | Monthly | Dependency updates, rule refreshes |

### Decision-Making Process
- **Configuration Changes**: Require team consensus via RFC
- **Threshold Adjustments**: Architecture Guild approval required
- **Tool Additions**: Technical evaluation and pilot testing
- **Baseline Updates**: Engineering Manager approval for major changes

## 8. Performance and Scalability

### Optimization Strategies
- **Parallel Execution**: Up to 8 concurrent language scanners
- **Caching**: Pre-commit and CI/CD result caching
- **Incremental Analysis**: Only scan changed files where possible
- **Resource Limits**: 60-second timeout per scanner to prevent hanging

### Monitoring Metrics
- **Scan Duration**: Target <5 minutes for full repository
- **Cache Hit Rate**: >70% for unchanged files
- **Resource Usage**: Memory and CPU monitoring
- **Error Rates**: <5% tool execution failures

## 9. Integration with Development Workflow

### Developer Experience
- **IDE Extensions**: ESLint, SonarLint, golangci-lint-langserver, markdownlint
- **Make Targets**: `make lint`, `make fix`, `make quality` convenience commands
- **Feedback**: Line-specific PR annotations and VS Code inline hints
- **Auto-fixing**: Automatic resolution of formatting and style issues

### Team Adoption
- **Training**: Documentation and best practices sessions
- **Support**: Dedicated channels for infrastructure questions
- **Metrics**: Quality trend dashboards and reports
- **Recognition**: Code quality achievements and improvements

## 10. Continuous Improvement

### Future Enhancements

#### Phase 1: Enhanced Automation
- **LLM Integration**: AI-powered refactoring suggestions with manual approval
- **Semantic Analysis**: Advanced pattern detection with Semgrep
- **Auto-Remediation**: Automatic GitHub issue creation for high-severity smells

#### Phase 2: Advanced Analytics
- **Dependency Tracking**: SBOM generation and supply-chain risk analysis
- **Trend Analysis**: Historical quality metrics and predictions
- **Team Metrics**: Individual and team-based quality scorecards

#### Phase 3: Ecosystem Integration
- **Security Scanning**: Integration with vulnerability databases
- **Performance Analysis**: Code smell correlation with performance metrics
- **Documentation**: Automatic documentation quality assessment

### Success Metrics
- **Technical Debt Reduction**: 50% reduction in code smells within 6 months
- **Developer Productivity**: Reduced code review cycle time
- **Release Quality**: Zero post-release defects related to code quality
- **Team Satisfaction**: >90% developer satisfaction with tooling

## 11. Implementation Roadmap

### Phase 1: Foundation (Completed)
- ✅ Core infrastructure implementation
- ✅ Multi-language linter configuration
- ✅ CI/CD pipeline integration
- ✅ Quality gate definition and enforcement

### Phase 2: Optimization (In Progress)
- 🚧 Baseline management for legacy code
- 🚧 Performance optimization and caching
- 🚧 Advanced reporting and analytics
- 🚧 Team training and adoption

### Phase 3: Enhancement (Planned)
- 📋 LLM-powered code review assistance
- 📋 Advanced security scanning integration
- 📋 Automated remediation workflows
- 📋 Cross-repository quality correlation

## 12. Risk Management

### Technical Risks
- **Tool Reliability**: Multiple tool redundancy and fallback mechanisms
- **Performance Impact**: Caching and parallel execution optimization
- **False Positives**: Baseline management and configurable thresholds
- **Integration Failures**: Comprehensive error handling and logging

### Operational Risks
- **Team Adoption**: Training programs and gradual rollout
- **Maintenance Overhead**: Automated updates and monitoring
- **Configuration Drift**: Version control for all configurations
- **Resource Constraints**: Cloud-based and scalable infrastructure

---

**Document Version**: 2.0.0
**Created**: 2025-07-03
**Last Updated**: 2025-07-04
**Next Review**: 2025-10-01

This strategic approach ensures comprehensive code quality management while maintaining developer productivity and project delivery velocity.
