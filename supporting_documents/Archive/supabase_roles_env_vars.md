# Supabase Role Password Environment Variables

This document describes the environment variables required for secure password management in the Supabase role bootstrap script (`./db/bootstrap/00_roles.sql`).

## Required Environment Variables

The following environment variables should be set in your `.env.local` file or Docker environment to provide secure passwords for the Supabase roles:

```bash
# Database role passwords
DB_PASSWORD_POSTGRES=your_secure_postgres_password
DB_PASSWORD_AUTHENTICATOR=your_secure_authenticator_password
DB_PASSWORD_ANON=your_secure_anon_password
DB_PASSWORD_AUTHENTICATED=your_secure_authenticated_password
DB_PASSWORD_SERVICE_ROLE=your_secure_service_role_password
```

## Password Security Guidelines

1. **Use Strong Passwords**: Generate cryptographically secure passwords with sufficient length and complexity
2. **Unique Passwords**: Each role should have a unique password
3. **Environment Variables**: Never hardcode passwords in SQL files or configuration files
4. **Default Fallback**: The script includes default passwords for development, but these should never be used in production

## Role Password Usage

| Role | Environment Variable | Usage |
|------|---------------------|--------|
| `postgres` | `DB_PASSWORD_POSTGRES` | Database superuser for administration |
| `authenticator` | `DB_PASSWORD_AUTHENTICATOR` | GoTrue connection and role switching |
| `anon` | `DB_PASSWORD_ANON` | Anonymous/unauthenticated users |
| `authenticated` | `DB_PASSWORD_AUTHENTICATED` | Authenticated users |
| `service_role` | `DB_PASSWORD_SERVICE_ROLE` | Backend services with elevated privileges |

## Implementation Notes

- The SQL script uses PostgreSQL's `current_setting()` function to read custom configuration variables
- Environment variables are mapped to PostgreSQL custom settings in the format `custom.db_password_*`
- If an environment variable is not set, the script falls back to a default password (for development only)
- The script uses dynamic SQL execution with `EXECUTE format()` to safely interpolate passwords

## Security Considerations

1. **Role Switching**: The `authenticator` role can switch to `anon`, `authenticated`, and `service_role` but cannot inherit their privileges directly
2. **NOINHERIT Flag**: The `authenticator` role has `NOINHERIT` to prevent automatic privilege escalation
3. **BYPASSRLS**: Only the `service_role` has `BYPASSRLS` (Row Level Security bypass) for backend operations
4. **LOGIN Restrictions**: Only `postgres` and `authenticator` roles have `LOGIN` capability

## Docker Integration

The role bootstrap script is automatically executed during PostgreSQL initialization through the `/docker-entrypoint-initdb.d` mount point configured in `docker-compose.yml`:

```yaml
volumes:
  - ./docker-entrypoint-initdb.d:/docker-entrypoint-initdb.d
```

The script `00_roles.sql` is symlinked into this directory to ensure it runs before other initialization scripts.
