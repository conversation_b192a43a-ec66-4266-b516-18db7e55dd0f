# Lifeboard Startup Diagnostics Report
**Date**: July 7, 2025
**Scope**: Auth Service and Postgres startup failure analysis

## Executive Summary
The auth service (GoTrue) is failing to start due to missing database schema elements, specifically the `auth.factor_type` enum type. The Postgres database is healthy but lacks proper Supabase auth schema initialization.

## Service Status Overview

### Container Status
| Service | Status | Issue |
|---------|--------|-------|
| lifeboard-db-1 | Up 6 minutes (healthy) | ✅ Running correctly |
| lifeboard-auth-1 | Restarting (1) 5 seconds ago | ❌ Migration failures |
| lifeboard-realtime-1 | Created | ⏸️ Waiting for auth dependency |
| lifeboard-rest-1 | Created | ⏸️ Waiting for database dependency |
| lifeboard-storage-1 | Created | ⏸️ Waiting for database dependency |

## Critical Error Analysis

### Auth Service Errors
**Primary Issue**: Migration failure for MFA phone configuration
```
ERROR: type "auth.factor_type" does not exist (SQLSTATE 42704)
```

**Error Pattern**: The auth service repeatedly attempts to run migration `20240729123726_add_mfa_phone_config.up.sql` which tries to:
1. Add 'phone' value to `auth.factor_type` enum
2. Add phone column to `auth.mfa_factors` table
3. Create unique index for verified phone factors

**Root Cause**: The `auth.factor_type` enum type was never created during initial database setup.

### Database Schema State
**Current auth schema tables**:
- ✅ audit_log_entries, flow_state, identities, instances
- ✅ mfa_amr_claims, mfa_challenges, mfa_factors, one_time_tokens
- ✅ refresh_tokens, saml_providers, saml_relay_states, schema_migrations
- ✅ sessions, sso_domains, sso_providers, users

**Missing elements**:
- ❌ `auth.factor_type` enum type
- ❌ Potentially other enum types required by GoTrue v2.158.1

## Configuration Analysis

### Database Configuration (.env.local)
```
DATABASE_URL: ***********************************************************************/postgres
GOTRUE_DB_DATABASE_URL: ***********************************************************************/postgres
PGRST_DB_URI: **********************************************************************/postgres
```

### Network Configuration
**Network Name**: `lifeboard_net` (isolated, no default bridge exposure) ✅

### Port Mapping
```
Database: localhost:55821 → container:5432 (from POSTGRES_PORT=5543 mapping error)
API: localhost:8810 → container:3000 (SUPABASE_PORT)
Studio: localhost:8811 → container:3000 (STUDIO_PORT)
```

**Note**: There's a port mapping discrepancy - POSTGRES_PORT=5543 in .env.local but container shows 55821.

## Log Directory Analysis
**Location**: `/logs` (following project rules) ✅
**Structure**:
- `/logs/auth/` - Empty (no logs generated due to startup failure)
- `/logs/postgres/` - Empty (configured but container logs to Docker)

**Available logs**:
- Container logs via `docker logs lifeboard-auth-1` - Shows migration errors
- Container logs via `docker logs lifeboard-db-1` - Shows connection attempts and same errors

## Migration System Status
**Init script**: `/docker-entrypoint-initdb.d/00_run_migrations.sh` exists but migrations directory is empty
**Project migrations**: Available in `/migrations/001_initial_schema.sql` but not automatically applied

## Recommendations for Resolution

### Immediate Actions Required
1. **Create missing enum type**: Add `auth.factor_type` enum creation to database init
2. **Update migration order**: Ensure Supabase auth schema loads before GoTrue migrations
3. **Fix port mapping**: Resolve POSTGRES_PORT configuration discrepancy

### Schema Initialization Fix
The auth service expects a complete Supabase auth schema but only has partial tables. Need to:
- Add proper Supabase auth schema initialization
- Include enum type definitions for factor_type, aal_level, code_challenge_method
- Ensure migration ordering prevents dependency conflicts

### Configuration Validation
- Database connectivity confirmed ✅
- JWT secrets and format validated ✅
- Network isolation working ✅
- Port mappings need review ⚠️

## Next Steps Priority
1. **High**: Fix auth.factor_type enum creation
2. **High**: Resolve auth service startup dependency
3. **Medium**: Standardize port configuration
4. **Low**: Implement proper log aggregation from containers

---
*Report generated for targeted remediation planning*
