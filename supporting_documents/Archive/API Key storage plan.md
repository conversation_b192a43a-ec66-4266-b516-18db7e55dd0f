
1. Project kick-off and context review
Review all files in `./supporting_documents`, existing Supabase setup, `.env.local`, and current plugin code to baseline requirements, secrets policy, naming conventions, network isolation, and logging rules.
2. Append schema 01_core_tables.sql

  - `api_keys` table with columns (`plugin_id` text NOT NULL, `key_name` text, `encrypted_key` text, `created_at` timestamptz DEFAULT now(), `updated_at` timestamptz, `user_id` uuid REFERENCES auth.users).
  - Index on `(user_id, plugin_id)`.
  - `audit_api_key_access` table (`id` bigserial PK, `api_key_id` bigint, `action` text, `performed_at` timestamptz DEFAULT now(), `performed_by` uuid).
  - Enable pgcrypto extension.
  - Insert Vault secret placeholder `API_ENC_KEY`.
  - RLS policies: row-level isolation by `user_id`; + plugin-scoped policy for multi-plugin users.

3. Implement encryption helper functions
Create SQL helper functions:
```sql
CREATE FUNCTION encrypt_api_key(raw_key text) RETURNS text
LANGUAGE plpgsql AS $$
DECLARE secret text := vault.get_secret('API_ENC_KEY');
BEGIN
  RETURN pgp_sym_encrypt(raw_key, secret);
END;
$$;

CREATE FUNCTION decrypt_api_key(enc_key text) RETURNS text
...
```

4. Stored procedures for CRUD with auditing
Write secured functions `sp_create_api_key`, `sp_get_api_keys`, `sp_update_api_key`, `sp_delete_api_key` that:
  - Validate caller via `auth.uid()`.
  - Encrypt/decrypt internally.
  - Insert rows in `audit_api_key_access`.
Expose them in PostgREST with appropriate `SECURITY DEFINER` and `jwt.claims.role` checks.

7. Update client-side SettingsManager.js
Refactor to call `/rpc/sp_*` endpoints with Supabase JS client; remove direct JSON storage; add verbose logging per rules to `/logs/<datetime>_frontend.log`.
8. Modify plugin files (api.js, settings-ui.js)
Integrate secure fetches, implement local state caching Add debugging instrumentation for API calls and responses.
10. End-to-end testing
Manual validation of CRUD, RLS enforcement, encryption (ciphertext checked), and audit logs.
11. Security & compliance review
