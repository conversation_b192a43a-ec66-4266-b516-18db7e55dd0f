# Lifeboard Developer Guide

## Overview

This guide provides comprehensive instructions for setting up a local development environment, understanding the codebase architecture, and contributing to the Lifeboard project.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Quick Start](#quick-start)
3. [Development Environment Setup](#development-environment-setup)
4. [Architecture Overview](#architecture-overview)
5. [Development Workflow](#development-workflow)
6. [Testing](#testing)
7. [Code Quality](#code-quality)
8. [Database Development](#database-development)
9. [API Development](#api-development)
10. [Frontend Development](#frontend-development)
11. [Contributing](#contributing)
12. [Troubleshooting](#troubleshooting)

## Prerequisites

### System Requirements

- **Operating System**: macOS 10.15+, Ubuntu 20.04+, Windows 10 with WSL2
- **RAM**: Minimum 8GB, recommended 16GB
- **Storage**: 20GB free space
- **Docker**: Docker Desktop 4.0+ or Docker Engine 20.10+

### Required Software

```bash
# Core tools
git 2.30+
docker 20.10+
docker-compose 2.0+
curl
wget

# Development tools (recommended)
node 18+               # For frontend development
python 3.9+           # For scripts and tools
go 1.19+              # For backend development

# Security tools
trufflehog            # Secret detection (brew install trufflesecurity/trufflehog/trufflehog)
pre-commit            # Git hooks (pip install pre-commit)
```

### Optional Tools

```bash
# Code editors
vscode                # Recommended editor
vim/neovim           # Terminal-based editing

# Database tools
pgcli                # Enhanced PostgreSQL CLI
dbeaver              # Database management GUI

# API testing
httpie               # HTTP client
postman              # API testing suite

# Monitoring
docker-compose-ui    # Web UI for Docker Compose
```

## Quick Start

### 1. Clone Repository

```bash
git clone https://github.com/bbookman/lifeboard-supabase.git
cd lifeboard-supabase
```

### 2. Environment Setup

```bash
# Copy development environment template
cp .env.example .env.local

# Install pre-commit hooks (recommended)
pip install pre-commit
pre-commit install
```

### 3. Start Development Environment

```bash
# Start minimal database only
./scripts/deploy.sh --detach minimal

# Start full development stack
./scripts/deploy.sh --health-check base

# Start with Supabase Studio UI
./scripts/deploy.sh --profile studio
```

### 4. Verify Setup

```bash
# Check service health
docker compose ps

# Test API connectivity
curl http://localhost:8810/health

# Access Supabase Studio (if using studio profile)
open http://localhost:3333
```

## Development Environment Setup

### 1. Environment Configuration

#### Development Environment Variables

Edit `.env.local` for local development:

```bash
# Database
POSTGRES_PASSWORD=dev_password_123
POSTGRES_USER=supabase_admin
POSTGRES_DB=postgres
POSTGRES_PORT=5543

# JWT (development keys)
JWT_SECRET=dev-jwt-secret-key-for-local-development-only
ANON_KEY=dev-anon-key-123
SERVICE_ROLE_KEY=dev-service-role-key-123

# API URLs
API_URL=http://localhost:8810
PUBLIC_REST_URL=http://localhost:8810/rest/v1
PUBLIC_REALTIME_URL=ws://localhost:8810/realtime/v1
PUBLIC_STORAGE_URL=http://localhost:8810/storage/v1

# Development settings
SITE_URL=http://localhost:3000
ADDITIONAL_REDIRECT_URLS=http://localhost:3333
DISABLE_SIGNUP=false

# Studio
STUDIO_PORT=3333
STUDIO_DEFAULT_ORGANIZATION=Default Organization
STUDIO_DEFAULT_PROJECT=Default Project

# Logging
LOG_LEVEL=debug
ENABLE_LOGS=true
```

#### Port Configuration

Default development ports:
- **Database**: 5543
- **REST API**: 8810
- **Auth Service**: Internal only
- **Realtime**: Internal only
- **Storage**: Internal only
- **Studio**: 3333
- **Grafana**: 9811 (observability profile)

### 2. IDE Configuration

#### VS Code Setup

Install recommended extensions:

```json
{
  "recommendations": [
    "ms-vscode.vscode-typescript-next",
    "bradlc.vscode-tailwindcss",
    "ms-python.python",
    "ms-vscode.Go",
    "ms-vscode-remote.remote-containers",
    "esbenp.prettier-vscode",
    "ms-vscode.vscode-json",
    "redhat.vscode-yaml",
    "ms-postgresql.postgresql"
  ]
}
```

Configure workspace settings (`.vscode/settings.json`):

```json
{
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "python.defaultInterpreterPath": "/usr/bin/python3",
  "go.toolsManagement.checkForUpdates": "local",
  "files.exclude": {
    "**/node_modules": true,
    "**/volumes": true,
    "**/logs": true
  }
}
```

#### Vim/Neovim Setup

Add to your `.vimrc` or `init.vim`:

```vim
" Docker and Docker Compose syntax
Plug 'ekalinin/Dockerfile.vim'
Plug 'stephpy/vim-yaml'

" SQL support
Plug 'tpope/vim-dadbod'
Plug 'kristijanhusak/vim-dadbod-ui'

" Git integration
Plug 'tpope/vim-fugitive'
```

## Architecture Overview

### 1. Project Structure

```
lifeboard-supabase/
├── .github/
│   └── workflows/           # CI/CD pipelines
├── config/                  # Service configurations
│   ├── grafana/            # Grafana dashboards and settings
│   ├── loki/               # Log aggregation config
│   └── promtail/           # Log collection config
├── logs/                   # Runtime logs
├── migrations/             # Database migrations
├── scripts/                # Deployment and utility scripts
├── seeds/                  # Database seed data
├── supporting_documents/   # Project documentation
├── tests/                  # Test suites
├── utils/                  # Utility scripts
├── volumes/                # Docker volumes and init scripts
│   └── db/                # Database initialization
├── docker-compose.yml     # Main services definition
├── docker-compose.*.yml   # Environment-specific overrides
└── .env.local             # Local environment variables
```

### 2. Service Architecture

#### Core Services

1. **PostgreSQL Database** (`db`)
   - Primary data store
   - Supabase extensions enabled
   - Row Level Security (RLS)
   - Audit logging

2. **GoTrue Auth** (`auth`)
   - User authentication
   - JWT token management
   - Multi-factor authentication
   - Social auth providers

3. **PostgREST API** (`rest`)
   - Auto-generated REST API
   - Based on database schema
   - Query optimization
   - Security policies

4. **Realtime Engine** (`realtime`)
   - WebSocket connections
   - Database change streams
   - Live subscriptions
   - Presence features

5. **Storage API** (`storage`)
   - File upload/download
   - Image transformations
   - Access control
   - CDN integration

6. **Supabase Studio** (`studio`)
   - Web-based admin interface
   - Database management
   - API explorer
   - User management

#### Observability Stack

1. **Grafana** - Dashboards and visualization
2. **Loki** - Log aggregation
3. **Promtail** - Log collection

### 3. Data Flow

```
Client Request → Nginx (if production) → PostgREST → PostgreSQL
                                      ↓
                                  Row Level Security
                                      ↓
                                  Response + Logs
```

## Development Workflow

### 1. Feature Development

#### Branching Strategy

```bash
# Create feature branch
git checkout -b feature/your-feature-name

# Work on feature
git add .
git commit -m "feat: add new feature"

# Push and create PR
git push origin feature/your-feature-name
```

#### Commit Message Convention

Follow conventional commits:

```
type(scope): description

feat: add new feature
fix: resolve bug
docs: update documentation
test: add test coverage
refactor: improve code structure
perf: optimize performance
chore: maintenance tasks
```

### 2. Local Development Commands

#### Service Management

```bash
# Start services
./scripts/deploy.sh --profile base --detach

# Stop services
docker compose down

# Restart specific service
docker compose restart auth

# View logs
docker compose logs -f rest

# Execute commands in containers
docker exec -it lifeboard-db-1 psql -U supabase_admin -d postgres
```

#### Database Operations

```bash
# Apply migrations
docker exec -it lifeboard-db-1 psql -U supabase_admin -d postgres -f /docker-entrypoint-initdb.d/migrations/001_initial_schema.sql

# Run seeds
docker exec -it lifeboard-db-1 psql -U supabase_admin -d postgres -f /docker-entrypoint-initdb.d/seeds/001_sample_data.sql

# Access database shell
docker exec -it lifeboard-db-1 psql -U supabase_admin -d postgres

# Backup database
docker exec lifeboard-db-1 pg_dump -U supabase_admin postgres > backup.sql
```

#### Testing Commands

```bash
# Run all tests
./tests/run_all_tests.sh

# Run specific test suites
./tests/test_health_checks.sh
./tests/test_security_scan.sh
./tests/test_crud_integration.sh

# Run individual tests
pytest tests/python/test_api.py
go test ./tests/go/...
```

### 3. Hot Reloading

#### Database Schema Changes

1. Create migration file in `migrations/`
2. Apply migration:
   ```bash
   docker exec -it lifeboard-db-1 psql -U supabase_admin -d postgres -f /docker-entrypoint-initdb.d/migrations/your_migration.sql
   ```
3. PostgREST automatically detects schema changes

#### Configuration Changes

1. Update configuration files
2. Restart affected services:
   ```bash
   docker compose restart service-name
   ```

## Testing

### 1. Test Structure

```
tests/
├── run_all_tests.sh        # Master test runner
├── test_health_checks.sh   # Service health validation
├── test_security_scan.sh   # Security testing
├── test_crud_integration.sh # API integration tests
├── test_observability_logging.sh # Logging tests
├── python/                 # Python test suites
├── go/                     # Go test suites
└── sql/                    # SQL test scripts
```

### 2. Test Categories

#### Unit Tests

Test individual components in isolation:

```bash
# Database functions
./tests/sql/test_functions.sql

# API endpoints
pytest tests/python/test_api.py

# Utility functions
go test ./utils/...
```

#### Integration Tests

Test service interactions:

```bash
# API integration
./tests/test_crud_integration.sh

# Authentication flow
./tests/test_auth_flow.sh

# Real-time subscriptions
./tests/test_realtime.sh
```

#### End-to-End Tests

Test complete user workflows:

```bash
# User registration and login
./tests/e2e/test_user_workflow.sh

# Data creation and retrieval
./tests/e2e/test_data_workflow.sh
```

### 3. Writing Tests

#### Database Tests

Create SQL test files:

```sql
-- tests/sql/test_user_functions.sql
BEGIN;
  SELECT plan(3);

  -- Test user creation
  SELECT ok(
    (SELECT create_user('<EMAIL>', 'password') IS NOT NULL),
    'User creation should succeed'
  );

  -- Test user authentication
  SELECT ok(
    (SELECT authenticate_user('<EMAIL>', 'password') = true),
    'User authentication should succeed'
  );

  -- Test user permissions
  SELECT ok(
    (SELECT user_has_permission('<EMAIL>', 'read') = true),
    'User should have read permission'
  );

  SELECT * FROM finish();
ROLLBACK;
```

#### API Tests

Create Python test files:

```python
# tests/python/test_api.py
import pytest
import requests

def test_health_endpoint():
    response = requests.get('http://localhost:8810/health')
    assert response.status_code == 200
    assert 'healthy' in response.text

def test_rest_api():
    headers = {'apikey': 'dev-anon-key-123'}
    response = requests.get(
        'http://localhost:8810/rest/v1/posts',
        headers=headers
    )
    assert response.status_code == 200
    assert isinstance(response.json(), list)
```

## Code Quality

### 1. Pre-commit Hooks

Configured hooks run automatically before commits:

- **ShellCheck**: Shell script linting
- **YAML Lint**: YAML file validation
- **JSON Format**: JSON formatting
- **Markdown Lint**: Documentation linting
- **Hadolint**: Dockerfile linting
- **TruffleHog**: Secret detection
- **SQL Check**: SQL syntax validation

### 2. Code Standards

#### Shell Scripts

Follow these standards:
- Use `#!/bin/bash` shebang
- Enable strict mode: `set -euo pipefail`
- Quote variables: `"$variable"`
- Use functions for reusable code
- Add comprehensive error handling

```bash
#!/bin/bash
set -euo pipefail

# Good example
deploy_service() {
    local service_name="$1"
    local profile="$2"

    if [[ -z "$service_name" ]]; then
        echo "Error: Service name required" >&2
        return 1
    fi

    docker compose -p lifeboard up -d "$service_name"
}
```

#### SQL Code

Follow these standards:
- Use lowercase for keywords
- Indent nested queries
- Use meaningful aliases
- Add comments for complex logic
- Use consistent naming conventions

```sql
-- Good example
select
    u.id,
    u.email,
    p.display_name,
    count(posts.id) as post_count
from auth.users u
inner join public.profiles p on u.id = p.user_id
left join public.posts on u.id = posts.author_id
where u.created_at >= current_date - interval '30 days'
group by u.id, u.email, p.display_name
order by post_count desc;
```

#### YAML Configuration

Follow these standards:
- Use 2-space indentation
- Quote strings when necessary
- Use consistent naming
- Add comments for complex configurations

```yaml
# Good example
services:
  db:
    image: public.ecr.aws/supabase/postgres:15.6.1.124
    environment:
      POSTGRES_DB: "${POSTGRES_DB}"
      POSTGRES_USER: "${POSTGRES_USER}"
      POSTGRES_PASSWORD: "${POSTGRES_PASSWORD}"
    volumes:
      - "db_data:/var/lib/postgresql/data"
      - "./volumes/db:/docker-entrypoint-initdb.d:ro"
    networks:
      - lifeboard_net
    restart: unless-stopped
```

### 3. Documentation Standards

#### Code Comments

```bash
# Single-line comments for brief explanations
complicated_function() {
    # This complex logic requires explanation
    local result
    result=$(some_complex_operation)
    echo "$result"
}

# Multi-line comments for detailed explanations
# This function processes user authentication by:
# 1. Validating the input credentials
# 2. Checking against the database
# 3. Generating a JWT token
# 4. Setting appropriate headers
authenticate_user() {
    # Implementation here
}
```

#### Function Documentation

```bash
#!/bin/bash

#######################################
# Deploys a specific service profile with health checking
# Globals:
#   LOG_FILE
# Arguments:
#   $1: Profile name (minimal, base, studio, etc.)
#   $2: Optional environment file path
# Returns:
#   0 on success, 1 on failure
# Example:
#   deploy_profile "base" ".env.local"
#######################################
deploy_profile() {
    local profile="$1"
    local env_file="${2:-.env.local}"

    # Implementation here
}
```

## Database Development

### 1. Schema Management

#### Migration Files

Create migration files in `migrations/`:

```sql
-- migrations/002_add_user_profiles.sql
-- Add user profiles table with RLS

-- Create profiles table
create table public.profiles (
    id uuid references auth.users on delete cascade not null primary key,
    updated_at timestamp with time zone,
    username text unique,
    full_name text,
    avatar_url text,
    website text,

    constraint username_length check (char_length(username) >= 3)
);

-- Enable RLS
alter table public.profiles enable row level security;

-- Create policies
create policy "Public profiles are viewable by everyone."
    on profiles for select
    using ( true );

create policy "Users can insert their own profile."
    on profiles for insert
    with check ( auth.uid() = id );

create policy "Users can update own profile."
    on profiles for update
    using ( auth.uid() = id );

-- Create indexes
create index profiles_username_idx on public.profiles(username);

-- Add triggers
create trigger handle_updated_at before update on public.profiles
    for each row execute procedure moddatetime (updated_at);
```

#### Seed Data

Create seed files in `seeds/`:

```sql
-- seeds/002_sample_profiles.sql
-- Sample profile data for development

insert into public.profiles (id, username, full_name, avatar_url, website)
values
    ('550e8400-e29b-41d4-a716-************', 'johndoe', 'John Doe', 'https://example.com/avatar1.jpg', 'https://johndoe.com'),
    ('550e8400-e29b-41d4-a716-************', 'janedoe', 'Jane Doe', 'https://example.com/avatar2.jpg', 'https://janedoe.com')
on conflict (id) do nothing;
```

### 2. RLS Policies

#### Policy Examples

```sql
-- Basic user isolation
create policy "Users can only see own data"
    on user_data for all
    using ( auth.uid() = user_id );

-- Role-based access
create policy "Admins can see all data"
    on sensitive_data for all
    using (
        auth.jwt() ->> 'role' = 'admin'
        or auth.uid() = user_id
    );

-- Time-based access
create policy "Users can only edit recent posts"
    on posts for update
    using (
        auth.uid() = author_id
        and created_at > now() - interval '1 hour'
    );

-- Complex business logic
create policy "Team members can access team data"
    on team_documents for all
    using (
        exists (
            select 1 from team_members tm
            where tm.team_id = team_documents.team_id
            and tm.user_id = auth.uid()
            and tm.status = 'active'
        )
    );
```

### 3. Database Functions

#### Custom Functions

```sql
-- Function to get user role
create or replace function public.get_user_role(user_id uuid)
returns text
language sql security definer
as $$
    select role from public.user_roles
    where user_roles.user_id = get_user_role.user_id
    limit 1;
$$;

-- Function to check permissions
create or replace function public.user_has_permission(
    user_id uuid,
    permission text
)
returns boolean
language plpgsql security definer
as $$
declare
    user_role text;
begin
    select get_user_role(user_has_permission.user_id) into user_role;

    return exists (
        select 1 from public.role_permissions rp
        where rp.role = user_role
        and rp.permission = user_has_permission.permission
    );
end;
$$;
```

## API Development

### 1. PostgREST Configuration

#### Query Optimization

```sql
-- Add indexes for common queries
create index idx_posts_author_status on posts(author_id, status);
create index idx_posts_created_at on posts(created_at desc);
create index idx_posts_tags on posts using gin(tags);

-- Materialized views for complex queries
create materialized view popular_posts as
select
    p.id,
    p.title,
    p.author_id,
    count(l.id) as like_count,
    count(c.id) as comment_count
from posts p
left join likes l on p.id = l.post_id
left join comments c on p.id = c.post_id
group by p.id, p.title, p.author_id;

-- Refresh materialized view
refresh materialized view popular_posts;
```

#### Custom Views

```sql
-- Create API-friendly views
create view api.user_posts as
select
    p.id,
    p.title,
    p.content,
    p.created_at,
    u.email as author_email,
    prof.username as author_username
from posts p
join auth.users u on p.author_id = u.id
join public.profiles prof on u.id = prof.id
where p.status = 'published';

-- Grant access to API user
grant select on api.user_posts to authenticator;
```

### 2. API Testing

#### Manual Testing with curl

```bash
# Get health status
curl http://localhost:8810/health

# List posts
curl -H "apikey: dev-anon-key-123" \
     http://localhost:8810/rest/v1/posts

# Create post (requires auth)
curl -X POST \
     -H "Authorization: Bearer $JWT_TOKEN" \
     -H "apikey: dev-anon-key-123" \
     -H "Content-Type: application/json" \
     -d '{"title": "Test Post", "content": "Test content"}' \
     http://localhost:8810/rest/v1/posts

# Complex query
curl -H "apikey: dev-anon-key-123" \
     "http://localhost:8810/rest/v1/posts?select=title,author:profiles(username)&status=eq.published&order=created_at.desc&limit=10"
```

#### API Testing Scripts

Create test scripts in `tests/api/`:

```bash
#!/bin/bash
# tests/api/test_posts_api.sh

API_URL="http://localhost:8810"
ANON_KEY="dev-anon-key-123"

test_get_posts() {
    echo "Testing GET /posts..."
    response=$(curl -s -H "apikey: $ANON_KEY" "$API_URL/rest/v1/posts")
    if [[ $(echo "$response" | jq 'type') == '"array"' ]]; then
        echo "✅ GET /posts returned array"
    else
        echo "❌ GET /posts failed"
        echo "$response"
    fi
}

test_get_posts
```

## Frontend Development

### 1. Supabase Client Setup

#### JavaScript/TypeScript

```javascript
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = 'http://localhost:8810'
const supabaseKey = 'dev-anon-key-123'

export const supabase = createClient(supabaseUrl, supabaseKey)

// Example usage
async function fetchPosts() {
    const { data, error } = await supabase
        .from('posts')
        .select('*')
        .eq('status', 'published')
        .order('created_at', { ascending: false })

    if (error) throw error
    return data
}

// Real-time subscriptions
const subscription = supabase
    .channel('posts')
    .on('postgres_changes',
        { event: '*', schema: 'public', table: 'posts' },
        (payload) => {
            console.log('Change received!', payload)
        }
    )
    .subscribe()
```

#### React Integration

```jsx
import React, { useState, useEffect } from 'react'
import { supabase } from './supabaseClient'

function PostList() {
    const [posts, setPosts] = useState([])
    const [loading, setLoading] = useState(true)

    useEffect(() => {
        fetchPosts()
    }, [])

    async function fetchPosts() {
        try {
            setLoading(true)
            const { data, error } = await supabase
                .from('posts')
                .select(`
                    *,
                    profiles:author_id (
                        username,
                        avatar_url
                    )
                `)
                .eq('status', 'published')

            if (error) throw error
            setPosts(data || [])
        } catch (error) {
            console.error('Error fetching posts:', error)
        } finally {
            setLoading(false)
        }
    }

    if (loading) return <div>Loading...</div>

    return (
        <div>
            {posts.map(post => (
                <div key={post.id}>
                    <h3>{post.title}</h3>
                    <p>By: {post.profiles?.username}</p>
                    <p>{post.content}</p>
                </div>
            ))}
        </div>
    )
}
```

### 2. Authentication

#### Auth Hook

```javascript
import { useState, useEffect } from 'react'
import { supabase } from './supabaseClient'

export function useAuth() {
    const [user, setUser] = useState(null)
    const [loading, setLoading] = useState(true)

    useEffect(() => {
        // Get initial session
        supabase.auth.getSession().then(({ data: { session } }) => {
            setUser(session?.user ?? null)
            setLoading(false)
        })

        // Listen for auth changes
        const { data: { subscription } } = supabase.auth.onAuthStateChange(
            async (event, session) => {
                setUser(session?.user ?? null)
                setLoading(false)
            }
        )

        return () => subscription.unsubscribe()
    }, [])

    const signIn = async (email, password) => {
        const { error } = await supabase.auth.signInWithPassword({
            email,
            password
        })
        if (error) throw error
    }

    const signUp = async (email, password) => {
        const { error } = await supabase.auth.signUp({
            email,
            password
        })
        if (error) throw error
    }

    const signOut = async () => {
        const { error } = await supabase.auth.signOut()
        if (error) throw error
    }

    return {
        user,
        loading,
        signIn,
        signUp,
        signOut
    }
}
```

## Contributing

### 1. Contribution Process

1. **Fork the repository**
2. **Create a feature branch**
3. **Make your changes**
4. **Add tests for new functionality**
5. **Run the test suite**
6. **Submit a pull request**

### 2. Pull Request Guidelines

#### PR Template

```markdown
## Description
Brief description of changes

## Type of Change
- [ ] Bug fix
- [ ] New feature
- [ ] Breaking change
- [ ] Documentation update

## Testing
- [ ] Tests pass locally
- [ ] New tests added for new functionality
- [ ] Manual testing completed

## Checklist
- [ ] Code follows project style guidelines
- [ ] Self-review completed
- [ ] Documentation updated
- [ ] No new warnings or errors
```

#### Code Review Checklist

**Functionality**
- [ ] Code works as intended
- [ ] Edge cases handled
- [ ] Error handling implemented
- [ ] Performance considerations addressed

**Quality**
- [ ] Code is readable and maintainable
- [ ] Follows project conventions
- [ ] No duplicate code
- [ ] Functions are focused and single-purpose

**Security**
- [ ] No security vulnerabilities
- [ ] Input validation implemented
- [ ] SQL injection prevented
- [ ] Authentication/authorization correct

**Testing**
- [ ] Adequate test coverage
- [ ] Tests are meaningful
- [ ] Tests pass consistently
- [ ] Integration tests included

### 3. Release Process

#### Version Management

Follow semantic versioning (semver):
- **MAJOR**: Breaking changes
- **MINOR**: New features (backward compatible)
- **PATCH**: Bug fixes (backward compatible)

#### Release Checklist

1. **Update version numbers**
2. **Update CHANGELOG.md**
3. **Run full test suite**
4. **Create release branch**
5. **Test deployment**
6. **Tag release**
7. **Update documentation**

## Troubleshooting

### 1. Common Development Issues

#### Docker Issues

```bash
# Container won't start
docker compose logs service-name

# Port conflicts
docker compose down
sudo lsof -i :8810  # Check what's using the port

# Permission issues
sudo chown -R $(id -u):$(id -g) volumes/

# Disk space issues
docker system prune -a
docker volume prune
```

#### Database Issues

```bash
# Connection refused
docker exec -it lifeboard-db-1 pg_isready -U supabase_admin

# Migration failures
docker exec -it lifeboard-db-1 psql -U supabase_admin -d postgres -c "\dt"

# Reset database
docker compose down -v
docker compose up -d db
```

#### API Issues

```bash
# PostgREST not responding
curl http://localhost:8810/

# Schema cache issues
docker compose restart rest

# Permission errors
# Check RLS policies in database
```

### 2. Performance Issues

#### Database Performance

```sql
-- Check slow queries
SELECT query, mean_time, calls, total_time
FROM pg_stat_statements
ORDER BY total_time DESC
LIMIT 10;

-- Check table sizes
SELECT
    tablename,
    pg_size_pretty(pg_total_relation_size(tablename)) as size
FROM pg_tables
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(tablename) DESC;

-- Check index usage
SELECT
    indexname,
    idx_scan,
    idx_tup_read,
    idx_tup_fetch
FROM pg_stat_user_indexes;
```

#### API Performance

```bash
# Monitor API response times
time curl http://localhost:8810/rest/v1/posts

# Check container resource usage
docker stats

# Profile database queries
tail -f logs/postgresql.log | grep "duration"
```

### 3. Debug Mode

#### Enable Debug Logging

```bash
# Update .env.local
LOG_LEVEL=debug

# Restart services
docker compose restart

# View debug logs
docker compose logs -f
```

#### Database Debug

```sql
-- Enable query logging
ALTER SYSTEM SET log_statement = 'all';
ALTER SYSTEM SET log_min_duration_statement = 0;
SELECT pg_reload_conf();

-- View active connections
SELECT * FROM pg_stat_activity;

-- Monitor locks
SELECT * FROM pg_locks WHERE NOT granted;
```

## Support

### 1. Getting Help

- **Documentation**: Check `supporting_documents/` directory
- **GitHub Issues**: Report bugs and request features
- **GitHub Discussions**: Ask questions and share ideas
- **Test Environment**: Use `./scripts/deploy.sh --profile studio` for exploration

### 2. Development Resources

- **Supabase Documentation**: https://supabase.com/docs
- **PostgREST Documentation**: https://postgrest.org/
- **PostgreSQL Documentation**: https://www.postgresql.org/docs/
- **Docker Documentation**: https://docs.docker.com/

---

**Document Version**: 1.0.0
**Last Updated**: 2025-07-02
**Next Review**: 2025-08-02
