# Lifeboard Plugin Developer Guide

## Overview

This guide provides comprehensive documentation for developing plugins for the Lifeboard platform. Lifeboard features an Obsidian-style plugin architecture that allows third-party extensions to execute JavaScript/TypeScript directly inside an Electron desktop shell, manipulate DOM elements, create custom UI components, and communicate with backend services while maintaining security through sandboxing and permissions.

## Table of Contents

1. [Getting Started](#getting-started)
2. [Plugin Architecture](#plugin-architecture)
3. [Development Environment Setup](#development-environment-setup)
4. [Plugin Structure](#plugin-structure)
5. [Plugin API Reference](#plugin-api-reference)
6. [Security & Permissions](#security--permissions)
7. [UI Development](#ui-development)
8. [Data Storage](#data-storage)
9. [Testing Your Plugin](#testing-your-plugin)
10. [Publishing to Marketplace](#publishing-to-marketplace)
11. [Examples](#examples)
12. [Best Practices](#best-practices)
13. [Troubleshooting](#troubleshooting)

## Getting Started

### Prerequisites

- **Node.js**: Version 18+ for development
- **Lifeboard Desktop**: Latest version with plugin support
- **Code Editor**: VS Code recommended with TypeScript support
- **Git**: For version control and marketplace publishing

### Development Tools

```bash
# Install development dependencies
npm install -g typescript
npm install -g @types/node

# Optional: Plugin development CLI
npm install -g lifeboard-plugin-cli
```

### Your First Plugin

Create a minimal "Hello World" plugin:

```bash
# Navigate to the development plugins directory
cd /path/to/lifeboard-supabase/desktop/plugins/

# Create plugin directory
mkdir my-first-plugin
cd my-first-plugin

# Create basic files
touch manifest.json main.js
```

**manifest.json**:
```json
{
  "id": "my-first-plugin",
  "name": "My First Plugin",
  "version": "0.1.0",
  "description": "A simple hello world plugin",
  "author": "Your Name",
  "main": "main.js",
  "permissions": ["workspace"],
  "minAppVersion": "0.1.0"
}
```

**main.js**:
```javascript
/**
 * My First Plugin - Hello World Example
 */

class MyFirstPlugin {
  constructor(api) {
    this.api = api;
  }

  async initialize() {
    // Register a command
    this.api.commands.addCommand({
      id: 'hello-world',
      name: 'Say Hello',
      callback: () => {
        console.log('Hello from my first plugin!');
        this.api.ui.showNotice('Hello, World!');
      }
    });

    // Add ribbon icon
    this.api.ui.addRibbonIcon(
      'star',
      'My First Plugin',
      () => {
        this.api.ui.showNotice('Plugin button clicked!');
      }
    );

    console.log('My First Plugin loaded successfully!');
  }
}

// Initialize plugin
const plugin = new MyFirstPlugin(PluginAPI);
plugin.initialize().catch(console.error);
```

## Plugin Architecture

### Runtime Environment

Plugins execute in a secure Node.js VM context within the Electron main process, providing:

- **Isolated Execution**: Each plugin runs in its own VM sandbox
- **Permission-Based Access**: Granular control over plugin capabilities
- **API Injection**: Controlled access to Lifeboard functionality
- **Security Boundaries**: Protection against malicious code

### Plugin Lifecycle

1. **Discovery**: Lifeboard scans plugin directories for `manifest.json`
2. **Validation**: Manifest schema and compatibility checking
3. **Loading**: VM context creation and API injection
4. **Initialization**: Plugin main file execution
5. **Registration**: Commands, UI elements, and event handlers
6. **Runtime**: Active plugin functionality
7. **Unloading**: Cleanup and resource disposal

### Directory Structure

```
plugins/
├── your-plugin-id/
│   ├── manifest.json         # Plugin metadata and configuration
│   ├── main.js              # Main plugin entry point (Node.js context)
│   ├── renderer.js          # Optional renderer process code
│   ├── ui/                  # UI components and styles
│   │   ├── settings.html    # Settings interface
│   │   ├── modal.html       # Custom modals
│   │   └── styles.css       # Plugin-specific styling
│   ├── src/                 # Source code modules
│   │   ├── api-client.js    # External API integration
│   │   ├── data-processor.js # Data transformation logic
│   │   └── utils.js         # Utility functions
│   ├── assets/              # Static assets
│   │   ├── icons/           # Plugin icons
│   │   └── images/          # Images and graphics
│   ├── tests/               # Test files
│   │   ├── unit/            # Unit tests
│   │   └── integration/     # Integration tests
│   ├── docs/                # Plugin documentation
│   │   ├── README.md        # Plugin overview
│   │   └── API.md           # API documentation
│   └── package.json         # Optional: npm dependencies
```

## Development Environment Setup

### Local Development

#### 1. Plugin Directory Setup

**Development Environment** (Primary for developers):
```bash
# Plugins are located within the project structure
# Project root: /path/to/lifeboard-supabase/
# Plugin directory: /path/to/lifeboard-supabase/desktop/plugins/

# Navigate to plugin development directory
cd /path/to/lifeboard-supabase/desktop/plugins/
mkdir your-plugin-id
cd your-plugin-id

# Your plugin development happens here
```

**Production Environment** (End-user installations):
```bash
# Plugins are installed in user data directory
# macOS: ~/Library/Application Support/lifeboard/plugins/
# Windows: %APPDATA%/lifeboard/plugins/
# Linux: ~/.config/lifeboard/plugins/
```

**Note**: During development, plugins are created and tested in the project's `desktop/plugins/` directory. When published to the marketplace, end users will have plugins installed in their system's user data directory.

#### 2. Development Workflow

```bash
# 1. Start Lifeboard in development mode from project root
cd /path/to/lifeboard-supabase/desktop/
npm run dev

# 2. Make plugin changes in desktop/plugins/your-plugin-id/
# Files are automatically reloaded on save

# 3. View plugin logs
tail -f logs/plugin-your-plugin-id.log

# 4. Test plugin functionality
# Use command palette or UI elements
```

#### 3. Hot Reloading

Plugin changes are automatically detected and reloaded:
- **Manifest changes**: Require app restart
- **Main file changes**: Hot reloaded
- **UI file changes**: Hot reloaded
- **Permission changes**: Require app restart

## Plugin Structure

### Manifest Schema

The `manifest.json` file defines plugin metadata and configuration:

```json
{
  "id": "unique-plugin-id",
  "name": "Human Readable Plugin Name",
  "version": "1.0.0",
  "description": "Brief description of plugin functionality",
  "author": "Your Name or Organization",
  "homepage": "https://your-website.com/plugin",
  "repository": "https://github.com/yourusername/your-plugin",
  "license": "MIT",
  "main": "main.js",
  "renderer": "renderer.js",
  "permissions": ["workspace", "network", "filesystem", "system"],
  "minAppVersion": "0.1.0",
  "maxAppVersion": "2.0.0",
  "settings": {
    "apiKey": {
      "type": "string",
      "sensitive": true,
      "description": "API key for external service",
      "default": ""
    },
    "autoSync": {
      "type": "boolean",
      "description": "Enable automatic synchronization",
      "default": true
    },
    "syncInterval": {
      "type": "number",
      "description": "Sync interval in minutes",
      "default": 60,
      "min": 5,
      "max": 1440
    }
  },
  "keywords": ["productivity", "automation", "sync"],
  "engines": {
    "lifeboard": ">=0.1.0"
  }
}
```

#### Manifest Field Reference

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `id` | string | ✅ | Unique plugin identifier (lowercase, no spaces) |
| `name` | string | ✅ | Human-readable plugin name |
| `version` | string | ✅ | Semantic version (semver) |
| `description` | string | ✅ | Brief plugin description |
| `author` | string | ✅ | Plugin author name |
| `main` | string | ✅ | Main entry point file |
| `permissions` | array | ❌ | Required permissions array |
| `minAppVersion` | string | ❌ | Minimum Lifeboard version |
| `maxAppVersion` | string | ❌ | Maximum Lifeboard version |
| `renderer` | string | ❌ | Renderer process entry point |
| `homepage` | string | ❌ | Plugin homepage URL |
| `repository` | string | ❌ | Source code repository |
| `license` | string | ❌ | License identifier |
| `settings` | object | ❌ | Plugin settings schema |
| `keywords` | array | ❌ | Searchable keywords |
| `engines` | object | ❌ | Engine compatibility |

### Main Entry Point

The main entry point (`main.js`) is executed in the Node.js context:

```javascript
/**
 * Plugin Main Entry Point
 * Executes in Node.js context with access to PluginAPI
 */

class YourPlugin {
  constructor(api) {
    this.api = api;
    this.logger = this.createLogger();
    this.settings = null;
  }

  /**
   * Plugin initialization
   * Called when plugin is loaded
   */
  async initialize() {
    try {
      // Load plugin settings
      this.settings = await this.api.storage.loadData();

      // Register commands
      await this.registerCommands();

      // Setup UI elements
      await this.setupUI();

      // Initialize background tasks
      await this.startBackgroundTasks();

      this.logger.info('Plugin initialized successfully');
    } catch (error) {
      this.logger.error('Plugin initialization failed', error);
      throw error;
    }
  }

  /**
   * Plugin cleanup
   * Called when plugin is unloaded
   */
  async cleanup() {
    // Stop background tasks
    if (this.backgroundTimer) {
      clearInterval(this.backgroundTimer);
    }

    // Save final state
    await this.api.storage.saveData(this.settings);

    this.logger.info('Plugin cleanup completed');
  }

  /**
   * Register plugin commands
   */
  async registerCommands() {
    this.api.commands.addCommand({
      id: 'your-plugin-main-action',
      name: 'Main Plugin Action',
      description: 'Primary plugin functionality',
      callback: () => this.executeMainAction(),
      hotkey: 'Ctrl+Shift+Y'
    });
  }

  /**
   * Setup UI elements
   */
  async setupUI() {
    // Add ribbon icon
    this.api.ui.addRibbonIcon(
      'plugin-icon',
      'Your Plugin',
      () => this.showMainInterface()
    );

    // Add status bar item
    this.statusBarItem = this.api.ui.addStatusBarItem();
    this.statusBarItem.setText('Plugin Ready');
  }

  /**
   * Create plugin-specific logger
   */
  createLogger() {
    return {
      info: (message, data = {}) => {
        console.log(`[${this.api.manifest.id}] INFO: ${message}`, data);
      },
      warn: (message, data = {}) => {
        console.warn(`[${this.api.manifest.id}] WARN: ${message}`, data);
      },
      error: (message, error = null, data = {}) => {
        console.error(`[${this.api.manifest.id}] ERROR: ${message}`, error, data);
      }
    };
  }
}

// Initialize plugin
const plugin = new YourPlugin(PluginAPI);
plugin.initialize().catch(console.error);

// Handle plugin unload
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { plugin };
}
```

## Plugin API Reference

### Core API Structure

The `PluginAPI` object provides access to Lifeboard functionality:

```typescript
interface PluginAPI {
  app: AppAPI;
  workspace: WorkspaceAPI;
  commands: CommandRegistry;
  events: EventBus;
  ui: UIAPI;
  storage: StorageAPI;
  network: NetworkAPI;
  manifest: PluginManifest;
}
```

### App API

```typescript
interface AppAPI {
  /**
   * Get application name
   */
  getName(): string;

  /**
   * Get application version
   */
  getVersion(): string;

  /**
   * Get platform information
   */
  getPlatform(): 'darwin' | 'win32' | 'linux';

  /**
   * Get user data directory
   */
  getUserDataPath(): string;

  /**
   * Get plugin directory
   */
  getPluginPath(): string;
}
```

**Example Usage**:
```javascript
console.log(`Running on ${this.api.app.getPlatform()}`);
console.log(`App version: ${this.api.app.getVersion()}`);
const userPath = this.api.app.getUserDataPath();
```

### Workspace API

```typescript
interface WorkspaceAPI {
  /**
   * Get active leaf
   */
  getActiveLeaf(): WorkspaceLeaf | null;

  /**
   * Create new leaf
   */
  createLeaf(parent?: WorkspaceParent): WorkspaceLeaf;

  /**
   * Split leaf
   */
  splitLeaf(leaf: WorkspaceLeaf, direction: 'horizontal' | 'vertical'): WorkspaceLeaf;

  /**
   * Open file in workspace
   */
  openFile(file: string, leaf?: WorkspaceLeaf): Promise<void>;

  /**
   * Register view type
   */
  registerViewType(type: string, viewCreator: ViewCreator): void;
}
```

**Example Usage**:
```javascript
// Create new pane
const leaf = this.api.workspace.createLeaf();

// Register custom view
this.api.workspace.registerViewType('my-custom-view', () => {
  return new MyCustomView();
});
```

### Command Registry

```typescript
interface CommandRegistry {
  /**
   * Add command
   */
  addCommand(command: Command): void;

  /**
   * Remove command
   */
  removeCommand(id: string): void;

  /**
   * Execute command
   */
  executeCommand(id: string, ...args: any[]): Promise<any>;

  /**
   * List all commands
   */
  listCommands(): Command[];
}

interface Command {
  id: string;
  name: string;
  description?: string;
  callback: (...args: any[]) => any;
  hotkey?: string;
  icon?: string;
  checkCallback?: (...args: any[]) => boolean;
}
```

**Example Usage**:
```javascript
// Add simple command
this.api.commands.addCommand({
  id: 'my-plugin-action',
  name: 'My Plugin Action',
  description: 'Execute main plugin functionality',
  callback: () => {
    console.log('Command executed!');
  },
  hotkey: 'Ctrl+Shift+M'
});

// Add conditional command
this.api.commands.addCommand({
  id: 'conditional-action',
  name: 'Conditional Action',
  checkCallback: () => {
    // Only show command when conditions are met
    return this.isActionAvailable();
  },
  callback: () => {
    this.executeConditionalAction();
  }
});
```

### Event Bus

```typescript
interface EventBus {
  /**
   * Add event listener
   */
  on(event: string, callback: (...args: any[]) => void): void;

  /**
   * Remove event listener
   */
  off(event: string, callback: (...args: any[]) => void): void;

  /**
   * Emit event
   */
  emit(event: string, ...args: any[]): void;

  /**
   * Add one-time event listener
   */
  once(event: string, callback: (...args: any[]) => void): void;
}
```

**Common Events**:
- `workspace:file-open` - File opened in workspace
- `workspace:layout-change` - Workspace layout changed
- `app:ready` - Application fully loaded
- `plugin:enabled` - Plugin enabled
- `plugin:disabled` - Plugin disabled

**Example Usage**:
```javascript
// Listen to file open events
this.api.events.on('workspace:file-open', (file) => {
  console.log(`File opened: ${file.path}`);
});

// Emit custom event
this.api.events.emit('my-plugin:data-updated', { data: newData });

// One-time listener
this.api.events.once('app:ready', () => {
  this.initializeAfterAppReady();
});
```

### UI API

```typescript
interface UIAPI {
  /**
   * Add ribbon icon
   */
  addRibbonIcon(icon: string, title: string, callback: () => void): HTMLElement;

  /**
   * Show modal
   */
  showModal(modal: Modal): void;

  /**
   * Show notice
   */
  showNotice(message: string, timeout?: number): void;

  /**
   * Add status bar item
   */
  addStatusBarItem(): StatusBarItem;

  /**
   * Create setting tab
   */
  addSettingTab(tab: SettingTab): void;

  /**
   * Show command palette
   */
  showCommandPalette(): void;
}

interface Modal {
  title: string;
  content: string | HTMLElement;
  buttons?: ModalButton[];
  onOpen?: () => void;
  onClose?: () => void;
}

interface ModalButton {
  text: string;
  callback: () => void;
  style?: 'primary' | 'secondary' | 'danger';
}

interface StatusBarItem {
  setText(text: string): void;
  setTooltip(tooltip: string): void;
  addClass(className: string): void;
  removeClass(className: string): void;
}
```

**Example Usage**:
```javascript
// Add ribbon icon
const ribbonIcon = this.api.ui.addRibbonIcon(
  'star',
  'My Plugin',
  () => {
    this.showMainInterface();
  }
);

// Show modal
this.api.ui.showModal({
  title: 'Plugin Settings',
  content: this.createSettingsHTML(),
  buttons: [
    {
      text: 'Save',
      style: 'primary',
      callback: () => this.saveSettings()
    },
    {
      text: 'Cancel',
      callback: () => {}
    }
  ]
});

// Show notification
this.api.ui.showNotice('Plugin action completed!', 3000);

// Add status bar item
const statusBar = this.api.ui.addStatusBarItem();
statusBar.setText('Plugin: Ready');
statusBar.setTooltip('Click to open plugin');
```

### Storage API

```typescript
interface StorageAPI {
  /**
   * Load plugin data
   */
  loadData(): Promise<any>;

  /**
   * Save plugin data
   */
  saveData(data: any): Promise<boolean>;

  /**
   * Get storage path
   */
  getStoragePath(): string;

  /**
   * Clear all data
   */
  clearData(): Promise<boolean>;
}
```

**Example Usage**:
```javascript
// Load settings
const settings = await this.api.storage.loadData() || {
  apiKey: '',
  autoSync: true,
  lastSync: null
};

// Save settings
await this.api.storage.saveData({
  ...settings,
  lastSync: new Date().toISOString()
});

// Clear all data
await this.api.storage.clearData();
```

### Network API

Available only with `network` permission:

```typescript
interface NetworkAPI {
  /**
   * HTTP fetch (if network permission granted)
   */
  fetch?: (url: string, options?: RequestInit) => Promise<Response>;
}
```

**Example Usage**:
```javascript
// Check if network permission available
if (this.api.network.fetch) {
  try {
    const response = await this.api.network.fetch('https://api.example.com/data', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      }
    });

    const data = await response.json();
    console.log('API data received:', data);
  } catch (error) {
    console.error('Network request failed:', error);
  }
} else {
  console.log('Network permission not granted');
}
```

## Security & Permissions

### Permission System

Plugins must declare required permissions in their manifest:

```json
{
  "permissions": ["workspace", "network", "filesystem", "system"]
}
```

#### Available Permissions

| Permission | Description | API Access |
|------------|-------------|------------|
| `workspace` | Access to workspace APIs | WorkspaceAPI, file operations |
| `network` | Network requests | NetworkAPI.fetch |
| `filesystem` | File system access | File read/write operations |
| `system` | System-level operations | Process execution, system info |

#### Permission Enforcement

```javascript
// Permission checking is automatic
if (this.api.network.fetch) {
  // Network permission granted
  await this.api.network.fetch(url);
} else {
  // Network permission not granted
  console.log('Network access denied');
}
```

### Security Best Practices

#### 1. Input Validation

```javascript
// Always validate user input
function validateApiKey(key) {
  if (typeof key !== 'string') {
    throw new Error('API key must be a string');
  }

  if (key.length < 10) {
    throw new Error('API key too short');
  }

  if (!/^[a-zA-Z0-9_-]+$/.test(key)) {
    throw new Error('API key contains invalid characters');
  }

  return true;
}
```

#### 2. Error Handling

```javascript
// Comprehensive error handling
async function safeApiCall(url, options) {
  try {
    const response = await this.api.network.fetch(url, options);

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    this.logger.error('API call failed', error);

    // Don't expose sensitive error details to users
    throw new Error('External service temporarily unavailable');
  }
}
```

#### 3. Secure Storage

```javascript
// Never store sensitive data in plain text
class SecureSettings {
  constructor(api) {
    this.api = api;
  }

  async saveApiKey(key) {
    // Encrypt sensitive data before storage
    const encrypted = await this.encrypt(key);
    const settings = await this.api.storage.loadData() || {};
    settings.apiKeyEncrypted = encrypted;
    await this.api.storage.saveData(settings);
  }

  async getApiKey() {
    const settings = await this.api.storage.loadData() || {};
    if (settings.apiKeyEncrypted) {
      return await this.decrypt(settings.apiKeyEncrypted);
    }
    return null;
  }
}
```

### Sandbox Limitations

Plugins run in a restricted environment:

#### Allowed Operations
- File operations within plugin directory
- Network requests (with permission)
- UI manipulation through API
- Event handling and emission
- Local data storage

#### Restricted Operations
- Direct DOM manipulation (use UI API)
- File system access outside plugin directory
- Process execution (without system permission)
- Module loading outside whitelist
- Direct Electron API access

## UI Development

### Creating Custom Views

```javascript
// Custom view class
class MyCustomView {
  constructor() {
    this.containerEl = null;
  }

  getViewType() {
    return 'my-custom-view';
  }

  getDisplayText() {
    return 'My Custom View';
  }

  async onOpen() {
    this.containerEl = document.createElement('div');
    this.containerEl.className = 'my-custom-view';

    // Build UI
    this.containerEl.innerHTML = `
      <div class="view-header">
        <h2>My Custom View</h2>
      </div>
      <div class="view-content">
        <p>Custom content goes here</p>
        <button id="my-action-btn">Action Button</button>
      </div>
    `;

    // Add event listeners
    this.containerEl
      .querySelector('#my-action-btn')
      .addEventListener('click', () => {
        this.handleAction();
      });

    return this.containerEl;
  }

  async onClose() {
    // Cleanup
    if (this.containerEl) {
      this.containerEl.remove();
    }
  }

  handleAction() {
    console.log('Action button clicked!');
  }
}

// Register the view
this.api.workspace.registerViewType('my-custom-view', () => {
  return new MyCustomView();
});
```

### Modal Development

```javascript
// Create custom modal
class SettingsModal {
  constructor(api, plugin) {
    this.api = api;
    this.plugin = plugin;
  }

  show() {
    const modal = {
      title: 'Plugin Settings',
      content: this.createContent(),
      buttons: [
        {
          text: 'Save',
          style: 'primary',
          callback: () => this.saveSettings()
        },
        {
          text: 'Cancel',
          callback: () => {}
        }
      ]
    };

    this.api.ui.showModal(modal);
  }

  createContent() {
    const container = document.createElement('div');
    container.innerHTML = `
      <div class="settings-container">
        <div class="setting-item">
          <label for="api-key">API Key:</label>
          <input type="password" id="api-key" placeholder="Enter API key">
        </div>

        <div class="setting-item">
          <label for="auto-sync">
            <input type="checkbox" id="auto-sync">
            Enable automatic sync
          </label>
        </div>

        <div class="setting-item">
          <label for="sync-interval">Sync interval (minutes):</label>
          <input type="number" id="sync-interval" min="5" max="1440" value="60">
        </div>
      </div>
    `;

    // Load current settings
    this.loadCurrentSettings(container);

    return container;
  }

  async loadCurrentSettings(container) {
    const settings = await this.api.storage.loadData() || {};

    container.querySelector('#auto-sync').checked = settings.autoSync || false;
    container.querySelector('#sync-interval').value = settings.syncInterval || 60;
  }

  async saveSettings() {
    const container = document.querySelector('.settings-container');
    const settings = {
      apiKey: container.querySelector('#api-key').value,
      autoSync: container.querySelector('#auto-sync').checked,
      syncInterval: parseInt(container.querySelector('#sync-interval').value)
    };

    await this.api.storage.saveData(settings);
    this.api.ui.showNotice('Settings saved successfully!');
  }
}
```

### Styling Guidelines

Create `ui/styles.css` for plugin-specific styles:

```css
/* Plugin-specific styles */
.my-plugin-container {
  padding: 16px;
  border-radius: 8px;
  background: var(--background-primary);
  border: 1px solid var(--background-modifier-border);
}

.my-plugin-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--background-modifier-border);
}

.my-plugin-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-normal);
  margin: 0;
}

.my-plugin-button {
  background: var(--interactive-accent);
  color: var(--text-on-accent);
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.my-plugin-button:hover {
  background: var(--interactive-accent-hover);
}

.my-plugin-button:disabled {
  background: var(--background-modifier-border);
  color: var(--text-muted);
  cursor: not-allowed;
}

/* Status indicators */
.status-connected {
  color: var(--text-success);
}

.status-disconnected {
  color: var(--text-error);
}

.status-syncing {
  color: var(--text-warning);
}

/* Form elements */
.plugin-form-group {
  margin-bottom: 16px;
}

.plugin-form-label {
  display: block;
  margin-bottom: 4px;
  font-weight: 500;
  color: var(--text-normal);
}

.plugin-form-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid var(--background-modifier-border);
  border-radius: 4px;
  background: var(--background-primary);
  color: var(--text-normal);
  font-size: 14px;
}

.plugin-form-input:focus {
  border-color: var(--interactive-accent);
  outline: none;
  box-shadow: 0 0 0 2px var(--interactive-accent-rgb), 0.2);
}
```

## Data Storage

### Storage Best Practices

```javascript
class PluginDataManager {
  constructor(api) {
    this.api = api;
    this.cache = new Map();
  }

  /**
   * Load settings with validation and defaults
   */
  async loadSettings() {
    try {
      const data = await this.api.storage.loadData() || {};

      // Apply defaults
      const settings = {
        apiKey: '',
        autoSync: true,
        syncInterval: 60,
        lastSync: null,
        ...data
      };

      // Validate settings
      this.validateSettings(settings);

      return settings;
    } catch (error) {
      console.error('Failed to load settings:', error);
      return this.getDefaultSettings();
    }
  }

  /**
   * Save settings with validation
   */
  async saveSettings(settings) {
    try {
      this.validateSettings(settings);
      await this.api.storage.saveData(settings);
      return true;
    } catch (error) {
      console.error('Failed to save settings:', error);
      return false;
    }
  }

  /**
   * Validate settings object
   */
  validateSettings(settings) {
    if (!settings || typeof settings !== 'object') {
      throw new Error('Settings must be an object');
    }

    if (settings.syncInterval &&
        (settings.syncInterval < 5 || settings.syncInterval > 1440)) {
      throw new Error('Sync interval must be between 5 and 1440 minutes');
    }

    if (settings.apiKey && typeof settings.apiKey !== 'string') {
      throw new Error('API key must be a string');
    }
  }

  /**
   * Get default settings
   */
  getDefaultSettings() {
    return {
      apiKey: '',
      autoSync: true,
      syncInterval: 60,
      lastSync: null
    };
  }

  /**
   * Cache management
   */
  setCache(key, value, ttl = 300000) { // 5 minutes default
    this.cache.set(key, {
      value,
      expires: Date.now() + ttl
    });
  }

  getCache(key) {
    const item = this.cache.get(key);
    if (!item) return null;

    if (Date.now() > item.expires) {
      this.cache.delete(key);
      return null;
    }

    return item.value;
  }

  clearCache() {
    this.cache.clear();
  }
}
```

### Data Migration

```javascript
class DataMigration {
  constructor(api) {
    this.api = api;
    this.currentVersion = '1.0.0';
  }

  async migrate() {
    const data = await this.api.storage.loadData() || {};
    const dataVersion = data._version || '0.0.0';

    if (this.needsMigration(dataVersion)) {
      const migratedData = await this.performMigration(data, dataVersion);
      migratedData._version = this.currentVersion;
      await this.api.storage.saveData(migratedData);
      console.log(`Data migrated from ${dataVersion} to ${this.currentVersion}`);
    }

    return data;
  }

  needsMigration(dataVersion) {
    return this.compareVersions(dataVersion, this.currentVersion) < 0;
  }

  async performMigration(data, fromVersion) {
    const migrations = [
      { version: '0.1.0', migrate: this.migrateTo010.bind(this) },
      { version: '1.0.0', migrate: this.migrateTo100.bind(this) }
    ];

    let migratedData = { ...data };

    for (const migration of migrations) {
      if (this.compareVersions(fromVersion, migration.version) < 0) {
        migratedData = await migration.migrate(migratedData);
      }
    }

    return migratedData;
  }

  migrateTo010(data) {
    // Migration logic for version 0.1.0
    if (data.oldApiKeyField) {
      data.apiKey = data.oldApiKeyField;
      delete data.oldApiKeyField;
    }
    return data;
  }

  migrateTo100(data) {
    // Migration logic for version 1.0.0
    if (typeof data.syncSettings === 'undefined') {
      data.syncSettings = {
        autoSync: data.autoSync || true,
        interval: data.syncInterval || 60
      };
      delete data.autoSync;
      delete data.syncInterval;
    }
    return data;
  }

  compareVersions(a, b) {
    const aParts = a.split('.').map(Number);
    const bParts = b.split('.').map(Number);

    for (let i = 0; i < Math.max(aParts.length, bParts.length); i++) {
      const aPart = aParts[i] || 0;
      const bPart = bParts[i] || 0;

      if (aPart < bPart) return -1;
      if (aPart > bPart) return 1;
    }

    return 0;
  }
}
```

## Testing Your Plugin

### Unit Testing

Create `tests/unit/plugin.test.js`:

```javascript
/**
 * Unit tests for plugin functionality
 */

const assert = require('assert');
const { PluginAPI } = require('../mocks/plugin-api');

describe('Plugin Unit Tests', () => {
  let plugin;
  let mockAPI;

  beforeEach(() => {
    mockAPI = new PluginAPI();
    plugin = new YourPlugin(mockAPI);
  });

  describe('Initialization', () => {
    it('should initialize without errors', async () => {
      await plugin.initialize();
      assert.strictEqual(plugin.isInitialized, true);
    });

    it('should register commands', async () => {
      await plugin.initialize();
      const commands = mockAPI.commands.listCommands();
      assert(commands.length > 0);
    });

    it('should load default settings', async () => {
      await plugin.initialize();
      assert(plugin.settings !== null);
      assert.strictEqual(plugin.settings.autoSync, true);
    });
  });

  describe('API Integration', () => {
    it('should validate API key format', () => {
      assert.throws(() => {
        plugin.validateApiKey('invalid');
      }, /API key too short/);
    });

    it('should handle API errors gracefully', async () => {
      mockAPI.network.fetch = async () => {
        throw new Error('Network error');
      };

      const result = await plugin.fetchData();
      assert.strictEqual(result, null);
    });
  });

  describe('Settings Management', () => {
    it('should save settings correctly', async () => {
      const settings = { apiKey: 'test-key', autoSync: false };
      await plugin.saveSettings(settings);

      const saved = await mockAPI.storage.loadData();
      assert.strictEqual(saved.apiKey, 'test-key');
      assert.strictEqual(saved.autoSync, false);
    });

    it('should validate settings before saving', async () => {
      const invalidSettings = { syncInterval: -1 };

      const result = await plugin.saveSettings(invalidSettings);
      assert.strictEqual(result, false);
    });
  });
});
```

### Integration Testing

Create `tests/integration/plugin-integration.test.js`:

```javascript
/**
 * Integration tests for plugin with real Lifeboard environment
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs').promises;

describe('Plugin Integration Tests', () => {
  let lifeboardProcess;

  before(async () => {
    // Start Lifeboard in test mode
    lifeboardProcess = spawn('npm', ['run', 'test'], {
      cwd: path.join(__dirname, '../../'),
      env: { ...process.env, NODE_ENV: 'test' }
    });

    // Wait for Lifeboard to start
    await new Promise(resolve => setTimeout(resolve, 5000));
  });

  after(async () => {
    // Cleanup
    if (lifeboardProcess) {
      lifeboardProcess.kill();
    }
  });

  it('should load plugin successfully', async () => {
    // Test plugin loading
    const response = await fetch('http://localhost:8810/plugins/your-plugin-id/status');
    const status = await response.json();

    assert.strictEqual(status.loaded, true);
    assert.strictEqual(status.error, null);
  });

  it('should register commands in command palette', async () => {
    const response = await fetch('http://localhost:8810/commands');
    const commands = await response.json();

    const pluginCommands = commands.filter(cmd =>
      cmd.id.startsWith('your-plugin-id')
    );

    assert(pluginCommands.length > 0);
  });

  it('should handle API requests correctly', async () => {
    // Trigger plugin API call
    const response = await fetch('http://localhost:8810/plugins/your-plugin-id/trigger', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ action: 'sync' })
    });

    assert.strictEqual(response.status, 200);
  });
});
```

### Mock API for Testing

Create `tests/mocks/plugin-api.js`:

```javascript
/**
 * Mock Plugin API for testing
 */

class MockPluginAPI {
  constructor() {
    this.app = new MockAppAPI();
    this.workspace = new MockWorkspaceAPI();
    this.commands = new MockCommandRegistry();
    this.events = new MockEventBus();
    this.ui = new MockUIAPI();
    this.storage = new MockStorageAPI();
    this.network = new MockNetworkAPI();
    this.manifest = {
      id: 'test-plugin',
      name: 'Test Plugin',
      version: '1.0.0',
      permissions: ['workspace', 'network']
    };
  }
}

class MockStorageAPI {
  constructor() {
    this.data = {};
  }

  async loadData() {
    return this.data;
  }

  async saveData(data) {
    this.data = { ...data };
    return true;
  }

  async clearData() {
    this.data = {};
    return true;
  }

  getStoragePath() {
    return '/tmp/test-plugin-storage';
  }
}

class MockCommandRegistry {
  constructor() {
    this.commands = new Map();
  }

  addCommand(command) {
    this.commands.set(command.id, command);
  }

  removeCommand(id) {
    this.commands.delete(id);
  }

  async executeCommand(id, ...args) {
    const command = this.commands.get(id);
    if (command) {
      return await command.callback(...args);
    }
    throw new Error(`Command not found: ${id}`);
  }

  listCommands() {
    return Array.from(this.commands.values());
  }
}

// Export for use in tests
module.exports = { MockPluginAPI };
```

### Test Runner Script

Create `tests/run-tests.sh`:

```bash
#!/bin/bash
set -euo pipefail

# Test runner for plugin
PLUGIN_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
TEST_DIR="${PLUGIN_DIR}/tests"

echo "🧪 Running Plugin Tests"
echo "Plugin: $(basename "$PLUGIN_DIR")"
echo "Test Directory: $TEST_DIR"

# Check dependencies
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is required for testing"
    exit 1
fi

# Install test dependencies
if [[ -f "${PLUGIN_DIR}/package.json" ]]; then
    echo "📦 Installing test dependencies..."
    cd "$PLUGIN_DIR"
    npm install --only=dev
fi

# Run unit tests
echo "🔍 Running unit tests..."
if [[ -d "${TEST_DIR}/unit" ]]; then
    npx mocha "${TEST_DIR}/unit/**/*.test.js" --recursive
else
    echo "⚠️  No unit tests found"
fi

# Run integration tests
echo "🔗 Running integration tests..."
if [[ -d "${TEST_DIR}/integration" ]]; then
    npx mocha "${TEST_DIR}/integration/**/*.test.js" --recursive --timeout 10000
else
    echo "⚠️  No integration tests found"
fi

# Run linting
echo "🧹 Running code quality checks..."
if command -v eslint &> /dev/null; then
    npx eslint "${PLUGIN_DIR}/**/*.js"
else
    echo "⚠️  ESLint not available, skipping linting"
fi

echo "✅ All tests completed!"
```

## Publishing to Marketplace

### Package Preparation

#### 1. Version Management

Update your `manifest.json` version following semantic versioning:

```json
{
  "version": "1.2.0"
}
```

#### 2. Documentation

Create comprehensive documentation:

**README.md**:
```markdown
# Your Plugin Name

Brief description of what your plugin does.

## Features

- Feature 1
- Feature 2
- Feature 3

## Installation

Install from the Lifeboard marketplace or manually:

1. Download the latest release
2. Extract to your plugins directory
3. Restart Lifeboard

## Configuration

1. Open Command Palette (Ctrl+Shift+P)
2. Run "Your Plugin: Configure"
3. Enter your settings

## Usage

Detailed usage instructions here.

## Support

Report issues at: https://github.com/yourusername/your-plugin/issues
```

**CHANGELOG.md**:
```markdown
# Changelog

## [1.2.0] - 2024-01-15

### Added
- New feature X
- Enhanced feature Y

### Fixed
- Bug fix A
- Bug fix B

### Changed
- Improved performance
- Updated dependencies

## [1.1.0] - 2024-01-01

### Added
- Initial marketplace release
```

#### 3. Testing

Run comprehensive tests before publishing:

```bash
# Run all tests
./tests/run-tests.sh

# Test plugin loading
npm run test:load

# Test API compatibility
npm run test:api

# Security scan
npm audit
```

### Publishing Process

#### 1. Using Plugin CLI

```bash
# Install CLI if not already installed
npm install -g lifeboard-plugin-cli

# Login to marketplace
lifeboard-plugin login

# Package plugin
lifeboard-plugin package

# Publish to marketplace
lifeboard-plugin publish
```

#### 2. Manual Publishing

```bash
# Create package
zip -r your-plugin-v1.2.0.zip \
  manifest.json \
  main.js \
  ui/ \
  src/ \
  README.md \
  CHANGELOG.md \
  -x "tests/*" "node_modules/*" ".git/*"

# Calculate checksum
sha256sum your-plugin-v1.2.0.zip > checksum.txt
```

#### 3. Marketplace Submission

Submit your plugin package through the marketplace interface:

1. **Upload Package**: Upload the ZIP file
2. **Provide Metadata**: Description, screenshots, tags
3. **Set Pricing**: Free or paid plugin
4. **Review Process**: Wait for marketplace review
5. **Publication**: Plugin becomes available

### Marketplace Metadata

Provide comprehensive metadata for better discoverability:

```json
{
  "marketplace": {
    "category": "productivity",
    "tags": ["automation", "sync", "api"],
    "screenshots": [
      "screenshots/main-interface.png",
      "screenshots/settings.png",
      "screenshots/usage.png"
    ],
    "description": "Detailed description for marketplace listing",
    "pricing": {
      "type": "free"
    },
    "support": {
      "email": "<EMAIL>",
      "website": "https://yourplugin.com/support",
      "documentation": "https://docs.yourplugin.com"
    }
  }
}
```

## Examples

### Complete Plugin Example: Task Manager

This example demonstrates a full-featured plugin:

**manifest.json**:
```json
{
  "id": "task-manager",
  "name": "Task Manager",
  "version": "1.0.0",
  "description": "Simple task management plugin for Lifeboard",
  "author": "Plugin Developer",
  "main": "main.js",
  "permissions": ["workspace", "storage"],
  "settings": {
    "defaultPriority": {
      "type": "string",
      "description": "Default task priority",
      "default": "medium",
      "options": ["low", "medium", "high"]
    },
    "autoArchive": {
      "type": "boolean",
      "description": "Auto-archive completed tasks",
      "default": true
    }
  }
}
```

**main.js**:
```javascript
/**
 * Task Manager Plugin
 * Provides simple task management functionality
 */

class TaskManagerPlugin {
  constructor(api) {
    this.api = api;
    this.tasks = [];
    this.settings = {};
  }

  async initialize() {
    // Load data
    await this.loadData();

    // Register commands
    this.registerCommands();

    // Setup UI
    this.setupUI();

    console.log('Task Manager Plugin loaded');
  }

  async loadData() {
    const data = await this.api.storage.loadData() || {};
    this.tasks = data.tasks || [];
    this.settings = data.settings || {
      defaultPriority: 'medium',
      autoArchive: true
    };
  }

  async saveData() {
    await this.api.storage.saveData({
      tasks: this.tasks,
      settings: this.settings
    });
  }

  registerCommands() {
    // Add new task command
    this.api.commands.addCommand({
      id: 'task-manager:add-task',
      name: 'Add New Task',
      callback: () => this.showAddTaskModal()
    });

    // Show task list command
    this.api.commands.addCommand({
      id: 'task-manager:show-tasks',
      name: 'Show Task List',
      callback: () => this.showTaskList()
    });

    // Mark task complete
    this.api.commands.addCommand({
      id: 'task-manager:complete-task',
      name: 'Complete Task',
      callback: () => this.showCompleteTaskModal()
    });
  }

  setupUI() {
    // Add ribbon icon
    this.api.ui.addRibbonIcon(
      'checkbox',
      'Task Manager',
      () => this.showTaskList()
    );

    // Add status bar item
    this.statusBar = this.api.ui.addStatusBarItem();
    this.updateStatusBar();
  }

  updateStatusBar() {
    const pendingTasks = this.tasks.filter(task => !task.completed).length;
    this.statusBar.setText(`Tasks: ${pendingTasks}`);
    this.statusBar.setTooltip(`${pendingTasks} pending tasks`);
  }

  showAddTaskModal() {
    const modal = {
      title: 'Add New Task',
      content: this.createAddTaskForm(),
      buttons: [
        {
          text: 'Add Task',
          style: 'primary',
          callback: () => this.addTask()
        },
        {
          text: 'Cancel',
          callback: () => {}
        }
      ]
    };

    this.api.ui.showModal(modal);
  }

  createAddTaskForm() {
    const form = document.createElement('div');
    form.innerHTML = `
      <div class="task-form">
        <div class="form-group">
          <label for="task-title">Task Title:</label>
          <input type="text" id="task-title" placeholder="Enter task title" required>
        </div>

        <div class="form-group">
          <label for="task-description">Description:</label>
          <textarea id="task-description" placeholder="Optional description"></textarea>
        </div>

        <div class="form-group">
          <label for="task-priority">Priority:</label>
          <select id="task-priority">
            <option value="low">Low</option>
            <option value="medium" selected>Medium</option>
            <option value="high">High</option>
          </select>
        </div>

        <div class="form-group">
          <label for="task-due">Due Date:</label>
          <input type="date" id="task-due">
        </div>
      </div>
    `;

    // Set default priority
    form.querySelector('#task-priority').value = this.settings.defaultPriority;

    return form;
  }

  async addTask() {
    const form = document.querySelector('.task-form');
    const title = form.querySelector('#task-title').value.trim();

    if (!title) {
      this.api.ui.showNotice('Task title is required', 3000);
      return;
    }

    const task = {
      id: Date.now().toString(),
      title,
      description: form.querySelector('#task-description').value.trim(),
      priority: form.querySelector('#task-priority').value,
      dueDate: form.querySelector('#task-due').value,
      completed: false,
      createdAt: new Date().toISOString()
    };

    this.tasks.push(task);
    await this.saveData();
    this.updateStatusBar();

    this.api.ui.showNotice('Task added successfully!', 2000);
  }

  showTaskList() {
    const leaf = this.api.workspace.createLeaf();
    leaf.openView('task-list-view');
  }

  async completeTask(taskId) {
    const task = this.tasks.find(t => t.id === taskId);
    if (task) {
      task.completed = true;
      task.completedAt = new Date().toISOString();

      await this.saveData();
      this.updateStatusBar();

      this.api.ui.showNotice('Task completed!', 2000);

      // Auto-archive if enabled
      if (this.settings.autoArchive) {
        setTimeout(() => this.archiveCompletedTasks(), 5000);
      }
    }
  }

  archiveCompletedTasks() {
    const beforeCount = this.tasks.length;
    this.tasks = this.tasks.filter(task => !task.completed);
    const archivedCount = beforeCount - this.tasks.length;

    if (archivedCount > 0) {
      this.saveData();
      this.api.ui.showNotice(`Archived ${archivedCount} completed tasks`, 2000);
    }
  }
}

// Task List View
class TaskListView {
  getViewType() {
    return 'task-list-view';
  }

  getDisplayText() {
    return 'Task List';
  }

  async onOpen() {
    this.containerEl = document.createElement('div');
    this.containerEl.className = 'task-list-view';

    this.render();
    return this.containerEl;
  }

  render() {
    const plugin = window.taskManagerPlugin; // Reference to plugin instance

    this.containerEl.innerHTML = `
      <div class="task-list-header">
        <h2>Tasks</h2>
        <button id="add-task-btn">Add Task</button>
      </div>
      <div class="task-list-content">
        ${this.renderTasks(plugin.tasks)}
      </div>
    `;

    // Add event listeners
    this.containerEl
      .querySelector('#add-task-btn')
      .addEventListener('click', () => plugin.showAddTaskModal());

    // Task completion listeners
    this.containerEl.querySelectorAll('.task-complete-btn').forEach(btn => {
      btn.addEventListener('click', (e) => {
        const taskId = e.target.dataset.taskId;
        plugin.completeTask(taskId);
        this.render(); // Re-render
      });
    });
  }

  renderTasks(tasks) {
    if (tasks.length === 0) {
      return '<p class="no-tasks">No tasks yet. Add one to get started!</p>';
    }

    return tasks
      .filter(task => !task.completed)
      .map(task => this.renderTask(task))
      .join('');
  }

  renderTask(task) {
    const priorityClass = `priority-${task.priority}`;
    const dueDate = task.dueDate ? new Date(task.dueDate).toLocaleDateString() : '';

    return `
      <div class="task-item ${priorityClass}">
        <div class="task-content">
          <h3 class="task-title">${task.title}</h3>
          ${task.description ? `<p class="task-description">${task.description}</p>` : ''}
          <div class="task-meta">
            <span class="task-priority">Priority: ${task.priority}</span>
            ${dueDate ? `<span class="task-due">Due: ${dueDate}</span>` : ''}
          </div>
        </div>
        <div class="task-actions">
          <button class="task-complete-btn" data-task-id="${task.id}">Complete</button>
        </div>
      </div>
    `;
  }
}

// Initialize plugin
const plugin = new TaskManagerPlugin(PluginAPI);
window.taskManagerPlugin = plugin; // Make available globally

// Register task list view
PluginAPI.workspace.registerViewType('task-list-view', () => new TaskListView());

plugin.initialize().catch(console.error);
```

**ui/styles.css**:
```css
/* Task Manager Plugin Styles */

.task-list-view {
  padding: 16px;
  height: 100%;
  overflow-y: auto;
}

.task-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid var(--background-modifier-border);
}

.task-list-header h2 {
  margin: 0;
  color: var(--text-normal);
}

#add-task-btn {
  background: var(--interactive-accent);
  color: var(--text-on-accent);
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
}

.task-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 12px;
  margin-bottom: 8px;
  border-radius: 6px;
  border-left: 4px solid transparent;
  background: var(--background-primary);
  border: 1px solid var(--background-modifier-border);
}

.task-item.priority-high {
  border-left-color: var(--text-error);
}

.task-item.priority-medium {
  border-left-color: var(--text-warning);
}

.task-item.priority-low {
  border-left-color: var(--text-success);
}

.task-content {
  flex: 1;
}

.task-title {
  margin: 0 0 4px 0;
  font-size: 16px;
  color: var(--text-normal);
}

.task-description {
  margin: 0 0 8px 0;
  color: var(--text-muted);
  font-size: 14px;
}

.task-meta {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: var(--text-muted);
}

.task-complete-btn {
  background: var(--text-success);
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.task-form .form-group {
  margin-bottom: 16px;
}

.task-form label {
  display: block;
  margin-bottom: 4px;
  font-weight: 500;
  color: var(--text-normal);
}

.task-form input,
.task-form textarea,
.task-form select {
  width: 100%;
  padding: 8px;
  border: 1px solid var(--background-modifier-border);
  border-radius: 4px;
  background: var(--background-primary);
  color: var(--text-normal);
}

.task-form textarea {
  height: 80px;
  resize: vertical;
}

.no-tasks {
  text-align: center;
  color: var(--text-muted);
  font-style: italic;
  padding: 40px 20px;
}
```

## Best Practices

### 1. Code Organization

```javascript
// Use clear class structure
class PluginName {
  constructor(api) {
    this.api = api;
    this.initialized = false;
    this.cleanup = [];
  }

  // Separate concerns into methods
  async initialize() {
    await this.loadConfiguration();
    await this.registerComponents();
    await this.setupEventListeners();
    this.initialized = true;
  }

  // Always provide cleanup
  async destroy() {
    this.cleanup.forEach(fn => fn());
    this.cleanup = [];
    this.initialized = false;
  }
}
```

### 2. Error Handling

```javascript
// Comprehensive error handling
async function safeOperation() {
  try {
    const result = await riskyOperation();
    return { success: true, data: result };
  } catch (error) {
    this.logger.error('Operation failed', error);

    // Provide user-friendly error messages
    this.api.ui.showNotice('Operation failed. Please try again.', 5000);

    return { success: false, error: error.message };
  }
}
```

### 3. Performance Optimization

```javascript
// Use debouncing for frequent operations
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

// Cache expensive operations
class DataManager {
  constructor() {
    this.cache = new Map();
    this.cacheTimeout = 5 * 60 * 1000; // 5 minutes
  }

  async getData(key) {
    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.data;
    }

    const data = await this.fetchData(key);
    this.cache.set(key, {
      data,
      timestamp: Date.now()
    });

    return data;
  }
}
```

### 4. User Experience

```javascript
// Provide visual feedback
class ProgressIndicator {
  constructor(api) {
    this.api = api;
    this.statusBar = null;
  }

  start(message) {
    this.statusBar = this.api.ui.addStatusBarItem();
    this.statusBar.setText(`⏳ ${message}`);
  }

  update(message) {
    if (this.statusBar) {
      this.statusBar.setText(`⏳ ${message}`);
    }
  }

  complete(message) {
    if (this.statusBar) {
      this.statusBar.setText(`✅ ${message}`);
      setTimeout(() => {
        this.statusBar.remove();
        this.statusBar = null;
      }, 2000);
    }
  }

  error(message) {
    if (this.statusBar) {
      this.statusBar.setText(`❌ ${message}`);
      setTimeout(() => {
        this.statusBar.remove();
        this.statusBar = null;
      }, 5000);
    }
  }
}
```

### 5. Security Considerations

```javascript
// Input sanitization
function sanitizeHTML(str) {
  const temp = document.createElement('div');
  temp.textContent = str;
  return temp.innerHTML;
}

// Secure API key handling
class SecureStorage {
  async storeApiKey(key) {
    // Never log sensitive data
    this.logger.info('Storing API key', { keyLength: key.length });

    // Encrypt before storage
    const encrypted = await this.encrypt(key);
    await this.api.storage.saveData({ apiKey: encrypted });
  }

  async validateInput(input) {
    if (typeof input !== 'string') {
      throw new Error('Invalid input type');
    }

    if (input.length > 1000) {
      throw new Error('Input too long');
    }

    // Check for injection attempts
    if (/<script|javascript:|data:/i.test(input)) {
      throw new Error('Invalid input detected');
    }

    return true;
  }
}
```

## Troubleshooting

### Common Issues

#### 1. Plugin Not Loading

**Symptoms**:
- Plugin doesn't appear in command palette
- No error messages in console

**Solutions**:
```bash
# Check manifest syntax
cat manifest.json | jq '.'

# Verify file permissions
ls -la main.js

# Check plugin directory location
echo $LIFEBOARD_PLUGINS_PATH

# View plugin logs
tail -f logs/plugin-loader.log
```

#### 2. Permission Errors

**Symptoms**:
- API methods return undefined
- Network requests fail silently

**Solutions**:
```javascript
// Check permissions in manifest
{
  "permissions": ["network", "workspace"]
}

// Verify permission availability
if (this.api.network.fetch) {
  // Network permission granted
} else {
  console.log('Network permission not available');
}
```

#### 3. Storage Issues

**Symptoms**:
- Settings not persisting
- Data loss between sessions

**Solutions**:
```javascript
// Ensure proper error handling
try {
  await this.api.storage.saveData(data);
} catch (error) {
  console.error('Storage error:', error);
  // Implement fallback storage
}

// Validate data before saving
function validateData(data) {
  if (!data || typeof data !== 'object') {
    throw new Error('Invalid data format');
  }
  return true;
}
```

#### 4. Memory Leaks

**Symptoms**:
- Increasing memory usage over time
- Sluggish performance

**Solutions**:
```javascript
// Proper cleanup
class PluginWithCleanup {
  constructor(api) {
    this.api = api;
    this.intervals = [];
    this.listeners = [];
  }

  addInterval(callback, delay) {
    const id = setInterval(callback, delay);
    this.intervals.push(id);
    return id;
  }

  addEventListener(element, event, callback) {
    element.addEventListener(event, callback);
    this.listeners.push({ element, event, callback });
  }

  cleanup() {
    // Clear intervals
    this.intervals.forEach(id => clearInterval(id));
    this.intervals = [];

    // Remove event listeners
    this.listeners.forEach(({ element, event, callback }) => {
      element.removeEventListener(event, callback);
    });
    this.listeners = [];
  }
}
```

### Debugging Tools

#### 1. Console Logging

```javascript
// Structured logging
class Logger {
  constructor(pluginId) {
    this.pluginId = pluginId;
    this.logLevel = 'info'; // debug, info, warn, error
  }

  debug(message, data = {}) {
    if (this.logLevel === 'debug') {
      console.log(`[${this.pluginId}] DEBUG:`, message, data);
    }
  }

  info(message, data = {}) {
    console.log(`[${this.pluginId}] INFO:`, message, data);
  }

  warn(message, data = {}) {
    console.warn(`[${this.pluginId}] WARN:`, message, data);
  }

  error(message, error = null, data = {}) {
    console.error(`[${this.pluginId}] ERROR:`, message, error, data);
  }
}
```

#### 2. Development Mode

```javascript
// Development helpers
class DevTools {
  static isDevMode() {
    return process.env.NODE_ENV === 'development';
  }

  static addDebugInfo(api) {
    if (!this.isDevMode()) return;

    // Add debug commands
    api.commands.addCommand({
      id: 'debug-plugin-state',
      name: 'Debug: Show Plugin State',
      callback: () => {
        console.log('Plugin State:', this.getState());
      }
    });

    // Add debug UI
    const debugInfo = document.createElement('div');
    debugInfo.style.cssText = `
      position: fixed;
      top: 10px;
      right: 10px;
      background: rgba(0,0,0,0.8);
      color: white;
      padding: 10px;
      font-family: monospace;
      font-size: 12px;
      z-index: 9999;
    `;
    debugInfo.innerHTML = `Plugin: ${api.manifest.name}<br>Version: ${api.manifest.version}`;
    document.body.appendChild(debugInfo);
  }
}
```

### Getting Help

1. **Check Documentation**: Review this guide and API reference
2. **Search Issues**: Look for similar problems in plugin repository
3. **Enable Debug Mode**: Use development tools for detailed logging
4. **Community Support**: Ask questions in Lifeboard developer forums
5. **Report Bugs**: Submit detailed bug reports with reproduction steps

---

**Document Version**: 1.0.0
**Last Updated**: July 5, 2025
**Compatible with**: Lifeboard 0.1.0+
**Plugin API Version**: 1.0.0

For the latest updates and additional examples, visit the [Lifeboard Plugin Development Repository](https://github.com/lifeboard/plugin-development).
