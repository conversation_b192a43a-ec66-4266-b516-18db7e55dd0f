# Lifeboard Dependencies

This file lists the core and recommended dependencies for the Lifeboard project, along with installation instructions.

## Core Dependencies

- **git**: Version control (`brew install git`)
- **docker**: Containerization (`brew install --cask docker`)
- **docker-compose**: Multi-container orchestration (`brew install docker-compose`)

## Development Tools

- **node**: JavaScript runtime (`brew install node`)
- **python**: Python for scripting (`brew install python`)
- **go**: Go programming language (`brew install go`)

## Security Tools

- **trufflehog**: Secret detection (`brew install trufflesecurity/trufflehog/trufflehog`)
- **pre-commit**: Git hooks for code quality (`pip install pre-commit`)

## Optional Tools

- **vscode**: Code editor (`brew install --cask visual-studio-code`)

Consult the [Developer Guide](./supporting_documents/Developer_Guide.md) for a complete setup and configuration.
