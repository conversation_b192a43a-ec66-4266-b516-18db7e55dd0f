# Lifeboard API Documentation

## Overview

The Lifeboard API provides a comprehensive REST interface built on Supabase's PostgREST, offering automatic API generation from PostgreSQL schemas with built-in authentication, real-time capabilities, and robust security.

## Base URLs

| Environment | Base URL | Description |
|-------------|----------|-------------|
| Development | `http://localhost:8810` | Local development API |
| Production | `https://your-domain.com` | Production API endpoint |

## Authentication

Lifeboard uses JWT-based authentication powered by Supabase GoTrue.

### Authentication Headers

All authenticated requests require the following headers:

```http
Authorization: Bearer <JWT_TOKEN>
apikey: <ANON_KEY>
Content-Type: application/json
```

### Authentication Endpoints

#### Sign Up
```http
POST /auth/v1/signup
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "secure_password"
}
```

**Response:**
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer",
  "expires_in": 3600,
  "refresh_token": "...",
  "user": {
    "id": "uuid",
    "email": "<EMAIL>",
    "created_at": "2025-07-02T02:09:14Z"
  }
}
```

#### Sign In
```http
POST /auth/v1/token?grant_type=password
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "secure_password"
}
```

#### Sign Out
```http
POST /auth/v1/logout
Authorization: Bearer <JWT_TOKEN>
```

#### Refresh Token
```http
POST /auth/v1/token?grant_type=refresh_token
Content-Type: application/json

{
  "refresh_token": "<REFRESH_TOKEN>"
}
```

### Multi-Factor Authentication (MFA)

#### Enroll MFA Factor
```http
POST /auth/v1/factors
Authorization: Bearer <JWT_TOKEN>
Content-Type: application/json

{
  "factor_type": "totp",
  "friendly_name": "My Authenticator"
}
```

#### Verify MFA Factor
```http
POST /auth/v1/factors/{factor_id}/verify
Authorization: Bearer <JWT_TOKEN>
Content-Type: application/json

{
  "code": "123456"
}
```

## REST API Endpoints

The REST API follows PostgREST conventions with automatic CRUD operations.

### Generic Operations

#### List Records
```http
GET /rest/v1/{table_name}
Authorization: Bearer <JWT_TOKEN>
apikey: <ANON_KEY>
```

**Query Parameters:**
- `select`: Specify columns to return
- `limit`: Limit number of results
- `offset`: Pagination offset
- `order`: Sort order
- `filter`: Apply filters

**Example:**
```http
GET /rest/v1/posts?select=id,title,created_at&limit=10&order=created_at.desc
```

#### Create Record
```http
POST /rest/v1/{table_name}
Authorization: Bearer <JWT_TOKEN>
apikey: <ANON_KEY>
Content-Type: application/json
Prefer: return=representation

{
  "title": "New Post",
  "content": "Post content here"
}
```

#### Update Record
```http
PATCH /rest/v1/{table_name}?id=eq.{record_id}
Authorization: Bearer <JWT_TOKEN>
apikey: <ANON_KEY>
Content-Type: application/json
Prefer: return=representation

{
  "title": "Updated Title"
}
```

#### Delete Record
```http
DELETE /rest/v1/{table_name}?id=eq.{record_id}
Authorization: Bearer <JWT_TOKEN>
apikey: <ANON_KEY>
```

### Filtering and Queries

#### Equality Filters
```http
GET /rest/v1/posts?status=eq.published
GET /rest/v1/posts?author_id=eq.123
```

#### Comparison Filters
```http
GET /rest/v1/posts?created_at=gte.2025-01-01
GET /rest/v1/posts?view_count=lt.100
```

#### Text Search
```http
GET /rest/v1/posts?title=ilike.*search*
GET /rest/v1/posts?content=fts.keyword
```

#### Multiple Filters
```http
GET /rest/v1/posts?status=eq.published&created_at=gte.2025-01-01&order=created_at.desc
```

### Relationships and Joins

#### Simple Joins
```http
GET /rest/v1/posts?select=title,author:profiles(name,avatar_url)
```

#### Nested Resources
```http
GET /rest/v1/profiles?select=name,posts(title,created_at)
```

#### Many-to-Many Relationships
```http
GET /rest/v1/posts?select=title,tags:post_tags(tag:tags(name))
```

## Real-time API

Lifeboard supports real-time subscriptions via WebSocket connections.

### WebSocket Connection

```javascript
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  'http://localhost:8810',
  'your-anon-key'
)

// Subscribe to changes
const subscription = supabase
  .channel('posts')
  .on('postgres_changes',
    { event: '*', schema: 'public', table: 'posts' },
    (payload) => {
      console.log('Change received!', payload)
    }
  )
  .subscribe()
```

### Real-time Events

- **INSERT**: New record created
- **UPDATE**: Record modified
- **DELETE**: Record removed

### Real-time Filters

```javascript
// Listen to specific user's posts
supabase
  .channel('user-posts')
  .on('postgres_changes',
    {
      event: '*',
      schema: 'public',
      table: 'posts',
      filter: 'author_id=eq.123'
    },
    handleChange
  )
  .subscribe()
```

## Storage API

File upload and management capabilities.

### Upload File

```http
POST /storage/v1/object/{bucket_name}/{file_path}
Authorization: Bearer <JWT_TOKEN>
Content-Type: multipart/form-data

file: <binary_data>
```

### Download File

```http
GET /storage/v1/object/{bucket_name}/{file_path}
Authorization: Bearer <JWT_TOKEN>
```

### Delete File

```http
DELETE /storage/v1/object/{bucket_name}/{file_path}
Authorization: Bearer <JWT_TOKEN>
```

### List Files

```http
GET /storage/v1/object/list/{bucket_name}
Authorization: Bearer <JWT_TOKEN>
```

## Edge Functions

Custom serverless functions for complex business logic.

### Invoke Function

```http
POST /functions/v1/{function_name}
Authorization: Bearer <JWT_TOKEN>
Content-Type: application/json

{
  "param1": "value1",
  "param2": "value2"
}
```

## Error Handling

### HTTP Status Codes

| Code | Description |
|------|-------------|
| 200 | Success |
| 201 | Created |
| 204 | No Content |
| 400 | Bad Request |
| 401 | Unauthorized |
| 403 | Forbidden |
| 404 | Not Found |
| 409 | Conflict |
| 422 | Unprocessable Entity |
| 500 | Internal Server Error |

### Error Response Format

```json
{
  "code": "error_code",
  "message": "Human readable error message",
  "details": "Additional error details",
  "hint": "Suggestion for fixing the error"
}
```

### Common Errors

#### Authentication Errors
```json
{
  "code": "401",
  "message": "JWT expired",
  "details": "The provided JWT token has expired"
}
```

#### Validation Errors
```json
{
  "code": "422",
  "message": "Validation failed",
  "details": "email: must be a valid email address"
}
```

#### Rate Limiting
```json
{
  "code": "429",
  "message": "Too many requests",
  "details": "Rate limit exceeded. Try again later."
}
```

## Rate Limiting

API requests are rate-limited to ensure fair usage:

- **Anonymous requests**: 100 requests per hour
- **Authenticated requests**: 1000 requests per hour
- **Bulk operations**: 50 requests per minute

### Rate Limit Headers

```http
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1640995200
```

## Security

### Row Level Security (RLS)

All tables have Row Level Security enabled by default:

```sql
-- Example RLS Policy
CREATE POLICY "Users can only see their own posts"
  ON posts FOR SELECT
  USING (auth.uid() = author_id);
```

### API Key Security

- **Anon Key**: Public key for client-side usage
- **Service Role Key**: Admin key for server-side operations
- **JWT Secret**: Used for token verification

### CORS Configuration

CORS is configured to allow requests from:
- `http://localhost:3000` (development)
- `https://your-domain.com` (production)

## Client Libraries

### JavaScript/TypeScript

```bash
npm install @supabase/supabase-js
```

```javascript
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  'http://localhost:8810',
  'your-anon-key'
)

// Example usage
const { data, error } = await supabase
  .from('posts')
  .select('*')
  .eq('status', 'published')
```

### Python

```bash
pip install supabase
```

```python
from supabase import create_client

supabase = create_client(
    "http://localhost:8810",
    "your-anon-key"
)

# Example usage
response = supabase.table('posts').select('*').eq('status', 'published').execute()
```

### cURL Examples

#### Get Posts
```bash
curl -X GET \
  'http://localhost:8810/rest/v1/posts' \
  -H 'Authorization: Bearer <JWT_TOKEN>' \
  -H 'apikey: <ANON_KEY>'
```

#### Create Post
```bash
curl -X POST \
  'http://localhost:8810/rest/v1/posts' \
  -H 'Authorization: Bearer <JWT_TOKEN>' \
  -H 'apikey: <ANON_KEY>' \
  -H 'Content-Type: application/json' \
  -H 'Prefer: return=representation' \
  -d '{
    "title": "My New Post",
    "content": "This is the content"
  }'
```

## Performance Optimization

### Connection Pooling

PgBouncer is configured for connection pooling:
- **Pool Size**: 25 connections
- **Pool Mode**: Transaction
- **Max Client Connections**: 100

### Caching

- **Query Result Caching**: Enabled for GET requests
- **Schema Caching**: Automatic schema introspection caching
- **CDN Integration**: Ready for CloudFlare integration

### Monitoring

Monitor API performance via:
- **Health Endpoints**: `/health`, `/ready`
- **Metrics**: Prometheus metrics available
- **Logging**: Structured JSON logs for analysis

## Development Tools

### API Testing

Use the provided test scripts:

```bash
# Test API endpoints
./tests/test_crud_integration.sh

# Performance testing
./tests/test_performance.sh
```

### Schema Introspection

```http
GET /rest/v1/
Authorization: Bearer <JWT_TOKEN>
apikey: <ANON_KEY>
Accept: application/openapi+json
```

### Database Schema

View current schema:
```bash
docker exec -it lifeboard-db-1 psql -U supabase_admin -d postgres -c "\dt"
```

## Troubleshooting

### Common Issues

#### Connection Refused
- Check if services are running: `docker compose ps`
- Verify port configuration in `.env.local`
- Ensure network connectivity

#### Authentication Failures
- Verify JWT token validity
- Check API key configuration
- Confirm user permissions

#### Rate Limiting
- Implement exponential backoff
- Use connection pooling
- Cache frequently accessed data

### Debug Logging

Enable debug logging:
```bash
# Set environment variable
export PGRST_LOG_LEVEL=debug

# View real-time logs
docker compose logs -f rest
```

## Support

For API support:
- **Documentation**: This guide and supporting documents
- **Issues**: GitHub Issues for bug reports
- **Community**: GitHub Discussions for questions

---

**API Version**: 1.0.0
**Last Updated**: 2025-07-02
**PostgREST Version**: 12.2.0
