# Lifeboard Production Deployment Guide

## Overview

This guide provides comprehensive instructions for deploying Lifeboard to production environments with security, performance, and reliability best practices.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Environment Setup](#environment-setup)
3. [Security Configuration](#security-configuration)
4. [Infrastructure Deployment](#infrastructure-deployment)
5. [SSL/TLS Configuration](#ssltls-configuration)
6. [Database Configuration](#database-configuration)
7. [Monitoring & Observability](#monitoring--observability)
8. [Backup Strategy](#backup-strategy)
9. [Performance Optimization](#performance-optimization)
10. [Troubleshooting](#troubleshooting)

## Prerequisites

### System Requirements

#### Minimum Requirements
- **CPU**: 2 cores
- **RAM**: 4GB
- **Storage**: 50GB SSD
- **Network**: 100 Mbps

#### Recommended Requirements
- **CPU**: 4+ cores
- **RAM**: 8GB+
- **Storage**: 100GB+ SSD with backup storage
- **Network**: 1 Gbps with redundancy

### Software Dependencies

```bash
# Required software
Docker Engine 20.10+
Docker Compose 2.0+
Git 2.30+

# Recommended tools
htop, iotop, nethogs  # System monitoring
fail2ban              # Intrusion prevention
ufw                   # Firewall management
certbot               # SSL certificate management
```

### Domain and DNS Setup

1. **Domain Configuration**:
   - Primary domain: `your-domain.com`
   - API subdomain: `api.your-domain.com`
   - Admin subdomain: `admin.your-domain.com`

2. **DNS Records**:
   ```
   A     your-domain.com        -> SERVER_IP
   A     api.your-domain.com    -> SERVER_IP
   A     admin.your-domain.com  -> SERVER_IP
   CNAME www.your-domain.com    -> your-domain.com
   ```

## Environment Setup

### 1. Server Preparation

#### Update System
```bash
# Ubuntu/Debian
sudo apt update && sudo apt upgrade -y
sudo apt install -y curl wget git htop ufw fail2ban

# CentOS/RHEL
sudo yum update -y
sudo yum install -y curl wget git htop
```

#### Install Docker
```bash
# Official Docker installation
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER
sudo systemctl enable docker
sudo systemctl start docker
```

#### Configure Firewall
```bash
# Configure UFW (Ubuntu)
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw --force enable
```

### 2. Clone and Configure Repository

```bash
# Clone repository
git clone https://github.com/bbookman/lifeboard-supabase.git
cd lifeboard-supabase

# Create production environment file
cp .env.example .env.production
```

### 3. Production Environment Configuration

Edit `.env.production` with production values:

```bash
# Database Configuration
POSTGRES_PASSWORD="$(openssl rand -base64 32)"
POSTGRES_USER="supabase_admin"
POSTGRES_DB="postgres"
POSTGRES_PORT=5432

# JWT Configuration
JWT_SECRET="$(openssl rand -base64 64)"
ANON_KEY="$(openssl rand -base64 32)"
SERVICE_ROLE_KEY="$(openssl rand -base64 32)"

# API Configuration
API_URL="https://api.your-domain.com"
PUBLIC_REST_URL="https://api.your-domain.com/rest/v1"
PUBLIC_REALTIME_URL="wss://api.your-domain.com/realtime/v1"
PUBLIC_STORAGE_URL="https://api.your-domain.com/storage/v1"

# Security
SITE_URL="https://your-domain.com"
ADDITIONAL_REDIRECT_URLS="https://admin.your-domain.com"
DISABLE_SIGNUP=false
MAILER_SECURE_EMAIL_CHANGE_ENABLED=true
SECURITY_UPDATE_PASSWORD_REQUIRE_REAUTHENTICATION=true

# SMTP Configuration (for production emails)
SMTP_HOST="your-smtp-server.com"
SMTP_PORT=587
SMTP_USER="<EMAIL>"
SMTP_PASS="smtp-password"
SMTP_ADMIN_EMAIL="<EMAIL>"
SMTP_SENDER_NAME="Lifeboard"

# Storage Configuration
STORAGE_BACKEND="file"
GLOBAL_S3_BUCKET="your-s3-bucket"
AWS_ACCESS_KEY_ID="your-access-key"
AWS_SECRET_ACCESS_KEY="your-secret-key"
AWS_DEFAULT_REGION="us-east-1"

# Analytics & Monitoring
ENABLE_LOGS=true
LOG_LEVEL="info"
```

## Security Configuration

### 1. Secrets Management

#### Generate Secure Secrets
```bash
# Generate JWT secret
echo "JWT_SECRET=$(openssl rand -base64 64)" >> .env.production

# Generate API keys
echo "ANON_KEY=$(openssl rand -base64 32)" >> .env.production
echo "SERVICE_ROLE_KEY=$(openssl rand -base64 32)" >> .env.production

# Generate database password
echo "POSTGRES_PASSWORD=$(openssl rand -base64 32)" >> .env.production
```

#### Secure File Permissions
```bash
# Restrict environment file access
chmod 600 .env.production
chown root:root .env.production

# Secure sensitive directories
chmod 700 volumes/
```

### 2. Database Security

#### Configure PostgreSQL Security
```sql
-- Connect to database
psql postgresql://supabase_admin:password@localhost:5432/postgres

-- Enable additional security features
ALTER SYSTEM SET log_statement = 'all';
ALTER SYSTEM SET log_min_duration_statement = 1000;
ALTER SYSTEM SET shared_preload_libraries = 'pg_stat_statements';

-- Restart required after these changes
```

#### Row Level Security (RLS)
```sql
-- Enable RLS on all user tables
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE posts ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_data ENABLE ROW LEVEL SECURITY;

-- Create security policies
CREATE POLICY "Users can only access own data"
ON profiles FOR ALL
USING (auth.uid() = user_id);
```

### 3. Container Security

#### Security Contexts
All containers run with restricted security contexts:
- No new privileges
- Dropped capabilities (ALL)
- Read-only filesystems where possible
- Non-root users

#### Network Security
```yaml
# Production docker-compose override
networks:
  lifeboard_net:
    driver: bridge
    internal: false
    enable_ipv6: false
    ipam:
      config:
        - subnet: **********/16
```

## Infrastructure Deployment

### 1. Production Docker Compose Override

Create `docker-compose.production.yml`:

```yaml
version: '3.8'

services:
  db:
    restart: always
    volumes:
      - /opt/lifeboard/db_data:/var/lib/postgresql/data
      - /opt/lifeboard/backups:/backups
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "5"

  auth:
    restart: always
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "5"

  rest:
    restart: always
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "5"

  realtime:
    restart: always
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "5"

  storage:
    restart: always
    volumes:
      - /opt/lifeboard/storage:/var/lib/storage
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "5"

volumes:
  db_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/lifeboard/db_data
```

### 2. Deploy Production Stack

```bash
# Create production directories
sudo mkdir -p /opt/lifeboard/{db_data,storage,backups,logs}
sudo chown -R $(id -u):$(id -g) /opt/lifeboard

# Deploy with production configuration
./scripts/deploy.sh \
  --profile production \
  --env-file .env.production \
  --compose-file docker-compose.yml,docker-compose.production.yml \
  --health-check \
  --verbose
```

### 3. Verify Deployment

```bash
# Check service health
docker compose ps

# Test API endpoints
curl -f https://api.your-domain.com/health
curl -f https://api.your-domain.com/rest/v1/

# Check logs
docker compose logs --tail=50
```

## SSL/TLS Configuration

### 1. Obtain SSL Certificates

#### Using Certbot (Let's Encrypt)
```bash
# Install certbot
sudo apt install certbot python3-certbot-nginx

# Obtain certificates
sudo certbot certonly --standalone \
  -d your-domain.com \
  -d api.your-domain.com \
  -d admin.your-domain.com
```

#### Using Custom Certificates
```bash
# Copy certificates to secure location
sudo mkdir -p /etc/ssl/lifeboard
sudo cp your-domain.com.crt /etc/ssl/lifeboard/
sudo cp your-domain.com.key /etc/ssl/lifeboard/
sudo chmod 600 /etc/ssl/lifeboard/*
```

### 2. Configure Reverse Proxy

#### Nginx Configuration
Create `/etc/nginx/sites-available/lifeboard`:

```nginx
# API Endpoint
server {
    listen 443 ssl http2;
    server_name api.your-domain.com;

    ssl_certificate /etc/letsencrypt/live/api.your-domain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/api.your-domain.com/privkey.pem;

    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";

    # API proxy
    location / {
        proxy_pass http://localhost:8810;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # WebSocket support
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
}

# Redirect HTTP to HTTPS
server {
    listen 80;
    server_name api.your-domain.com;
    return 301 https://$server_name$request_uri;
}
```

Enable the configuration:
```bash
sudo ln -s /etc/nginx/sites-available/lifeboard /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

## Database Configuration

### 1. Production Database Settings

#### PostgreSQL Configuration
Edit `postgresql.conf` for production:

```ini
# Connection settings
max_connections = 200
shared_buffers = 256MB
effective_cache_size = 1GB
work_mem = 4MB
maintenance_work_mem = 64MB

# WAL settings
wal_level = replica
max_wal_size = 1GB
min_wal_size = 80MB
checkpoint_completion_target = 0.9

# Logging
log_destination = 'stderr'
log_statement = 'mod'
log_min_duration_statement = 1000
log_line_prefix = '%t [%p]: [%l-1] user=%u,db=%d,app=%a,client=%h '

# Security
ssl = on
ssl_cert_file = 'server.crt'
ssl_key_file = 'server.key'
```

#### Connection Pooling
Configure PgBouncer for production:

```ini
# /etc/pgbouncer/pgbouncer.ini
[databases]
postgres = host=localhost port=5432 dbname=postgres

[pgbouncer]
pool_mode = transaction
listen_port = 6432
listen_addr = localhost
auth_type = md5
auth_file = /etc/pgbouncer/userlist.txt
logfile = /var/log/pgbouncer/pgbouncer.log
pidfile = /var/run/pgbouncer/pgbouncer.pid
admin_users = postgres
max_client_conn = 100
default_pool_size = 25
server_lifetime = 3600
server_idle_timeout = 600
```

### 2. Database Maintenance

#### Automated Backups
Create backup script `/opt/lifeboard/scripts/backup.sh`:

```bash
#!/bin/bash

# Configuration
BACKUP_DIR="/opt/lifeboard/backups"
DB_NAME="postgres"
DB_USER="supabase_admin"
RETENTION_DAYS=30

# Create backup
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="$BACKUP_DIR/lifeboard_backup_$TIMESTAMP.sql"

# Perform backup
pg_dump -h localhost -U $DB_USER -d $DB_NAME > $BACKUP_FILE

# Compress backup
gzip $BACKUP_FILE

# Remove old backups
find $BACKUP_DIR -name "*.sql.gz" -mtime +$RETENTION_DAYS -delete

# Log backup completion
echo "$(date): Backup completed: $BACKUP_FILE.gz" >> /var/log/lifeboard-backup.log
```

#### Set up Cron Job
```bash
# Add to crontab
0 2 * * * /opt/lifeboard/scripts/backup.sh
```

## Monitoring & Observability

### 1. Production Monitoring Stack

Deploy with observability profile:
```bash
./scripts/deploy.sh \
  --profile production \
  --env-file .env.production \
  --health-check
```

### 2. Grafana Configuration

#### Access Grafana
- URL: `https://admin.your-domain.com/grafana`
- Default credentials: admin/admin (change immediately)

#### Import Dashboards
1. PostgreSQL Dashboard (ID: 9628)
2. Docker Container Dashboard (ID: 193)
3. Nginx Dashboard (ID: 12559)

### 3. Log Management

#### Centralized Logging
Configure log forwarding to external services:

```yaml
# docker-compose.logging.yml override
services:
  promtail:
    environment:
      - LOKI_URL=https://logs.your-domain.com/loki/api/v1/push
```

#### Log Retention
```bash
# Configure logrotate
cat > /etc/logrotate.d/lifeboard << EOF
/opt/lifeboard/logs/*.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 644 root root
}
EOF
```

## Backup Strategy

### 1. Database Backups

#### Full Backups
- **Frequency**: Daily at 2 AM
- **Retention**: 30 days local, 90 days remote
- **Location**: `/opt/lifeboard/backups`

#### Point-in-Time Recovery
Enable WAL archiving:
```bash
# Add to postgresql.conf
archive_mode = on
archive_command = 'test ! -f /opt/lifeboard/wal-archive/%f && cp %p /opt/lifeboard/wal-archive/%f'
```

### 2. File System Backups

#### Storage Volumes
```bash
# Backup script for volumes
rsync -av /opt/lifeboard/storage/ /backup/storage/
rsync -av /opt/lifeboard/db_data/ /backup/db_data/
```

#### Configuration Backups
```bash
# Backup configurations
tar -czf /backup/config_$(date +%Y%m%d).tar.gz \
  /opt/lifeboard/.env.production \
  /opt/lifeboard/docker-compose*.yml \
  /etc/nginx/sites-available/lifeboard
```

### 3. Remote Backup Storage

#### AWS S3 Backup
```bash
# Install AWS CLI
pip install awscli

# Sync backups to S3
aws s3 sync /opt/lifeboard/backups/ s3://your-backup-bucket/lifeboard/
```

## Performance Optimization

### 1. Database Performance

#### Index Optimization
```sql
-- Monitor slow queries
SELECT query, mean_time, calls, total_time
FROM pg_stat_statements
ORDER BY total_time DESC
LIMIT 10;

-- Add performance indexes
CREATE INDEX CONCURRENTLY idx_posts_author_created
ON posts(author_id, created_at DESC);

CREATE INDEX CONCURRENTLY idx_users_email_active
ON users(email) WHERE active = true;
```

#### Connection Pooling Tuning
```ini
# Adjust based on load
default_pool_size = 25
max_client_conn = 100
pool_mode = transaction
```

### 2. Application Performance

#### Caching Strategy
- **Redis**: Session and query caching
- **CDN**: Static asset delivery
- **Browser Caching**: Client-side caching headers

#### Rate Limiting
```nginx
# Nginx rate limiting
limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;

location /rest/v1/ {
    limit_req zone=api burst=20 nodelay;
    proxy_pass http://localhost:8810;
}
```

### 3. Infrastructure Scaling

#### Horizontal Scaling
- **Load Balancer**: Nginx or HAProxy
- **Database Replicas**: Read replicas for scaling
- **Container Orchestration**: Docker Swarm or Kubernetes

#### Vertical Scaling
- **CPU**: Scale based on request volume
- **Memory**: Monitor PostgreSQL memory usage
- **Storage**: SSD with adequate IOPS

## Troubleshooting

### 1. Common Issues

#### Service Health Issues
```bash
# Check service status
docker compose ps

# View service logs
docker compose logs service-name

# Check resource usage
docker stats

# Monitor system resources
htop
iotop
```

#### Database Connection Issues
```bash
# Test database connectivity
docker exec -it lifeboard-db-1 psql -U supabase_admin -d postgres -c "SELECT version();"

# Check connection pool
docker exec -it lifeboard-db-1 psql -U supabase_admin -d postgres -c "SELECT * FROM pg_stat_activity;"

# Monitor database locks
docker exec -it lifeboard-db-1 psql -U supabase_admin -d postgres -c "SELECT * FROM pg_locks WHERE NOT granted;"
```

#### SSL Certificate Issues
```bash
# Check certificate validity
openssl x509 -in /etc/letsencrypt/live/api.your-domain.com/cert.pem -text -noout

# Test SSL configuration
curl -I https://api.your-domain.com

# Renew certificates
sudo certbot renew --dry-run
```

### 2. Performance Issues

#### Database Performance
```sql
-- Check slow queries
SELECT query, mean_time, calls
FROM pg_stat_statements
ORDER BY mean_time DESC
LIMIT 10;

-- Check database size
SELECT
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;
```

#### Application Performance
```bash
# Monitor API response times
curl -w "@curl-format.txt" -s -o /dev/null https://api.your-domain.com/rest/v1/posts

# Check container resource usage
docker stats --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}"
```

### 3. Security Incident Response

#### Log Analysis
```bash
# Check authentication failures
grep "authentication failed" /var/log/postgresql/postgresql.log

# Monitor suspicious API access
docker compose logs auth | grep -i "failed\|error\|unauthorized"

# Check for intrusion attempts
sudo tail -f /var/log/fail2ban.log
```

#### Emergency Procedures
1. **Isolate affected services**
2. **Enable maintenance mode**
3. **Preserve logs and evidence**
4. **Apply security patches**
5. **Restore from clean backup if necessary**

## Maintenance Procedures

### 1. Routine Maintenance

#### Weekly Tasks
- Review security logs
- Check backup integrity
- Monitor resource usage
- Update system packages

#### Monthly Tasks
- Security patching
- Certificate renewal check
- Database maintenance (VACUUM, ANALYZE)
- Capacity planning review

### 2. Update Procedures

#### Application Updates
```bash
# Backup current deployment
./scripts/backup_deployment.sh

# Pull latest changes
git pull origin main

# Deploy updates
./scripts/deploy.sh --profile production --env-file .env.production

# Verify deployment
./tests/test_health_checks.sh
```

#### Security Updates
```bash
# System updates
sudo apt update && sudo apt upgrade -y

# Docker image updates
docker compose pull
docker compose up -d

# Restart services if required
docker compose restart
```

## Support and Escalation

### 1. Monitoring Alerts

Configure alerts for:
- **Service downtime** (> 1 minute)
- **High resource usage** (> 80%)
- **Failed authentication attempts** (> 10/minute)
- **Database connection failures**
- **SSL certificate expiration** (< 30 days)

### 2. Contact Information

- **Primary Administrator**: <EMAIL>
- **Technical Support**: <EMAIL>
- **Emergency Contact**: +1-XXX-XXX-XXXX

### 3. Documentation References

- [API Documentation](API_Documentation.md)
- [Security Guide](Security_Guide.md)
- [Operations Manual](Operations_Manual.md)
- [GitHub Repository](https://github.com/bbookman/lifeboard-supabase)

---

**Document Version**: 1.0.0
**Last Updated**: 2025-07-02
**Review Date**: 2025-08-02
