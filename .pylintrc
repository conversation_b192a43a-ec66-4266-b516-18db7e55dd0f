# Pylint Configuration for Lifeboard Project
#
# This configuration customizes pylint for the Lifeboard codebase,
# ensuring consistent Python code quality standards.

[MASTER]
# Use multiple processes to speed up pylint
jobs=4

# Python code to execute, usually for sys.path manipulation
init-hook='import sys; sys.path.append(".")'

# Files or directories to be skipped
ignore=volumes,logs,node_modules,.git

# <PERSON><PERSON> collected data for later comparisons
persistent=yes

# List of plugins to load
load-plugins=pylint.extensions.check_elif,
             pylint.extensions.bad_builtin,
             pylint.extensions.docparams,
             pylint.extensions.for_any_all,
             pylint.extensions.set_membership,
             pylint.extensions.code_style,
             pylint.extensions.overlapping_exceptions,
             pylint.extensions.typing

[MESSAGES CONTROL]
# Disable specific warnings and errors
disable=
    # Temporarily disabled for legacy code
    C0114,  # missing-module-docstring
    C0115,  # missing-class-docstring
    C0116,  # missing-function-docstring

    # Style preferences
    C0103,  # invalid-name (for short variable names)
    R0903,  # too-few-public-methods

    # Import related
    C0412,  # ungrouped-imports (handled by isort)

    # Type checking
    R1705,  # no-else-return (sometimes clearer with else)
    R1720,  # no-else-raise

    # Too strict for our use case
    R0913,  # too-many-arguments (sometimes necessary)
    R0914,  # too-many-locals
    R0912,  # too-many-branches
    R0915,  # too-many-statements

# Enable specific warnings
enable=
    W0613,  # unused-argument
    W0622,  # redefined-builtin
    W0703,  # broad-except
    C0325,  # superfluous-parens
    C0326,  # bad-whitespace

[REPORTS]
# Set the output format
output-format=text

# Include message's id in the output
include-ids=yes

# Include symbolic name of checks in the output
symbols=yes

# Put messages in a separate file for each module / package
files-output=no

# Display a full report or only the messages
reports=yes

# Evaluation expression for the global note
evaluation=10.0 - ((float(5 * error + warning + refactor + convention) / statement) * 10)

[REFACTORING]
# Maximum number of nested blocks for function / method body
max-nested-blocks=5

# Complete name of functions that never returns
never-returning-functions=sys.exit

[BASIC]
# Good variable names which should always be accepted
good-names=i,j,k,ex,Run,_,id,db,pk,fk

# Bad variable names which should always be refused
bad-names=foo,bar,baz,toto,tutu,tata

# Include a hint for the correct naming format with invalid-name
include-naming-hint=yes

# Naming style matching correct function names
function-naming-style=snake_case

# Naming style matching correct variable names
variable-naming-style=snake_case

# Naming style matching correct constant names
const-naming-style=UPPER_CASE

# Naming style matching correct attribute names
attr-naming-style=snake_case

# Naming style matching correct argument names
argument-naming-style=snake_case

# Naming style matching correct class attribute names
class-attribute-naming-style=any

# Naming style matching correct class names
class-naming-style=PascalCase

# Naming style matching correct module names
module-naming-style=snake_case

# Naming style matching correct method names
method-naming-style=snake_case

# Regular expression matching correct function names
function-rgx=[a-z_][a-z0-9_]{2,30}$

# Regular expression matching correct variable names
variable-rgx=[a-z_][a-z0-9_]{2,30}$

# Regular expression matching correct constant names
const-rgx=(([A-Z_][A-Z0-9_]*)|(__.*__))$

# Regular expression matching correct attribute names
attr-rgx=[a-z_][a-z0-9_]{2,30}$

# Regular expression matching correct argument names
argument-rgx=[a-z_][a-z0-9_]{2,30}$

# Regular expression matching correct class attribute names
class-attribute-rgx=([A-Za-z_][A-Za-z0-9_]{2,30}|(__.*__))$

# Regular expression matching correct class names
class-rgx=[A-Z_][a-zA-Z0-9]+$

# Regular expression matching correct module names
module-rgx=(([a-z_][a-z0-9_]*)|([A-Z][a-zA-Z0-9]+))$

# Regular expression matching correct method names
method-rgx=[a-z_][a-z0-9_]{2,30}$

# Minimum line length for functions/classes that require docstrings
docstring-min-length=10

[FORMAT]
# Maximum number of characters on a single line
max-line-length=120

# Regexp for a line that is allowed to be longer than the limit
ignore-long-lines=^\s*(# )?<?https?://\S+>?$

# Allow the body of an if to be on the same line if there is no else
single-line-if-stmt=no

# Maximum number of lines in a module
max-module-lines=1000

# String used as indentation unit
indent-string='    '

# Number of spaces of indent required inside a hanging or continued line
indent-after-paren=4

# Expected format of line ending
expected-line-ending-format=LF

[LOGGING]
# Logging modules to check that the string format arguments are in logging function parameter format
logging-modules=logging

[MISCELLANEOUS]
# List of note tags to take in consideration
notes=FIXME,XXX,TODO,BUG,HACK

[SIMILARITIES]
# Minimum lines number of a similarity
min-similarity-lines=4

# Ignore comments when computing similarities
ignore-comments=yes

# Ignore docstrings when computing similarities
ignore-docstrings=yes

# Ignore imports when computing similarities
ignore-imports=no

[SPELLING]
# Spelling dictionary name
spelling-dict=

# List of comma separated words that should not be checked
spelling-ignore-words=

# A path to a file that contains private dictionary
spelling-private-dict-file=

# Tells whether to store unknown words to indicated private dictionary
spelling-store-unknown-words=no

[TYPECHECK]
# List of decorators that produce context managers
contextmanager-decorators=contextlib.contextmanager

# List of members which are set dynamically and missed by pylint inference
generated-members=

# Tells whether missing members accessed in mixin class should be ignored
ignore-mixin-members=yes

# This flag controls whether pylint should warn about no-member and similar checks
ignore-on-opaque-inference=yes

# List of class names for which member attributes should not be checked
ignored-classes=optparse.Values,thread._local,_thread._local

# List of module names for which member attributes should not be checked
ignored-modules=

# Show a hint with possible names when a member name was not found
missing-member-hint=yes

# The minimum edit distance a name should have to be considered a similar match
missing-member-hint-distance=1

# The total number of similar names that should be taken in consideration
missing-member-max-choices=1

[VARIABLES]
# Tells whether we should check for unused import in __init__ files
init-import=no

# A regular expression matching the name of dummy variables
dummy-variables-rgx=_+$|(_[a-zA-Z0-9_]*[a-zA-Z0-9]+?$)|dummy|^ignored_|^unused_

# List of additional names supposed to be defined in builtins
additional-builtins=

# List of strings which can identify a callback function by name
callbacks=cb_,_cb

# List of qualified module names which can have objects that can redefine builtins
redefining-builtins-modules=six.moves,past.builtins,future.builtins,builtins,io

[CLASSES]
# List of method names used to declare (i.e. assign) instance attributes
defining-attr-methods=__init__,__new__,setUp,__post_init__

# List of member names, which should be excluded from the protected access warning
exclude-protected=_asdict,_fields,_replace,_source,_make

# List of valid names for the first argument in a class method
valid-classmethod-first-arg=cls

# List of valid names for the first argument in a metaclass class method
valid-metaclass-classmethod-first-arg=cls

[DESIGN]
# Maximum number of arguments for function / method
max-args=7

# Maximum number of attributes for a class
max-attributes=10

# Maximum number of boolean expressions in an if statement
max-bool-expr=5

# Maximum number of branch for function / method body
max-branches=15

# Maximum number of locals for function / method body
max-locals=20

# Maximum number of parents for a class
max-parents=7

# Maximum number of public methods for a class
max-public-methods=25

# Maximum number of return / yield for function / method body
max-returns=6

# Maximum number of statements in function / method body
max-statements=50

# Minimum number of public methods for a class
min-public-methods=1

[IMPORTS]
# Deprecated modules which should not be used
deprecated-modules=optparse,tkinter.tix

# Create a graph of external dependencies in the given file
ext-import-graph=

# Create a graph of every (i.e. internal and external) dependencies in the given file
import-graph=

# Create a graph of internal dependencies in the given file
int-import-graph=

# Force import order to recognize a module as part of the standard compatibility libraries
known-standard-library=

# Force import order to recognize a module as part of a third party library
known-third-party=

# Analyse import fallback blocks
analyse-fallback-blocks=no

[EXCEPTIONS]
# Exceptions that will emit a warning when being caught
overgeneral-exceptions=Exception
