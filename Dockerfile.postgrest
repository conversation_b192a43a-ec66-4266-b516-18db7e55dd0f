# Custom PostgREST image with diagnostic tools
# Extends the official Supabase PostgREST image with useful debugging utilities

FROM public.ecr.aws/supabase/postgrest:v12.2.0

# Switch to root to install packages
USER root

# Update package list and install diagnostic tools
# hadolint ignore=DL3008
RUN apt-get update && apt-get install -y --no-install-recommends \
    curl \
    wget \
    netcat-openbsd \
    iputils-ping \
    dnsutils \
    telnet \
    vim-tiny \
    less \
    procps \
    net-tools \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Create symbolic link for nc (some systems expect 'nc' instead of 'netcat')
RUN ln -sf /bin/nc /usr/bin/nc

# Switch back to the original user (ubuntu)
USER ubuntu

# Keep the original entrypoint and command
# The base image handles PostgREST startup
