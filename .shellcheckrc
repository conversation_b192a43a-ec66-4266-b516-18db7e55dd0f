# Shellcheck configuration for lifeboard-supabase project
# Disable specific checks that are inappropriate for our use case

# SC2317: Function declared but not used directly
# Many functions are used as callbacks or in eval contexts
disable=SC2317

# SC2034: Variable assigned but not used
# Some variables are used by sourced scripts or exported
disable=SC2034

# SC1091: File not found when sourcing
# Some sourced files are generated or conditionally exist
disable=SC1091

# SC2154: Variable used but not assigned
# Variables often come from sourced environment files
disable=SC2154

# SC2155: Declare and assign separately to avoid masking return values
# This is a style preference; our usage is clear and intentional
disable=SC2155

# SC2086: Double quote to prevent globbing and word splitting
# We handle this appropriately in our context
disable=SC2086

# SC2126: Consider using 'grep -c' instead of 'grep|wc -l'
# Style preference; both are functionally equivalent
disable=SC2126

# SC2129: Consider using { cmd1; cmd2; } >> file instead of individual redirects
# Style preference; individual redirects are clearer in our context
disable=SC2129
