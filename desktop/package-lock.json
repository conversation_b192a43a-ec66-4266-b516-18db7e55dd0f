{"lockfileVersion": 3, "name": "lifeboard-desktop", "packages": {"": {"bin": {"lifeboard-plugin": "src/cli/plugin-cli.js"}, "dependencies": {"ajv": "^8.12.0", "archiver": "^6.0.1", "chalk": "^4.1.2", "commander": "^11.1.0", "electron-log": "^5.0.1", "electron-updater": "^6.1.7", "inquirer": "^8.2.6", "ora": "^5.4.1", "pino": "^9.7.0", "semver": "^7.7.2", "yauzl": "^3.0.0"}, "devDependencies": {"@types/node": "^20.10.0", "electron": "^28.1.0", "electron-builder": "^24.9.1", "jest": "^29.7.0", "typescript": "^5.3.3"}, "hasInstallScript": true, "license": "MIT", "name": "lifeboard-desktop", "version": "0.1.0"}, "node_modules/7zip-bin": {"dev": true, "integrity": "sha512-ukTPVhqG4jNzMro2qA9HSCSSVJN3aN7tlb+hfqYCt3ER0yWroeA2VR38MNrOHLQ/cVj+DaIMad0kFCtWWowh/A==", "license": "MIT", "resolved": "https://registry.npmjs.org/7zip-bin/-/7zip-bin-5.2.0.tgz", "version": "5.2.0"}, "node_modules/@ampproject/remapping": {"dependencies": {"@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.24"}, "dev": true, "engines": {"node": ">=6.0.0"}, "integrity": "sha512-30iZtAPgz+LTIYoeivqYo853f02jBYSd5uGnGpkFV0M3xOt9aN73erkgYAmZU43x4VfqcnLxW9Kpg3R5LC4YYw==", "license": "Apache-2.0", "resolved": "https://registry.npmjs.org/@ampproject/remapping/-/remapping-2.3.0.tgz", "version": "2.3.0"}, "node_modules/@babel/code-frame": {"dependencies": {"@babel/helper-validator-identifier": "^7.27.1", "js-tokens": "^4.0.0", "picocolors": "^1.1.1"}, "dev": true, "engines": {"node": ">=6.9.0"}, "integrity": "sha512-cjQ7ZlQ0Mv3b47hABuTevyTuYN4i+loJKGeV9flcCgIK37cCXRh+L1bd3iBHlynerhQ7BhCkn2BPbQUL+rGqFg==", "license": "MIT", "resolved": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.27.1.tgz", "version": "7.27.1"}, "node_modules/@babel/compat-data": {"dev": true, "engines": {"node": ">=6.9.0"}, "integrity": "sha512-60X7qkglvrap8mn1lh2ebxXdZYtUcpd7gsmy9kLaBJ4i/WdY8PqTSdxyA8qraikqKQK5C1KRBKXqznrVapyNaw==", "license": "MIT", "resolved": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.28.0.tgz", "version": "7.28.0"}, "node_modules/@babel/core": {"dependencies": {"@ampproject/remapping": "^2.2.0", "@babel/code-frame": "^7.27.1", "@babel/generator": "^7.28.0", "@babel/helper-compilation-targets": "^7.27.2", "@babel/helper-module-transforms": "^7.27.3", "@babel/helpers": "^7.27.6", "@babel/parser": "^7.28.0", "@babel/template": "^7.27.2", "@babel/traverse": "^7.28.0", "@babel/types": "^7.28.0", "convert-source-map": "^2.0.0", "debug": "^4.1.0", "gensync": "^1.0.0-beta.2", "json5": "^2.2.3", "semver": "^6.3.1"}, "dev": true, "engines": {"node": ">=6.9.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/babel"}, "integrity": "sha512-UlLAnTPrFdNGoFtbSXwcGFQBtQZJCNjaN6hQNP3UPvuNXT1i82N26KL3dZeIpNalWywr9IuQuncaAfUaS1g6sQ==", "license": "MIT", "resolved": "https://registry.npmjs.org/@babel/core/-/core-7.28.0.tgz", "version": "7.28.0"}, "node_modules/@babel/core/node_modules/semver": {"bin": {"semver": "bin/semver.js"}, "dev": true, "integrity": "sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA==", "license": "ISC", "resolved": "https://registry.npmjs.org/semver/-/semver-6.3.1.tgz", "version": "6.3.1"}, "node_modules/@babel/generator": {"dependencies": {"@babel/parser": "^7.28.0", "@babel/types": "^7.28.0", "@jridgewell/gen-mapping": "^0.3.12", "@jridgewell/trace-mapping": "^0.3.28", "jsesc": "^3.0.2"}, "dev": true, "engines": {"node": ">=6.9.0"}, "integrity": "sha512-lJjzvrbEeWrhB4P3QBsH7tey117PjLZnDbLiQEKjQ/fNJTjuq4HSqgFA+UNSwZT8D7dxxbnuSBMsa1lrWzKlQg==", "license": "MIT", "resolved": "https://registry.npmjs.org/@babel/generator/-/generator-7.28.0.tgz", "version": "7.28.0"}, "node_modules/@babel/helper-compilation-targets": {"dependencies": {"@babel/compat-data": "^7.27.2", "@babel/helper-validator-option": "^7.27.1", "browserslist": "^4.24.0", "lru-cache": "^5.1.1", "semver": "^6.3.1"}, "dev": true, "engines": {"node": ">=6.9.0"}, "integrity": "sha512-2+1thGUUWWjLTYTHZWK1n8Yga0ijBz1XAhUXcKy81rd5g6yh7hGqMp45v7cadSbEHc9G3OTv45SyneRN3ps4DQ==", "license": "MIT", "resolved": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.27.2.tgz", "version": "7.27.2"}, "node_modules/@babel/helper-compilation-targets/node_modules/lru-cache": {"dependencies": {"yallist": "^3.0.2"}, "dev": true, "integrity": "sha512-KpNARQA3Iwv+jTA0utUVVbrh+Jlrr1Fv0e56GGzAFOXN7dk/FviaDW8LHmK52DlcH4WP2n6gI8vN1aesBFgo9w==", "license": "ISC", "resolved": "https://registry.npmjs.org/lru-cache/-/lru-cache-5.1.1.tgz", "version": "5.1.1"}, "node_modules/@babel/helper-compilation-targets/node_modules/semver": {"bin": {"semver": "bin/semver.js"}, "dev": true, "integrity": "sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA==", "license": "ISC", "resolved": "https://registry.npmjs.org/semver/-/semver-6.3.1.tgz", "version": "6.3.1"}, "node_modules/@babel/helper-compilation-targets/node_modules/yallist": {"dev": true, "integrity": "sha512-a4UGQaWPH59mOXUYnAG2ewncQS4i4F43Tv3JoAM+s2VDAmS9NsK8GpDMLrCHPksFT7h3K6TOoUNn2pb7RoXx4g==", "license": "ISC", "resolved": "https://registry.npmjs.org/yallist/-/yallist-3.1.1.tgz", "version": "3.1.1"}, "node_modules/@babel/helper-globals": {"dev": true, "engines": {"node": ">=6.9.0"}, "integrity": "sha512-+W6cISkXFa1jXsDEdYA8HeevQT/FULhxzR99pxphltZcVaugps53THCeiWA8SguxxpSp3gKPiuYfSWopkLQ4hw==", "license": "MIT", "resolved": "https://registry.npmjs.org/@babel/helper-globals/-/helper-globals-7.28.0.tgz", "version": "7.28.0"}, "node_modules/@babel/helper-module-imports": {"dependencies": {"@babel/traverse": "^7.27.1", "@babel/types": "^7.27.1"}, "dev": true, "engines": {"node": ">=6.9.0"}, "integrity": "sha512-0gSFWUPNXNopqtIPQvlD5WgXYI5GY2kP2cCvoT8kczjbfcfuIljTbcWrulD1CIPIX2gt1wghbDy08yE1p+/r3w==", "license": "MIT", "resolved": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.27.1.tgz", "version": "7.27.1"}, "node_modules/@babel/helper-module-transforms": {"dependencies": {"@babel/helper-module-imports": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1", "@babel/traverse": "^7.27.3"}, "dev": true, "engines": {"node": ">=6.9.0"}, "integrity": "sha512-dSOvYwvyLsWBeIRyOeHXp5vPj5l1I011r52FM1+r1jCERv+aFXYk4whgQccYEGYxK2H3ZAIA8nuPkQ0HaUo3qg==", "license": "MIT", "peerDependencies": {"@babel/core": "^7.0.0"}, "resolved": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.27.3.tgz", "version": "7.27.3"}, "node_modules/@babel/helper-plugin-utils": {"dev": true, "engines": {"node": ">=6.9.0"}, "integrity": "sha512-1gn1Up5YXka3YYAHGKpbideQ5Yjf1tDa9qYcgysz+cNCXukyLl6DjPXhD3VRwSb8c0J9tA4b2+rHEZtc6R0tlw==", "license": "MIT", "resolved": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz", "version": "7.27.1"}, "node_modules/@babel/helper-string-parser": {"dev": true, "engines": {"node": ">=6.9.0"}, "integrity": "sha512-qMlSxKbpRlAridDExk92nSobyDdpPijUq2DW6oDnUqd0iOGxmQjyqhMIihI9+zv4LPyZdRje2cavWPbCbWm3eA==", "license": "MIT", "resolved": "https://registry.npmjs.org/@babel/helper-string-parser/-/helper-string-parser-7.27.1.tgz", "version": "7.27.1"}, "node_modules/@babel/helper-validator-identifier": {"dev": true, "engines": {"node": ">=6.9.0"}, "integrity": "sha512-D2hP9eA+Sqx1kBZgzxZh0y1trbuU+JoDkiEwqhQ36nodYqJwyEIhPSdMNd7lOm/4io72luTPWH20Yda0xOuUow==", "license": "MIT", "resolved": "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.27.1.tgz", "version": "7.27.1"}, "node_modules/@babel/helper-validator-option": {"dev": true, "engines": {"node": ">=6.9.0"}, "integrity": "sha512-YvjJow9FxbhFFKDSuFnVCe2WxXk1zWc22fFePVNEaWJEu8IrZVlda6N0uHwzZrUM1il7NC9Mlp4MaJYbYd9JSg==", "license": "MIT", "resolved": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.27.1.tgz", "version": "7.27.1"}, "node_modules/@babel/helpers": {"dependencies": {"@babel/template": "^7.27.2", "@babel/types": "^7.27.6"}, "dev": true, "engines": {"node": ">=6.9.0"}, "integrity": "sha512-muE8Tt8M22638HU31A3CgfSUciwz1fhATfoVai05aPXGor//CdWDCbnlY1yvBPo07njuVOCNGCSp/GTt12lIug==", "license": "MIT", "resolved": "https://registry.npmjs.org/@babel/helpers/-/helpers-7.27.6.tgz", "version": "7.27.6"}, "node_modules/@babel/parser": {"bin": {"parser": "bin/babel-parser.js"}, "dependencies": {"@babel/types": "^7.28.0"}, "dev": true, "engines": {"node": ">=6.0.0"}, "integrity": "sha512-jVZGvOxOuNSsuQuLRTh13nU0AogFlw32w/MT+LV6D3sP5WdbW61E77RnkbaO2dUvmPAYrBDJXGn5gGS6tH4j8g==", "license": "MIT", "resolved": "https://registry.npmjs.org/@babel/parser/-/parser-7.28.0.tgz", "version": "7.28.0"}, "node_modules/@babel/plugin-syntax-async-generators": {"dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "dev": true, "integrity": "sha512-tycmZxkGfZaxhMRbXlPXuVFpdWlXpir2W4AMhSJgRKzk/eDlIXOhb2LHWoLpDF7TEHylV5zNhykX6KAgHJmTNw==", "license": "MIT", "peerDependencies": {"@babel/core": "^7.0.0-0"}, "resolved": "https://registry.npmjs.org/@babel/plugin-syntax-async-generators/-/plugin-syntax-async-generators-7.8.4.tgz", "version": "7.8.4"}, "node_modules/@babel/plugin-syntax-bigint": {"dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "dev": true, "integrity": "sha512-wnTnFlG+YxQm3vDxpGE57Pj0srRU4sHE/mDkt1qv2YJJSeUAec2ma4WLUnUPeKjyrfntVwe/N6dCXpU+zL3Npg==", "license": "MIT", "peerDependencies": {"@babel/core": "^7.0.0-0"}, "resolved": "https://registry.npmjs.org/@babel/plugin-syntax-bigint/-/plugin-syntax-bigint-7.8.3.tgz", "version": "7.8.3"}, "node_modules/@babel/plugin-syntax-class-properties": {"dependencies": {"@babel/helper-plugin-utils": "^7.12.13"}, "dev": true, "integrity": "sha512-fm4idjKla0YahUNgFNLCB0qySdsoPiZP3iQE3rky0mBUtMZ23yDJ9SJdg6dXTSDnulOVqiF3Hgr9nbXvXTQZYA==", "license": "MIT", "peerDependencies": {"@babel/core": "^7.0.0-0"}, "resolved": "https://registry.npmjs.org/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.12.13.tgz", "version": "7.12.13"}, "node_modules/@babel/plugin-syntax-class-static-block": {"dependencies": {"@babel/helper-plugin-utils": "^7.14.5"}, "dev": true, "engines": {"node": ">=6.9.0"}, "integrity": "sha512-b+YyPmr6ldyNnM6sqYeMWE+bgJcJpO6yS4QD7ymxgH34GBPNDM/THBh8iunyvKIZztiwLH4CJZ0RxTk9emgpjw==", "license": "MIT", "peerDependencies": {"@babel/core": "^7.0.0-0"}, "resolved": "https://registry.npmjs.org/@babel/plugin-syntax-class-static-block/-/plugin-syntax-class-static-block-7.14.5.tgz", "version": "7.14.5"}, "node_modules/@babel/plugin-syntax-import-attributes": {"dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "dev": true, "engines": {"node": ">=6.9.0"}, "integrity": "sha512-oFT0FrKHgF53f4vOsZGi2Hh3I35PfSmVs4IBFLFj4dnafP+hIWDLg3VyKmUHfLoLHlyxY4C7DGtmHuJgn+IGww==", "license": "MIT", "peerDependencies": {"@babel/core": "^7.0.0-0"}, "resolved": "https://registry.npmjs.org/@babel/plugin-syntax-import-attributes/-/plugin-syntax-import-attributes-7.27.1.tgz", "version": "7.27.1"}, "node_modules/@babel/plugin-syntax-import-meta": {"dependencies": {"@babel/helper-plugin-utils": "^7.10.4"}, "dev": true, "integrity": "sha512-Yqfm+XDx0+Prh3VSeEQCPU81yC+JWZ2pDPFSS4ZdpfZhp4MkFMaDC1UqseovEKwSUpnIL7+vK+Clp7bfh0iD7g==", "license": "MIT", "peerDependencies": {"@babel/core": "^7.0.0-0"}, "resolved": "https://registry.npmjs.org/@babel/plugin-syntax-import-meta/-/plugin-syntax-import-meta-7.10.4.tgz", "version": "7.10.4"}, "node_modules/@babel/plugin-syntax-json-strings": {"dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "dev": true, "integrity": "sha512-lY6kdGpWHvjoe2vk4WrAapEuBR69EMxZl+RoGRhrFGNYVK8mOPAW8VfbT/ZgrFbXlDNiiaxQnAtgVCZ6jv30EA==", "license": "MIT", "peerDependencies": {"@babel/core": "^7.0.0-0"}, "resolved": "https://registry.npmjs.org/@babel/plugin-syntax-json-strings/-/plugin-syntax-json-strings-7.8.3.tgz", "version": "7.8.3"}, "node_modules/@babel/plugin-syntax-jsx": {"dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "dev": true, "engines": {"node": ">=6.9.0"}, "integrity": "sha512-y8YTNIeKoyhGd9O0Jiyzyyqk8gdjnumGTQPsz0xOZOQ2RmkVJeZ1vmmfIvFEKqucBG6axJGBZDE/7iI5suUI/w==", "license": "MIT", "peerDependencies": {"@babel/core": "^7.0.0-0"}, "resolved": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.27.1.tgz", "version": "7.27.1"}, "node_modules/@babel/plugin-syntax-logical-assignment-operators": {"dependencies": {"@babel/helper-plugin-utils": "^7.10.4"}, "dev": true, "integrity": "sha512-d8waShlpFDinQ5MtvGU9xDAOzKH47+FFoney2baFIoMr952hKOLp1HR7VszoZvOsV/4+RRszNY7D17ba0te0ig==", "license": "MIT", "peerDependencies": {"@babel/core": "^7.0.0-0"}, "resolved": "https://registry.npmjs.org/@babel/plugin-syntax-logical-assignment-operators/-/plugin-syntax-logical-assignment-operators-7.10.4.tgz", "version": "7.10.4"}, "node_modules/@babel/plugin-syntax-nullish-coalescing-operator": {"dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "dev": true, "integrity": "sha512-aSff4zPII1u2QD7y+F8oDsz19ew4IGEJg9SVW+bqwpwtfFleiQDMdzA/R+UlWDzfnHFCxxleFT0PMIrR36XLNQ==", "license": "MIT", "peerDependencies": {"@babel/core": "^7.0.0-0"}, "resolved": "https://registry.npmjs.org/@babel/plugin-syntax-nullish-coalescing-operator/-/plugin-syntax-nullish-coalescing-operator-7.8.3.tgz", "version": "7.8.3"}, "node_modules/@babel/plugin-syntax-numeric-separator": {"dependencies": {"@babel/helper-plugin-utils": "^7.10.4"}, "dev": true, "integrity": "sha512-9H6YdfkcK/uOnY/K7/aA2xpzaAgkQn37yzWUMRK7OaPOqOpGS1+n0H5hxT9AUw9EsSjPW8SVyMJwYRtWs3X3ug==", "license": "MIT", "peerDependencies": {"@babel/core": "^7.0.0-0"}, "resolved": "https://registry.npmjs.org/@babel/plugin-syntax-numeric-separator/-/plugin-syntax-numeric-separator-7.10.4.tgz", "version": "7.10.4"}, "node_modules/@babel/plugin-syntax-object-rest-spread": {"dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "dev": true, "integrity": "sha512-XoqMijGZb9y3y2XskN+P1wUGiVwWZ5JmoDRwx5+3GmEplNyVM2s2Dg8ILFQm8rWM48orGy5YpI5Bl8U1y7ydlA==", "license": "MIT", "peerDependencies": {"@babel/core": "^7.0.0-0"}, "resolved": "https://registry.npmjs.org/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.8.3.tgz", "version": "7.8.3"}, "node_modules/@babel/plugin-syntax-optional-catch-binding": {"dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "dev": true, "integrity": "sha512-6VPD0Pc1lpTqw0aKoeRTMiB+kWhAoT24PA+ksWSBrFtl5SIRVpZlwN3NNPQjehA2E/91FV3RjLWoVTglWcSV3Q==", "license": "MIT", "peerDependencies": {"@babel/core": "^7.0.0-0"}, "resolved": "https://registry.npmjs.org/@babel/plugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.8.3.tgz", "version": "7.8.3"}, "node_modules/@babel/plugin-syntax-optional-chaining": {"dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "dev": true, "integrity": "sha512-KoK9ErH1MBlCPxV0VANkXW2/dw4vlbGDrFgz8bmUsBGYkFRcbRwMh6cIJubdPrkxRwuGdtCk0v/wPTKbQgBjkg==", "license": "MIT", "peerDependencies": {"@babel/core": "^7.0.0-0"}, "resolved": "https://registry.npmjs.org/@babel/plugin-syntax-optional-chaining/-/plugin-syntax-optional-chaining-7.8.3.tgz", "version": "7.8.3"}, "node_modules/@babel/plugin-syntax-private-property-in-object": {"dependencies": {"@babel/helper-plugin-utils": "^7.14.5"}, "dev": true, "engines": {"node": ">=6.9.0"}, "integrity": "sha512-0wVnp9dxJ72ZUJDV27ZfbSj6iHLoytYZmh3rFcxNnvsJF3ktkzLDZPy/mA17HGsaQT3/DQsWYX1f1QGWkCoVUg==", "license": "MIT", "peerDependencies": {"@babel/core": "^7.0.0-0"}, "resolved": "https://registry.npmjs.org/@babel/plugin-syntax-private-property-in-object/-/plugin-syntax-private-property-in-object-7.14.5.tgz", "version": "7.14.5"}, "node_modules/@babel/plugin-syntax-top-level-await": {"dependencies": {"@babel/helper-plugin-utils": "^7.14.5"}, "dev": true, "engines": {"node": ">=6.9.0"}, "integrity": "sha512-hx++upLv5U1rgYfwe1xBQUhRmU41NEvpUvrp8jkrSCdvGSnM5/qdRMtylJ6PG5OFkBaHkbTAKTnd3/YyESRHFw==", "license": "MIT", "peerDependencies": {"@babel/core": "^7.0.0-0"}, "resolved": "https://registry.npmjs.org/@babel/plugin-syntax-top-level-await/-/plugin-syntax-top-level-await-7.14.5.tgz", "version": "7.14.5"}, "node_modules/@babel/plugin-syntax-typescript": {"dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "dev": true, "engines": {"node": ">=6.9.0"}, "integrity": "sha512-xfYCBMxveHrRMnAWl1ZlPXOZjzkN82THFvLhQhFXFt81Z5HnN+EtUkZhv/zcKpmT3fzmWZB0ywiBrbC3vogbwQ==", "license": "MIT", "peerDependencies": {"@babel/core": "^7.0.0-0"}, "resolved": "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.27.1.tgz", "version": "7.27.1"}, "node_modules/@babel/template": {"dependencies": {"@babel/code-frame": "^7.27.1", "@babel/parser": "^7.27.2", "@babel/types": "^7.27.1"}, "dev": true, "engines": {"node": ">=6.9.0"}, "integrity": "sha512-LPDZ85aEJyYSd18/DkjNh4/y1ntkE5KwUHWTiqgRxruuZL2F1yuHligVHLvcHY2vMHXttKFpJn6LwfI7cw7ODw==", "license": "MIT", "resolved": "https://registry.npmjs.org/@babel/template/-/template-7.27.2.tgz", "version": "7.27.2"}, "node_modules/@babel/traverse": {"dependencies": {"@babel/code-frame": "^7.27.1", "@babel/generator": "^7.28.0", "@babel/helper-globals": "^7.28.0", "@babel/parser": "^7.28.0", "@babel/template": "^7.27.2", "@babel/types": "^7.28.0", "debug": "^4.3.1"}, "dev": true, "engines": {"node": ">=6.9.0"}, "integrity": "sha512-mGe7UK5wWyh0bKRfupsUchrQGqvDbZDbKJw+kcRGSmdHVYrv+ltd0pnpDTVpiTqnaBru9iEvA8pz8W46v0Amwg==", "license": "MIT", "resolved": "https://registry.npmjs.org/@babel/traverse/-/traverse-7.28.0.tgz", "version": "7.28.0"}, "node_modules/@babel/types": {"dependencies": {"@babel/helper-string-parser": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1"}, "dev": true, "engines": {"node": ">=6.9.0"}, "integrity": "sha512-jYn<PERSON>+JyZG5YThjHiF28oT4SIZLnYOcSBb6+SDaFIyzDVSkXQmQQYclJ2R+YxcdmK0AX6x1E5OQNtuh3jHDrUg==", "license": "MIT", "resolved": "https://registry.npmjs.org/@babel/types/-/types-7.28.0.tgz", "version": "7.28.0"}, "node_modules/@bcoe/v8-coverage": {"dev": true, "integrity": "sha512-0hYQ8SB4Db5zvZB4axdMHGwEaQjkZzFjQiN9LVYvIFB2nSUHW9tYpxWriPrWDASIxiaXax83REcLxuSdnGPZtw==", "license": "MIT", "resolved": "https://registry.npmjs.org/@bcoe/v8-coverage/-/v8-coverage-0.2.3.tgz", "version": "0.2.3"}, "node_modules/@develar/schema-utils": {"dependencies": {"ajv": "^6.12.0", "ajv-keywords": "^3.4.1"}, "dev": true, "engines": {"node": ">= 8.9.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "integrity": "sha512-0cp4PsWQ/9avqTVMCtZ+GirikIA36ikvjtHweU4/j8yLtgObI0+JUPhYFScgwlteveGB1rt3Cm8UhN04XayDig==", "license": "MIT", "resolved": "https://registry.npmjs.org/@develar/schema-utils/-/schema-utils-2.6.5.tgz", "version": "2.6.5"}, "node_modules/@develar/schema-utils/node_modules/ajv": {"dependencies": {"fast-deep-equal": "^3.1.1", "fast-json-stable-stringify": "^2.0.0", "json-schema-traverse": "^0.4.1", "uri-js": "^4.2.2"}, "dev": true, "funding": {"type": "github", "url": "https://github.com/sponsors/epoberezkin"}, "integrity": "sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g==", "license": "MIT", "resolved": "https://registry.npmjs.org/ajv/-/ajv-6.12.6.tgz", "version": "6.12.6"}, "node_modules/@develar/schema-utils/node_modules/ajv-keywords": {"dev": true, "integrity": "sha512-5p6WTN0DdTGVQk6VjcEju19IgaHudalcfabD7yhDGeA6bcQnmL+CpveLJq/3hvfwd1aof6L386Ougkx6RfyMIQ==", "license": "MIT", "peerDependencies": {"ajv": "^6.9.1"}, "resolved": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-3.5.2.tgz", "version": "3.5.2"}, "node_modules/@develar/schema-utils/node_modules/json-schema-traverse": {"dev": true, "integrity": "sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg==", "license": "MIT", "resolved": "https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz", "version": "0.4.1"}, "node_modules/@electron/asar": {"bin": {"asar": "bin/asar.js"}, "dependencies": {"commander": "^5.0.0", "glob": "^7.1.6", "minimatch": "^3.0.4"}, "dev": true, "engines": {"node": ">=10.12.0"}, "integrity": "sha512-i4/rNPRS84t0vSRa2HorerGRXWyF4vThfHesw0dmcWHp+cspK743UanA0suA5Q5y8kzY2y6YKrvbIUn69BCAiA==", "license": "MIT", "resolved": "https://registry.npmjs.org/@electron/asar/-/asar-3.4.1.tgz", "version": "3.4.1"}, "node_modules/@electron/asar/node_modules/brace-expansion": {"dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}, "dev": true, "integrity": "sha512-9T9UjW3r0UW5c1Q7GTwllptXwhvYmEzFhzMfZ9H7FQWt+uZePjZPjBP/W1ZEyZ1twGWom5/56TF4lPcqjnDHcg==", "license": "MIT", "resolved": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.12.tgz", "version": "1.1.12"}, "node_modules/@electron/asar/node_modules/commander": {"dev": true, "engines": {"node": ">= 6"}, "integrity": "sha512-P0CysNDQ7rtVw4QIQtm+MRxV66vKFSvlsQvGYXZWR3qFU0jlMKHZZZgw8e+8DSah4UDKMqnknRDQz+xuQXQ/Zg==", "license": "MIT", "resolved": "https://registry.npmjs.org/commander/-/commander-5.1.0.tgz", "version": "5.1.0"}, "node_modules/@electron/asar/node_modules/minimatch": {"dependencies": {"brace-expansion": "^1.1.7"}, "dev": true, "engines": {"node": "*"}, "integrity": "sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==", "license": "ISC", "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-3.1.2.tgz", "version": "3.1.2"}, "node_modules/@electron/get": {"dependencies": {"debug": "^4.1.1", "env-paths": "^2.2.0", "fs-extra": "^8.1.0", "got": "^11.8.5", "progress": "^2.0.3", "semver": "^6.2.0", "sumchecker": "^3.0.1"}, "dev": true, "engines": {"node": ">=12"}, "integrity": "sha512-Qkzpg2s9GnVV2I2BjRksUi43U5e6+zaQMcjoJy0C+C5oxaKl+fmckGDQFtRpZpZV0NQekuZZ+tGz7EA9TVnQtQ==", "license": "MIT", "optionalDependencies": {"global-agent": "^3.0.0"}, "resolved": "https://registry.npmjs.org/@electron/get/-/get-2.0.3.tgz", "version": "2.0.3"}, "node_modules/@electron/get/node_modules/semver": {"bin": {"semver": "bin/semver.js"}, "dev": true, "integrity": "sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA==", "license": "ISC", "resolved": "https://registry.npmjs.org/semver/-/semver-6.3.1.tgz", "version": "6.3.1"}, "node_modules/@electron/notarize": {"dependencies": {"debug": "^4.1.1", "fs-extra": "^9.0.1", "promise-retry": "^2.0.1"}, "dev": true, "engines": {"node": ">= 10.0.0"}, "integrity": "sha512-aL+bFMIkpR0cmmj5Zgy0LMKEpgy43/hw5zadEArgmAMWWlKc5buwFvFT9G/o/YJkvXAJm5q3iuTuLaiaXW39sg==", "license": "MIT", "resolved": "https://registry.npmjs.org/@electron/notarize/-/notarize-2.2.1.tgz", "version": "2.2.1"}, "node_modules/@electron/notarize/node_modules/fs-extra": {"dependencies": {"at-least-node": "^1.0.0", "graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}, "dev": true, "engines": {"node": ">=10"}, "integrity": "sha512-hcg3ZmepS30/7BSFqRvoo3DOMQu7IjqxO5nCDt+zM9XWjb33Wg7ziNT+Qvqbuc3+gWpzO02JubVyk2G4Zvo1OQ==", "license": "MIT", "resolved": "https://registry.npmjs.org/fs-extra/-/fs-extra-9.1.0.tgz", "version": "9.1.0"}, "node_modules/@electron/notarize/node_modules/jsonfile": {"dependencies": {"universalify": "^2.0.0"}, "dev": true, "integrity": "sha512-5dgndWOriYSm5cnYaJNhalLNDKOqFwyDB/rr1E9ZsGciGvKPs8R2xYGCacuf3z6K1YKDz182fd+fY3cn3pMqXQ==", "license": "MIT", "optionalDependencies": {"graceful-fs": "^4.1.6"}, "resolved": "https://registry.npmjs.org/jsonfile/-/jsonfile-6.1.0.tgz", "version": "6.1.0"}, "node_modules/@electron/notarize/node_modules/universalify": {"dev": true, "engines": {"node": ">= 10.0.0"}, "integrity": "sha512-gptHNQghINnc/vTGIk0SOFGFNXw7JVrlRUtConJRlvaw6DuX0wO5Jeko9sWrMBhh+PsYAZ7oXAiOnf/UKogyiw==", "license": "MIT", "resolved": "https://registry.npmjs.org/universalify/-/universalify-2.0.1.tgz", "version": "2.0.1"}, "node_modules/@electron/osx-sign": {"bin": {"electron-osx-flat": "bin/electron-osx-flat.js", "electron-osx-sign": "bin/electron-osx-sign.js"}, "dependencies": {"compare-version": "^0.1.2", "debug": "^4.3.4", "fs-extra": "^10.0.0", "isbinaryfile": "^4.0.8", "minimist": "^1.2.6", "plist": "^3.0.5"}, "dev": true, "engines": {"node": ">=12.0.0"}, "integrity": "sha512-k9ZzUQtamSoweGQDV2jILiRIHUu7lYlJ3c6IEmjv1hC17rclE+eb9U+f6UFlOOETo0JzY1HNlXy4YOlCvl+Lww==", "license": "BSD-2-<PERSON><PERSON>", "resolved": "https://registry.npmjs.org/@electron/osx-sign/-/osx-sign-1.0.5.tgz", "version": "1.0.5"}, "node_modules/@electron/osx-sign/node_modules/fs-extra": {"dependencies": {"graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}, "dev": true, "engines": {"node": ">=12"}, "integrity": "sha512-oRXApq54ETRj4eMiFzGnHWGy+zo5raudjuxN0b8H7s/RU2oW0Wvsx9O0ACRN/kRq9E8Vu/ReskGB5o3ji+FzHQ==", "license": "MIT", "resolved": "https://registry.npmjs.org/fs-extra/-/fs-extra-10.1.0.tgz", "version": "10.1.0"}, "node_modules/@electron/osx-sign/node_modules/isbinaryfile": {"dev": true, "engines": {"node": ">= 8.0.0"}, "funding": {"url": "https://github.com/sponsors/gjtorikian/"}, "integrity": "sha512-iHrqe5shvBUcFbmZq9zOQHBoeOhZJu6RQGrDpBgenUm/Am+F3JM2MgQj+rK3Z601fzrL5gLZWtAPH2OBaSVcyw==", "license": "MIT", "resolved": "https://registry.npmjs.org/isbinaryfile/-/isbinaryfile-4.0.10.tgz", "version": "4.0.10"}, "node_modules/@electron/osx-sign/node_modules/jsonfile": {"dependencies": {"universalify": "^2.0.0"}, "dev": true, "integrity": "sha512-5dgndWOriYSm5cnYaJNhalLNDKOqFwyDB/rr1E9ZsGciGvKPs8R2xYGCacuf3z6K1YKDz182fd+fY3cn3pMqXQ==", "license": "MIT", "optionalDependencies": {"graceful-fs": "^4.1.6"}, "resolved": "https://registry.npmjs.org/jsonfile/-/jsonfile-6.1.0.tgz", "version": "6.1.0"}, "node_modules/@electron/osx-sign/node_modules/universalify": {"dev": true, "engines": {"node": ">= 10.0.0"}, "integrity": "sha512-gptHNQghINnc/vTGIk0SOFGFNXw7JVrlRUtConJRlvaw6DuX0wO5Jeko9sWrMBhh+PsYAZ7oXAiOnf/UKogyiw==", "license": "MIT", "resolved": "https://registry.npmjs.org/universalify/-/universalify-2.0.1.tgz", "version": "2.0.1"}, "node_modules/@electron/universal": {"dependencies": {"@electron/asar": "^3.2.1", "@malept/cross-spawn-promise": "^1.1.0", "debug": "^4.3.1", "dir-compare": "^3.0.0", "fs-extra": "^9.0.1", "minimatch": "^3.0.4", "plist": "^3.0.4"}, "dev": true, "engines": {"node": ">=8.6"}, "integrity": "sha512-kbgXxyEauPJiQQUNG2VgUeyfQNFk6hBF11ISN2PNI6agUgPl55pv4eQmaqHzTAzchBvqZ2tQuRVaPStGf0mxGw==", "license": "MIT", "resolved": "https://registry.npmjs.org/@electron/universal/-/universal-1.5.1.tgz", "version": "1.5.1"}, "node_modules/@electron/universal/node_modules/brace-expansion": {"dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}, "dev": true, "integrity": "sha512-9T9UjW3r0UW5c1Q7GTwllptXwhvYmEzFhzMfZ9H7FQWt+uZePjZPjBP/W1ZEyZ1twGWom5/56TF4lPcqjnDHcg==", "license": "MIT", "resolved": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.12.tgz", "version": "1.1.12"}, "node_modules/@electron/universal/node_modules/fs-extra": {"dependencies": {"at-least-node": "^1.0.0", "graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}, "dev": true, "engines": {"node": ">=10"}, "integrity": "sha512-hcg3ZmepS30/7BSFqRvoo3DOMQu7IjqxO5nCDt+zM9XWjb33Wg7ziNT+Qvqbuc3+gWpzO02JubVyk2G4Zvo1OQ==", "license": "MIT", "resolved": "https://registry.npmjs.org/fs-extra/-/fs-extra-9.1.0.tgz", "version": "9.1.0"}, "node_modules/@electron/universal/node_modules/jsonfile": {"dependencies": {"universalify": "^2.0.0"}, "dev": true, "integrity": "sha512-5dgndWOriYSm5cnYaJNhalLNDKOqFwyDB/rr1E9ZsGciGvKPs8R2xYGCacuf3z6K1YKDz182fd+fY3cn3pMqXQ==", "license": "MIT", "optionalDependencies": {"graceful-fs": "^4.1.6"}, "resolved": "https://registry.npmjs.org/jsonfile/-/jsonfile-6.1.0.tgz", "version": "6.1.0"}, "node_modules/@electron/universal/node_modules/minimatch": {"dependencies": {"brace-expansion": "^1.1.7"}, "dev": true, "engines": {"node": "*"}, "integrity": "sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==", "license": "ISC", "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-3.1.2.tgz", "version": "3.1.2"}, "node_modules/@electron/universal/node_modules/universalify": {"dev": true, "engines": {"node": ">= 10.0.0"}, "integrity": "sha512-gptHNQghINnc/vTGIk0SOFGFNXw7JVrlRUtConJRlvaw6DuX0wO5Jeko9sWrMBhh+PsYAZ7oXAiOnf/UKogyiw==", "license": "MIT", "resolved": "https://registry.npmjs.org/universalify/-/universalify-2.0.1.tgz", "version": "2.0.1"}, "node_modules/@isaacs/cliui": {"dependencies": {"string-width": "^5.1.2", "string-width-cjs": "npm:string-width@^4.2.0", "strip-ansi": "^7.0.1", "strip-ansi-cjs": "npm:strip-ansi@^6.0.1", "wrap-ansi": "^8.1.0", "wrap-ansi-cjs": "npm:wrap-ansi@^7.0.0"}, "dev": true, "engines": {"node": ">=12"}, "integrity": "sha512-O8jcjabXaleOG9DQ0+ARXWZBTfnP4WNAqzuiJK7ll44AmxGKv/J2M4TPjxjY3znBCfvBXFzucm1twdyFybFqEA==", "license": "ISC", "resolved": "https://registry.npmjs.org/@isaacs/cliui/-/cliui-8.0.2.tgz", "version": "8.0.2"}, "node_modules/@isaacs/cliui/node_modules/ansi-regex": {"dev": true, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/ansi-regex?sponsor=1"}, "integrity": "sha512-7HSX4QQb4CspciLpVFwyRe79O3xsIZDDLER21kERQ71oaPodF8jL725AgJMFAYbooIqolJoRLuM81SpeUkpkvA==", "license": "MIT", "resolved": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-6.1.0.tgz", "version": "6.1.0"}, "node_modules/@isaacs/cliui/node_modules/ansi-styles": {"dev": true, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}, "integrity": "sha512-bN798gFfQX+viw3R7yrGWRqnrN2oRkEkUjjl4JNn4E8GxxbjtG3FbrEIIY3l8/hrwUwIeCZvi4QuOTP4MErVug==", "license": "MIT", "resolved": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-6.2.1.tgz", "version": "6.2.1"}, "node_modules/@isaacs/cliui/node_modules/emoji-regex": {"dev": true, "integrity": "sha512-L18DaJsXSUk2+42pv8mLs5jJT2hqFkFE4j21wOmgbUqsZ2hL72NsUU785g9RXgo3s0ZNgVl42TiHp3ZtOv/Vyg==", "license": "MIT", "resolved": "https://registry.npmjs.org/emoji-regex/-/emoji-regex-9.2.2.tgz", "version": "9.2.2"}, "node_modules/@isaacs/cliui/node_modules/string-width": {"dependencies": {"eastasianwidth": "^0.2.0", "emoji-regex": "^9.2.2", "strip-ansi": "^7.0.1"}, "dev": true, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}, "integrity": "sha512-HnLOCR3vjcY8beoNLtcjZ5/nxn2afmME6lhrDrebokqMap+XbeW8n9TXpPDOqdGK5qcI3oT0GKTW6wC7EMiVqA==", "license": "MIT", "resolved": "https://registry.npmjs.org/string-width/-/string-width-5.1.2.tgz", "version": "5.1.2"}, "node_modules/@isaacs/cliui/node_modules/strip-ansi": {"dependencies": {"ansi-regex": "^6.0.1"}, "dev": true, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/strip-ansi?sponsor=1"}, "integrity": "sha512-iq6eVVI64nQQTRYq2KtEg2d2uU7LElhTJwsH4YzIHZshxlgZms/wIc4VoDQTlG/IvVIrBKG06CrZnp0qv7hkcQ==", "license": "MIT", "resolved": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-7.1.0.tgz", "version": "7.1.0"}, "node_modules/@isaacs/cliui/node_modules/wrap-ansi": {"dependencies": {"ansi-styles": "^6.1.0", "string-width": "^5.0.1", "strip-ansi": "^7.0.1"}, "dev": true, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/wrap-ansi?sponsor=1"}, "integrity": "sha512-si7QWI6zUMq56bESFvagtmzMdGOtoxfR+Sez11Mobfc7tm+VkUckk9bW2UeffTGVUbOksxmSw0AA2gs8g71NCQ==", "license": "MIT", "resolved": "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-8.1.0.tgz", "version": "8.1.0"}, "node_modules/@istanbuljs/load-nyc-config": {"dependencies": {"camelcase": "^5.3.1", "find-up": "^4.1.0", "get-package-type": "^0.1.0", "js-yaml": "^3.13.1", "resolve-from": "^5.0.0"}, "dev": true, "engines": {"node": ">=8"}, "integrity": "sha512-VjeHSlIzpv/NyD3N0YuHfXOPDIixcA1q2ZV98wsMqcYlPmv2n3Yb2lYP9XMElnaFVXg5A7YLTeLu6V84uQDjmQ==", "license": "ISC", "resolved": "https://registry.npmjs.org/@istanbuljs/load-nyc-config/-/load-nyc-config-1.1.0.tgz", "version": "1.1.0"}, "node_modules/@istanbuljs/load-nyc-config/node_modules/argparse": {"dependencies": {"sprintf-js": "~1.0.2"}, "dev": true, "integrity": "sha512-o5Roy6tNG4SL/FOkCAN6RzjiakZS25RLYFrcMttJqbdd8BWrnA+fGz57iN5Pb06pvBGvl5gQ0B48dJlslXvoTg==", "license": "MIT", "resolved": "https://registry.npmjs.org/argparse/-/argparse-1.0.10.tgz", "version": "1.0.10"}, "node_modules/@istanbuljs/load-nyc-config/node_modules/js-yaml": {"bin": {"js-yaml": "bin/js-yaml.js"}, "dependencies": {"argparse": "^1.0.7", "esprima": "^4.0.0"}, "dev": true, "integrity": "sha512-okMH7OXXJ7YrN9Ok3/SXrnu4iX9yOk+25nqX4imS2npuvTYDmo/QEZoqwZkYaIDk3jVvBOTOIEgEhaLOynBS9g==", "license": "MIT", "resolved": "https://registry.npmjs.org/js-yaml/-/js-yaml-3.14.1.tgz", "version": "3.14.1"}, "node_modules/@istanbuljs/load-nyc-config/node_modules/sprintf-js": {"dev": true, "integrity": "sha512-D9cPgkvLlV3t3IzL0D0YLvGA9Ahk4PcvVwUbN0dSGr1aP0Nrt4AEnTUbuGvquEC0mA64Gqt1fzirlRs5ibXx8g==", "license": "BSD-3-<PERSON><PERSON>", "resolved": "https://registry.npmjs.org/sprintf-js/-/sprintf-js-1.0.3.tgz", "version": "1.0.3"}, "node_modules/@istanbuljs/schema": {"dev": true, "engines": {"node": ">=8"}, "integrity": "sha512-ZXRY4jNvVgSVQ8DL3LTcakaAtXwTVUxE81hslsyD2AtoXW/wVob10HkOJ1X/pAlcI7D+2YoZKg5do8G/w6RYgA==", "license": "MIT", "resolved": "https://registry.npmjs.org/@istanbuljs/schema/-/schema-0.1.3.tgz", "version": "0.1.3"}, "node_modules/@jest/console": {"dependencies": {"@jest/types": "^29.6.3", "@types/node": "*", "chalk": "^4.0.0", "jest-message-util": "^29.7.0", "jest-util": "^29.7.0", "slash": "^3.0.0"}, "dev": true, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "integrity": "sha512-5Ni4CU7XHQi32IJ398EEP4RrB8eV09sXP2ROqD4bksHrnTree52PsxvX8tpL8LvTZ3pFzXyPbNQReSN41CAhOg==", "license": "MIT", "resolved": "https://registry.npmjs.org/@jest/console/-/console-29.7.0.tgz", "version": "29.7.0"}, "node_modules/@jest/core": {"dependencies": {"@jest/console": "^29.7.0", "@jest/reporters": "^29.7.0", "@jest/test-result": "^29.7.0", "@jest/transform": "^29.7.0", "@jest/types": "^29.6.3", "@types/node": "*", "ansi-escapes": "^4.2.1", "chalk": "^4.0.0", "ci-info": "^3.2.0", "exit": "^0.1.2", "graceful-fs": "^4.2.9", "jest-changed-files": "^29.7.0", "jest-config": "^29.7.0", "jest-haste-map": "^29.7.0", "jest-message-util": "^29.7.0", "jest-regex-util": "^29.6.3", "jest-resolve": "^29.7.0", "jest-resolve-dependencies": "^29.7.0", "jest-runner": "^29.7.0", "jest-runtime": "^29.7.0", "jest-snapshot": "^29.7.0", "jest-util": "^29.7.0", "jest-validate": "^29.7.0", "jest-watcher": "^29.7.0", "micromatch": "^4.0.4", "pretty-format": "^29.7.0", "slash": "^3.0.0", "strip-ansi": "^6.0.0"}, "dev": true, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "integrity": "sha512-n7aeXWKMnGtDA48y8TLWJPJmLmmZ642Ceo78cYWEpiD7FzDgmNDV/GCVRorPABdXLJZ/9wzzgZAlHjXjxDHGsg==", "license": "MIT", "peerDependencies": {"node-notifier": "^8.0.1 || ^9.0.0 || ^10.0.0"}, "peerDependenciesMeta": {"node-notifier": {"optional": true}}, "resolved": "https://registry.npmjs.org/@jest/core/-/core-29.7.0.tgz", "version": "29.7.0"}, "node_modules/@jest/environment": {"dependencies": {"@jest/fake-timers": "^29.7.0", "@jest/types": "^29.6.3", "@types/node": "*", "jest-mock": "^29.7.0"}, "dev": true, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "integrity": "sha512-aQIfHDq33ExsN4jP1NWGXhxgQ/wixs60gDiKO+XVMd8Mn0NWPWgc34ZQDTb2jKaUWQ7MuwoitXAsN2XVXNMpAw==", "license": "MIT", "resolved": "https://registry.npmjs.org/@jest/environment/-/environment-29.7.0.tgz", "version": "29.7.0"}, "node_modules/@jest/expect": {"dependencies": {"expect": "^29.7.0", "jest-snapshot": "^29.7.0"}, "dev": true, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "integrity": "sha512-8uMeAMycttpva3P1lBHB8VciS9V0XAr3GymPpipdyQXbBcuhkLQOSe8E/p92RyAdToS6ZD1tFkX+CkhoECE0dQ==", "license": "MIT", "resolved": "https://registry.npmjs.org/@jest/expect/-/expect-29.7.0.tgz", "version": "29.7.0"}, "node_modules/@jest/expect-utils": {"dependencies": {"jest-get-type": "^29.6.3"}, "dev": true, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "integrity": "sha512-GlsNBWiFQFCVi9QVSx7f5AgMeLxe9YCCs5PuP2O2LdjDAA8Jh9eX7lA1Jq/xdXw3Wb3hyvlFNfZIfcRetSzYcA==", "license": "MIT", "resolved": "https://registry.npmjs.org/@jest/expect-utils/-/expect-utils-29.7.0.tgz", "version": "29.7.0"}, "node_modules/@jest/fake-timers": {"dependencies": {"@jest/types": "^29.6.3", "@sinonjs/fake-timers": "^10.0.2", "@types/node": "*", "jest-message-util": "^29.7.0", "jest-mock": "^29.7.0", "jest-util": "^29.7.0"}, "dev": true, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "integrity": "sha512-q4DH1Ha4TTFPdxLsqDXK1d3+ioSL7yL5oCMJZgDYm6i+6CygW5E5xVr/D1HdsGxjt1ZWSfUAs9OxSB/BNelWrQ==", "license": "MIT", "resolved": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-29.7.0.tgz", "version": "29.7.0"}, "node_modules/@jest/globals": {"dependencies": {"@jest/environment": "^29.7.0", "@jest/expect": "^29.7.0", "@jest/types": "^29.6.3", "jest-mock": "^29.7.0"}, "dev": true, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "integrity": "sha512-mpiz3dutLbkW2MNFubUGUEVLkTGiqW6yLVTA+JbP6fI6J5iL9Y0Nlg8k95pcF8ctKwCS7WVxteBs29hhfAotzQ==", "license": "MIT", "resolved": "https://registry.npmjs.org/@jest/globals/-/globals-29.7.0.tgz", "version": "29.7.0"}, "node_modules/@jest/reporters": {"dependencies": {"@bcoe/v8-coverage": "^0.2.3", "@jest/console": "^29.7.0", "@jest/test-result": "^29.7.0", "@jest/transform": "^29.7.0", "@jest/types": "^29.6.3", "@jridgewell/trace-mapping": "^0.3.18", "@types/node": "*", "chalk": "^4.0.0", "collect-v8-coverage": "^1.0.0", "exit": "^0.1.2", "glob": "^7.1.3", "graceful-fs": "^4.2.9", "istanbul-lib-coverage": "^3.0.0", "istanbul-lib-instrument": "^6.0.0", "istanbul-lib-report": "^3.0.0", "istanbul-lib-source-maps": "^4.0.0", "istanbul-reports": "^3.1.3", "jest-message-util": "^29.7.0", "jest-util": "^29.7.0", "jest-worker": "^29.7.0", "slash": "^3.0.0", "string-length": "^4.0.1", "strip-ansi": "^6.0.0", "v8-to-istanbul": "^9.0.1"}, "dev": true, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "integrity": "sha512-DApq0KJbJOEzAFYjHADNNxAE3KbhxQB1y5Kplb5Waqw6zVbuWatSnMjE5gs8FUgEPmNsnZA3NCWl9NG0ia04Pg==", "license": "MIT", "peerDependencies": {"node-notifier": "^8.0.1 || ^9.0.0 || ^10.0.0"}, "peerDependenciesMeta": {"node-notifier": {"optional": true}}, "resolved": "https://registry.npmjs.org/@jest/reporters/-/reporters-29.7.0.tgz", "version": "29.7.0"}, "node_modules/@jest/schemas": {"dependencies": {"@sinclair/typebox": "^0.27.8"}, "dev": true, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "integrity": "sha512-mo5j5X+jIZmJQveBKeS/clAueipV7KgiX1vMgCxam1RNYiqE1w62n0/tJJnHtjW8ZHcQco5gY85jA3mi0L+nSA==", "license": "MIT", "resolved": "https://registry.npmjs.org/@jest/schemas/-/schemas-29.6.3.tgz", "version": "29.6.3"}, "node_modules/@jest/source-map": {"dependencies": {"@jridgewell/trace-mapping": "^0.3.18", "callsites": "^3.0.0", "graceful-fs": "^4.2.9"}, "dev": true, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "integrity": "sha512-MHjT95QuipcPrpLM+8JMSzFx6eHp5Bm+4XeFDJlwsvVBjmKNiIAvasGK2fxz2WbGRlnvqehFbh07MMa7n3YJnw==", "license": "MIT", "resolved": "https://registry.npmjs.org/@jest/source-map/-/source-map-29.6.3.tgz", "version": "29.6.3"}, "node_modules/@jest/test-result": {"dependencies": {"@jest/console": "^29.7.0", "@jest/types": "^29.6.3", "@types/istanbul-lib-coverage": "^2.0.0", "collect-v8-coverage": "^1.0.0"}, "dev": true, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "integrity": "sha512-Fdx+tv6x1zlkJPcWXmMDAG2HBnaR9XPSd5aDWQVsfrZmLVT3lU1cwyxLgRmXR9yrq4NBoEm9BMsfgFzTQAbJYA==", "license": "MIT", "resolved": "https://registry.npmjs.org/@jest/test-result/-/test-result-29.7.0.tgz", "version": "29.7.0"}, "node_modules/@jest/test-sequencer": {"dependencies": {"@jest/test-result": "^29.7.0", "graceful-fs": "^4.2.9", "jest-haste-map": "^29.7.0", "slash": "^3.0.0"}, "dev": true, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "integrity": "sha512-GQwJ5WZVrKnOJuiYiAF52UNUJXgTZx1NHjFSEB0qEMmSZKAkdMoIzw/Cj6x6NF4AvV23AUqDpFzQkN/eYCYTxw==", "license": "MIT", "resolved": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-29.7.0.tgz", "version": "29.7.0"}, "node_modules/@jest/transform": {"dependencies": {"@babel/core": "^7.11.6", "@jest/types": "^29.6.3", "@jridgewell/trace-mapping": "^0.3.18", "babel-plugin-istanbul": "^6.1.1", "chalk": "^4.0.0", "convert-source-map": "^2.0.0", "fast-json-stable-stringify": "^2.1.0", "graceful-fs": "^4.2.9", "jest-haste-map": "^29.7.0", "jest-regex-util": "^29.6.3", "jest-util": "^29.7.0", "micromatch": "^4.0.4", "pirates": "^4.0.4", "slash": "^3.0.0", "write-file-atomic": "^4.0.2"}, "dev": true, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "integrity": "sha512-ok/BTPFzFKVMwO5eOHRrvnBVHdRy9IrsrW1GpMaQ9MCnilNLXQKmAX8s1YXDFaai9xJpac2ySzV0YeRRECr2Vw==", "license": "MIT", "resolved": "https://registry.npmjs.org/@jest/transform/-/transform-29.7.0.tgz", "version": "29.7.0"}, "node_modules/@jest/types": {"dependencies": {"@jest/schemas": "^29.6.3", "@types/istanbul-lib-coverage": "^2.0.0", "@types/istanbul-reports": "^3.0.0", "@types/node": "*", "@types/yargs": "^17.0.8", "chalk": "^4.0.0"}, "dev": true, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "integrity": "sha512-u3UPsIilWKOM3F9CXtrG8LEJmNxwoCQC/XVj4IKYXvvpx7QIi/Kg1LI5uDmDpKlac62NUtX7eLjRh+jVZcLOzw==", "license": "MIT", "resolved": "https://registry.npmjs.org/@jest/types/-/types-29.6.3.tgz", "version": "29.6.3"}, "node_modules/@jridgewell/gen-mapping": {"dependencies": {"@jridgewell/sourcemap-codec": "^1.5.0", "@jridgewell/trace-mapping": "^0.3.24"}, "dev": true, "integrity": "sha512-OuLGC46TjB5BbN1dH8JULVVZY4WTdkF7tV9Ys6wLL1rubZnCMstOhNHueU5bLCrnRuDhKPDM4g6sw4Bel5Gzqg==", "license": "MIT", "resolved": "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.12.tgz", "version": "0.3.12"}, "node_modules/@jridgewell/resolve-uri": {"dev": true, "engines": {"node": ">=6.0.0"}, "integrity": "sha512-bRISgCIjP20/tbWSPWMEi54QVPRZExkuD9lJL+UIxUKtwVJA8wW1Trb1jMs1RFXo1CBTNZ/5hpC9QvmKWdopKw==", "license": "MIT", "resolved": "https://registry.npmjs.org/@jridgewell/resolve-uri/-/resolve-uri-3.1.2.tgz", "version": "3.1.2"}, "node_modules/@jridgewell/sourcemap-codec": {"dev": true, "integrity": "sha512-VT2+G1VQs/9oz078bLrYbecdZKs912zQlkelYpuf+SXF+QvZDYJlbx/LSx+meSAwdDFnF8FVXW92AVjjkVmgFw==", "license": "MIT", "resolved": "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.4.tgz", "version": "1.5.4"}, "node_modules/@jridgewell/trace-mapping": {"dependencies": {"@jridgewell/resolve-uri": "^3.1.0", "@jridgewell/sourcemap-codec": "^1.4.14"}, "dev": true, "integrity": "sha512-uw6guiW/gcAGPDhLmd77/6lW8QLeiV5RUTsAX46Db6oLhGaVj4lhnPwb184s1bkc8kdVg/+h988dro8GRDpmYQ==", "license": "MIT", "resolved": "https://registry.npmjs.org/@jridgewell/trace-mapping/-/trace-mapping-0.3.29.tgz", "version": "0.3.29"}, "node_modules/@malept/cross-spawn-promise": {"dependencies": {"cross-spawn": "^7.0.1"}, "dev": true, "engines": {"node": ">= 10"}, "funding": [{"type": "individual", "url": "https://github.com/sponsors/malept"}, {"type": "tidelift", "url": "https://tidelift.com/subscription/pkg/npm-.malept-cross-spawn-promise?utm_medium=referral&utm_source=npm_fund"}], "integrity": "sha512-RTBGWL5FWQcg9orDOCcp4LvItNzUPcyEU9bwaeJX0rJ1IQxzucC48Y0/sQLp/g6t99IQgAlGIaesJS+gTn7tVQ==", "license": "Apache-2.0", "resolved": "https://registry.npmjs.org/@malept/cross-spawn-promise/-/cross-spawn-promise-1.1.1.tgz", "version": "1.1.1"}, "node_modules/@malept/flatpak-bundler": {"dependencies": {"debug": "^4.1.1", "fs-extra": "^9.0.0", "lodash": "^4.17.15", "tmp-promise": "^3.0.2"}, "dev": true, "engines": {"node": ">= 10.0.0"}, "integrity": "sha512-9QOtNffcOF/c1seMCDnjckb3R9WHcG34tky+FHpNKKCW0wc/scYLwMtO+ptyGUfMW0/b/n4qRiALlaFHc9Oj7Q==", "license": "MIT", "resolved": "https://registry.npmjs.org/@malept/flatpak-bundler/-/flatpak-bundler-0.4.0.tgz", "version": "0.4.0"}, "node_modules/@malept/flatpak-bundler/node_modules/fs-extra": {"dependencies": {"at-least-node": "^1.0.0", "graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}, "dev": true, "engines": {"node": ">=10"}, "integrity": "sha512-hcg3ZmepS30/7BSFqRvoo3DOMQu7IjqxO5nCDt+zM9XWjb33Wg7ziNT+Qvqbuc3+gWpzO02JubVyk2G4Zvo1OQ==", "license": "MIT", "resolved": "https://registry.npmjs.org/fs-extra/-/fs-extra-9.1.0.tgz", "version": "9.1.0"}, "node_modules/@malept/flatpak-bundler/node_modules/jsonfile": {"dependencies": {"universalify": "^2.0.0"}, "dev": true, "integrity": "sha512-5dgndWOriYSm5cnYaJNhalLNDKOqFwyDB/rr1E9ZsGciGvKPs8R2xYGCacuf3z6K1YKDz182fd+fY3cn3pMqXQ==", "license": "MIT", "optionalDependencies": {"graceful-fs": "^4.1.6"}, "resolved": "https://registry.npmjs.org/jsonfile/-/jsonfile-6.1.0.tgz", "version": "6.1.0"}, "node_modules/@malept/flatpak-bundler/node_modules/universalify": {"dev": true, "engines": {"node": ">= 10.0.0"}, "integrity": "sha512-gptHNQghINnc/vTGIk0SOFGFNXw7JVrlRUtConJRlvaw6DuX0wO5Jeko9sWrMBhh+PsYAZ7oXAiOnf/UKogyiw==", "license": "MIT", "resolved": "https://registry.npmjs.org/universalify/-/universalify-2.0.1.tgz", "version": "2.0.1"}, "node_modules/@pkgjs/parseargs": {"dev": true, "engines": {"node": ">=14"}, "integrity": "sha512-+1VkjdD0QBLPodGrJUeqarH8VAIvQODIbwh9XpP5Syisf7YoQgsJKPNFoqqLQlu+VQ/tVSshMR6loPMn8U+dPg==", "license": "MIT", "optional": true, "resolved": "https://registry.npmjs.org/@pkgjs/parseargs/-/parseargs-0.11.0.tgz", "version": "0.11.0"}, "node_modules/@sinclair/typebox": {"dev": true, "integrity": "sha512-+Fj43pSMwJs4KRrH/938Uf+uAELIgVBmQzg/q1YG10djyfA3TnrU8N8XzqCh/okZdszqBQTZf96idMfE5lnwTA==", "license": "MIT", "resolved": "https://registry.npmjs.org/@sinclair/typebox/-/typebox-0.27.8.tgz", "version": "0.27.8"}, "node_modules/@sindresorhus/is": {"dev": true, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sindresorhus/is?sponsor=1"}, "integrity": "sha512-t09vSN3MdfsyCHoFcTRCH/iUtG7OJ0CsjzB8cjAmKc/va/kIgeDI/TxsigdncE/4be734m0cvIYwNaV4i2XqAw==", "license": "MIT", "resolved": "https://registry.npmjs.org/@sindresorhus/is/-/is-4.6.0.tgz", "version": "4.6.0"}, "node_modules/@sinonjs/commons": {"dependencies": {"type-detect": "4.0.8"}, "dev": true, "integrity": "sha512-K3mCHKQ9sVh8o1C9cxkwxaOmXoAMlDxC1mYyHrjqOWEcBjYr76t96zL2zlj5dUGZ3HSw240X1qgH3Mjf1yJWpQ==", "license": "BSD-3-<PERSON><PERSON>", "resolved": "https://registry.npmjs.org/@sinonjs/commons/-/commons-3.0.1.tgz", "version": "3.0.1"}, "node_modules/@sinonjs/fake-timers": {"dependencies": {"@sinonjs/commons": "^3.0.0"}, "dev": true, "integrity": "sha512-V4BG07kuYSUkTCSBHG8G8TNhM+F19jXFWnQtzj+we8DrkpSBCee9Z3Ms8yiGer/dlmhe35/Xdgyo3/0rQKg7YA==", "license": "BSD-3-<PERSON><PERSON>", "resolved": "https://registry.npmjs.org/@sinonjs/fake-timers/-/fake-timers-10.3.0.tgz", "version": "10.3.0"}, "node_modules/@szmarczak/http-timer": {"dependencies": {"defer-to-connect": "^2.0.0"}, "dev": true, "engines": {"node": ">=10"}, "integrity": "sha512-4BAffykYOgO+5nzBWYwE3W90sBgLJoUPRWWcL8wlyiM8IB8ipJz3UMJ9KXQd1RKQXpKp8Tutn80HZtWsu2u76w==", "license": "MIT", "resolved": "https://registry.npmjs.org/@szmarczak/http-timer/-/http-timer-4.0.6.tgz", "version": "4.0.6"}, "node_modules/@tootallnate/once": {"dev": true, "engines": {"node": ">= 10"}, "integrity": "sha512-XCuKFP5PS55gnMVu3dty8KPatLqUoy/ZYzDzAGCQ8JNFCkLXzmI7vNHCR+XpbZaMWQK/vQubr7PkYq8g470J/A==", "license": "MIT", "resolved": "https://registry.npmjs.org/@tootallnate/once/-/once-2.0.0.tgz", "version": "2.0.0"}, "node_modules/@types/babel__core": {"dependencies": {"@babel/parser": "^7.20.7", "@babel/types": "^7.20.7", "@types/babel__generator": "*", "@types/babel__template": "*", "@types/babel__traverse": "*"}, "dev": true, "integrity": "sha512-qoQprZvz5wQFJwMDqeseRXWv3rqMvhgpbXFfVyWhbx9X47POIA6i/+dXefEmZKoAgOaTdaIgNSMqMIU61yRyzA==", "license": "MIT", "resolved": "https://registry.npmjs.org/@types/babel__core/-/babel__core-7.20.5.tgz", "version": "7.20.5"}, "node_modules/@types/babel__generator": {"dependencies": {"@babel/types": "^7.0.0"}, "dev": true, "integrity": "sha512-ufFd2Xi92OAVPYsy+P4n7/U7e68fex0+Ee8gSG9KX7eo084CWiQ4sdxktvdl0bOPupXtVJPY19zk6EwWqUQ8lg==", "license": "MIT", "resolved": "https://registry.npmjs.org/@types/babel__generator/-/babel__generator-7.27.0.tgz", "version": "7.27.0"}, "node_modules/@types/babel__template": {"dependencies": {"@babel/parser": "^7.1.0", "@babel/types": "^7.0.0"}, "dev": true, "integrity": "sha512-h/NUaSyG5EyxBIp8YRxo4RMe2/qQgvyowRwVMzhYhBCONbW8PUsg4lkFMrhgZhUe5z3L3MiLDuvyJ/CaPa2A8A==", "license": "MIT", "resolved": "https://registry.npmjs.org/@types/babel__template/-/babel__template-7.4.4.tgz", "version": "7.4.4"}, "node_modules/@types/babel__traverse": {"dependencies": {"@babel/types": "^7.20.7"}, "dev": true, "integrity": "sha512-dkO5fhS7+/oos4ciWxyEyjWe48zmG6wbCheo/G2ZnHx4fs3EU6YC6UM8rk56gAjNJ9P3MTH2jo5jb92/K6wbng==", "license": "MIT", "resolved": "https://registry.npmjs.org/@types/babel__traverse/-/babel__traverse-7.20.7.tgz", "version": "7.20.7"}, "node_modules/@types/cacheable-request": {"dependencies": {"@types/http-cache-semantics": "*", "@types/keyv": "^3.1.4", "@types/node": "*", "@types/responselike": "^1.0.0"}, "dev": true, "integrity": "sha512-IQ3EbTzGxIigb1I3qPZc1rWJnH0BmSKv5QYTalEwweFvyBDLSAe24zP0le/hyi7ecGfZVlIVAg4BZqb8WBwKqw==", "license": "MIT", "resolved": "https://registry.npmjs.org/@types/cacheable-request/-/cacheable-request-6.0.3.tgz", "version": "6.0.3"}, "node_modules/@types/debug": {"dependencies": {"@types/ms": "*"}, "dev": true, "integrity": "sha512-vIChWdVG3LG1SMxEvI/AK+FWJthlrqlTu7fbrlywTkkaONwk/UAGaULXRlf8vkzFBLVm0zkMdCquhL5aOjhXPQ==", "license": "MIT", "resolved": "https://registry.npmjs.org/@types/debug/-/debug-4.1.12.tgz", "version": "4.1.12"}, "node_modules/@types/fs-extra": {"dependencies": {"@types/node": "*"}, "dev": true, "integrity": "sha512-nEnwB++1u5lVDM2UI4c1+5R+FYaKfaAzS4OococimjVm3nQw3TuzH5UNsocrcTBbhnerblyHj4A49qXbIiZdpA==", "license": "MIT", "resolved": "https://registry.npmjs.org/@types/fs-extra/-/fs-extra-9.0.13.tgz", "version": "9.0.13"}, "node_modules/@types/graceful-fs": {"dependencies": {"@types/node": "*"}, "dev": true, "integrity": "sha512-olP3sd1qOEe5dXTSaFvQG+02VdRXcdytWLAZsAq1PecU8uqQAhkrnbli7DagjtXKW/Bl7YJbUsa8MPcuc8LHEQ==", "license": "MIT", "resolved": "https://registry.npmjs.org/@types/graceful-fs/-/graceful-fs-4.1.9.tgz", "version": "4.1.9"}, "node_modules/@types/http-cache-semantics": {"dev": true, "integrity": "sha512-1m0bIFVc7eJWyve9S0RnuRgcQqF/Xd5QsUZAZeQFr1Q3/p9JWoQQEqmVy+DPTNpGXwhgIetAoYF8JSc33q29QA==", "license": "MIT", "resolved": "https://registry.npmjs.org/@types/http-cache-semantics/-/http-cache-semantics-4.0.4.tgz", "version": "4.0.4"}, "node_modules/@types/istanbul-lib-coverage": {"dev": true, "integrity": "sha512-2QF/t/auWm0lsy8XtKVPG19v3sSOQlJe/YHZgfjb/KBBHOGSV+J2q/S671rcq9uTBrLAXmZpqJiaQbMT+zNU1w==", "license": "MIT", "resolved": "https://registry.npmjs.org/@types/istanbul-lib-coverage/-/istanbul-lib-coverage-2.0.6.tgz", "version": "2.0.6"}, "node_modules/@types/istanbul-lib-report": {"dependencies": {"@types/istanbul-lib-coverage": "*"}, "dev": true, "integrity": "sha512-NQn7AHQnk/RSLOxrBbGyJM/aVQ+pjj5HCgasFxc0K/KhoATfQ/47AyUl15I2yBUpihjmas+a+VJBOqecrFH+uA==", "license": "MIT", "resolved": "https://registry.npmjs.org/@types/istanbul-lib-report/-/istanbul-lib-report-3.0.3.tgz", "version": "3.0.3"}, "node_modules/@types/istanbul-reports": {"dependencies": {"@types/istanbul-lib-report": "*"}, "dev": true, "integrity": "sha512-pk2B1NWalF9toCRu6gjBzR69syFjP4Od8WRAX+0mmf9lAjCRicLOWc+ZrxZHx/0XRjotgkF9t6iaMJ+aXcOdZQ==", "license": "MIT", "resolved": "https://registry.npmjs.org/@types/istanbul-reports/-/istanbul-reports-3.0.4.tgz", "version": "3.0.4"}, "node_modules/@types/keyv": {"dependencies": {"@types/node": "*"}, "dev": true, "integrity": "sha512-BQ5aZNSCpj7D6K2ksrRCTmKRLEpnPvWDiLPfoGyhZ++8YtiK9d/3DBKPJgry359X/P1PfruyYwvnvwFjuEiEIg==", "license": "MIT", "resolved": "https://registry.npmjs.org/@types/keyv/-/keyv-3.1.4.tgz", "version": "3.1.4"}, "node_modules/@types/ms": {"dev": true, "integrity": "sha512-GsCCIZDE/p3i96vtEqx+7dBUGXrc7zeSK3wwPHIaRThS+9OhWIXRqzs4d6k1SVU8g91DrNRWxWUGhp5KXQb2VA==", "license": "MIT", "resolved": "https://registry.npmjs.org/@types/ms/-/ms-2.1.0.tgz", "version": "2.1.0"}, "node_modules/@types/node": {"dependencies": {"undici-types": "~6.21.0"}, "dev": true, "integrity": "sha512-OP+We5WV8Xnbuvw0zC2m4qfB/BJvjyCwtNjhHdJxV1639SGSKrLmJkc3fMnp2Qy8nJyHp8RO6umxELN/dS1/EA==", "license": "MIT", "resolved": "https://registry.npmjs.org/@types/node/-/node-20.19.4.tgz", "version": "20.19.4"}, "node_modules/@types/plist": {"dependencies": {"@types/node": "*", "xmlbuilder": ">=11.0.1"}, "dev": true, "integrity": "sha512-E6OCaRmAe4WDmWNsL/9RMqdkkzDCY1etutkflWk4c+AcjDU07Pcz1fQwTX0TQz+Pxqn9i4L1TU3UFpjnrcDgxA==", "license": "MIT", "optional": true, "resolved": "https://registry.npmjs.org/@types/plist/-/plist-3.0.5.tgz", "version": "3.0.5"}, "node_modules/@types/responselike": {"dependencies": {"@types/node": "*"}, "dev": true, "integrity": "sha512-H/+L+UkTV33uf49PH5pCAUBVPNj2nDBXTN+qS1dOwyyg24l3CcicicCA7ca+HMvJBZcFgl5r8e+RR6elsb4Lyw==", "license": "MIT", "resolved": "https://registry.npmjs.org/@types/responselike/-/responselike-1.0.3.tgz", "version": "1.0.3"}, "node_modules/@types/stack-utils": {"dev": true, "integrity": "sha512-9aEbYZ3TbYMznPdcdr3SmIrLXwC/AKZXQeCf9Pgao5CKb8CyHuEX5jzWPTkvregvhRJHcpRO6BFoGW9ycaOkYw==", "license": "MIT", "resolved": "https://registry.npmjs.org/@types/stack-utils/-/stack-utils-2.0.3.tgz", "version": "2.0.3"}, "node_modules/@types/verror": {"dev": true, "integrity": "sha512-RlDm9K7+o5stv0Co8i8ZRGxDbrTxhJtgjqjFyVh/tXQyl/rYtTKlnTvZ88oSTeYREWurwx20Js4kTuKCsFkUtg==", "license": "MIT", "optional": true, "resolved": "https://registry.npmjs.org/@types/verror/-/verror-1.10.11.tgz", "version": "1.10.11"}, "node_modules/@types/yargs": {"dependencies": {"@types/yargs-parser": "*"}, "dev": true, "integrity": "sha512-WpxBCKWPLr4xSsHgz511rFJAM+wS28w2zEO1QDNY5zM/S8ok70NNfztH0xwhqKyaK0OHCbN98LDAZuy1ctxDkA==", "license": "MIT", "resolved": "https://registry.npmjs.org/@types/yargs/-/yargs-17.0.33.tgz", "version": "17.0.33"}, "node_modules/@types/yargs-parser": {"dev": true, "integrity": "sha512-I4q9QU9MQv4oEOz4tAHJtNz1cwuLxn2F3xcc2iV5WdqLPpUnj30aUuxt1mAxYTG+oe8CZMV/+6rU4S4gRDzqtQ==", "license": "MIT", "resolved": "https://registry.npmjs.org/@types/yargs-parser/-/yargs-parser-21.0.3.tgz", "version": "21.0.3"}, "node_modules/@types/yauzl": {"dependencies": {"@types/node": "*"}, "dev": true, "integrity": "sha512-oJoftv0LSuaDZE3Le4DbKX+KS9G36NzOeSap90UIK0yMA/NhKJhqlSGtNDORNRaIbQfzjXDrQa0ytJ6mNRGz/Q==", "license": "MIT", "optional": true, "resolved": "https://registry.npmjs.org/@types/yauzl/-/yauzl-2.10.3.tgz", "version": "2.10.3"}, "node_modules/@xmldom/xmldom": {"dev": true, "engines": {"node": ">=10.0.0"}, "integrity": "sha512-2WALfTl4xo2SkGCYRt6rDTFfk9R1czmBvUQy12gK2KuRKIpWEhcbbzy8EZXtz/jkRqHX8bFEc6FC1HjX4TUWYw==", "license": "MIT", "resolved": "https://registry.npmjs.org/@xmldom/xmldom/-/xmldom-0.8.10.tgz", "version": "0.8.10"}, "node_modules/agent-base": {"dependencies": {"debug": "4"}, "dev": true, "engines": {"node": ">= 6.0.0"}, "integrity": "sha512-RZNwNclF7+MS/8bDg70amg32dyeZGZxiDuQmZxKLAlQjr3jGyLx+4Kkk58UO7D2QdgFIQCovuSuZESne6RG6XQ==", "license": "MIT", "resolved": "https://registry.npmjs.org/agent-base/-/agent-base-6.0.2.tgz", "version": "6.0.2"}, "node_modules/ajv": {"dependencies": {"fast-deep-equal": "^3.1.3", "fast-uri": "^3.0.1", "json-schema-traverse": "^1.0.0", "require-from-string": "^2.0.2"}, "funding": {"type": "github", "url": "https://github.com/sponsors/epoberezkin"}, "integrity": "sha512-B/gBuNg5SiMTrPkC+A2+cW0RszwxYmn6VYxB/inlBStS5nx6xHIt/ehKRhIMhqusl7a8LjQoZnjCs5vhwxOQ1g==", "license": "MIT", "resolved": "https://registry.npmjs.org/ajv/-/ajv-8.17.1.tgz", "version": "8.17.1"}, "node_modules/ansi-escapes": {"dependencies": {"type-fest": "^0.21.3"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}, "integrity": "sha512-gKXj5ALrKWQLsYG9jlTRmR/xKluxHV+Z9QEwNIgCfM1/uwPMCuzVVnh5mwTd+OuBZcwSIMbqssNWRm1lE51QaQ==", "license": "MIT", "resolved": "https://registry.npmjs.org/ansi-escapes/-/ansi-escapes-4.3.2.tgz", "version": "4.3.2"}, "node_modules/ansi-escapes/node_modules/type-fest": {"engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}, "integrity": "sha512-t0rzBq87m3fVcduHDUFhKmyyX+9eo6WQjZvf51Ea/M0Q7+T374Jp1aUiyUl0GKxp8M/OETVHSDvmkyPgvX+X2w==", "license": "(MIT OR CC0-1.0)", "resolved": "https://registry.npmjs.org/type-fest/-/type-fest-0.21.3.tgz", "version": "0.21.3"}, "node_modules/ansi-regex": {"engines": {"node": ">=8"}, "integrity": "sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==", "license": "MIT", "resolved": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz", "version": "5.0.1"}, "node_modules/ansi-styles": {"dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}, "integrity": "sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==", "license": "MIT", "resolved": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.3.0.tgz", "version": "4.3.0"}, "node_modules/anymatch": {"dependencies": {"normalize-path": "^3.0.0", "picomatch": "^2.0.4"}, "dev": true, "engines": {"node": ">= 8"}, "integrity": "sha512-KMReFUr0B4t+D+OBkjR3KYqvocp2XaSzO55UcB6mgQMd3KbcE+mWTyvVV7D/zsdEbNnV6acZUutkiHQXvTr1Rw==", "license": "ISC", "resolved": "https://registry.npmjs.org/anymatch/-/anymatch-3.1.3.tgz", "version": "3.1.3"}, "node_modules/app-builder-bin": {"dev": true, "integrity": "sha512-xwdG0FJPQMe0M0UA4Tz0zEB8rBJTRA5a476ZawAqiBkMv16GRK5xpXThOjMaEOFnZ6zabejjG4J3da0SXG63KA==", "license": "MIT", "resolved": "https://registry.npmjs.org/app-builder-bin/-/app-builder-bin-4.0.0.tgz", "version": "4.0.0"}, "node_modules/app-builder-lib": {"dependencies": {"@develar/schema-utils": "~2.6.5", "@electron/notarize": "2.2.1", "@electron/osx-sign": "1.0.5", "@electron/universal": "1.5.1", "@malept/flatpak-bundler": "^0.4.0", "@types/fs-extra": "9.0.13", "async-exit-hook": "^2.0.1", "bluebird-lst": "^1.0.9", "builder-util": "24.13.1", "builder-util-runtime": "9.2.4", "chromium-pickle-js": "^0.2.0", "debug": "^4.3.4", "ejs": "^3.1.8", "electron-publish": "24.13.1", "form-data": "^4.0.0", "fs-extra": "^10.1.0", "hosted-git-info": "^4.1.0", "is-ci": "^3.0.0", "isbinaryfile": "^5.0.0", "js-yaml": "^4.1.0", "lazy-val": "^1.0.5", "minimatch": "^5.1.1", "read-config-file": "6.3.2", "sanitize-filename": "^1.6.3", "semver": "^7.3.8", "tar": "^6.1.12", "temp-file": "^3.4.0"}, "dev": true, "engines": {"node": ">=14.0.0"}, "integrity": "sha512-FAzX6IBit2POXYGnTCT8YHFO/lr5AapAII6zzhQO3Rw4cEDOgK+t1xhLc5tNcKlicTHlo9zxIwnYCX9X2DLkig==", "license": "MIT", "peerDependencies": {"dmg-builder": "24.13.3", "electron-builder-squirrel-windows": "24.13.3"}, "resolved": "https://registry.npmjs.org/app-builder-lib/-/app-builder-lib-24.13.3.tgz", "version": "24.13.3"}, "node_modules/app-builder-lib/node_modules/fs-extra": {"dependencies": {"graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}, "dev": true, "engines": {"node": ">=12"}, "integrity": "sha512-oRXApq54ETRj4eMiFzGnHWGy+zo5raudjuxN0b8H7s/RU2oW0Wvsx9O0ACRN/kRq9E8Vu/ReskGB5o3ji+FzHQ==", "license": "MIT", "resolved": "https://registry.npmjs.org/fs-extra/-/fs-extra-10.1.0.tgz", "version": "10.1.0"}, "node_modules/app-builder-lib/node_modules/jsonfile": {"dependencies": {"universalify": "^2.0.0"}, "dev": true, "integrity": "sha512-5dgndWOriYSm5cnYaJNhalLNDKOqFwyDB/rr1E9ZsGciGvKPs8R2xYGCacuf3z6K1YKDz182fd+fY3cn3pMqXQ==", "license": "MIT", "optionalDependencies": {"graceful-fs": "^4.1.6"}, "resolved": "https://registry.npmjs.org/jsonfile/-/jsonfile-6.1.0.tgz", "version": "6.1.0"}, "node_modules/app-builder-lib/node_modules/universalify": {"dev": true, "engines": {"node": ">= 10.0.0"}, "integrity": "sha512-gptHNQghINnc/vTGIk0SOFGFNXw7JVrlRUtConJRlvaw6DuX0wO5Jeko9sWrMBhh+PsYAZ7oXAiOnf/UKogyiw==", "license": "MIT", "resolved": "https://registry.npmjs.org/universalify/-/universalify-2.0.1.tgz", "version": "2.0.1"}, "node_modules/archiver": {"dependencies": {"archiver-utils": "^4.0.1", "async": "^3.2.4", "buffer-crc32": "^0.2.1", "readable-stream": "^3.6.0", "readdir-glob": "^1.1.2", "tar-stream": "^3.0.0", "zip-stream": "^5.0.1"}, "engines": {"node": ">= 12.0.0"}, "integrity": "sha512-UQ/2nW7NMl1G+1UnrLypQw1VdT9XZg/ECcKPq7l+STzStrSivFIXIp34D8M5zeNGW5NoOupdYCHv6VySCPNNlw==", "license": "MIT", "resolved": "https://registry.npmjs.org/archiver/-/archiver-6.0.2.tgz", "version": "6.0.2"}, "node_modules/archiver-utils": {"dependencies": {"glob": "^8.0.0", "graceful-fs": "^4.2.0", "lazystream": "^1.0.0", "lodash": "^4.17.15", "normalize-path": "^3.0.0", "readable-stream": "^3.6.0"}, "engines": {"node": ">= 12.0.0"}, "integrity": "sha512-Q4Q99idbvzmgCTEAAhi32BkOyq8iVI5EwdO0PmBDSGIzzjYNdcFn7Q7k3OzbLy4kLUPXfJtG6fO2RjftXbobBg==", "license": "MIT", "resolved": "https://registry.npmjs.org/archiver-utils/-/archiver-utils-4.0.1.tgz", "version": "4.0.1"}, "node_modules/archiver-utils/node_modules/glob": {"dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^5.0.1", "once": "^1.3.0"}, "deprecated": "Glob versions prior to v9 are no longer supported", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "integrity": "sha512-r8hpEjiQEYlF2QU0df3dS+nxxSIreXQS1qRhMJM0Q5NDdR386C7jb7Hwwod8Fgiuex+k0GFjgft18yvxm5XoCQ==", "license": "ISC", "resolved": "https://registry.npmjs.org/glob/-/glob-8.1.0.tgz", "version": "8.1.0"}, "node_modules/argparse": {"integrity": "sha512-8+9WqebbFzpX9OR+Wa6O29asIogeRMzcGtAINdpMHHyAg10f05aSFVBbcEqGf/PXw1EjAZ+q2/bEBg3DvurK3Q==", "license": "Python-2.0", "resolved": "https://registry.npmjs.org/argparse/-/argparse-2.0.1.tgz", "version": "2.0.1"}, "node_modules/assert-plus": {"dev": true, "engines": {"node": ">=0.8"}, "integrity": "sha512-NfJ4UzBCcQGLDlQq7nHxH+tv3kyZ0hHQqF5BO6J7tNJeP5do1llPr8dZ8zHonfhAu0PHAdMkSo+8o0wxg9lZWw==", "license": "MIT", "optional": true, "resolved": "https://registry.npmjs.org/assert-plus/-/assert-plus-1.0.0.tgz", "version": "1.0.0"}, "node_modules/astral-regex": {"dev": true, "engines": {"node": ">=8"}, "integrity": "sha512-Z7tMw1ytTXt5jqMcOP+OQteU1VuNK9Y02uuJtKQ1Sv69jXQKKg5cibLwGJow8yzZP+eAc18EmLGPal0bp36rvQ==", "license": "MIT", "optional": true, "resolved": "https://registry.npmjs.org/astral-regex/-/astral-regex-2.0.0.tgz", "version": "2.0.0"}, "node_modules/async": {"integrity": "sha512-htCUDlxyyCLMgaM3xXg0C0LW2xqfuQ6p05pCEIsXuyQ+a1koYKTuBMzRNwmybfLgvJDMd0r1LTn4+E0Ti6C2AA==", "license": "MIT", "resolved": "https://registry.npmjs.org/async/-/async-3.2.6.tgz", "version": "3.2.6"}, "node_modules/async-exit-hook": {"dev": true, "engines": {"node": ">=0.12.0"}, "integrity": "sha512-NW2cX8m1Q7KPA7a5M2ULQeZ2wR5qI5PAbw5L0UOMxdioVk9PMZ0h1TmyZEkPYrCvYjDlFICusOu1dlEKAAeXBw==", "license": "MIT", "resolved": "https://registry.npmjs.org/async-exit-hook/-/async-exit-hook-2.0.1.tgz", "version": "2.0.1"}, "node_modules/asynckit": {"dev": true, "integrity": "sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q==", "license": "MIT", "resolved": "https://registry.npmjs.org/asynckit/-/asynckit-0.4.0.tgz", "version": "0.4.0"}, "node_modules/at-least-node": {"dev": true, "engines": {"node": ">= 4.0.0"}, "integrity": "sha512-+q/t7Ekv1EDY2l6Gda6LLiX14rU9TV20Wa3ofeQmwPFZbOMo9DXrLbOjFaaclkXKWidIaopwAObQDqwWtGUjqg==", "license": "ISC", "resolved": "https://registry.npmjs.org/at-least-node/-/at-least-node-1.0.0.tgz", "version": "1.0.0"}, "node_modules/atomic-sleep": {"engines": {"node": ">=8.0.0"}, "integrity": "sha512-kNOjDqAh7px0XWNI+4QbzoiR/nTkHAWNud2uvnJquD1/x5a7EQZMJT0AczqK0Qn67oY/TTQ1LbUKajZpp3I9tQ==", "license": "MIT", "resolved": "https://registry.npmjs.org/atomic-sleep/-/atomic-sleep-1.0.0.tgz", "version": "1.0.0"}, "node_modules/b4a": {"integrity": "sha512-OnAYlL5b7LEkALw87fUVafQw5rVR9RjwGd4KUwNQ6DrrNmaVaUCgLipfVlzrPQ4tWOR9P0IXGNOx50jYCCdSJg==", "license": "Apache-2.0", "resolved": "https://registry.npmjs.org/b4a/-/b4a-1.6.7.tgz", "version": "1.6.7"}, "node_modules/babel-jest": {"dependencies": {"@jest/transform": "^29.7.0", "@types/babel__core": "^7.1.14", "babel-plugin-istanbul": "^6.1.1", "babel-preset-jest": "^29.6.3", "chalk": "^4.0.0", "graceful-fs": "^4.2.9", "slash": "^3.0.0"}, "dev": true, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "integrity": "sha512-BrvGY3xZSwEcCzKvKsCi2GgHqDqsYkOP4/by5xCgIwGXQxIEh+8ew3gmrE1y7XRR6LHZIj6yLYnUi/mm2KXKBg==", "license": "MIT", "peerDependencies": {"@babel/core": "^7.8.0"}, "resolved": "https://registry.npmjs.org/babel-jest/-/babel-jest-29.7.0.tgz", "version": "29.7.0"}, "node_modules/babel-plugin-istanbul": {"dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@istanbuljs/load-nyc-config": "^1.0.0", "@istanbuljs/schema": "^0.1.2", "istanbul-lib-instrument": "^5.0.4", "test-exclude": "^6.0.0"}, "dev": true, "engines": {"node": ">=8"}, "integrity": "sha512-Y1IQok9821cC9onCx5otgFfRm7Lm+I+wwxOx738M/WLPZ9Q42m4IG5W0FNX8WLL2gYMZo3JkuXIH2DOpWM+qwA==", "license": "BSD-3-<PERSON><PERSON>", "resolved": "https://registry.npmjs.org/babel-plugin-istanbul/-/babel-plugin-istanbul-6.1.1.tgz", "version": "6.1.1"}, "node_modules/babel-plugin-istanbul/node_modules/istanbul-lib-instrument": {"dependencies": {"@babel/core": "^7.12.3", "@babel/parser": "^7.14.7", "@istanbuljs/schema": "^0.1.2", "istanbul-lib-coverage": "^3.2.0", "semver": "^6.3.0"}, "dev": true, "engines": {"node": ">=8"}, "integrity": "sha512-pzqtp31nLv/XFOzXGuvhCb8qhjmTVo5vjVk19XE4CRlSWz0KoeJ3bw9XsA7nOp9YBf4qHjwBxkDzKcME/J29Yg==", "license": "BSD-3-<PERSON><PERSON>", "resolved": "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-5.2.1.tgz", "version": "5.2.1"}, "node_modules/babel-plugin-istanbul/node_modules/semver": {"bin": {"semver": "bin/semver.js"}, "dev": true, "integrity": "sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA==", "license": "ISC", "resolved": "https://registry.npmjs.org/semver/-/semver-6.3.1.tgz", "version": "6.3.1"}, "node_modules/babel-plugin-jest-hoist": {"dependencies": {"@babel/template": "^7.3.3", "@babel/types": "^7.3.3", "@types/babel__core": "^7.1.14", "@types/babel__traverse": "^7.0.6"}, "dev": true, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "integrity": "sha512-ESAc/RJvGTFEzRwOTT4+lNDk/GNHMkKbNzsvT0qKRfDyyYTskxB5rnU2njIDYVxXCBHHEI1c0YwHob3WaYujOg==", "license": "MIT", "resolved": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-29.6.3.tgz", "version": "29.6.3"}, "node_modules/babel-preset-current-node-syntax": {"dependencies": {"@babel/plugin-syntax-async-generators": "^7.8.4", "@babel/plugin-syntax-bigint": "^7.8.3", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-syntax-class-static-block": "^7.14.5", "@babel/plugin-syntax-import-attributes": "^7.24.7", "@babel/plugin-syntax-import-meta": "^7.10.4", "@babel/plugin-syntax-json-strings": "^7.8.3", "@babel/plugin-syntax-logical-assignment-operators": "^7.10.4", "@babel/plugin-syntax-nullish-coalescing-operator": "^7.8.3", "@babel/plugin-syntax-numeric-separator": "^7.10.4", "@babel/plugin-syntax-object-rest-spread": "^7.8.3", "@babel/plugin-syntax-optional-catch-binding": "^7.8.3", "@babel/plugin-syntax-optional-chaining": "^7.8.3", "@babel/plugin-syntax-private-property-in-object": "^7.14.5", "@babel/plugin-syntax-top-level-await": "^7.14.5"}, "dev": true, "integrity": "sha512-ldYss8SbBlWva1bs28q78Ju5Zq1F+8BrqBZZ0VFhLBvhh6lCpC2o3gDJi/5DRLs9FgYZCnmPYIVFU4lRXCkyUw==", "license": "MIT", "peerDependencies": {"@babel/core": "^7.0.0"}, "resolved": "https://registry.npmjs.org/babel-preset-current-node-syntax/-/babel-preset-current-node-syntax-1.1.0.tgz", "version": "1.1.0"}, "node_modules/babel-preset-jest": {"dependencies": {"babel-plugin-jest-hoist": "^29.6.3", "babel-preset-current-node-syntax": "^1.0.0"}, "dev": true, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "integrity": "sha512-0B3bhxR6snWXJZtR/RliHTDPRgn1sNHOR0yVtq/IiQFyuOVjFS+wuio/R4gSNkyYmKmJB4wGZv2NZanmKmTnNA==", "license": "MIT", "peerDependencies": {"@babel/core": "^7.0.0"}, "resolved": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-29.6.3.tgz", "version": "29.6.3"}, "node_modules/balanced-match": {"integrity": "sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==", "license": "MIT", "resolved": "https://registry.npmjs.org/balanced-match/-/balanced-match-1.0.2.tgz", "version": "1.0.2"}, "node_modules/bare-events": {"integrity": "sha512-+gFfDkR8pj4/TrWCGUGWmJIkBwuxPS5F+a5yWjOHQt2hHvNZd5YLzadjmDUtFmMM4y429bnKLa8bYBMHcYdnQA==", "license": "Apache-2.0", "optional": true, "resolved": "https://registry.npmjs.org/bare-events/-/bare-events-2.5.4.tgz", "version": "2.5.4"}, "node_modules/base64-js": {"funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "integrity": "sha512-AKpaYlHn8t4SVbOHCy+b5+KKgvR4vrsD8vbvrbiQJps7fKDTkjkDry6ji0rUJjC0kzbNePLwzxq8iypo41qeWA==", "license": "MIT", "resolved": "https://registry.npmjs.org/base64-js/-/base64-js-1.5.1.tgz", "version": "1.5.1"}, "node_modules/bl": {"dependencies": {"buffer": "^5.5.0", "inherits": "^2.0.4", "readable-stream": "^3.4.0"}, "integrity": "sha512-1W07cM9gS6DcLperZfFSj+bWLtaPGSOHWhPiGzXmvVJbRLdG82sH/Kn8EtW1VqWVA54AKf2h5k5BbnIbwF3h6w==", "license": "MIT", "resolved": "https://registry.npmjs.org/bl/-/bl-4.1.0.tgz", "version": "4.1.0"}, "node_modules/bluebird": {"dev": true, "integrity": "sha512-XpNj6GDQzdfW+r2Wnn7xiSAd7TM3jzkxGXBGTtWKuSXv1xUV+azxAm8jdWZN06QTQk+2N2XB9jRDkvbmQmcRtg==", "license": "MIT", "resolved": "https://registry.npmjs.org/bluebird/-/bluebird-3.7.2.tgz", "version": "3.7.2"}, "node_modules/bluebird-lst": {"dependencies": {"bluebird": "^3.5.5"}, "dev": true, "integrity": "sha512-7B1Rtx82hjnSD4PGLAjVWeYH3tHAcVUmChh85a3lltKQm6FresXh9ErQo6oAv6CqxttczC3/kEg8SY5NluPuUw==", "license": "MIT", "resolved": "https://registry.npmjs.org/bluebird-lst/-/bluebird-lst-1.0.9.tgz", "version": "1.0.9"}, "node_modules/boolean": {"deprecated": "Package no longer supported. Contact Support at https://www.npmjs.com/support for more info.", "dev": true, "integrity": "sha512-d0II/GO9uf9lfUHH2BQsjxzRJZBdsjgsBiW4BvhWk/3qoKwQFjIDVN19PfX8F2D/r9PCMTtLWjYVCFrpeYUzsw==", "license": "MIT", "optional": true, "resolved": "https://registry.npmjs.org/boolean/-/boolean-3.2.0.tgz", "version": "3.2.0"}, "node_modules/brace-expansion": {"dependencies": {"balanced-match": "^1.0.0"}, "integrity": "sha512-Jt0vHyM+jmUBqojB7E1NIYadt0vI0Qxjxd2TErW94wDz+E2LAm5vKMXXwg6ZZBTHPuUlDgQHKXvjGBdfcF1ZDQ==", "license": "MIT", "resolved": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-2.0.2.tgz", "version": "2.0.2"}, "node_modules/braces": {"dependencies": {"fill-range": "^7.1.1"}, "dev": true, "engines": {"node": ">=8"}, "integrity": "sha512-yQbXgO/OSZVD2IsiLlro+7Hf6Q18EJrKSEsdoMzKePKXct3gvD8oLcOQdIzGupr5Fj+EDe8gO/lxc1BzfMpxvA==", "license": "MIT", "resolved": "https://registry.npmjs.org/braces/-/braces-3.0.3.tgz", "version": "3.0.3"}, "node_modules/browserslist": {"bin": {"browserslist": "cli.js"}, "dependencies": {"caniuse-lite": "^1.0.30001726", "electron-to-chromium": "^1.5.173", "node-releases": "^2.0.19", "update-browserslist-db": "^1.1.3"}, "dev": true, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}, "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/browserslist"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "integrity": "sha512-KGj0KoOMXLpSNkkEI6Z6mShmQy0bc1I+T7K9N81k4WWMrfz+6fQ6es80B/YLAeRoKvjYE1YSHHOW1qe9xIVzHw==", "license": "MIT", "resolved": "https://registry.npmjs.org/browserslist/-/browserslist-4.25.1.tgz", "version": "4.25.1"}, "node_modules/bser": {"dependencies": {"node-int64": "^0.4.0"}, "dev": true, "integrity": "sha512-gQxTNE/GAfIIrmHLUE3oJyp5FO6HRBfhjnw4/wMmA63ZGDJnWBmgY/lyQBpnDUkGmAhbSe39tx2d/iTOAfglwQ==", "license": "Apache-2.0", "resolved": "https://registry.npmjs.org/bser/-/bser-2.1.1.tgz", "version": "2.1.1"}, "node_modules/buffer": {"dependencies": {"base64-js": "^1.3.1", "ieee754": "^1.1.13"}, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "integrity": "sha512-EHcyIPBQ4BSGlvjB16k5KgAJ27CIsHY/2JBmCRReo48y9rQ3MaUzWX3KVlBa4U7MyX02HdVj0K7C3WaB3ju7FQ==", "license": "MIT", "resolved": "https://registry.npmjs.org/buffer/-/buffer-5.7.1.tgz", "version": "5.7.1"}, "node_modules/buffer-crc32": {"engines": {"node": "*"}, "integrity": "sha512-VO9Ht/+p3SN7SKWqcrgEzjGbRSJYTx+Q1pTQC0wrWqHx0vpJraQ6GtHx8tvcg1rlK1byhU5gccxgOgj7B0TDkQ==", "license": "MIT", "resolved": "https://registry.npmjs.org/buffer-crc32/-/buffer-crc32-0.2.13.tgz", "version": "0.2.13"}, "node_modules/buffer-equal": {"dev": true, "engines": {"node": ">=0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "integrity": "sha512-QoV3ptgEaQpvVwbXdSO39iqPQTCxSF7A5U99AxbHYqUdCizL/lH2Z0A2y6nbZucxMEOtNyZfG2s6gsVugGpKkg==", "license": "MIT", "resolved": "https://registry.npmjs.org/buffer-equal/-/buffer-equal-1.0.1.tgz", "version": "1.0.1"}, "node_modules/buffer-from": {"dev": true, "integrity": "sha512-E+XQCRwSbaaiChtv6k6Dwgc+bx+Bs6vuKJHHl5kox/BaKbhiXzqQOwK4cO22yElGp2OCmjwVhT3HmxgyPGnJfQ==", "license": "MIT", "resolved": "https://registry.npmjs.org/buffer-from/-/buffer-from-1.1.2.tgz", "version": "1.1.2"}, "node_modules/builder-util": {"dependencies": {"7zip-bin": "~5.2.0", "@types/debug": "^4.1.6", "app-builder-bin": "4.0.0", "bluebird-lst": "^1.0.9", "builder-util-runtime": "9.2.4", "chalk": "^4.1.2", "cross-spawn": "^7.0.3", "debug": "^4.3.4", "fs-extra": "^10.1.0", "http-proxy-agent": "^5.0.0", "https-proxy-agent": "^5.0.1", "is-ci": "^3.0.0", "js-yaml": "^4.1.0", "source-map-support": "^0.5.19", "stat-mode": "^1.0.0", "temp-file": "^3.4.0"}, "dev": true, "integrity": "sha512-NhbCSIntruNDTOVI9fdXz0dihaqX2YuE1D6zZMrwiErzH4ELZHE6mdiB40wEgZNprDia+FghRFgKoAqMZRRjSA==", "license": "MIT", "resolved": "https://registry.npmjs.org/builder-util/-/builder-util-24.13.1.tgz", "version": "24.13.1"}, "node_modules/builder-util-runtime": {"dependencies": {"debug": "^4.3.4", "sax": "^1.2.4"}, "dev": true, "engines": {"node": ">=12.0.0"}, "integrity": "sha512-upp+biKpN/XZMLim7aguUyW8s0FUpDvOtK6sbanMFDAMBzpHDqdhgVYm6zc9HJ6nWo7u2Lxk60i2M6Jd3aiNrA==", "license": "MIT", "resolved": "https://registry.npmjs.org/builder-util-runtime/-/builder-util-runtime-9.2.4.tgz", "version": "9.2.4"}, "node_modules/builder-util/node_modules/fs-extra": {"dependencies": {"graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}, "dev": true, "engines": {"node": ">=12"}, "integrity": "sha512-oRXApq54ETRj4eMiFzGnHWGy+zo5raudjuxN0b8H7s/RU2oW0Wvsx9O0ACRN/kRq9E8Vu/ReskGB5o3ji+FzHQ==", "license": "MIT", "resolved": "https://registry.npmjs.org/fs-extra/-/fs-extra-10.1.0.tgz", "version": "10.1.0"}, "node_modules/builder-util/node_modules/jsonfile": {"dependencies": {"universalify": "^2.0.0"}, "dev": true, "integrity": "sha512-5dgndWOriYSm5cnYaJNhalLNDKOqFwyDB/rr1E9ZsGciGvKPs8R2xYGCacuf3z6K1YKDz182fd+fY3cn3pMqXQ==", "license": "MIT", "optionalDependencies": {"graceful-fs": "^4.1.6"}, "resolved": "https://registry.npmjs.org/jsonfile/-/jsonfile-6.1.0.tgz", "version": "6.1.0"}, "node_modules/builder-util/node_modules/universalify": {"dev": true, "engines": {"node": ">= 10.0.0"}, "integrity": "sha512-gptHNQghINnc/vTGIk0SOFGFNXw7JVrlRUtConJRlvaw6DuX0wO5Jeko9sWrMBhh+PsYAZ7oXAiOnf/UKogyiw==", "license": "MIT", "resolved": "https://registry.npmjs.org/universalify/-/universalify-2.0.1.tgz", "version": "2.0.1"}, "node_modules/cacheable-lookup": {"dev": true, "engines": {"node": ">=10.6.0"}, "integrity": "sha512-2/kNscPhpcxrOigMZzbiWF7dz8ilhb/nIHU3EyZiXWXpeq/au8qJ8VhdftMkty3n7Gj6HIGalQG8oiBNB3AJgA==", "license": "MIT", "resolved": "https://registry.npmjs.org/cacheable-lookup/-/cacheable-lookup-5.0.4.tgz", "version": "5.0.4"}, "node_modules/cacheable-request": {"dependencies": {"clone-response": "^1.0.2", "get-stream": "^5.1.0", "http-cache-semantics": "^4.0.0", "keyv": "^4.0.0", "lowercase-keys": "^2.0.0", "normalize-url": "^6.0.1", "responselike": "^2.0.0"}, "dev": true, "engines": {"node": ">=8"}, "integrity": "sha512-v+p6ongsrp0yTGbJXjgxPow2+DL93DASP4kXCDKb8/bwRtt9OEF3whggkkDkGNzgcWy2XaF4a8nZglC7uElscg==", "license": "MIT", "resolved": "https://registry.npmjs.org/cacheable-request/-/cacheable-request-7.0.4.tgz", "version": "7.0.4"}, "node_modules/call-bind-apply-helpers": {"dependencies": {"es-errors": "^1.3.0", "function-bind": "^1.1.2"}, "dev": true, "engines": {"node": ">= 0.4"}, "integrity": "sha512-Sp1ablJ0ivDkSzjcaJdxEunN5/XvksFJ2sMBFfq6x0ryhQV/2b/KwFe21cMpmHtPOSij8K99/wSfoEuTObmuMQ==", "license": "MIT", "resolved": "https://registry.npmjs.org/call-bind-apply-helpers/-/call-bind-apply-helpers-1.0.2.tgz", "version": "1.0.2"}, "node_modules/callsites": {"dev": true, "engines": {"node": ">=6"}, "integrity": "sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ==", "license": "MIT", "resolved": "https://registry.npmjs.org/callsites/-/callsites-3.1.0.tgz", "version": "3.1.0"}, "node_modules/camelcase": {"dev": true, "engines": {"node": ">=6"}, "integrity": "sha512-L28STB170nwWS63UjtlEOE3dldQApaJXZkOI1uMFfzf3rRuPegHaHesyee+YxQ+W6SvRDQV6UrdOdRiR153wJg==", "license": "MIT", "resolved": "https://registry.npmjs.org/camelcase/-/camelcase-5.3.1.tgz", "version": "5.3.1"}, "node_modules/caniuse-lite": {"dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/caniuse-lite"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "integrity": "sha512-VQAUIUzBiZ/UnlM28fSp2CRF3ivUn1BWEvxMcVTNwpw91Py1pGbPIyIKtd+tzct9C3ouceCVdGAXxZOpZAsgdw==", "license": "CC-BY-4.0", "resolved": "https://registry.npmjs.org/caniuse-lite/-/caniuse-lite-1.0.30001726.tgz", "version": "1.0.30001726"}, "node_modules/chalk": {"dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}, "integrity": "sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==", "license": "MIT", "resolved": "https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz", "version": "4.1.2"}, "node_modules/char-regex": {"dev": true, "engines": {"node": ">=10"}, "integrity": "sha512-kWWXztvZ5SBQV+eRgKFeh8q5sLuZY2+8WUIzlxWVTg+oGwY14qylx1KbKzHd8P6ZYkAg0xyIDU9JMHhyJMZ1jw==", "license": "MIT", "resolved": "https://registry.npmjs.org/char-regex/-/char-regex-1.0.2.tgz", "version": "1.0.2"}, "node_modules/chardet": {"integrity": "sha512-mT8iDcrh03qDGRRmoA2hmBJnxpllMR+0/0qlzjqZES6NdiWDcZkCNAk4rPFZ9Q85r27unkiNNg8ZOiwZXBHwcA==", "license": "MIT", "resolved": "https://registry.npmjs.org/chardet/-/chardet-0.7.0.tgz", "version": "0.7.0"}, "node_modules/chownr": {"dev": true, "engines": {"node": ">=10"}, "integrity": "sha512-bIomtDF5KGpdogkLd9VspvFzk9KfpyyGlS8YFVZl7TGPBHL5snIOnxeshwVgPteQ9b4Eydl+pVbIyE1DcvCWgQ==", "license": "ISC", "resolved": "https://registry.npmjs.org/chownr/-/chownr-2.0.0.tgz", "version": "2.0.0"}, "node_modules/chromium-pickle-js": {"dev": true, "integrity": "sha512-1R5Fho+jBq0DDydt+/vHWj5KJNJCKdARKOCwZUen84I5BreWoLqRLANH1U87eJy1tiASPtMnGqJJq0ZsLoRPOw==", "license": "MIT", "resolved": "https://registry.npmjs.org/chromium-pickle-js/-/chromium-pickle-js-0.2.0.tgz", "version": "0.2.0"}, "node_modules/ci-info": {"dev": true, "engines": {"node": ">=8"}, "funding": [{"type": "github", "url": "https://github.com/sponsors/sibir<PERSON>-s"}], "integrity": "sha512-NIxF55hv4nSqQswkAeiOi1r83xy8JldOFDTWiug55KBu9Jnblncd2U6ViHmYgHf01TPZS77NJBhBMKdWj9HQMQ==", "license": "MIT", "resolved": "https://registry.npmjs.org/ci-info/-/ci-info-3.9.0.tgz", "version": "3.9.0"}, "node_modules/cjs-module-lexer": {"dev": true, "integrity": "sha512-9z8TZaGM1pfswYeXrUpzPrkx8UnWYdhJclsiYMm6x/w5+nN+8Tf/LnAgfLGQCm59qAOxU8WwHEq2vNwF6i4j+Q==", "license": "MIT", "resolved": "https://registry.npmjs.org/cjs-module-lexer/-/cjs-module-lexer-1.4.3.tgz", "version": "1.4.3"}, "node_modules/cli-cursor": {"dependencies": {"restore-cursor": "^3.1.0"}, "engines": {"node": ">=8"}, "integrity": "sha512-I/zHAwsKf9FqGoXM4WWRACob9+SNukZTd94DWF57E4toouRulbCxcUh6RKUEOQlYTHJnzkPMySvPNaaSLNfLZw==", "license": "MIT", "resolved": "https://registry.npmjs.org/cli-cursor/-/cli-cursor-3.1.0.tgz", "version": "3.1.0"}, "node_modules/cli-spinners": {"engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}, "integrity": "sha512-ywqV+5MmyL4E7ybXgKys4DugZbX0FC6LnwrhjuykIjnK9k8OQacQ7axGKnjDXWNhns0xot3bZI5h55H8yo9cJg==", "license": "MIT", "resolved": "https://registry.npmjs.org/cli-spinners/-/cli-spinners-2.9.2.tgz", "version": "2.9.2"}, "node_modules/cli-truncate": {"dependencies": {"slice-ansi": "^3.0.0", "string-width": "^4.2.0"}, "dev": true, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}, "integrity": "sha512-n8fOixwDD6b/ObinzTrp1ZKFzbgvKZvuz/TvejnLn1aQfC6r52XEx85FmuC+3HI+JM7coBRXUvNqEU2PHVrHpg==", "license": "MIT", "optional": true, "resolved": "https://registry.npmjs.org/cli-truncate/-/cli-truncate-2.1.0.tgz", "version": "2.1.0"}, "node_modules/cli-width": {"engines": {"node": ">= 10"}, "integrity": "sha512-FxqpkPPwu1HjuN93Omfm4h8uIanXofW0RxVEW3k5RKx+mJJYSthzNhp32Kzxxy3YAEZ/Dc/EWN1vZRY0+kOhbw==", "license": "ISC", "resolved": "https://registry.npmjs.org/cli-width/-/cli-width-3.0.0.tgz", "version": "3.0.0"}, "node_modules/cliui": {"dependencies": {"string-width": "^4.2.0", "strip-ansi": "^6.0.1", "wrap-ansi": "^7.0.0"}, "dev": true, "engines": {"node": ">=12"}, "integrity": "sha512-BSeNnyus75C4//NQ9gQt1/csTXyo/8Sb+afLAkzAptFuMsod9HFokGNudZpi/oQV73hnVK+sR+5PVRMd+Dr7YQ==", "license": "ISC", "resolved": "https://registry.npmjs.org/cliui/-/cliui-8.0.1.tgz", "version": "8.0.1"}, "node_modules/clone": {"engines": {"node": ">=0.8"}, "integrity": "sha512-JQHZ2QMW6l3aH/j6xCqQThY/9OH4D/9ls34cgkUBiEeocRTU04tHfKPBsUK1PqZCUQM7GiA0IIXJSuXHI64Kbg==", "license": "MIT", "resolved": "https://registry.npmjs.org/clone/-/clone-1.0.4.tgz", "version": "1.0.4"}, "node_modules/clone-response": {"dependencies": {"mimic-response": "^1.0.0"}, "dev": true, "funding": {"url": "https://github.com/sponsors/sindresorhus"}, "integrity": "sha512-ROoL94jJH2dUVML2Y/5PEDNaSHgeOdSDicUyS7izcF63G6sTc/FTjLub4b8Il9S8S0beOfYt0TaA5qvFK+w0wA==", "license": "MIT", "resolved": "https://registry.npmjs.org/clone-response/-/clone-response-1.0.3.tgz", "version": "1.0.3"}, "node_modules/co": {"dev": true, "engines": {"iojs": ">= 1.0.0", "node": ">= 0.12.0"}, "integrity": "sha512-QVb0dM5HvG+uaxitm8wONl7jltx8dqhfU33DcqtOZcLSVIKSDDLDi7+0LbAKiyI8hD9u42m2YxXSkMGWThaecQ==", "license": "MIT", "resolved": "https://registry.npmjs.org/co/-/co-4.6.0.tgz", "version": "4.6.0"}, "node_modules/collect-v8-coverage": {"dev": true, "integrity": "sha512-lHl4d5/ONEbLlJvaJNtsF/Lz+WvB07u2ycqTYbdrq7UypDXailES4valYb2eWiJFxZlVmpGekfqoxQhzyFdT4Q==", "license": "MIT", "resolved": "https://registry.npmjs.org/collect-v8-coverage/-/collect-v8-coverage-1.0.2.tgz", "version": "1.0.2"}, "node_modules/color-convert": {"dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}, "integrity": "sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==", "license": "MIT", "resolved": "https://registry.npmjs.org/color-convert/-/color-convert-2.0.1.tgz", "version": "2.0.1"}, "node_modules/color-name": {"integrity": "sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==", "license": "MIT", "resolved": "https://registry.npmjs.org/color-name/-/color-name-1.1.4.tgz", "version": "1.1.4"}, "node_modules/combined-stream": {"dependencies": {"delayed-stream": "~1.0.0"}, "dev": true, "engines": {"node": ">= 0.8"}, "integrity": "sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==", "license": "MIT", "resolved": "https://registry.npmjs.org/combined-stream/-/combined-stream-1.0.8.tgz", "version": "1.0.8"}, "node_modules/commander": {"engines": {"node": ">=16"}, "integrity": "sha512-yPVavfyCcRhmorC7rWlkHn15b4wDVgVmBA7kV4QVBsF7kv/9TKJAbAXVTxvTnwP8HHKjRCJDClKbciiYS7p0DQ==", "license": "MIT", "resolved": "https://registry.npmjs.org/commander/-/commander-11.1.0.tgz", "version": "11.1.0"}, "node_modules/compare-version": {"dev": true, "engines": {"node": ">=0.10.0"}, "integrity": "sha512-pJDh5/4wrEnXX/VWRZvruAGHkzKdr46z11OlTPN+VrATlWWhSKewNCJ1futCO5C7eJB3nPMFZA1LeYtcFboZ2A==", "license": "MIT", "resolved": "https://registry.npmjs.org/compare-version/-/compare-version-0.1.2.tgz", "version": "0.1.2"}, "node_modules/compress-commons": {"dependencies": {"crc-32": "^1.2.0", "crc32-stream": "^5.0.0", "normalize-path": "^3.0.0", "readable-stream": "^3.6.0"}, "engines": {"node": ">= 12.0.0"}, "integrity": "sha512-/UIcLWvwAQyVibgpQDPtfNM3SvqN7G9elAPAV7GM0L53EbNWwWiCsWtK8Fwed/APEbptPHXs5PuW+y8Bq8lFTA==", "license": "MIT", "resolved": "https://registry.npmjs.org/compress-commons/-/compress-commons-5.0.3.tgz", "version": "5.0.3"}, "node_modules/concat-map": {"dev": true, "integrity": "sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg==", "license": "MIT", "resolved": "https://registry.npmjs.org/concat-map/-/concat-map-0.0.1.tgz", "version": "0.0.1"}, "node_modules/config-file-ts": {"dependencies": {"glob": "^10.3.10", "typescript": "^5.3.3"}, "dev": true, "integrity": "sha512-6boGVaglwblBgJqGyxm4+xCmEGcWgnWHSWHY5jad58awQhB6gftq0G8HbzU39YqCIYHMLAiL1yjwiZ36m/CL8w==", "license": "MIT", "resolved": "https://registry.npmjs.org/config-file-ts/-/config-file-ts-0.2.6.tgz", "version": "0.2.6"}, "node_modules/config-file-ts/node_modules/glob": {"bin": {"glob": "dist/esm/bin.mjs"}, "dependencies": {"foreground-child": "^3.1.0", "jackspeak": "^3.1.2", "minimatch": "^9.0.4", "minipass": "^7.1.2", "package-json-from-dist": "^1.0.0", "path-scurry": "^1.11.1"}, "dev": true, "funding": {"url": "https://github.com/sponsors/isaacs"}, "integrity": "sha512-7Bv8RF0k6xjo7d4A/PxYLbUCfb6c+Vpd2/mB2yRDlew7Jb5hEXiCD9ibfO7wpk8i4sevK6DFny9h7EYbM3/sHg==", "license": "ISC", "resolved": "https://registry.npmjs.org/glob/-/glob-10.4.5.tgz", "version": "10.4.5"}, "node_modules/config-file-ts/node_modules/minimatch": {"dependencies": {"brace-expansion": "^2.0.1"}, "dev": true, "engines": {"node": ">=16 || 14 >=14.17"}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "integrity": "sha512-G6T0ZX48xgozx7587koeX9Ys2NYy6Gmv//P89sEte9V9whIapMNF4idKxnW2QtCcLiTWlb/wfCabAtAFWhhBow==", "license": "ISC", "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-9.0.5.tgz", "version": "9.0.5"}, "node_modules/config-file-ts/node_modules/minipass": {"dev": true, "engines": {"node": ">=16 || 14 >=14.17"}, "integrity": "sha512-qOOzS1cBTWYF4BH8fVePDBOO9iptMnGUEZwNc/cMWnTV2nVLZ7VoNWEPHkYczZA0pdoA7dl6e7FL659nX9S2aw==", "license": "ISC", "resolved": "https://registry.npmjs.org/minipass/-/minipass-7.1.2.tgz", "version": "7.1.2"}, "node_modules/convert-source-map": {"dev": true, "integrity": "sha512-Kvp459HrV2FEJ1CAsi1Ku+MY3kasH19TFykTz2xWmMeq6bk2NU3XXvfJ+Q61m0xktWwt+1HSYf3JZsTms3aRJg==", "license": "MIT", "resolved": "https://registry.npmjs.org/convert-source-map/-/convert-source-map-2.0.0.tgz", "version": "2.0.0"}, "node_modules/core-util-is": {"integrity": "sha512-3lqz5YjWTYnW6dlDa5TLaTCcShfar1e40rmcJVwCBJC6mWlFuj0eCHIElmG1g5kyuJ/GD+8Wn4FFCcz4gJPfaQ==", "license": "MIT", "resolved": "https://registry.npmjs.org/core-util-is/-/core-util-is-1.0.2.tgz", "version": "1.0.2"}, "node_modules/crc": {"dependencies": {"buffer": "^5.1.0"}, "dev": true, "integrity": "sha512-iX3mfgcTMIq3ZKLIsVFAbv7+Mc10kxabAGQb8HvjA1o3T1PIYprbakQ65d3I+2HGHt6nSKkM9PYjgoJO2KcFBQ==", "license": "MIT", "optional": true, "resolved": "https://registry.npmjs.org/crc/-/crc-3.8.0.tgz", "version": "3.8.0"}, "node_modules/crc-32": {"bin": {"crc32": "bin/crc32.njs"}, "engines": {"node": ">=0.8"}, "integrity": "sha512-ROmzCKrTnOwybPcJApAA6WBWij23HVfGVNKqqrZpuyZOHqK2CwHSvpGuyt/UNNvaIjEd8X5IFGp4Mh+Ie1IHJQ==", "license": "Apache-2.0", "resolved": "https://registry.npmjs.org/crc-32/-/crc-32-1.2.2.tgz", "version": "1.2.2"}, "node_modules/crc32-stream": {"dependencies": {"crc-32": "^1.2.0", "readable-stream": "^3.4.0"}, "engines": {"node": ">= 12.0.0"}, "integrity": "sha512-lO1dFui+CEUh/ztYIpgpKItKW9Bb4NWakCRJrnqAbFIYD+OZAwb2VfD5T5eXMw2FNcsDHkQcNl/Wh3iVXYwU6g==", "license": "MIT", "resolved": "https://registry.npmjs.org/crc32-stream/-/crc32-stream-5.0.1.tgz", "version": "5.0.1"}, "node_modules/create-jest": {"bin": {"create-jest": "bin/create-jest.js"}, "dependencies": {"@jest/types": "^29.6.3", "chalk": "^4.0.0", "exit": "^0.1.2", "graceful-fs": "^4.2.9", "jest-config": "^29.7.0", "jest-util": "^29.7.0", "prompts": "^2.0.1"}, "dev": true, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "integrity": "sha512-Adz2bdH0Vq3F53KEMJOoftQFutWCukm6J24wbPWRO4k1kMY7gS7ds/uoJkNuV8wDCtWWnuwGcJwpWcih+zEW1Q==", "license": "MIT", "resolved": "https://registry.npmjs.org/create-jest/-/create-jest-29.7.0.tgz", "version": "29.7.0"}, "node_modules/cross-spawn": {"dependencies": {"path-key": "^3.1.0", "shebang-command": "^2.0.0", "which": "^2.0.1"}, "dev": true, "engines": {"node": ">= 8"}, "integrity": "sha512-uV2QOWP2nWzsy2aMp8aRibhi9dlzF5Hgh5SHaB9OiTGEyDTiJJyx0uy51QXdyWbtAHNua4XJzUKca3OzKUd3vA==", "license": "MIT", "resolved": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-7.0.6.tgz", "version": "7.0.6"}, "node_modules/debug": {"dependencies": {"ms": "^2.1.3"}, "engines": {"node": ">=6.0"}, "integrity": "sha512-KcKCqiftBJcZr++7ykoDIEwSa3XWowTfNPo92BYxjXiyYEVrUQh2aLyhxBCwww+heortUFxEJYcRzosstTEBYQ==", "license": "MIT", "peerDependenciesMeta": {"supports-color": {"optional": true}}, "resolved": "https://registry.npmjs.org/debug/-/debug-4.4.1.tgz", "version": "4.4.1"}, "node_modules/decompress-response": {"dependencies": {"mimic-response": "^3.1.0"}, "dev": true, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}, "integrity": "sha512-aW35yZM6Bb/4oJlZncMH2LCoZtJXTRxES17vE3hoRiowU2kWHaJKFkSBDnDR+cm9J+9QhXmREyIfv0pji9ejCQ==", "license": "MIT", "resolved": "https://registry.npmjs.org/decompress-response/-/decompress-response-6.0.0.tgz", "version": "6.0.0"}, "node_modules/decompress-response/node_modules/mimic-response": {"dev": true, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}, "integrity": "sha512-z0yWI+4FDrrweS8Zmt4Ej5HdJmky15+L2e6Wgn3+iK5fWzb6T3fhNFq2+MeTRb064c6Wr4N/wv0DzQTjNzHNGQ==", "license": "MIT", "resolved": "https://registry.npmjs.org/mimic-response/-/mimic-response-3.1.0.tgz", "version": "3.1.0"}, "node_modules/dedent": {"dev": true, "integrity": "sha512-F1Z+5UCFpmQUzJa11agbyPVMbpgT/qA3/SKyJ1jyBgm7dUcUEa8v9JwDkerSQXfakBwFljIxhOJqGkjUwZ9FSA==", "license": "MIT", "peerDependencies": {"babel-plugin-macros": "^3.1.0"}, "peerDependenciesMeta": {"babel-plugin-macros": {"optional": true}}, "resolved": "https://registry.npmjs.org/dedent/-/dedent-1.6.0.tgz", "version": "1.6.0"}, "node_modules/deepmerge": {"dev": true, "engines": {"node": ">=0.10.0"}, "integrity": "sha512-3sUqbMEc77XqpdNO7FRyRog+eW3ph+GYCbj+rK+uYyRMuwsVy0rMiVtPn+QJlKFvWP/1PYpapqYn0Me2knFn+A==", "license": "MIT", "resolved": "https://registry.npmjs.org/deepmerge/-/deepmerge-4.3.1.tgz", "version": "4.3.1"}, "node_modules/defaults": {"dependencies": {"clone": "^1.0.2"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}, "integrity": "sha512-eFuaLoy/Rxalv2kr+lqMlUnrDWV+3j4pljOIJgLIhI058IQfWJ7vXhyEIHu+HtC738klGALYxOKDO0bQP3tg8A==", "license": "MIT", "resolved": "https://registry.npmjs.org/defaults/-/defaults-1.0.4.tgz", "version": "1.0.4"}, "node_modules/defer-to-connect": {"dev": true, "engines": {"node": ">=10"}, "integrity": "sha512-4tvttepXG1VaYGrRibk5EwJd1t4udunSOVMdLSAL6mId1ix438oPwPZMALY41FCijukO1L0twNcGsdzS7dHgDg==", "license": "MIT", "resolved": "https://registry.npmjs.org/defer-to-connect/-/defer-to-connect-2.0.1.tgz", "version": "2.0.1"}, "node_modules/define-data-property": {"dependencies": {"es-define-property": "^1.0.0", "es-errors": "^1.3.0", "gopd": "^1.0.1"}, "dev": true, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "integrity": "sha512-rBMvIzlpA8v6E+SJZoo++HAYqsLrkg7MSfIinMPFhmkorw7X+dOXVJQs+QT69zGkzMyfDnIMN2Wid1+NbL3T+A==", "license": "MIT", "optional": true, "resolved": "https://registry.npmjs.org/define-data-property/-/define-data-property-1.1.4.tgz", "version": "1.1.4"}, "node_modules/define-properties": {"dependencies": {"define-data-property": "^1.0.1", "has-property-descriptors": "^1.0.0", "object-keys": "^1.1.1"}, "dev": true, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "integrity": "sha512-8QmQKqEASLd5nx0U1B1okLElbUuuttJ/AnYmRXbbbGDWh6uS208EjD4Xqq/I9wK7u0v6O08XhTWnt5XtEbR6Dg==", "license": "MIT", "optional": true, "resolved": "https://registry.npmjs.org/define-properties/-/define-properties-1.2.1.tgz", "version": "1.2.1"}, "node_modules/delayed-stream": {"dev": true, "engines": {"node": ">=0.4.0"}, "integrity": "sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ==", "license": "MIT", "resolved": "https://registry.npmjs.org/delayed-stream/-/delayed-stream-1.0.0.tgz", "version": "1.0.0"}, "node_modules/detect-newline": {"dev": true, "engines": {"node": ">=8"}, "integrity": "sha512-TLz+x/vEXm/Y7P7wn1EJFNLxYpUD4TgMosxY6fAVJUnJMbupHBOncxyWUG9OpTaH9EBD7uFI5LfEgmMOc54DsA==", "license": "MIT", "resolved": "https://registry.npmjs.org/detect-newline/-/detect-newline-3.1.0.tgz", "version": "3.1.0"}, "node_modules/detect-node": {"dev": true, "integrity": "sha512-T0NIuQpnTvFDATNuHN5roPwSBG83rFsuO+MXXH9/3N1eFbn4wcPjttvjMLEPWJ0RGUYgQE7cGgS3tNxbqCGM7g==", "license": "MIT", "optional": true, "resolved": "https://registry.npmjs.org/detect-node/-/detect-node-2.1.0.tgz", "version": "2.1.0"}, "node_modules/diff-sequences": {"dev": true, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "integrity": "sha512-EjePK1srD3P08o2j4f0ExnylqRs5B9tJjcp9t1krH2qRi8CCdsYfwe9JgSLurFBWwq4uOlipzfk5fHNvwFKr8Q==", "license": "MIT", "resolved": "https://registry.npmjs.org/diff-sequences/-/diff-sequences-29.6.3.tgz", "version": "29.6.3"}, "node_modules/dir-compare": {"dependencies": {"buffer-equal": "^1.0.0", "minimatch": "^3.0.4"}, "dev": true, "integrity": "sha512-J7/et3WlGUCxjdnD3HAAzQ6nsnc0WL6DD7WcwJb7c39iH1+AWfg+9OqzJNaI6PkBwBvm1mhZNL9iY/nRiZXlPg==", "license": "MIT", "resolved": "https://registry.npmjs.org/dir-compare/-/dir-compare-3.3.0.tgz", "version": "3.3.0"}, "node_modules/dir-compare/node_modules/brace-expansion": {"dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}, "dev": true, "integrity": "sha512-9T9UjW3r0UW5c1Q7GTwllptXwhvYmEzFhzMfZ9H7FQWt+uZePjZPjBP/W1ZEyZ1twGWom5/56TF4lPcqjnDHcg==", "license": "MIT", "resolved": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.12.tgz", "version": "1.1.12"}, "node_modules/dir-compare/node_modules/minimatch": {"dependencies": {"brace-expansion": "^1.1.7"}, "dev": true, "engines": {"node": "*"}, "integrity": "sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==", "license": "ISC", "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-3.1.2.tgz", "version": "3.1.2"}, "node_modules/dmg-builder": {"dependencies": {"app-builder-lib": "24.13.3", "builder-util": "24.13.1", "builder-util-runtime": "9.2.4", "fs-extra": "^10.1.0", "iconv-lite": "^0.6.2", "js-yaml": "^4.1.0"}, "dev": true, "integrity": "sha512-rcJUkMfnJpfCboZoOOPf4L29TRtEieHNOeAbYPWPxlaBw/Z1RKrRA86dOI9rwaI4tQSc/RD82zTNHprfUHXsoQ==", "license": "MIT", "optionalDependencies": {"dmg-license": "^1.0.11"}, "resolved": "https://registry.npmjs.org/dmg-builder/-/dmg-builder-24.13.3.tgz", "version": "24.13.3"}, "node_modules/dmg-builder/node_modules/fs-extra": {"dependencies": {"graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}, "dev": true, "engines": {"node": ">=12"}, "integrity": "sha512-oRXApq54ETRj4eMiFzGnHWGy+zo5raudjuxN0b8H7s/RU2oW0Wvsx9O0ACRN/kRq9E8Vu/ReskGB5o3ji+FzHQ==", "license": "MIT", "resolved": "https://registry.npmjs.org/fs-extra/-/fs-extra-10.1.0.tgz", "version": "10.1.0"}, "node_modules/dmg-builder/node_modules/jsonfile": {"dependencies": {"universalify": "^2.0.0"}, "dev": true, "integrity": "sha512-5dgndWOriYSm5cnYaJNhalLNDKOqFwyDB/rr1E9ZsGciGvKPs8R2xYGCacuf3z6K1YKDz182fd+fY3cn3pMqXQ==", "license": "MIT", "optionalDependencies": {"graceful-fs": "^4.1.6"}, "resolved": "https://registry.npmjs.org/jsonfile/-/jsonfile-6.1.0.tgz", "version": "6.1.0"}, "node_modules/dmg-builder/node_modules/universalify": {"dev": true, "engines": {"node": ">= 10.0.0"}, "integrity": "sha512-gptHNQghINnc/vTGIk0SOFGFNXw7JVrlRUtConJRlvaw6DuX0wO5Jeko9sWrMBhh+PsYAZ7oXAiOnf/UKogyiw==", "license": "MIT", "resolved": "https://registry.npmjs.org/universalify/-/universalify-2.0.1.tgz", "version": "2.0.1"}, "node_modules/dmg-license": {"bin": {"dmg-license": "bin/dmg-license.js"}, "dependencies": {"@types/plist": "^3.0.1", "@types/verror": "^1.10.3", "ajv": "^6.10.0", "crc": "^3.8.0", "iconv-corefoundation": "^1.1.7", "plist": "^3.0.4", "smart-buffer": "^4.0.2", "verror": "^1.10.0"}, "dev": true, "engines": {"node": ">=8"}, "integrity": "sha512-ZdzmqwKmECOWJpqefloC5OJy1+WZBBse5+MR88z9g9Zn4VY+WYUkAyojmhzJckH5YbbZGcYIuGAkY5/Ys5OM2Q==", "license": "MIT", "optional": true, "os": ["darwin"], "resolved": "https://registry.npmjs.org/dmg-license/-/dmg-license-1.0.11.tgz", "version": "1.0.11"}, "node_modules/dmg-license/node_modules/ajv": {"dependencies": {"fast-deep-equal": "^3.1.1", "fast-json-stable-stringify": "^2.0.0", "json-schema-traverse": "^0.4.1", "uri-js": "^4.2.2"}, "dev": true, "funding": {"type": "github", "url": "https://github.com/sponsors/epoberezkin"}, "integrity": "sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g==", "license": "MIT", "optional": true, "resolved": "https://registry.npmjs.org/ajv/-/ajv-6.12.6.tgz", "version": "6.12.6"}, "node_modules/dmg-license/node_modules/json-schema-traverse": {"dev": true, "integrity": "sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg==", "license": "MIT", "optional": true, "resolved": "https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz", "version": "0.4.1"}, "node_modules/dotenv": {"dev": true, "engines": {"node": ">=10"}, "integrity": "sha512-I9OvvrHp4pIARv4+x9iuewrWycX6CcZtoAu1XrzPxc5UygMJXJZYmBsynku8IkrJwgypE5DGNjDPmPRhDCptUg==", "license": "BSD-2-<PERSON><PERSON>", "resolved": "https://registry.npmjs.org/dotenv/-/dotenv-9.0.2.tgz", "version": "9.0.2"}, "node_modules/dotenv-expand": {"dev": true, "integrity": "sha512-YXQl1DSa4/PQyRfgrv6aoNjhasp/p4qs9FjJ4q4cQk+8m4r6k4ZSiEyytKG8f8W9gi8WsQtIObNmKd+tMzNTmA==", "license": "BSD-2-<PERSON><PERSON>", "resolved": "https://registry.npmjs.org/dotenv-expand/-/dotenv-expand-5.1.0.tgz", "version": "5.1.0"}, "node_modules/dunder-proto": {"dependencies": {"call-bind-apply-helpers": "^1.0.1", "es-errors": "^1.3.0", "gopd": "^1.2.0"}, "dev": true, "engines": {"node": ">= 0.4"}, "integrity": "sha512-KIN/nDJBQRcXw0MLVhZE9iQHmG68qAVIBg9CqmUYjmQIhgij9U5MFvrqkUL5FbtyyzZuOeOt0zdeRe4UY7ct+A==", "license": "MIT", "resolved": "https://registry.npmjs.org/dunder-proto/-/dunder-proto-1.0.1.tgz", "version": "1.0.1"}, "node_modules/eastasianwidth": {"dev": true, "integrity": "sha512-I88TYZWc9XiYHRQ4/3c5rjjfgkjhLyW2luGIheGERbNQ6OY7yTybanSpDXZa8y7VUP9YmDcYa+eyq4ca7iLqWA==", "license": "MIT", "resolved": "https://registry.npmjs.org/eastasianwidth/-/eastasianwidth-0.2.0.tgz", "version": "0.2.0"}, "node_modules/ejs": {"bin": {"ejs": "bin/cli.js"}, "dependencies": {"jake": "^10.8.5"}, "dev": true, "engines": {"node": ">=0.10.0"}, "integrity": "sha512-UeJmFfOrAQS8OJWPZ4qtgHyWExa088/MtK5UEyoJGFH67cDEXkZSviOiKRCZ4Xij0zxI3JECgYs3oKx+AizQBA==", "license": "Apache-2.0", "resolved": "https://registry.npmjs.org/ejs/-/ejs-3.1.10.tgz", "version": "3.1.10"}, "node_modules/electron": {"bin": {"electron": "cli.js"}, "dependencies": {"@electron/get": "^2.0.0", "@types/node": "^18.11.18", "extract-zip": "^2.0.1"}, "dev": true, "engines": {"node": ">= 12.20.55"}, "hasInstallScript": true, "integrity": "sha512-ObKMLSPNhomtCOBAxFS8P2DW/4umkh72ouZUlUKzXGtYuPzgr1SYhskhFWgzAsPtUzhL2CzyV2sfbHcEW4CXqw==", "license": "MIT", "resolved": "https://registry.npmjs.org/electron/-/electron-28.3.3.tgz", "version": "28.3.3"}, "node_modules/electron-builder": {"bin": {"electron-builder": "cli.js", "install-app-deps": "install-app-deps.js"}, "dependencies": {"app-builder-lib": "24.13.3", "builder-util": "24.13.1", "builder-util-runtime": "9.2.4", "chalk": "^4.1.2", "dmg-builder": "24.13.3", "fs-extra": "^10.1.0", "is-ci": "^3.0.0", "lazy-val": "^1.0.5", "read-config-file": "6.3.2", "simple-update-notifier": "2.0.0", "yargs": "^17.6.2"}, "dev": true, "engines": {"node": ">=14.0.0"}, "integrity": "sha512-yZSgVHft5dNVlo31qmJAe4BVKQfFdwpRw7sFp1iQglDRCDD6r22zfRJuZlhtB5gp9FHUxCMEoWGq10SkCnMAIg==", "license": "MIT", "resolved": "https://registry.npmjs.org/electron-builder/-/electron-builder-24.13.3.tgz", "version": "24.13.3"}, "node_modules/electron-builder-squirrel-windows": {"dependencies": {"app-builder-lib": "24.13.3", "archiver": "^5.3.1", "builder-util": "24.13.1", "fs-extra": "^10.1.0"}, "dev": true, "integrity": "sha512-oHkV0iogWfyK+ah9ZIvMDpei1m9ZRpdXcvde1wTpra2U8AFDNNpqJdnin5z+PM1GbQ5BoaKCWas2HSjtR0HwMg==", "license": "MIT", "peer": true, "resolved": "https://registry.npmjs.org/electron-builder-squirrel-windows/-/electron-builder-squirrel-windows-24.13.3.tgz", "version": "24.13.3"}, "node_modules/electron-builder-squirrel-windows/node_modules/archiver": {"dependencies": {"archiver-utils": "^2.1.0", "async": "^3.2.4", "buffer-crc32": "^0.2.1", "readable-stream": "^3.6.0", "readdir-glob": "^1.1.2", "tar-stream": "^2.2.0", "zip-stream": "^4.1.0"}, "dev": true, "engines": {"node": ">= 10"}, "integrity": "sha512-+25nxyyznAXF7Nef3y0EbBeqmGZgeN/BxHX29Rs39djAfaFalmQ89SE6CWyDCHzGL0yt/ycBtNOmGTW0FyGWNw==", "license": "MIT", "peer": true, "resolved": "https://registry.npmjs.org/archiver/-/archiver-5.3.2.tgz", "version": "5.3.2"}, "node_modules/electron-builder-squirrel-windows/node_modules/archiver-utils": {"dependencies": {"glob": "^7.1.4", "graceful-fs": "^4.2.0", "lazystream": "^1.0.0", "lodash.defaults": "^4.2.0", "lodash.difference": "^4.5.0", "lodash.flatten": "^4.4.0", "lodash.isplainobject": "^4.0.6", "lodash.union": "^4.6.0", "normalize-path": "^3.0.0", "readable-stream": "^2.0.0"}, "dev": true, "engines": {"node": ">= 6"}, "integrity": "sha512-bEL/yUb/fNNiNTuUz979Z0Yg5L+LzLxGJz8x79lYmR54fmTIb6ob/hNQgkQnIUDWIFjZVQwl9Xs356I6BAMHfw==", "license": "MIT", "peer": true, "resolved": "https://registry.npmjs.org/archiver-utils/-/archiver-utils-2.1.0.tgz", "version": "2.1.0"}, "node_modules/electron-builder-squirrel-windows/node_modules/archiver-utils/node_modules/readable-stream": {"dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}, "dev": true, "integrity": "sha512-8p0AUk4XODgIewSi0l8Epjs+EVnWiK7NoDIEGU0HhE7+ZyY8D1IMY7odu5lRrFXGg71L15KG8QrPmum45RTtdA==", "license": "MIT", "peer": true, "resolved": "https://registry.npmjs.org/readable-stream/-/readable-stream-2.3.8.tgz", "version": "2.3.8"}, "node_modules/electron-builder-squirrel-windows/node_modules/compress-commons": {"dependencies": {"buffer-crc32": "^0.2.13", "crc32-stream": "^4.0.2", "normalize-path": "^3.0.0", "readable-stream": "^3.6.0"}, "dev": true, "engines": {"node": ">= 10"}, "integrity": "sha512-D3uMHtGc/fcO1Gt1/L7i1e33VOvD4A9hfQLP+6ewd+BvG/gQ84Yh4oftEhAdjSMgBgwGL+jsppT7JYNpo6MHHg==", "license": "MIT", "peer": true, "resolved": "https://registry.npmjs.org/compress-commons/-/compress-commons-4.1.2.tgz", "version": "4.1.2"}, "node_modules/electron-builder-squirrel-windows/node_modules/crc32-stream": {"dependencies": {"crc-32": "^1.2.0", "readable-stream": "^3.4.0"}, "dev": true, "engines": {"node": ">= 10"}, "integrity": "sha512-NT7w2JVU7DFroFdYkeq8cywxrgjPHWkdX1wjpRQXPX5Asews3tA+Ght6lddQO5Mkumffp3X7GEqku3epj2toIw==", "license": "MIT", "peer": true, "resolved": "https://registry.npmjs.org/crc32-stream/-/crc32-stream-4.0.3.tgz", "version": "4.0.3"}, "node_modules/electron-builder-squirrel-windows/node_modules/fs-extra": {"dependencies": {"graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}, "dev": true, "engines": {"node": ">=12"}, "integrity": "sha512-oRXApq54ETRj4eMiFzGnHWGy+zo5raudjuxN0b8H7s/RU2oW0Wvsx9O0ACRN/kRq9E8Vu/ReskGB5o3ji+FzHQ==", "license": "MIT", "peer": true, "resolved": "https://registry.npmjs.org/fs-extra/-/fs-extra-10.1.0.tgz", "version": "10.1.0"}, "node_modules/electron-builder-squirrel-windows/node_modules/jsonfile": {"dependencies": {"universalify": "^2.0.0"}, "dev": true, "integrity": "sha512-5dgndWOriYSm5cnYaJNhalLNDKOqFwyDB/rr1E9ZsGciGvKPs8R2xYGCacuf3z6K1YKDz182fd+fY3cn3pMqXQ==", "license": "MIT", "optionalDependencies": {"graceful-fs": "^4.1.6"}, "peer": true, "resolved": "https://registry.npmjs.org/jsonfile/-/jsonfile-6.1.0.tgz", "version": "6.1.0"}, "node_modules/electron-builder-squirrel-windows/node_modules/safe-buffer": {"dev": true, "integrity": "sha512-Gd2UZBJDkXlY7GbJxfsE8/nvKkUEU1G38c1siN6QP6a9PT9MmHB8GnpscSmMJSoF8LOIrt8ud/wPtojys4G6+g==", "license": "MIT", "peer": true, "resolved": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.1.2.tgz", "version": "5.1.2"}, "node_modules/electron-builder-squirrel-windows/node_modules/string_decoder": {"dependencies": {"safe-buffer": "~5.1.0"}, "dev": true, "integrity": "sha512-n/ShnvDi6FHbbVfviro+WojiFzv+s8MPMHBczVePfUpDJLwoLT0ht1l4YwBCbi8pJAveEEdnkHyPyTP/mzRfwg==", "license": "MIT", "peer": true, "resolved": "https://registry.npmjs.org/string_decoder/-/string_decoder-1.1.1.tgz", "version": "1.1.1"}, "node_modules/electron-builder-squirrel-windows/node_modules/tar-stream": {"dependencies": {"bl": "^4.0.3", "end-of-stream": "^1.4.1", "fs-constants": "^1.0.0", "inherits": "^2.0.3", "readable-stream": "^3.1.1"}, "dev": true, "engines": {"node": ">=6"}, "integrity": "sha512-ujeqbceABgwMZxEJnk2HDY2DlnUZ+9oEcb1KzTVfYHio0UE6dG71n60d8D2I4qNvleWrrXpmjpt7vZeF1LnMZQ==", "license": "MIT", "peer": true, "resolved": "https://registry.npmjs.org/tar-stream/-/tar-stream-2.2.0.tgz", "version": "2.2.0"}, "node_modules/electron-builder-squirrel-windows/node_modules/universalify": {"dev": true, "engines": {"node": ">= 10.0.0"}, "integrity": "sha512-gptHNQghINnc/vTGIk0SOFGFNXw7JVrlRUtConJRlvaw6DuX0wO5Jeko9sWrMBhh+PsYAZ7oXAiOnf/UKogyiw==", "license": "MIT", "peer": true, "resolved": "https://registry.npmjs.org/universalify/-/universalify-2.0.1.tgz", "version": "2.0.1"}, "node_modules/electron-builder-squirrel-windows/node_modules/zip-stream": {"dependencies": {"archiver-utils": "^3.0.4", "compress-commons": "^4.1.2", "readable-stream": "^3.6.0"}, "dev": true, "engines": {"node": ">= 10"}, "integrity": "sha512-9qv4rlDiopXg4E69k+vMHjNN63YFMe9sZMrdlvKnCjlCRWeCBswPPMPUfx+ipsAWq1LXHe70RcbaHdJJpS6hyQ==", "license": "MIT", "peer": true, "resolved": "https://registry.npmjs.org/zip-stream/-/zip-stream-4.1.1.tgz", "version": "4.1.1"}, "node_modules/electron-builder-squirrel-windows/node_modules/zip-stream/node_modules/archiver-utils": {"dependencies": {"glob": "^7.2.3", "graceful-fs": "^4.2.0", "lazystream": "^1.0.0", "lodash.defaults": "^4.2.0", "lodash.difference": "^4.5.0", "lodash.flatten": "^4.4.0", "lodash.isplainobject": "^4.0.6", "lodash.union": "^4.6.0", "normalize-path": "^3.0.0", "readable-stream": "^3.6.0"}, "dev": true, "engines": {"node": ">= 10"}, "integrity": "sha512-KVgf4XQVrTjhyWmx6cte4RxonPLR9onExufI1jhvw/MQ4BB6IsZD5gT8Lq+u/+pRkWna/6JoHpiQioaqFP5Rzw==", "license": "MIT", "peer": true, "resolved": "https://registry.npmjs.org/archiver-utils/-/archiver-utils-3.0.4.tgz", "version": "3.0.4"}, "node_modules/electron-builder/node_modules/fs-extra": {"dependencies": {"graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}, "dev": true, "engines": {"node": ">=12"}, "integrity": "sha512-oRXApq54ETRj4eMiFzGnHWGy+zo5raudjuxN0b8H7s/RU2oW0Wvsx9O0ACRN/kRq9E8Vu/ReskGB5o3ji+FzHQ==", "license": "MIT", "resolved": "https://registry.npmjs.org/fs-extra/-/fs-extra-10.1.0.tgz", "version": "10.1.0"}, "node_modules/electron-builder/node_modules/jsonfile": {"dependencies": {"universalify": "^2.0.0"}, "dev": true, "integrity": "sha512-5dgndWOriYSm5cnYaJNhalLNDKOqFwyDB/rr1E9ZsGciGvKPs8R2xYGCacuf3z6K1YKDz182fd+fY3cn3pMqXQ==", "license": "MIT", "optionalDependencies": {"graceful-fs": "^4.1.6"}, "resolved": "https://registry.npmjs.org/jsonfile/-/jsonfile-6.1.0.tgz", "version": "6.1.0"}, "node_modules/electron-builder/node_modules/universalify": {"dev": true, "engines": {"node": ">= 10.0.0"}, "integrity": "sha512-gptHNQghINnc/vTGIk0SOFGFNXw7JVrlRUtConJRlvaw6DuX0wO5Jeko9sWrMBhh+PsYAZ7oXAiOnf/UKogyiw==", "license": "MIT", "resolved": "https://registry.npmjs.org/universalify/-/universalify-2.0.1.tgz", "version": "2.0.1"}, "node_modules/electron-log": {"engines": {"node": ">= 14"}, "integrity": "sha512-QvisA18Z++8E3Th0zmhUelys9dEv7aIeXJlbFw3UrxCc8H9qSRW0j8/ooTef/EtHui8tVmbKSL+EIQzP9GoRLg==", "license": "MIT", "resolved": "https://registry.npmjs.org/electron-log/-/electron-log-5.4.1.tgz", "version": "5.4.1"}, "node_modules/electron-publish": {"dependencies": {"@types/fs-extra": "^9.0.11", "builder-util": "24.13.1", "builder-util-runtime": "9.2.4", "chalk": "^4.1.2", "fs-extra": "^10.1.0", "lazy-val": "^1.0.5", "mime": "^2.5.2"}, "dev": true, "integrity": "sha512-2ZgdEqJ8e9D17Hwp5LEq5mLQPjqU3lv/IALvgp+4W8VeNhryfGhYEQC/PgDPMrnWUp+l60Ou5SJLsu+k4mhQ8A==", "license": "MIT", "resolved": "https://registry.npmjs.org/electron-publish/-/electron-publish-24.13.1.tgz", "version": "24.13.1"}, "node_modules/electron-publish/node_modules/fs-extra": {"dependencies": {"graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}, "dev": true, "engines": {"node": ">=12"}, "integrity": "sha512-oRXApq54ETRj4eMiFzGnHWGy+zo5raudjuxN0b8H7s/RU2oW0Wvsx9O0ACRN/kRq9E8Vu/ReskGB5o3ji+FzHQ==", "license": "MIT", "resolved": "https://registry.npmjs.org/fs-extra/-/fs-extra-10.1.0.tgz", "version": "10.1.0"}, "node_modules/electron-publish/node_modules/jsonfile": {"dependencies": {"universalify": "^2.0.0"}, "dev": true, "integrity": "sha512-5dgndWOriYSm5cnYaJNhalLNDKOqFwyDB/rr1E9ZsGciGvKPs8R2xYGCacuf3z6K1YKDz182fd+fY3cn3pMqXQ==", "license": "MIT", "optionalDependencies": {"graceful-fs": "^4.1.6"}, "resolved": "https://registry.npmjs.org/jsonfile/-/jsonfile-6.1.0.tgz", "version": "6.1.0"}, "node_modules/electron-publish/node_modules/universalify": {"dev": true, "engines": {"node": ">= 10.0.0"}, "integrity": "sha512-gptHNQghINnc/vTGIk0SOFGFNXw7JVrlRUtConJRlvaw6DuX0wO5Jeko9sWrMBhh+PsYAZ7oXAiOnf/UKogyiw==", "license": "MIT", "resolved": "https://registry.npmjs.org/universalify/-/universalify-2.0.1.tgz", "version": "2.0.1"}, "node_modules/electron-to-chromium": {"dev": true, "integrity": "sha512-UWKi/EbBopgfFsc5k61wFpV7WrnnSlSzW/e2XcBmS6qKYTivZlLtoll5/rdqRTxGglGHkmkW0j0pFNJG10EUIQ==", "license": "ISC", "resolved": "https://registry.npmjs.org/electron-to-chromium/-/electron-to-chromium-1.5.179.tgz", "version": "1.5.179"}, "node_modules/electron-updater": {"dependencies": {"builder-util-runtime": "9.3.1", "fs-extra": "^10.1.0", "js-yaml": "^4.1.0", "lazy-val": "^1.0.5", "lodash.escaperegexp": "^4.1.2", "lodash.isequal": "^4.5.0", "semver": "^7.6.3", "tiny-typed-emitter": "^2.1.0"}, "integrity": "sha512-Cr4GDOkbAUqRHP5/oeOmH/L2Bn6+FQPxVLZtPbcmKZC63a1F3uu5EefYOssgZXG3u/zBlubbJ5PJdITdMVggbw==", "license": "MIT", "resolved": "https://registry.npmjs.org/electron-updater/-/electron-updater-6.6.2.tgz", "version": "6.6.2"}, "node_modules/electron-updater/node_modules/builder-util-runtime": {"dependencies": {"debug": "^4.3.4", "sax": "^1.2.4"}, "engines": {"node": ">=12.0.0"}, "integrity": "sha512-2/egrNDDnRaxVwK3A+cJq6UOlqOdedGA7JPqCeJjN2Zjk1/QB/6QUi3b714ScIGS7HafFXTyzJEOr5b44I3kvQ==", "license": "MIT", "resolved": "https://registry.npmjs.org/builder-util-runtime/-/builder-util-runtime-9.3.1.tgz", "version": "9.3.1"}, "node_modules/electron-updater/node_modules/fs-extra": {"dependencies": {"graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}, "engines": {"node": ">=12"}, "integrity": "sha512-oRXApq54ETRj4eMiFzGnHWGy+zo5raudjuxN0b8H7s/RU2oW0Wvsx9O0ACRN/kRq9E8Vu/ReskGB5o3ji+FzHQ==", "license": "MIT", "resolved": "https://registry.npmjs.org/fs-extra/-/fs-extra-10.1.0.tgz", "version": "10.1.0"}, "node_modules/electron-updater/node_modules/jsonfile": {"dependencies": {"universalify": "^2.0.0"}, "integrity": "sha512-5dgndWOriYSm5cnYaJNhalLNDKOqFwyDB/rr1E9ZsGciGvKPs8R2xYGCacuf3z6K1YKDz182fd+fY3cn3pMqXQ==", "license": "MIT", "optionalDependencies": {"graceful-fs": "^4.1.6"}, "resolved": "https://registry.npmjs.org/jsonfile/-/jsonfile-6.1.0.tgz", "version": "6.1.0"}, "node_modules/electron-updater/node_modules/universalify": {"engines": {"node": ">= 10.0.0"}, "integrity": "sha512-gptHNQghINnc/vTGIk0SOFGFNXw7JVrlRUtConJRlvaw6DuX0wO5Jeko9sWrMBhh+PsYAZ7oXAiOnf/UKogyiw==", "license": "MIT", "resolved": "https://registry.npmjs.org/universalify/-/universalify-2.0.1.tgz", "version": "2.0.1"}, "node_modules/electron/node_modules/@types/node": {"dependencies": {"undici-types": "~5.26.4"}, "dev": true, "integrity": "sha512-kNrFiTgG4a9JAn1LMQeLOv3MvXIPokzXziohMrMsvpYgLpdEt/mMiVYc4sGKtDfyxM5gIDF4VgrPRyCw4fHOYg==", "license": "MIT", "resolved": "https://registry.npmjs.org/@types/node/-/node-18.19.115.tgz", "version": "18.19.115"}, "node_modules/electron/node_modules/undici-types": {"dev": true, "integrity": "sha512-JlCMO+ehdEIKqlFxk6IfVoAUVmgz7cU7zD/h9XZ0qzeosSHmUJVOzSQvvYSYWXkFXC+IfLKSIffhv0sVZup6pA==", "license": "MIT", "resolved": "https://registry.npmjs.org/undici-types/-/undici-types-5.26.5.tgz", "version": "5.26.5"}, "node_modules/emittery": {"dev": true, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sindresorhus/emittery?sponsor=1"}, "integrity": "sha512-DeWwawk6r5yR9jFgnDKYt4sLS0LmHJJi3ZOnb5/JdbYwj3nW+FxQnHIjhBKz8YLC7oRNPVM9NQ47I3CVx34eqQ==", "license": "MIT", "resolved": "https://registry.npmjs.org/emittery/-/emittery-0.13.1.tgz", "version": "0.13.1"}, "node_modules/emoji-regex": {"integrity": "sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==", "license": "MIT", "resolved": "https://registry.npmjs.org/emoji-regex/-/emoji-regex-8.0.0.tgz", "version": "8.0.0"}, "node_modules/end-of-stream": {"dependencies": {"once": "^1.4.0"}, "dev": true, "integrity": "sha512-ooEGc6HP26xXq/N+GCGOT0JKCLDGrq2bQUZrQ7gyrJiZANJ/8YDTxTpQBXGMn+WbIQXNVpyWymm7KYVICQnyOg==", "license": "MIT", "resolved": "https://registry.npmjs.org/end-of-stream/-/end-of-stream-1.4.5.tgz", "version": "1.4.5"}, "node_modules/env-paths": {"dev": true, "engines": {"node": ">=6"}, "integrity": "sha512-+h1lkLKhZMTYjog1VEpJNG7NZJWcuc2DDk/qsqSTRRCOXiLjeQ1d1/udrUGhqMxUgAlwKNZ0cf2uqan5GLuS2A==", "license": "MIT", "resolved": "https://registry.npmjs.org/env-paths/-/env-paths-2.2.1.tgz", "version": "2.2.1"}, "node_modules/err-code": {"dev": true, "integrity": "sha512-2bmlRpNKBxT/CRmPOlyISQpNj+qSeYvcym/uT0Jx2bMOlKLtSy1ZmLuVxSEKKyor/N5yhvp/ZiG1oE3DEYMSFA==", "license": "MIT", "resolved": "https://registry.npmjs.org/err-code/-/err-code-2.0.3.tgz", "version": "2.0.3"}, "node_modules/error-ex": {"dependencies": {"is-arrayish": "^0.2.1"}, "dev": true, "integrity": "sha512-7dFHNmqeFSEt2ZBsCriorKnn3Z2pj+fd9kmI6QoWw4//DL+icEBfc0U7qJCisqrTsKTjw4fNFy2pW9OqStD84g==", "license": "MIT", "resolved": "https://registry.npmjs.org/error-ex/-/error-ex-1.3.2.tgz", "version": "1.3.2"}, "node_modules/es-define-property": {"dev": true, "engines": {"node": ">= 0.4"}, "integrity": "sha512-e3nRfgfUZ4rNGL232gUgX06QNyyez04KdjFrF+LTRoOXmrOgFKDg4BCdsjW8EnT69eqdYGmRpJwiPVYNrCaW3g==", "license": "MIT", "resolved": "https://registry.npmjs.org/es-define-property/-/es-define-property-1.0.1.tgz", "version": "1.0.1"}, "node_modules/es-errors": {"dev": true, "engines": {"node": ">= 0.4"}, "integrity": "sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw==", "license": "MIT", "resolved": "https://registry.npmjs.org/es-errors/-/es-errors-1.3.0.tgz", "version": "1.3.0"}, "node_modules/es-object-atoms": {"dependencies": {"es-errors": "^1.3.0"}, "dev": true, "engines": {"node": ">= 0.4"}, "integrity": "sha512-FGgH2h8zKNim9ljj7dankFPcICIK9Cp5bm+c2gQSYePhpaG5+esrLODihIorn+Pe6FGJzWhXQotPv73jTaldXA==", "license": "MIT", "resolved": "https://registry.npmjs.org/es-object-atoms/-/es-object-atoms-1.1.1.tgz", "version": "1.1.1"}, "node_modules/es-set-tostringtag": {"dependencies": {"es-errors": "^1.3.0", "get-intrinsic": "^1.2.6", "has-tostringtag": "^1.0.2", "hasown": "^2.0.2"}, "dev": true, "engines": {"node": ">= 0.4"}, "integrity": "sha512-j6vWzfrGVfyXxge+O0x5sh6cvxAog0a/4Rdd2K36zCMV5eJ+/+tOAngRO8cODMNWbVRdVlmGZQL2YS3yR8bIUA==", "license": "MIT", "resolved": "https://registry.npmjs.org/es-set-tostringtag/-/es-set-tostringtag-2.1.0.tgz", "version": "2.1.0"}, "node_modules/es6-error": {"dev": true, "integrity": "sha512-Um/+FxMr9CISWh0bi5Zv0iOD+4cFh5qLeks1qhAopKVAJw3drgKbKySikp7wGhDL0HPeaja0P5ULZrxLkniUVg==", "license": "MIT", "optional": true, "resolved": "https://registry.npmjs.org/es6-error/-/es6-error-4.1.1.tgz", "version": "4.1.1"}, "node_modules/escalade": {"dev": true, "engines": {"node": ">=6"}, "integrity": "sha512-WUj2qlxaQtO4g6Pq5c29GTcWGDyd8itL8zTlipgECz3JesAiiOKotd8JU6otB3PACgG6xkJUyVhboMS+bje/jA==", "license": "MIT", "resolved": "https://registry.npmjs.org/escalade/-/escalade-3.2.0.tgz", "version": "3.2.0"}, "node_modules/escape-string-regexp": {"dev": true, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}, "integrity": "sha512-TtpcNJ3XAzx3Gq8sWRzJaVajRs0uVxA2YAkdb1jm2YkPz4G6egUFAyA3n5vtEIZefPk5Wa4UXbKuS5fKkJWdgA==", "license": "MIT", "optional": true, "resolved": "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-4.0.0.tgz", "version": "4.0.0"}, "node_modules/esprima": {"bin": {"esparse": "bin/esparse.js", "esvalidate": "bin/esvalidate.js"}, "dev": true, "engines": {"node": ">=4"}, "integrity": "sha512-eGuFFw7Upda+g4p+QHvnW0RyTX/SVeJBDM/gCtMARO0cLuT2HcEKnTPvhjV6aGeqrCB/sbNop0Kszm0jsaWU4A==", "license": "BSD-2-<PERSON><PERSON>", "resolved": "https://registry.npmjs.org/esprima/-/esprima-4.0.1.tgz", "version": "4.0.1"}, "node_modules/execa": {"dependencies": {"cross-spawn": "^7.0.3", "get-stream": "^6.0.0", "human-signals": "^2.1.0", "is-stream": "^2.0.0", "merge-stream": "^2.0.0", "npm-run-path": "^4.0.1", "onetime": "^5.1.2", "signal-exit": "^3.0.3", "strip-final-newline": "^2.0.0"}, "dev": true, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sindresorhus/execa?sponsor=1"}, "integrity": "sha512-8uSpZZocAZRBAPIEINJj3Lo9HyGitllczc27Eh5YYojjMFMn8yHMDMaUHE2Jqfq05D/wucwI4JGURyXt1vchyg==", "license": "MIT", "resolved": "https://registry.npmjs.org/execa/-/execa-5.1.1.tgz", "version": "5.1.1"}, "node_modules/execa/node_modules/get-stream": {"dev": true, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}, "integrity": "sha512-ts6Wi+2j3jQjqi70w5AlN8DFnkSwC+MqmxEzdEALB2qXZYV3X/b1CTfgPLGJNMeAWxdPfU8FO1ms3NUfaHCPYg==", "license": "MIT", "resolved": "https://registry.npmjs.org/get-stream/-/get-stream-6.0.1.tgz", "version": "6.0.1"}, "node_modules/execa/node_modules/signal-exit": {"dev": true, "integrity": "sha512-wnD2ZE+l+SPC/uoS0vXeE9L1+0wuaMqKlfz9AMUo38JsyLSBWSFcHR1Rri62LZc12vLr1gb3jl7iwQhgwpAbGQ==", "license": "ISC", "resolved": "https://registry.npmjs.org/signal-exit/-/signal-exit-3.0.7.tgz", "version": "3.0.7"}, "node_modules/exit": {"dev": true, "engines": {"node": ">= 0.8.0"}, "integrity": "sha512-Zk/eNKV2zbjpKzrsQ+n1G6poVbErQxJ0LBOJXaKZ1EViLzH+hrLu9cdXI4zw9dBQJslwBEpbQ2P1oS7nDxs6jQ==", "resolved": "https://registry.npmjs.org/exit/-/exit-0.1.2.tgz", "version": "0.1.2"}, "node_modules/expect": {"dependencies": {"@jest/expect-utils": "^29.7.0", "jest-get-type": "^29.6.3", "jest-matcher-utils": "^29.7.0", "jest-message-util": "^29.7.0", "jest-util": "^29.7.0"}, "dev": true, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "integrity": "sha512-2Zks0hf1VLFYI1kbh0I5jP3KHHyCHpkfyHBzsSXRFgl/Bg9mWYfMW8oD+PdMPlEwy5HNsR9JutYy6pMeOh61nw==", "license": "MIT", "resolved": "https://registry.npmjs.org/expect/-/expect-29.7.0.tgz", "version": "29.7.0"}, "node_modules/external-editor": {"dependencies": {"chardet": "^0.7.0", "iconv-lite": "^0.4.24", "tmp": "^0.0.33"}, "engines": {"node": ">=4"}, "integrity": "sha512-hMQ4CX1p1izmuLYyZqLMO/qGNw10wSv9QDCPfzXfyFrOaCSSoRfqE1Kf1s5an66J5JZC62NewG+mK49jOCtQew==", "license": "MIT", "resolved": "https://registry.npmjs.org/external-editor/-/external-editor-3.1.0.tgz", "version": "3.1.0"}, "node_modules/external-editor/node_modules/iconv-lite": {"dependencies": {"safer-buffer": ">= 2.1.2 < 3"}, "engines": {"node": ">=0.10.0"}, "integrity": "sha512-v3MXnZAcvnywkTUEZomIActle7RXXeedOR31wwl7VlyoXO4Qi9arvSenNQWne1TcRwhCL1HwLI21bEqdpj8/rA==", "license": "MIT", "resolved": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.4.24.tgz", "version": "0.4.24"}, "node_modules/external-editor/node_modules/tmp": {"dependencies": {"os-tmpdir": "~1.0.2"}, "engines": {"node": ">=0.6.0"}, "integrity": "sha512-jRCJlojKnZ3addtTOjdIqoRuPEKBvNXcGYqzO6zWZX8KfKEpnGY5jfggJQ3EjKuu8D4bJRr0y+cYJFmYbImXGw==", "license": "MIT", "resolved": "https://registry.npmjs.org/tmp/-/tmp-0.0.33.tgz", "version": "0.0.33"}, "node_modules/extract-zip": {"bin": {"extract-zip": "cli.js"}, "dependencies": {"debug": "^4.1.1", "get-stream": "^5.1.0", "yauzl": "^2.10.0"}, "dev": true, "engines": {"node": ">= 10.17.0"}, "integrity": "sha512-GDhU9ntwuKyGXdZBUgTIe+vXnWj0fppUEtMDL0+idd5Sta8TGpHssn/eusA9mrPr9qNDym6SxAYZjNvCn/9RBg==", "license": "BSD-2-<PERSON><PERSON>", "optionalDependencies": {"@types/yauzl": "^2.9.1"}, "resolved": "https://registry.npmjs.org/extract-zip/-/extract-zip-2.0.1.tgz", "version": "2.0.1"}, "node_modules/extract-zip/node_modules/yauzl": {"dependencies": {"buffer-crc32": "~0.2.3", "fd-slicer": "~1.1.0"}, "dev": true, "integrity": "sha512-p4a9I6X6nu6IhoGmBqAcbJy1mlC4j27vEPZX9F4L4/vZT3Lyq1VkFHw/V/PUcB9Buo+DG3iHkT0x3Qya58zc3g==", "license": "MIT", "resolved": "https://registry.npmjs.org/yauzl/-/yauzl-2.10.0.tgz", "version": "2.10.0"}, "node_modules/extsprintf": {"dev": true, "engines": ["node >=0.6.0"], "integrity": "sha512-Wrk35e8ydCKDj/ArClo1VrPVmN8zph5V4AtHwIuHhvMXsKf73UT3BOD+azBIW+3wOJ4FhEH7zyaJCFvChjYvMA==", "license": "MIT", "optional": true, "resolved": "https://registry.npmjs.org/extsprintf/-/extsprintf-1.4.1.tgz", "version": "1.4.1"}, "node_modules/fast-deep-equal": {"integrity": "sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q==", "license": "MIT", "resolved": "https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz", "version": "3.1.3"}, "node_modules/fast-fifo": {"integrity": "sha512-/d9sfos4yxzpwkDkuN7k2SqFKtYNmCTzgfEpz82x34IM9/zc8KGxQoXg1liNC/izpRM/MBdt44Nmx41ZWqk+FQ==", "license": "MIT", "resolved": "https://registry.npmjs.org/fast-fifo/-/fast-fifo-1.3.2.tgz", "version": "1.3.2"}, "node_modules/fast-json-stable-stringify": {"dev": true, "integrity": "sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw==", "license": "MIT", "resolved": "https://registry.npmjs.org/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz", "version": "2.1.0"}, "node_modules/fast-redact": {"engines": {"node": ">=6"}, "integrity": "sha512-dwsoQlS7h9hMeYUq1W++23NDcBLV4KqONnITDV9DjfS3q1SgDGVrBdvvTLUotWtPSD7asWDV9/CmsZPy8Hf70A==", "license": "MIT", "resolved": "https://registry.npmjs.org/fast-redact/-/fast-redact-3.5.0.tgz", "version": "3.5.0"}, "node_modules/fast-uri": {"funding": [{"type": "github", "url": "https://github.com/sponsors/fastify"}, {"type": "opencollective", "url": "https://opencollective.com/fastify"}], "integrity": "sha512-Atfo14OibSv5wAp4VWNsFYE1AchQRTv9cBGWET4pZWHzYshFSS9NQI6I57rdKn9croWVMbYFbLhJ+yJvmZIIHw==", "license": "BSD-3-<PERSON><PERSON>", "resolved": "https://registry.npmjs.org/fast-uri/-/fast-uri-3.0.6.tgz", "version": "3.0.6"}, "node_modules/fb-watchman": {"dependencies": {"bser": "2.1.1"}, "dev": true, "integrity": "sha512-p5161BqbuCaSnB8jIbzQHOlpgsPmK5rJVDfDKO91Axs5NC1uu3HRQm6wt9cd9/+GtQQIO53JdGXXoyDpTAsgYA==", "license": "Apache-2.0", "resolved": "https://registry.npmjs.org/fb-watchman/-/fb-watchman-2.0.2.tgz", "version": "2.0.2"}, "node_modules/fd-slicer": {"dependencies": {"pend": "~1.2.0"}, "dev": true, "integrity": "sha512-cE1qsB/VwyQozZ+q1dGxR8LBYNZeofhEdUNGSMbQD3Gw2lAzX9Zb3uIU6Ebc/Fmyjo9AWWfnn0AUCHqtevs/8g==", "license": "MIT", "resolved": "https://registry.npmjs.org/fd-slicer/-/fd-slicer-1.1.0.tgz", "version": "1.1.0"}, "node_modules/figures": {"dependencies": {"escape-string-regexp": "^1.0.5"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}, "integrity": "sha512-yaduQFRKLXYOGgEn6AZau90j3ggSOyiqXU0F9JZfeXYhNa+Jk4X+s45A2zg5jns87GAFa34BBm2kXw4XpNcbdg==", "license": "MIT", "resolved": "https://registry.npmjs.org/figures/-/figures-3.2.0.tgz", "version": "3.2.0"}, "node_modules/figures/node_modules/escape-string-regexp": {"engines": {"node": ">=0.8.0"}, "integrity": "sha512-vbRorB5FUQWvla16U8R/qgaFIya2qGzwDrNmCZuYKrbdSUMG6I1ZCGQRefkRVhuOkIGVne7BQ35DSfo1qvJqFg==", "license": "MIT", "resolved": "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz", "version": "1.0.5"}, "node_modules/filelist": {"dependencies": {"minimatch": "^5.0.1"}, "dev": true, "integrity": "sha512-w1cEuf3S+DrLCQL7ET6kz+gmlJdbq9J7yXCSjK/OZCPA+qEN1WyF4ZAf0YYJa4/shHJra2t/d/r8SV4Ji+x+8Q==", "license": "Apache-2.0", "resolved": "https://registry.npmjs.org/filelist/-/filelist-1.0.4.tgz", "version": "1.0.4"}, "node_modules/fill-range": {"dependencies": {"to-regex-range": "^5.0.1"}, "dev": true, "engines": {"node": ">=8"}, "integrity": "sha512-YsGpe3WHLK8ZYi4tWDg2Jy3ebRz2rXowDxnld4bkQB00cc/1Zw9AWnC0i9ztDJitivtQvaI9KaLyKrc+hBW0yg==", "license": "MIT", "resolved": "https://registry.npmjs.org/fill-range/-/fill-range-7.1.1.tgz", "version": "7.1.1"}, "node_modules/find-up": {"dependencies": {"locate-path": "^5.0.0", "path-exists": "^4.0.0"}, "dev": true, "engines": {"node": ">=8"}, "integrity": "sha512-PpOwAdQ/YlXQ2vj8a3h8IipDuYRi3wceVQQGYWxNINccq40Anw7BlsEXCMbt1Zt+OLA6Fq9suIpIWD0OsnISlw==", "license": "MIT", "resolved": "https://registry.npmjs.org/find-up/-/find-up-4.1.0.tgz", "version": "4.1.0"}, "node_modules/foreground-child": {"dependencies": {"cross-spawn": "^7.0.6", "signal-exit": "^4.0.1"}, "dev": true, "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "integrity": "sha512-gIXjKqtFuWEgzFRJA9WCQeSJLZDjgJUOMCMzxtvFq/37KojM1BFGufqsCy0r4qSQmYLsZYMeyRqzIWOMup03sw==", "license": "ISC", "resolved": "https://registry.npmjs.org/foreground-child/-/foreground-child-3.3.1.tgz", "version": "3.3.1"}, "node_modules/form-data": {"dependencies": {"asynckit": "^0.4.0", "combined-stream": "^1.0.8", "es-set-tostringtag": "^2.1.0", "hasown": "^2.0.2", "mime-types": "^2.1.12"}, "dev": true, "engines": {"node": ">= 6"}, "integrity": "sha512-qsITQPfmvMOSAdeyZ+12I1c+CKSstAFAwu+97zrnWAbIr5u8wfsExUzCesVLC8NgHuRUqNN4Zy6UPWUTRGslcA==", "license": "MIT", "resolved": "https://registry.npmjs.org/form-data/-/form-data-4.0.3.tgz", "version": "4.0.3"}, "node_modules/fs-constants": {"dev": true, "integrity": "sha512-y6OAwoSIf7FyjMIv94u+b5rdheZEjzR63GTyZJm5qh4Bi+2YgwLCcI/fPFZkL5PSixOt6ZNKm+w+Hfp/Bciwow==", "license": "MIT", "peer": true, "resolved": "https://registry.npmjs.org/fs-constants/-/fs-constants-1.0.0.tgz", "version": "1.0.0"}, "node_modules/fs-extra": {"dependencies": {"graceful-fs": "^4.2.0", "jsonfile": "^4.0.0", "universalify": "^0.1.0"}, "dev": true, "engines": {"node": ">=6 <7 || >=8"}, "integrity": "sha512-yhlQgA6mnOJUKOsRUFsgJdQCvkKhcz8tlZG5HBQfReYZy46OwLcY+Zia0mtdHsOo9y/hP+CxMN0TU9QxoOtG4g==", "license": "MIT", "resolved": "https://registry.npmjs.org/fs-extra/-/fs-extra-8.1.0.tgz", "version": "8.1.0"}, "node_modules/fs-minipass": {"dependencies": {"minipass": "^3.0.0"}, "dev": true, "engines": {"node": ">= 8"}, "integrity": "sha512-V/JgOLFCS+R6Vcq0slCuaeWEdNC3ouDlJMNIsacH2VtALiu9mV4LPrHc5cDl8k5aw6J8jwgWWpiTo5RYhmIzvg==", "license": "ISC", "resolved": "https://registry.npmjs.org/fs-minipass/-/fs-minipass-2.1.0.tgz", "version": "2.1.0"}, "node_modules/fs-minipass/node_modules/minipass": {"dependencies": {"yallist": "^4.0.0"}, "dev": true, "engines": {"node": ">=8"}, "integrity": "sha512-DxiNidxSEK+tHG6zOIklvNOwm3hvCrbUrdtzY74U6HKTJxvIDfOUL5W5P2Ghd3DTkhhKPYGqeNUIh5qcM4YBfw==", "license": "ISC", "resolved": "https://registry.npmjs.org/minipass/-/minipass-3.3.6.tgz", "version": "3.3.6"}, "node_modules/fs.realpath": {"integrity": "sha512-OO0pH2lK6a0hZnAdau5ItzHPI6pUlvI7jMVnxUQRtw4owF2wk8lOSabtGDCTP4Ggrg2MbGnWO9X8K1t4+fGMDw==", "license": "ISC", "resolved": "https://registry.npmjs.org/fs.realpath/-/fs.realpath-1.0.0.tgz", "version": "1.0.0"}, "node_modules/fsevents": {"dev": true, "engines": {"node": "^8.16.0 || ^10.6.0 || >=11.0.0"}, "hasInstallScript": true, "integrity": "sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==", "license": "MIT", "optional": true, "os": ["darwin"], "resolved": "https://registry.npmjs.org/fsevents/-/fsevents-2.3.3.tgz", "version": "2.3.3"}, "node_modules/function-bind": {"dev": true, "funding": {"url": "https://github.com/sponsors/ljharb"}, "integrity": "sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==", "license": "MIT", "resolved": "https://registry.npmjs.org/function-bind/-/function-bind-1.1.2.tgz", "version": "1.1.2"}, "node_modules/gensync": {"dev": true, "engines": {"node": ">=6.9.0"}, "integrity": "sha512-3hN7NaskYvMDLQY55gnW3NQ+mesEAepTqlg+VEbj7zzqEMBVNhzcGYYeqFo/TlYz6eQiFcp1HcsCZO+nGgS8zg==", "license": "MIT", "resolved": "https://registry.npmjs.org/gensync/-/gensync-1.0.0-beta.2.tgz", "version": "1.0.0-beta.2"}, "node_modules/get-caller-file": {"dev": true, "engines": {"node": "6.* || 8.* || >= 10.*"}, "integrity": "sha512-DyFP3BM/3YHTQOCUL/w0OZHR0lpKeGrxotcHWcqNEdnltqFwXVfhEBQ94eIo34AfQpo0rGki4cyIiftY06h2Fg==", "license": "ISC", "resolved": "https://registry.npmjs.org/get-caller-file/-/get-caller-file-2.0.5.tgz", "version": "2.0.5"}, "node_modules/get-intrinsic": {"dependencies": {"call-bind-apply-helpers": "^1.0.2", "es-define-property": "^1.0.1", "es-errors": "^1.3.0", "es-object-atoms": "^1.1.1", "function-bind": "^1.1.2", "get-proto": "^1.0.1", "gopd": "^1.2.0", "has-symbols": "^1.1.0", "hasown": "^2.0.2", "math-intrinsics": "^1.1.0"}, "dev": true, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "integrity": "sha512-9fSjSaos/fRIVIp+xSJlE6lfwhES7LNtKaCBIamHsjr2na1BiABJPo0mOjjz8GJDURarmCPGqaiVg5mfjb98CQ==", "license": "MIT", "resolved": "https://registry.npmjs.org/get-intrinsic/-/get-intrinsic-1.3.0.tgz", "version": "1.3.0"}, "node_modules/get-package-type": {"dev": true, "engines": {"node": ">=8.0.0"}, "integrity": "sha512-pjzuKtY64GYfWizNAJ0fr9VqttZkNiK2iS430LtIHzjBEr6bX8Am2zm4sW4Ro5wjWW5cAlRL1qAMTcXbjNAO2Q==", "license": "MIT", "resolved": "https://registry.npmjs.org/get-package-type/-/get-package-type-0.1.0.tgz", "version": "0.1.0"}, "node_modules/get-proto": {"dependencies": {"dunder-proto": "^1.0.1", "es-object-atoms": "^1.0.0"}, "dev": true, "engines": {"node": ">= 0.4"}, "integrity": "sha512-sTSfBjoXBp89JvIKIefqw7U2CCebsc74kiY6awiGogKtoSGbgjYE/G/+l9sF3MWFPNc9IcoOC4ODfKHfxFmp0g==", "license": "MIT", "resolved": "https://registry.npmjs.org/get-proto/-/get-proto-1.0.1.tgz", "version": "1.0.1"}, "node_modules/get-stream": {"dependencies": {"pump": "^3.0.0"}, "dev": true, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}, "integrity": "sha512-nBF+F1rAZVCu/p7rjzgA+Yb4lfYXrpl7a6VmJrU8wF9I1CKvP/QwPNZHnOlwbTkY6dvtFIzFMSyQXbLoTQPRpA==", "license": "MIT", "resolved": "https://registry.npmjs.org/get-stream/-/get-stream-5.2.0.tgz", "version": "5.2.0"}, "node_modules/glob": {"dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.1.1", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "deprecated": "Glob versions prior to v9 are no longer supported", "dev": true, "engines": {"node": "*"}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "integrity": "sha512-nFR0zLpU2YCaRxwoCJvL6UvCH2JFyFVIvwTLsIf21AuHlMskA1hhTdk+LlYJtOlYt9v6dvszD2BGRqBL+iQK9Q==", "license": "ISC", "resolved": "https://registry.npmjs.org/glob/-/glob-7.2.3.tgz", "version": "7.2.3"}, "node_modules/glob/node_modules/brace-expansion": {"dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}, "dev": true, "integrity": "sha512-9T9UjW3r0UW5c1Q7GTwllptXwhvYmEzFhzMfZ9H7FQWt+uZePjZPjBP/W1ZEyZ1twGWom5/56TF4lPcqjnDHcg==", "license": "MIT", "resolved": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.12.tgz", "version": "1.1.12"}, "node_modules/glob/node_modules/minimatch": {"dependencies": {"brace-expansion": "^1.1.7"}, "dev": true, "engines": {"node": "*"}, "integrity": "sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==", "license": "ISC", "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-3.1.2.tgz", "version": "3.1.2"}, "node_modules/global-agent": {"dependencies": {"boolean": "^3.0.1", "es6-error": "^4.1.1", "matcher": "^3.0.0", "roarr": "^2.15.3", "semver": "^7.3.2", "serialize-error": "^7.0.1"}, "dev": true, "engines": {"node": ">=10.0"}, "integrity": "sha512-PT6XReJ+D07JvGoxQMkT6qji/jVNfX/h364XHZOWeRzy64sSFr+xJ5OX7LI3b4MPQzdL4H8Y8M0xzPpsVMwA8Q==", "license": "BSD-3-<PERSON><PERSON>", "optional": true, "resolved": "https://registry.npmjs.org/global-agent/-/global-agent-3.0.0.tgz", "version": "3.0.0"}, "node_modules/globalthis": {"dependencies": {"define-properties": "^1.2.1", "gopd": "^1.0.1"}, "dev": true, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "integrity": "sha512-DpLKbNU4WylpxJykQujfCcwYWiV/Jhm50Goo0wrVILAv5jOr9d+H+UR3PhSCD2rCCEIg0uc+G+muBTwD54JhDQ==", "license": "MIT", "optional": true, "resolved": "https://registry.npmjs.org/globalthis/-/globalthis-1.0.4.tgz", "version": "1.0.4"}, "node_modules/gopd": {"dev": true, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "integrity": "sha512-ZUKRh6/kUFoAiTAtTYPZJ3hw9wNxx+BIBOijnlG9PnrJsCcSjs1wyyD6vJpaYtgnzDrKYRSqf3OO6Rfa93xsRg==", "license": "MIT", "resolved": "https://registry.npmjs.org/gopd/-/gopd-1.2.0.tgz", "version": "1.2.0"}, "node_modules/got": {"dependencies": {"@sindresorhus/is": "^4.0.0", "@szmarczak/http-timer": "^4.0.5", "@types/cacheable-request": "^6.0.1", "@types/responselike": "^1.0.0", "cacheable-lookup": "^5.0.3", "cacheable-request": "^7.0.2", "decompress-response": "^6.0.0", "http2-wrapper": "^1.0.0-beta.5.2", "lowercase-keys": "^2.0.0", "p-cancelable": "^2.0.0", "responselike": "^2.0.0"}, "dev": true, "engines": {"node": ">=10.19.0"}, "funding": {"url": "https://github.com/sindresorhus/got?sponsor=1"}, "integrity": "sha512-6tfZ91bOr7bOXnK7PRDCGBLa1H4U080YHNaAQ2KsMGlLEzRbk44nsZF2E1IeRc3vtJHPVbKCYgdFbaGO2ljd8g==", "license": "MIT", "resolved": "https://registry.npmjs.org/got/-/got-11.8.6.tgz", "version": "11.8.6"}, "node_modules/graceful-fs": {"integrity": "sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ==", "license": "ISC", "resolved": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.11.tgz", "version": "4.2.11"}, "node_modules/has-flag": {"engines": {"node": ">=8"}, "integrity": "sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==", "license": "MIT", "resolved": "https://registry.npmjs.org/has-flag/-/has-flag-4.0.0.tgz", "version": "4.0.0"}, "node_modules/has-property-descriptors": {"dependencies": {"es-define-property": "^1.0.0"}, "dev": true, "funding": {"url": "https://github.com/sponsors/ljharb"}, "integrity": "sha512-55JNKuIW+vq4Ke1BjOTjM2YctQIvCT7GFzHwmfZPGo5wnrgkid0YQtnAleFSqumZm4az3n2BS+erby5ipJdgrg==", "license": "MIT", "optional": true, "resolved": "https://registry.npmjs.org/has-property-descriptors/-/has-property-descriptors-1.0.2.tgz", "version": "1.0.2"}, "node_modules/has-symbols": {"dev": true, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "integrity": "sha512-1cDNdwJ2Jaohmb3sg4OmKaMBwuC48sYni5HUw2DvsC8LjGTLK9h+eb1X6RyuOHe4hT0ULCW68iomhjUoKUqlPQ==", "license": "MIT", "resolved": "https://registry.npmjs.org/has-symbols/-/has-symbols-1.1.0.tgz", "version": "1.1.0"}, "node_modules/has-tostringtag": {"dependencies": {"has-symbols": "^1.0.3"}, "dev": true, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "integrity": "sha512-NqADB8VjPFLM2V0VvHUewwwsw0ZWBaIdgo+ieHtK3hasLz4qeCRjYcqfB6AQrBggRKppKF8L52/VqdVsO47Dlw==", "license": "MIT", "resolved": "https://registry.npmjs.org/has-tostringtag/-/has-tostringtag-1.0.2.tgz", "version": "1.0.2"}, "node_modules/hasown": {"dependencies": {"function-bind": "^1.1.2"}, "dev": true, "engines": {"node": ">= 0.4"}, "integrity": "sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==", "license": "MIT", "resolved": "https://registry.npmjs.org/hasown/-/hasown-2.0.2.tgz", "version": "2.0.2"}, "node_modules/hosted-git-info": {"dependencies": {"lru-cache": "^6.0.0"}, "dev": true, "engines": {"node": ">=10"}, "integrity": "sha512-kyCuEOWjJqZuDbRHzL8V93NzQhwIB71oFWSyzVo+KPZI+pnQPPxucdkrOZvkLRnrf5URsQM+IJ09Dw29cRALIA==", "license": "ISC", "resolved": "https://registry.npmjs.org/hosted-git-info/-/hosted-git-info-4.1.0.tgz", "version": "4.1.0"}, "node_modules/html-escaper": {"dev": true, "integrity": "sha512-H2iMtd0I4Mt5eYiapRdIDjp+XzelXQ0tFE4JS7YFwFevXXMmOp9myNrUvCg0D6ws8iqkRPBfKHgbwig1SmlLfg==", "license": "MIT", "resolved": "https://registry.npmjs.org/html-escaper/-/html-escaper-2.0.2.tgz", "version": "2.0.2"}, "node_modules/http-cache-semantics": {"dev": true, "integrity": "sha512-dTxcvPXqPvXBQpq5dUr6mEMJX4oIEFv6bwom3FDwKRDsuIjjJGANqhBuoAn9c1RQJIdAKav33ED65E2ys+87QQ==", "license": "BSD-2-<PERSON><PERSON>", "resolved": "https://registry.npmjs.org/http-cache-semantics/-/http-cache-semantics-4.2.0.tgz", "version": "4.2.0"}, "node_modules/http-proxy-agent": {"dependencies": {"@tootallnate/once": "2", "agent-base": "6", "debug": "4"}, "dev": true, "engines": {"node": ">= 6"}, "integrity": "sha512-n2hY8YdoRE1i7r6M0w9DIw5GgZN0G25P8zLCRQ8rjXtTU3vsNFBI/vWK/UIeE6g5MUUz6avwAPXmL6Fy9D/90w==", "license": "MIT", "resolved": "https://registry.npmjs.org/http-proxy-agent/-/http-proxy-agent-5.0.0.tgz", "version": "5.0.0"}, "node_modules/http2-wrapper": {"dependencies": {"quick-lru": "^5.1.1", "resolve-alpn": "^1.0.0"}, "dev": true, "engines": {"node": ">=10.19.0"}, "integrity": "sha512-V+23sDMr12Wnz7iTcDeJr3O6AIxlnvT/bmaAAAP/Xda35C90p9599p0F1eHR/N1KILWSoWVAiOMFjBBXaXSMxg==", "license": "MIT", "resolved": "https://registry.npmjs.org/http2-wrapper/-/http2-wrapper-1.0.3.tgz", "version": "1.0.3"}, "node_modules/https-proxy-agent": {"dependencies": {"agent-base": "6", "debug": "4"}, "dev": true, "engines": {"node": ">= 6"}, "integrity": "sha512-dFcAjpTQFgoLMzC2VwU+C/CbS7uRL0lWmxDITmqm7C+7F0Odmj6s9l6alZc6AELXhrnggM2CeWSXHGOdX2YtwA==", "license": "MIT", "resolved": "https://registry.npmjs.org/https-proxy-agent/-/https-proxy-agent-5.0.1.tgz", "version": "5.0.1"}, "node_modules/human-signals": {"dev": true, "engines": {"node": ">=10.17.0"}, "integrity": "sha512-B4FFZ6q/T2jhhksgkbEW3HBvWIfDW85snkQgawt07S7J5QXTk6BkNV+0yAeZrM5QpMAdYlocGoljn0sJ/WQkFw==", "license": "Apache-2.0", "resolved": "https://registry.npmjs.org/human-signals/-/human-signals-2.1.0.tgz", "version": "2.1.0"}, "node_modules/iconv-corefoundation": {"dependencies": {"cli-truncate": "^2.1.0", "node-addon-api": "^1.6.3"}, "dev": true, "engines": {"node": "^8.11.2 || >=10"}, "integrity": "sha512-T10qvkw0zz4wnm560lOEg0PovVqUXuOFhhHAkixw8/sycy7TJt7v/RrkEKEQnAw2viPSJu6iAkErxnzR0g8PpQ==", "license": "MIT", "optional": true, "os": ["darwin"], "resolved": "https://registry.npmjs.org/iconv-corefoundation/-/iconv-corefoundation-1.1.7.tgz", "version": "1.1.7"}, "node_modules/iconv-lite": {"dependencies": {"safer-buffer": ">= 2.1.2 < 3.0.0"}, "dev": true, "engines": {"node": ">=0.10.0"}, "integrity": "sha512-4fCk79wshMdzMp2rH06qWrJE4iolqLhCUH+OiuIgU++RB0+94NlDL81atO7GX55uUKueo0txHNtvEyI6D7WdMw==", "license": "MIT", "resolved": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.6.3.tgz", "version": "0.6.3"}, "node_modules/ieee754": {"funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "integrity": "sha512-dcyqhDvX1C46lXZcVqCpK+FtMRQVdIMN6/Df5js2zouUsqG7I6sFxitIC+7KYK29KdXOLHdu9zL4sFnoVQnqaA==", "license": "BSD-3-<PERSON><PERSON>", "resolved": "https://registry.npmjs.org/ieee754/-/ieee754-1.2.1.tgz", "version": "1.2.1"}, "node_modules/import-local": {"bin": {"import-local-fixture": "fixtures/cli.js"}, "dependencies": {"pkg-dir": "^4.2.0", "resolve-cwd": "^3.0.0"}, "dev": true, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}, "integrity": "sha512-2SPlun1JUPWoM6t3F0dw0FkCF/jWY8kttcY4f599GLTSjh2OCuuhdTkJQsEcZzBqbXZGKMK2OqW1oZsjtf/gQA==", "license": "MIT", "resolved": "https://registry.npmjs.org/import-local/-/import-local-3.2.0.tgz", "version": "3.2.0"}, "node_modules/imurmurhash": {"dev": true, "engines": {"node": ">=0.8.19"}, "integrity": "sha512-JmXMZ6wuvDmLiHEml9ykzqO6lwFbof0GG4IkcGaENdCRDDmMVnny7s5HsIgHCbaq0w2MyPhDqkhTUgS2LU2PHA==", "license": "MIT", "resolved": "https://registry.npmjs.org/imurmurhash/-/imurmurhash-0.1.4.tgz", "version": "0.1.4"}, "node_modules/inflight": {"dependencies": {"once": "^1.3.0", "wrappy": "1"}, "deprecated": "This module is not supported, and leaks memory. Do not use it. Check out lru-cache if you want a good and tested way to coalesce async requests by a key value, which is much more comprehensive and powerful.", "integrity": "sha512-k92I/b08q4wvFscXCLvqfsHCrjrF7yiXsQuIVvVE7N82W3+aqpzuUdBbfhWcy/FZR3/4IgflMgKLOsvPDrGCJA==", "license": "ISC", "resolved": "https://registry.npmjs.org/inflight/-/inflight-1.0.6.tgz", "version": "1.0.6"}, "node_modules/inherits": {"integrity": "sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==", "license": "ISC", "resolved": "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz", "version": "2.0.4"}, "node_modules/inquirer": {"dependencies": {"ansi-escapes": "^4.2.1", "chalk": "^4.1.1", "cli-cursor": "^3.1.0", "cli-width": "^3.0.0", "external-editor": "^3.0.3", "figures": "^3.0.0", "lodash": "^4.17.21", "mute-stream": "0.0.8", "ora": "^5.4.1", "run-async": "^2.4.0", "rxjs": "^7.5.5", "string-width": "^4.1.0", "strip-ansi": "^6.0.0", "through": "^2.3.6", "wrap-ansi": "^6.0.1"}, "engines": {"node": ">=12.0.0"}, "integrity": "sha512-M1WuAmb7pn9zdFRtQYk26ZBoY043Sse0wVDdk4Bppr+JOXyQYybdtvK+l9wUibhtjdjvtoiNy8tk+EgsYIUqKg==", "license": "MIT", "resolved": "https://registry.npmjs.org/inquirer/-/inquirer-8.2.6.tgz", "version": "8.2.6"}, "node_modules/inquirer/node_modules/wrap-ansi": {"dependencies": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}, "engines": {"node": ">=8"}, "integrity": "sha512-r6lPcBGxZXlIcymEu7InxDMhdW0KDxpLgoFLcguasxCaJ/SOIZwINatK9KY/tf+ZrlywOKU0UDj3ATXUBfxJXA==", "license": "MIT", "resolved": "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-6.2.0.tgz", "version": "6.2.0"}, "node_modules/is-arrayish": {"dev": true, "integrity": "sha512-zz06S8t0ozoDXMG+ube26zeCTNXcKIPJZJi8hBrF4idCLms4CG9QtK7qBl1boi5ODzFpjswb5JPmHCbMpjaYzg==", "license": "MIT", "resolved": "https://registry.npmjs.org/is-arrayish/-/is-arrayish-0.2.1.tgz", "version": "0.2.1"}, "node_modules/is-ci": {"bin": {"is-ci": "bin.js"}, "dependencies": {"ci-info": "^3.2.0"}, "dev": true, "integrity": "sha512-ZYvCgrefwqoQ6yTyYUbQu64HsITZ3NfKX1lzaEYdkTDcfKzzCI/wthRRYKkdjHKFVgNiXKAKm65Zo1pk2as/QQ==", "license": "MIT", "resolved": "https://registry.npmjs.org/is-ci/-/is-ci-3.0.1.tgz", "version": "3.0.1"}, "node_modules/is-core-module": {"dependencies": {"hasown": "^2.0.2"}, "dev": true, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "integrity": "sha512-UfoeMA6fIJ8wTYFEUjelnaGI67v6+N7qXJEvQuIGa99l4xsCruSYOVSQ0uPANn4dAzm8lkYPaKLrrijLq7x23w==", "license": "MIT", "resolved": "https://registry.npmjs.org/is-core-module/-/is-core-module-2.16.1.tgz", "version": "2.16.1"}, "node_modules/is-fullwidth-code-point": {"engines": {"node": ">=8"}, "integrity": "sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==", "license": "MIT", "resolved": "https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz", "version": "3.0.0"}, "node_modules/is-generator-fn": {"dev": true, "engines": {"node": ">=6"}, "integrity": "sha512-cTIB4yPYL/Grw0EaSzASzg6bBy9gqCofvWN8okThAYIxKJZC+udlRAmGbM0XLeniEJSs8uEgHPGuHSe1XsOLSQ==", "license": "MIT", "resolved": "https://registry.npmjs.org/is-generator-fn/-/is-generator-fn-2.1.0.tgz", "version": "2.1.0"}, "node_modules/is-interactive": {"engines": {"node": ">=8"}, "integrity": "sha512-2HvIEKRoqS62guEC+qBjpvRubdX910WCMuJTZ+I9yvqKU2/12eSL549HMwtabb4oupdj2sMP50k+XJfB/8JE6w==", "license": "MIT", "resolved": "https://registry.npmjs.org/is-interactive/-/is-interactive-1.0.0.tgz", "version": "1.0.0"}, "node_modules/is-number": {"dev": true, "engines": {"node": ">=0.12.0"}, "integrity": "sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==", "license": "MIT", "resolved": "https://registry.npmjs.org/is-number/-/is-number-7.0.0.tgz", "version": "7.0.0"}, "node_modules/is-stream": {"dev": true, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}, "integrity": "sha512-hFoiJiTl63nn+kstHGBtewWSKnQLpyb155KHheA1l39uvtO9nWIop1p3udqPcUd/xbF1VLMO4n7OI6p7RbngDg==", "license": "MIT", "resolved": "https://registry.npmjs.org/is-stream/-/is-stream-2.0.1.tgz", "version": "2.0.1"}, "node_modules/is-unicode-supported": {"engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}, "integrity": "sha512-knxG2q4UC3u8stRGyAVJCOdxFmv5DZiRcdlIaAQXAbSfJya+OhopNotLQrstBhququ4ZpuKbDc/8S6mgXgPFPw==", "license": "MIT", "resolved": "https://registry.npmjs.org/is-unicode-supported/-/is-unicode-supported-0.1.0.tgz", "version": "0.1.0"}, "node_modules/isarray": {"integrity": "sha512-VLghIWNM6ELQzo7zwmcg0NmTVyWKYjvIeM83yjp0wRDTmUnrM678fQbcKBo6n2CJEF0szoG//ytg+TKla89ALQ==", "license": "MIT", "resolved": "https://registry.npmjs.org/isarray/-/isarray-1.0.0.tgz", "version": "1.0.0"}, "node_modules/isbinaryfile": {"dev": true, "engines": {"node": ">= 18.0.0"}, "funding": {"url": "https://github.com/sponsors/gjtorikian/"}, "integrity": "sha512-YKBKVkKhty7s8rxddb40oOkuP0NbaeXrQvLin6QMHL7Ypiy2RW9LwOVrVgZRyOrhQlayMd9t+D8yDy8MKFTSDQ==", "license": "MIT", "resolved": "https://registry.npmjs.org/isbinaryfile/-/isbinaryfile-5.0.4.tgz", "version": "5.0.4"}, "node_modules/isexe": {"dev": true, "integrity": "sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw==", "license": "ISC", "resolved": "https://registry.npmjs.org/isexe/-/isexe-2.0.0.tgz", "version": "2.0.0"}, "node_modules/istanbul-lib-coverage": {"dev": true, "engines": {"node": ">=8"}, "integrity": "sha512-O8dpsF+r0WV/8MNRKfnmrtCWhuKjxrq2w+jpzBL5UZKTi2LeVWnWOmWRxFlesJONmc+wLAGvKQZEOanko0LFTg==", "license": "BSD-3-<PERSON><PERSON>", "resolved": "https://registry.npmjs.org/istanbul-lib-coverage/-/istanbul-lib-coverage-3.2.2.tgz", "version": "3.2.2"}, "node_modules/istanbul-lib-instrument": {"dependencies": {"@babel/core": "^7.23.9", "@babel/parser": "^7.23.9", "@istanbuljs/schema": "^0.1.3", "istanbul-lib-coverage": "^3.2.0", "semver": "^7.5.4"}, "dev": true, "engines": {"node": ">=10"}, "integrity": "sha512-Vtgk7L/R2JHyyGW07spoFlB8/lpjiOLTjMdms6AFMraYt3BaJauod/NGrfnVG/y4Ix1JEuMRPDPEj2ua+zz1/Q==", "license": "BSD-3-<PERSON><PERSON>", "resolved": "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-6.0.3.tgz", "version": "6.0.3"}, "node_modules/istanbul-lib-report": {"dependencies": {"istanbul-lib-coverage": "^3.0.0", "make-dir": "^4.0.0", "supports-color": "^7.1.0"}, "dev": true, "engines": {"node": ">=10"}, "integrity": "sha512-GCfE1mtsHGOELCU8e/Z7YWzpmybrx/+dSTfLrvY8qRmaY6zXTKWn6WQIjaAFw069icm6GVMNkgu0NzI4iPZUNw==", "license": "BSD-3-<PERSON><PERSON>", "resolved": "https://registry.npmjs.org/istanbul-lib-report/-/istanbul-lib-report-3.0.1.tgz", "version": "3.0.1"}, "node_modules/istanbul-lib-source-maps": {"dependencies": {"debug": "^4.1.1", "istanbul-lib-coverage": "^3.0.0", "source-map": "^0.6.1"}, "dev": true, "engines": {"node": ">=10"}, "integrity": "sha512-n3s8EwkdFIJCG3BPKBYvskgXGoy88ARzvegkitk60NxRdwltLOTaH7CUiMRXvwYorl0Q712iEjcWB+fK/MrWVw==", "license": "BSD-3-<PERSON><PERSON>", "resolved": "https://registry.npmjs.org/istanbul-lib-source-maps/-/istanbul-lib-source-maps-4.0.1.tgz", "version": "4.0.1"}, "node_modules/istanbul-reports": {"dependencies": {"html-escaper": "^2.0.0", "istanbul-lib-report": "^3.0.0"}, "dev": true, "engines": {"node": ">=8"}, "integrity": "sha512-BewmUXImeuRk2YY0PVbxgKAysvhRPUQE0h5QRM++nVWyubKGV0l8qQ5op8+B2DOmwSe63Jivj0BjkPQVf8fP5g==", "license": "BSD-3-<PERSON><PERSON>", "resolved": "https://registry.npmjs.org/istanbul-reports/-/istanbul-reports-3.1.7.tgz", "version": "3.1.7"}, "node_modules/jackspeak": {"dependencies": {"@isaacs/cliui": "^8.0.2"}, "dev": true, "funding": {"url": "https://github.com/sponsors/isaacs"}, "integrity": "sha512-OGlZQpz2yfahA/Rd1Y8Cd9SIEsqvXkLVoSw/cgwhnhFMDbsQFeZYoJJ7bIZBS9BcamUW96asq/npPWugM+RQBw==", "license": "BlueOak-1.0.0", "optionalDependencies": {"@pkgjs/parseargs": "^0.11.0"}, "resolved": "https://registry.npmjs.org/jackspeak/-/jackspeak-3.4.3.tgz", "version": "3.4.3"}, "node_modules/jake": {"bin": {"jake": "bin/cli.js"}, "dependencies": {"async": "^3.2.3", "chalk": "^4.0.2", "filelist": "^1.0.4", "minimatch": "^3.1.2"}, "dev": true, "engines": {"node": ">=10"}, "integrity": "sha512-2P4SQ0HrLQ+fw6llpLnOaGAvN2Zu6778SJMrCUwns4fOoG9ayrTiZk3VV8sCPkVZF8ab0zksVpS8FDY5pRCNBA==", "license": "Apache-2.0", "resolved": "https://registry.npmjs.org/jake/-/jake-10.9.2.tgz", "version": "10.9.2"}, "node_modules/jake/node_modules/brace-expansion": {"dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}, "dev": true, "integrity": "sha512-9T9UjW3r0UW5c1Q7GTwllptXwhvYmEzFhzMfZ9H7FQWt+uZePjZPjBP/W1ZEyZ1twGWom5/56TF4lPcqjnDHcg==", "license": "MIT", "resolved": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.12.tgz", "version": "1.1.12"}, "node_modules/jake/node_modules/minimatch": {"dependencies": {"brace-expansion": "^1.1.7"}, "dev": true, "engines": {"node": "*"}, "integrity": "sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==", "license": "ISC", "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-3.1.2.tgz", "version": "3.1.2"}, "node_modules/jest": {"bin": {"jest": "bin/jest.js"}, "dependencies": {"@jest/core": "^29.7.0", "@jest/types": "^29.6.3", "import-local": "^3.0.2", "jest-cli": "^29.7.0"}, "dev": true, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "integrity": "sha512-NIy3oAFp9shda19hy4HK0HRTWKtPJmGdnvywu01nOqNC2vZg+Z+fvJDxpMQA88eb2I9EcafcdjYgsDthnYTvGw==", "license": "MIT", "peerDependencies": {"node-notifier": "^8.0.1 || ^9.0.0 || ^10.0.0"}, "peerDependenciesMeta": {"node-notifier": {"optional": true}}, "resolved": "https://registry.npmjs.org/jest/-/jest-29.7.0.tgz", "version": "29.7.0"}, "node_modules/jest-changed-files": {"dependencies": {"execa": "^5.0.0", "jest-util": "^29.7.0", "p-limit": "^3.1.0"}, "dev": true, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "integrity": "sha512-fEArFiwf1BpQ+4bXSprcDc3/x4HSzL4al2tozwVpDFpsxALjLYdyiIK4e5Vz66GQJIbXJ82+35PtysofptNX2w==", "license": "MIT", "resolved": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-29.7.0.tgz", "version": "29.7.0"}, "node_modules/jest-circus": {"dependencies": {"@jest/environment": "^29.7.0", "@jest/expect": "^29.7.0", "@jest/test-result": "^29.7.0", "@jest/types": "^29.6.3", "@types/node": "*", "chalk": "^4.0.0", "co": "^4.6.0", "dedent": "^1.0.0", "is-generator-fn": "^2.0.0", "jest-each": "^29.7.0", "jest-matcher-utils": "^29.7.0", "jest-message-util": "^29.7.0", "jest-runtime": "^29.7.0", "jest-snapshot": "^29.7.0", "jest-util": "^29.7.0", "p-limit": "^3.1.0", "pretty-format": "^29.7.0", "pure-rand": "^6.0.0", "slash": "^3.0.0", "stack-utils": "^2.0.3"}, "dev": true, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "integrity": "sha512-3E1nCMgipcTkCocFwM90XXQab9bS+GMsjdpmPrlelaxwD93Ad8iVEjX/vvHPdLPnFf+L40u+5+iutRdA1N9myw==", "license": "MIT", "resolved": "https://registry.npmjs.org/jest-circus/-/jest-circus-29.7.0.tgz", "version": "29.7.0"}, "node_modules/jest-cli": {"bin": {"jest": "bin/jest.js"}, "dependencies": {"@jest/core": "^29.7.0", "@jest/test-result": "^29.7.0", "@jest/types": "^29.6.3", "chalk": "^4.0.0", "create-jest": "^29.7.0", "exit": "^0.1.2", "import-local": "^3.0.2", "jest-config": "^29.7.0", "jest-util": "^29.7.0", "jest-validate": "^29.7.0", "yargs": "^17.3.1"}, "dev": true, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "integrity": "sha512-OVVobw2IubN/GSYsxETi+gOe7Ka59EFMR/twOU3Jb2GnKKeMGJB5SGUUrEz3SFVmJASUdZUzy83sLNNQ2gZslg==", "license": "MIT", "peerDependencies": {"node-notifier": "^8.0.1 || ^9.0.0 || ^10.0.0"}, "peerDependenciesMeta": {"node-notifier": {"optional": true}}, "resolved": "https://registry.npmjs.org/jest-cli/-/jest-cli-29.7.0.tgz", "version": "29.7.0"}, "node_modules/jest-config": {"dependencies": {"@babel/core": "^7.11.6", "@jest/test-sequencer": "^29.7.0", "@jest/types": "^29.6.3", "babel-jest": "^29.7.0", "chalk": "^4.0.0", "ci-info": "^3.2.0", "deepmerge": "^4.2.2", "glob": "^7.1.3", "graceful-fs": "^4.2.9", "jest-circus": "^29.7.0", "jest-environment-node": "^29.7.0", "jest-get-type": "^29.6.3", "jest-regex-util": "^29.6.3", "jest-resolve": "^29.7.0", "jest-runner": "^29.7.0", "jest-util": "^29.7.0", "jest-validate": "^29.7.0", "micromatch": "^4.0.4", "parse-json": "^5.2.0", "pretty-format": "^29.7.0", "slash": "^3.0.0", "strip-json-comments": "^3.1.1"}, "dev": true, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "integrity": "sha512-uXbpfeQ7R6TZBqI3/TxCU4q4ttk3u0PJeC+E0zbfSoSjq6bJ7buBPxzQPL0ifrkY4DNu4JUdk0ImlBUYi840eQ==", "license": "MIT", "peerDependencies": {"@types/node": "*", "ts-node": ">=9.0.0"}, "peerDependenciesMeta": {"@types/node": {"optional": true}, "ts-node": {"optional": true}}, "resolved": "https://registry.npmjs.org/jest-config/-/jest-config-29.7.0.tgz", "version": "29.7.0"}, "node_modules/jest-diff": {"dependencies": {"chalk": "^4.0.0", "diff-sequences": "^29.6.3", "jest-get-type": "^29.6.3", "pretty-format": "^29.7.0"}, "dev": true, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "integrity": "sha512-LMIgiIrhigmPrs03JHpxUh2yISK3vLFPkAodPeo0+BuF7wA2FoQbkEg1u8gBYBThncu7e1oEDUfIXVuTqLRUjw==", "license": "MIT", "resolved": "https://registry.npmjs.org/jest-diff/-/jest-diff-29.7.0.tgz", "version": "29.7.0"}, "node_modules/jest-docblock": {"dependencies": {"detect-newline": "^3.0.0"}, "dev": true, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "integrity": "sha512-q617Auw3A612guyaFgsbFeYpNP5t2aoUNLwBUbc/0kD1R4t9ixDbyFTHd1nok4epoVFpr7PmeWHrhvuV3XaJ4g==", "license": "MIT", "resolved": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-29.7.0.tgz", "version": "29.7.0"}, "node_modules/jest-each": {"dependencies": {"@jest/types": "^29.6.3", "chalk": "^4.0.0", "jest-get-type": "^29.6.3", "jest-util": "^29.7.0", "pretty-format": "^29.7.0"}, "dev": true, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "integrity": "sha512-gns+Er14+ZrEoC5fhOfYCY1LOHHr0TI+rQUHZS8Ttw2l7gl+80eHc/gFf2Ktkw0+SIACDTeWvpFcv3B04VembQ==", "license": "MIT", "resolved": "https://registry.npmjs.org/jest-each/-/jest-each-29.7.0.tgz", "version": "29.7.0"}, "node_modules/jest-environment-node": {"dependencies": {"@jest/environment": "^29.7.0", "@jest/fake-timers": "^29.7.0", "@jest/types": "^29.6.3", "@types/node": "*", "jest-mock": "^29.7.0", "jest-util": "^29.7.0"}, "dev": true, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "integrity": "sha512-DOSwCRqXirTOyheM+4d5YZOrWcdu0LNZ87ewUoywbcb2XR4wKgqiG8vNeYwhjFMbEkfju7wx2GYH0P2gevGvFw==", "license": "MIT", "resolved": "https://registry.npmjs.org/jest-environment-node/-/jest-environment-node-29.7.0.tgz", "version": "29.7.0"}, "node_modules/jest-get-type": {"dev": true, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "integrity": "sha512-zrteXnqYxfQh7l5FHyL38jL39di8H8rHoecLH3JNxH3BwOrBsNeabdap5e0I23lD4HHI8W5VFBZqG4Eaq5LNcw==", "license": "MIT", "resolved": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-29.6.3.tgz", "version": "29.6.3"}, "node_modules/jest-haste-map": {"dependencies": {"@jest/types": "^29.6.3", "@types/graceful-fs": "^4.1.3", "@types/node": "*", "anymatch": "^3.0.3", "fb-watchman": "^2.0.0", "graceful-fs": "^4.2.9", "jest-regex-util": "^29.6.3", "jest-util": "^29.7.0", "jest-worker": "^29.7.0", "micromatch": "^4.0.4", "walker": "^1.0.8"}, "dev": true, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "integrity": "sha512-fP8u2pyfqx0K1rGn1R9pyE0/KTn+G7PxktWidOBTqFPLYX0b9ksaMFkhK5vrS3DVun09pckLdlx90QthlW7AmA==", "license": "MIT", "optionalDependencies": {"fsevents": "^2.3.2"}, "resolved": "https://registry.npmjs.org/jest-haste-map/-/jest-haste-map-29.7.0.tgz", "version": "29.7.0"}, "node_modules/jest-leak-detector": {"dependencies": {"jest-get-type": "^29.6.3", "pretty-format": "^29.7.0"}, "dev": true, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "integrity": "sha512-kYA8IJcSYtST2BY9I+SMC32nDpBT3J2NvWJx8+JCuCdl/CR1I4EKUJROiP8XtCcxqgTTBGJNdbB1A8XRKbTetw==", "license": "MIT", "resolved": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-29.7.0.tgz", "version": "29.7.0"}, "node_modules/jest-matcher-utils": {"dependencies": {"chalk": "^4.0.0", "jest-diff": "^29.7.0", "jest-get-type": "^29.6.3", "pretty-format": "^29.7.0"}, "dev": true, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "integrity": "sha512-sBkD+Xi9DtcChsI3L3u0+N0opgPYnCRPtGcQYrgXmR+hmt/fYfWAL0xRXYU8eWOdfuLgBe0YCW3AFtnRLagq/g==", "license": "MIT", "resolved": "https://registry.npmjs.org/jest-matcher-utils/-/jest-matcher-utils-29.7.0.tgz", "version": "29.7.0"}, "node_modules/jest-message-util": {"dependencies": {"@babel/code-frame": "^7.12.13", "@jest/types": "^29.6.3", "@types/stack-utils": "^2.0.0", "chalk": "^4.0.0", "graceful-fs": "^4.2.9", "micromatch": "^4.0.4", "pretty-format": "^29.7.0", "slash": "^3.0.0", "stack-utils": "^2.0.3"}, "dev": true, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "integrity": "sha512-GBEV4GRADeP+qtB2+6u61stea8mGcOT4mCtrYISZwfu9/ISHFJ/5zOMXYbpBE9RsS5+Gb63DW4FgmnKJ79Kf6w==", "license": "MIT", "resolved": "https://registry.npmjs.org/jest-message-util/-/jest-message-util-29.7.0.tgz", "version": "29.7.0"}, "node_modules/jest-mock": {"dependencies": {"@jest/types": "^29.6.3", "@types/node": "*", "jest-util": "^29.7.0"}, "dev": true, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "integrity": "sha512-ITOMZn+UkYS4ZFh83xYAOzWStloNzJFO2s8DWrE4lhtGD+AorgnbkiKERe4wQVBydIGPx059g6riW5Btp6Llnw==", "license": "MIT", "resolved": "https://registry.npmjs.org/jest-mock/-/jest-mock-29.7.0.tgz", "version": "29.7.0"}, "node_modules/jest-pnp-resolver": {"dev": true, "engines": {"node": ">=6"}, "integrity": "sha512-+3NpwQEnRoIBtx4fyhblQDPgJI0H1IEIkX7ShLUjPGA7TtUTvI1oiKi3SR4oBR0hQhQR80l4WAe5RrXBwWMA8w==", "license": "MIT", "peerDependencies": {"jest-resolve": "*"}, "peerDependenciesMeta": {"jest-resolve": {"optional": true}}, "resolved": "https://registry.npmjs.org/jest-pnp-resolver/-/jest-pnp-resolver-1.2.3.tgz", "version": "1.2.3"}, "node_modules/jest-regex-util": {"dev": true, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "integrity": "sha512-KJJBsRCyyLNWCNBOvZyRDnAIfUiRJ8v+hOBQYGn8gDyF3UegwiP4gwRR3/SDa42g1YbVycTidUF3rKjyLFDWbg==", "license": "MIT", "resolved": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-29.6.3.tgz", "version": "29.6.3"}, "node_modules/jest-resolve": {"dependencies": {"chalk": "^4.0.0", "graceful-fs": "^4.2.9", "jest-haste-map": "^29.7.0", "jest-pnp-resolver": "^1.2.2", "jest-util": "^29.7.0", "jest-validate": "^29.7.0", "resolve": "^1.20.0", "resolve.exports": "^2.0.0", "slash": "^3.0.0"}, "dev": true, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "integrity": "sha512-IOVhZSrg+UvVAshDSDtHyFCCBUl/Q3AAJv8iZ6ZjnZ74xzvwuzLXid9IIIPgTnY62SJjfuupMKZsZQRsCvxEgA==", "license": "MIT", "resolved": "https://registry.npmjs.org/jest-resolve/-/jest-resolve-29.7.0.tgz", "version": "29.7.0"}, "node_modules/jest-resolve-dependencies": {"dependencies": {"jest-regex-util": "^29.6.3", "jest-snapshot": "^29.7.0"}, "dev": true, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "integrity": "sha512-un0zD/6qxJ+S0et7WxeI3H5XSe9lTBBR7bOHCHXkKR6luG5mwDDlIzVQ0V5cZCuoTgEdcdwzTghYkTWfubi+nA==", "license": "MIT", "resolved": "https://registry.npmjs.org/jest-resolve-dependencies/-/jest-resolve-dependencies-29.7.0.tgz", "version": "29.7.0"}, "node_modules/jest-runner": {"dependencies": {"@jest/console": "^29.7.0", "@jest/environment": "^29.7.0", "@jest/test-result": "^29.7.0", "@jest/transform": "^29.7.0", "@jest/types": "^29.6.3", "@types/node": "*", "chalk": "^4.0.0", "emittery": "^0.13.1", "graceful-fs": "^4.2.9", "jest-docblock": "^29.7.0", "jest-environment-node": "^29.7.0", "jest-haste-map": "^29.7.0", "jest-leak-detector": "^29.7.0", "jest-message-util": "^29.7.0", "jest-resolve": "^29.7.0", "jest-runtime": "^29.7.0", "jest-util": "^29.7.0", "jest-watcher": "^29.7.0", "jest-worker": "^29.7.0", "p-limit": "^3.1.0", "source-map-support": "0.5.13"}, "dev": true, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "integrity": "sha512-fsc4N6cPCAahybGBfTRcq5wFR6fpLznMg47sY5aDpsoejOcVYFb07AHuSnR0liMcPTgBsA3ZJL6kFOjPdoNipQ==", "license": "MIT", "resolved": "https://registry.npmjs.org/jest-runner/-/jest-runner-29.7.0.tgz", "version": "29.7.0"}, "node_modules/jest-runner/node_modules/source-map-support": {"dependencies": {"buffer-from": "^1.0.0", "source-map": "^0.6.0"}, "dev": true, "integrity": "sha512-SHSKFHadjVA5oR4PPqhtAVdcBWwRYVd6g6cAXnIbRiIwc2EhPrTuKUBdSLvlEKyIP3GCf89fltvcZiP9MMFA1w==", "license": "MIT", "resolved": "https://registry.npmjs.org/source-map-support/-/source-map-support-0.5.13.tgz", "version": "0.5.13"}, "node_modules/jest-runtime": {"dependencies": {"@jest/environment": "^29.7.0", "@jest/fake-timers": "^29.7.0", "@jest/globals": "^29.7.0", "@jest/source-map": "^29.6.3", "@jest/test-result": "^29.7.0", "@jest/transform": "^29.7.0", "@jest/types": "^29.6.3", "@types/node": "*", "chalk": "^4.0.0", "cjs-module-lexer": "^1.0.0", "collect-v8-coverage": "^1.0.0", "glob": "^7.1.3", "graceful-fs": "^4.2.9", "jest-haste-map": "^29.7.0", "jest-message-util": "^29.7.0", "jest-mock": "^29.7.0", "jest-regex-util": "^29.6.3", "jest-resolve": "^29.7.0", "jest-snapshot": "^29.7.0", "jest-util": "^29.7.0", "slash": "^3.0.0", "strip-bom": "^4.0.0"}, "dev": true, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "integrity": "sha512-gUnLjgwdGqW7B4LvOIkbKs9WGbn+QLqRQQ9juC6HndeDiezIwhDP+mhMwHWCEcfQ5RUXa6OPnFF8BJh5xegwwQ==", "license": "MIT", "resolved": "https://registry.npmjs.org/jest-runtime/-/jest-runtime-29.7.0.tgz", "version": "29.7.0"}, "node_modules/jest-snapshot": {"dependencies": {"@babel/core": "^7.11.6", "@babel/generator": "^7.7.2", "@babel/plugin-syntax-jsx": "^7.7.2", "@babel/plugin-syntax-typescript": "^7.7.2", "@babel/types": "^7.3.3", "@jest/expect-utils": "^29.7.0", "@jest/transform": "^29.7.0", "@jest/types": "^29.6.3", "babel-preset-current-node-syntax": "^1.0.0", "chalk": "^4.0.0", "expect": "^29.7.0", "graceful-fs": "^4.2.9", "jest-diff": "^29.7.0", "jest-get-type": "^29.6.3", "jest-matcher-utils": "^29.7.0", "jest-message-util": "^29.7.0", "jest-util": "^29.7.0", "natural-compare": "^1.4.0", "pretty-format": "^29.7.0", "semver": "^7.5.3"}, "dev": true, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "integrity": "sha512-Rm0BMWtxBcioHr1/OX5YCP8Uov4riHvKPknOGs804Zg9JGZgmIBkbtlxJC/7Z4msKYVbIJtfU+tKb8xlYNfdkw==", "license": "MIT", "resolved": "https://registry.npmjs.org/jest-snapshot/-/jest-snapshot-29.7.0.tgz", "version": "29.7.0"}, "node_modules/jest-util": {"dependencies": {"@jest/types": "^29.6.3", "@types/node": "*", "chalk": "^4.0.0", "ci-info": "^3.2.0", "graceful-fs": "^4.2.9", "picomatch": "^2.2.3"}, "dev": true, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "integrity": "sha512-z6<PERSON>bKajIpqGKU56y5KBUgy1dt1ihhQJgWzUlZHArA/+X2ad7Cb5iF+AK1EWVL/Bo7Rz9uurpqw6SiBCefUbCGA==", "license": "MIT", "resolved": "https://registry.npmjs.org/jest-util/-/jest-util-29.7.0.tgz", "version": "29.7.0"}, "node_modules/jest-validate": {"dependencies": {"@jest/types": "^29.6.3", "camelcase": "^6.2.0", "chalk": "^4.0.0", "jest-get-type": "^29.6.3", "leven": "^3.1.0", "pretty-format": "^29.7.0"}, "dev": true, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "integrity": "sha512-ZB7wHqaRGVw/9hST/OuFUReG7M8vKeq0/J2egIGLdvjHCmYqGARhzXmtgi+gVeZ5uXFF219aOc3Ls2yLg27tkw==", "license": "MIT", "resolved": "https://registry.npmjs.org/jest-validate/-/jest-validate-29.7.0.tgz", "version": "29.7.0"}, "node_modules/jest-validate/node_modules/camelcase": {"dev": true, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}, "integrity": "sha512-Gmy6FhYlCY7uOElZUSbxo2UCDH8owEk996gkbrpsgGtrJLM3J7jGxl9Ic7Qwwj4ivOE5AWZWRMecDdF7hqGjFA==", "license": "MIT", "resolved": "https://registry.npmjs.org/camelcase/-/camelcase-6.3.0.tgz", "version": "6.3.0"}, "node_modules/jest-watcher": {"dependencies": {"@jest/test-result": "^29.7.0", "@jest/types": "^29.6.3", "@types/node": "*", "ansi-escapes": "^4.2.1", "chalk": "^4.0.0", "emittery": "^0.13.1", "jest-util": "^29.7.0", "string-length": "^4.0.1"}, "dev": true, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "integrity": "sha512-49Fg7WXkU3Vl2h6LbLtMQ/HyB6rXSIX7SqvBLQmssRBGN9I0PNvPmAmCWSOY6SOvrjhI/F7/bGAv9RtnsPA03g==", "license": "MIT", "resolved": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-29.7.0.tgz", "version": "29.7.0"}, "node_modules/jest-worker": {"dependencies": {"@types/node": "*", "jest-util": "^29.7.0", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "dev": true, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "integrity": "sha512-eIz2msL/EzL9UFTFFx7jBTkeZfku0yUAyZZZmJ93H2TYEiroIx2PQjEXcwYtYl8zXCxb+PAmA2hLIt/6ZEkPHw==", "license": "MIT", "resolved": "https://registry.npmjs.org/jest-worker/-/jest-worker-29.7.0.tgz", "version": "29.7.0"}, "node_modules/jest-worker/node_modules/supports-color": {"dependencies": {"has-flag": "^4.0.0"}, "dev": true, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/supports-color?sponsor=1"}, "integrity": "sha512-MpUEN2OodtUzxvKQl72cUF7RQ5EiHsGvSsVG0ia9c5RbWGL2CI4C7EpPS8UTBIplnlzZiNuV56w+FuNxy3ty2Q==", "license": "MIT", "resolved": "https://registry.npmjs.org/supports-color/-/supports-color-8.1.1.tgz", "version": "8.1.1"}, "node_modules/js-tokens": {"dev": true, "integrity": "sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==", "license": "MIT", "resolved": "https://registry.npmjs.org/js-tokens/-/js-tokens-4.0.0.tgz", "version": "4.0.0"}, "node_modules/js-yaml": {"bin": {"js-yaml": "bin/js-yaml.js"}, "dependencies": {"argparse": "^2.0.1"}, "integrity": "sha512-wpxZs9NoxZaJESJGIZTyDEaYpl0FKSA+FB9aJiyemKhMwkxQg63h4T1KJgUGHpTqPDNRcmmYLugrRjJlBtWvRA==", "license": "MIT", "resolved": "https://registry.npmjs.org/js-yaml/-/js-yaml-4.1.0.tgz", "version": "4.1.0"}, "node_modules/jsesc": {"bin": {"jsesc": "bin/jsesc"}, "dev": true, "engines": {"node": ">=6"}, "integrity": "sha512-/sM3dO2FOzXjKQhJuo0Q173wf2KOo8t4I8vHy6lF9poUp7bKT0/NHE8fPX23PwfhnykfqnC2xRxOnVw5XuGIaA==", "license": "MIT", "resolved": "https://registry.npmjs.org/jsesc/-/jsesc-3.1.0.tgz", "version": "3.1.0"}, "node_modules/json-buffer": {"dev": true, "integrity": "sha512-4bV5BfR2mqfQTJm+V5tPPdf+ZpuhiIvTuAB5g8kcrXOZpTT/QwwVRWBywX1ozr6lEuPdbHxwaJlm9G6mI2sfSQ==", "license": "MIT", "resolved": "https://registry.npmjs.org/json-buffer/-/json-buffer-3.0.1.tgz", "version": "3.0.1"}, "node_modules/json-parse-even-better-errors": {"dev": true, "integrity": "sha512-xyFwyhro/JEof6Ghe2iz2NcXoj2sloNsWr/XsERDK/oiPCfaNhl5ONfp+jQdAZRQQ0IJWNzH9zIZF7li91kh2w==", "license": "MIT", "resolved": "https://registry.npmjs.org/json-parse-even-better-errors/-/json-parse-even-better-errors-2.3.1.tgz", "version": "2.3.1"}, "node_modules/json-schema-traverse": {"integrity": "sha512-NM8/P9n3XjXhIZn1lLhkFaACTOURQXjWhV4BA/RnOv8xvgqtqpAX9IO4mRQxSx1Rlo4tqzeqb0sOlruaOy3dug==", "license": "MIT", "resolved": "https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-1.0.0.tgz", "version": "1.0.0"}, "node_modules/json-stringify-safe": {"dev": true, "integrity": "sha512-ZClg6AaYvamvYEE82d3Iyd3vSSIjQ+odgjaTzRuO3s7toCdFKczob2i0zCh7JE8kWn17yvAWhUVxvqGwUalsRA==", "license": "ISC", "optional": true, "resolved": "https://registry.npmjs.org/json-stringify-safe/-/json-stringify-safe-5.0.1.tgz", "version": "5.0.1"}, "node_modules/json5": {"bin": {"json5": "lib/cli.js"}, "dev": true, "engines": {"node": ">=6"}, "integrity": "sha512-<PERSON>m<PERSON>e7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg==", "license": "MIT", "resolved": "https://registry.npmjs.org/json5/-/json5-2.2.3.tgz", "version": "2.2.3"}, "node_modules/jsonfile": {"dev": true, "integrity": "sha512-m6F1R3z8jjlf2imQHS2Qez5sjKWQzbuuhuJ/FKYFRZvPE3PuHcSMVZzfsLhGVOkfd20obL5SWEBew5ShlquNxg==", "license": "MIT", "optionalDependencies": {"graceful-fs": "^4.1.6"}, "resolved": "https://registry.npmjs.org/jsonfile/-/jsonfile-4.0.0.tgz", "version": "4.0.0"}, "node_modules/keyv": {"dependencies": {"json-buffer": "3.0.1"}, "dev": true, "integrity": "sha512-oxVHkHR/EJf2CNXnWxRLW6mg7JyCCUcG0DtEGmL2ctUo1PNTin1PUil+r/+4r5MpVgC/fn1kjsx7mjSujKqIpw==", "license": "MIT", "resolved": "https://registry.npmjs.org/keyv/-/keyv-4.5.4.tgz", "version": "4.5.4"}, "node_modules/kleur": {"dev": true, "engines": {"node": ">=6"}, "integrity": "sha512-eTIzlVOSUR+JxdDFepEYcBMtZ9Qqdef+rnzWdRZuMbOywu5tO2w2N7rqjoANZ5k9vywhL6Br1VRjUIgTQx4E8w==", "license": "MIT", "resolved": "https://registry.npmjs.org/kleur/-/kleur-3.0.3.tgz", "version": "3.0.3"}, "node_modules/lazy-val": {"integrity": "sha512-0/BnGCCfyUMkBpeDgWihanIAF9JmZhHBgUhEqzvf+adhNGLoP6TaiI5oF8oyb3I45P+PcnrqihSf01M0l0G5+Q==", "license": "MIT", "resolved": "https://registry.npmjs.org/lazy-val/-/lazy-val-1.0.5.tgz", "version": "1.0.5"}, "node_modules/lazystream": {"dependencies": {"readable-stream": "^2.0.5"}, "engines": {"node": ">= 0.6.3"}, "integrity": "sha512-b94GiNHQNy6JNTrt5w6zNyffMrNkXZb3KTkCZJb2V1xaEGCk093vkZ2jk3tpaeP33/OiXC+WvK9AxUebnf5nbw==", "license": "MIT", "resolved": "https://registry.npmjs.org/lazystream/-/lazystream-1.0.1.tgz", "version": "1.0.1"}, "node_modules/lazystream/node_modules/readable-stream": {"dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}, "integrity": "sha512-8p0AUk4XODgIewSi0l8Epjs+EVnWiK7NoDIEGU0HhE7+ZyY8D1IMY7odu5lRrFXGg71L15KG8QrPmum45RTtdA==", "license": "MIT", "resolved": "https://registry.npmjs.org/readable-stream/-/readable-stream-2.3.8.tgz", "version": "2.3.8"}, "node_modules/lazystream/node_modules/safe-buffer": {"integrity": "sha512-Gd2UZBJDkXlY7GbJxfsE8/nvKkUEU1G38c1siN6QP6a9PT9MmHB8GnpscSmMJSoF8LOIrt8ud/wPtojys4G6+g==", "license": "MIT", "resolved": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.1.2.tgz", "version": "5.1.2"}, "node_modules/lazystream/node_modules/string_decoder": {"dependencies": {"safe-buffer": "~5.1.0"}, "integrity": "sha512-n/ShnvDi6FHbbVfviro+WojiFzv+s8MPMHBczVePfUpDJLwoLT0ht1l4YwBCbi8pJAveEEdnkHyPyTP/mzRfwg==", "license": "MIT", "resolved": "https://registry.npmjs.org/string_decoder/-/string_decoder-1.1.1.tgz", "version": "1.1.1"}, "node_modules/leven": {"dev": true, "engines": {"node": ">=6"}, "integrity": "sha512-qsda+H8jTaUaN/x5vzW2rzc+8Rw4TAQ/4KjB46IwK5VH+IlVeeeje/EoZRpiXvIqjFgK84QffqPztGI3VBLG1A==", "license": "MIT", "resolved": "https://registry.npmjs.org/leven/-/leven-3.1.0.tgz", "version": "3.1.0"}, "node_modules/lines-and-columns": {"dev": true, "integrity": "sha512-7ylylesZQ/PV29jhEDl3Ufjo6ZX7gCqJr5F7PKrqc93v7fzSymt1BpwEU8nAUXs8qzzvqhbjhK5QZg6Mt/HkBg==", "license": "MIT", "resolved": "https://registry.npmjs.org/lines-and-columns/-/lines-and-columns-1.2.4.tgz", "version": "1.2.4"}, "node_modules/locate-path": {"dependencies": {"p-locate": "^4.1.0"}, "dev": true, "engines": {"node": ">=8"}, "integrity": "sha512-t7hw9pI+WvuwNJXwk5zVHpyhIqzg2qTlklJOf0mVxGSbe3Fp2VieZcduNYjaLDoy6p9uGpQEGWG87WpMKlNq8g==", "license": "MIT", "resolved": "https://registry.npmjs.org/locate-path/-/locate-path-5.0.0.tgz", "version": "5.0.0"}, "node_modules/lodash": {"integrity": "sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==", "license": "MIT", "resolved": "https://registry.npmjs.org/lodash/-/lodash-4.17.21.tgz", "version": "4.17.21"}, "node_modules/lodash.defaults": {"dev": true, "integrity": "sha512-qjxPLHd3r5DnsdGacqOMU6pb/avJzdh9tFX2ymgoZE27BmjXrNy/y4LoaiTeAb+O3gL8AfpJGtqfX/ae2leYYQ==", "license": "MIT", "peer": true, "resolved": "https://registry.npmjs.org/lodash.defaults/-/lodash.defaults-4.2.0.tgz", "version": "4.2.0"}, "node_modules/lodash.difference": {"dev": true, "integrity": "sha512-dS2j+W26TQ7taQBGN8Lbbq04ssV3emRw4NY58WErlTO29pIqS0HmoT5aJ9+TUQ1N3G+JOZSji4eugsWwGp9yPA==", "license": "MIT", "peer": true, "resolved": "https://registry.npmjs.org/lodash.difference/-/lodash.difference-4.5.0.tgz", "version": "4.5.0"}, "node_modules/lodash.escaperegexp": {"integrity": "sha512-TM9YBvyC84ZxE3rgfefxUWiQKLilstD6k7PTGt6wfbtXF8ixIJLOL3VYyV/z+ZiPLsVxAsKAFVwWlWeb2Y8Yyw==", "license": "MIT", "resolved": "https://registry.npmjs.org/lodash.escaperegexp/-/lodash.escaperegexp-4.1.2.tgz", "version": "4.1.2"}, "node_modules/lodash.flatten": {"dev": true, "integrity": "sha512-C5N2Z3DgnnKr0LOpv/hKCgKdb7ZZwafIrsesve6lmzvZIRZRGaZ/l6Q8+2W7NaT+ZwO3fFlSCzCzrDCFdJfZ4g==", "license": "MIT", "peer": true, "resolved": "https://registry.npmjs.org/lodash.flatten/-/lodash.flatten-4.4.0.tgz", "version": "4.4.0"}, "node_modules/lodash.isequal": {"deprecated": "This package is deprecated. Use require('node:util').isDeepStrictEqual instead.", "integrity": "sha512-pDo3lu8Jhfjqls6GkMgpahsF9kCyayhgykjyLMNFTKWrpVdAQtYyB4muAMWozBB4ig/dtWAmsMxLEI8wuz+DYQ==", "license": "MIT", "resolved": "https://registry.npmjs.org/lodash.isequal/-/lodash.isequal-4.5.0.tgz", "version": "4.5.0"}, "node_modules/lodash.isplainobject": {"dev": true, "integrity": "sha512-oSXzaWypCMHkPC3NvBEaPHf0KsA5mvPrOPgQWDsbg8n7orZ290M0BmC/jgRZ4vcJ6DTAhjrsSYgdsW/F+MFOBA==", "license": "MIT", "peer": true, "resolved": "https://registry.npmjs.org/lodash.isplainobject/-/lodash.isplainobject-4.0.6.tgz", "version": "4.0.6"}, "node_modules/lodash.union": {"dev": true, "integrity": "sha512-c4pB2CdGrGdjMKYLA+XiRDO7Y0PRQbm/Gzg8qMj+QH+pFVAoTp5sBpO0odL3FjoPCGjK96p6qsP+yQoiLoOBcw==", "license": "MIT", "peer": true, "resolved": "https://registry.npmjs.org/lodash.union/-/lodash.union-4.6.0.tgz", "version": "4.6.0"}, "node_modules/log-symbols": {"dependencies": {"chalk": "^4.1.0", "is-unicode-supported": "^0.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}, "integrity": "sha512-8XPvpAA8uyhfteu8pIvQxpJZ7SYYdpUivZpGy6sFsBuKRY/7rQGavedeB8aK+Zkyq6upMFVL/9AW6vOYzfRyLg==", "license": "MIT", "resolved": "https://registry.npmjs.org/log-symbols/-/log-symbols-4.1.0.tgz", "version": "4.1.0"}, "node_modules/lowercase-keys": {"dev": true, "engines": {"node": ">=8"}, "integrity": "sha512-tqNXrS78oMOE73NMxK4EMLQsQowWf8jKooH9g7xPavRT706R6bkQJ6DY2Te7QukaZsulxa30wQ7bk0pm4XiHmA==", "license": "MIT", "resolved": "https://registry.npmjs.org/lowercase-keys/-/lowercase-keys-2.0.0.tgz", "version": "2.0.0"}, "node_modules/lru-cache": {"dependencies": {"yallist": "^4.0.0"}, "dev": true, "engines": {"node": ">=10"}, "integrity": "sha512-Jo6dJ04CmSjuznwJSS3pUeWmd/H0ffTlkXXgwZi+eq1UCmqQwCh+eLsYOYCwY991i2Fah4h1BEMCx4qThGbsiA==", "license": "ISC", "resolved": "https://registry.npmjs.org/lru-cache/-/lru-cache-6.0.0.tgz", "version": "6.0.0"}, "node_modules/make-dir": {"dependencies": {"semver": "^7.5.3"}, "dev": true, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}, "integrity": "sha512-hXdUTZYIVOt1Ex//jAQi+wTZZpUpwBj/0QsOzqegb3rGMMeJiSEu5xLHnYfBrRV4RH2+OCSOO95Is/7x1WJ4bw==", "license": "MIT", "resolved": "https://registry.npmjs.org/make-dir/-/make-dir-4.0.0.tgz", "version": "4.0.0"}, "node_modules/makeerror": {"dependencies": {"tmpl": "1.0.5"}, "dev": true, "integrity": "sha512-JmqCvUhmt43madlpFzG4BQzG2Z3m6tvQDNKdClZnO3VbIudJYmxsT0FNJMeiB2+JTSlTQTSbU8QdesVmwJcmLg==", "license": "BSD-3-<PERSON><PERSON>", "resolved": "https://registry.npmjs.org/makeerror/-/makeerror-1.0.12.tgz", "version": "1.0.12"}, "node_modules/matcher": {"dependencies": {"escape-string-regexp": "^4.0.0"}, "dev": true, "engines": {"node": ">=10"}, "integrity": "sha512-OkeDaAZ/bQCxeFAozM55PKcKU0yJMPGifLwV4Qgjitu+5MoAfSQN4lsLJeXZ1b8w0x+/Emda6MZgXS1jvsapng==", "license": "MIT", "optional": true, "resolved": "https://registry.npmjs.org/matcher/-/matcher-3.0.0.tgz", "version": "3.0.0"}, "node_modules/math-intrinsics": {"dev": true, "engines": {"node": ">= 0.4"}, "integrity": "sha512-/IXtbwEk5HTPyEwyKX6hGkYXxM9nbj64B+ilVJnC/R6B0pH5G4V3b0pVbL7DBj4tkhBAppbQUlf6F6Xl9LHu1g==", "license": "MIT", "resolved": "https://registry.npmjs.org/math-intrinsics/-/math-intrinsics-1.1.0.tgz", "version": "1.1.0"}, "node_modules/merge-stream": {"dev": true, "integrity": "sha512-abv/qOcuPfk3URPfDzmZU1LKmuw8kT+0nIHvKrKgFrwifol/doWcdA4ZqsWQ8ENrFKkd67Mfpo/LovbIUsbt3w==", "license": "MIT", "resolved": "https://registry.npmjs.org/merge-stream/-/merge-stream-2.0.0.tgz", "version": "2.0.0"}, "node_modules/micromatch": {"dependencies": {"braces": "^3.0.3", "picomatch": "^2.3.1"}, "dev": true, "engines": {"node": ">=8.6"}, "integrity": "sha512-PXwfBhYu0hBCPw8Dn0E+WDYb7af3dSLVWKi3HGv84IdF4TyFoC0ysxFd0Goxw7nSv4T/PzEJQxsYsEiFCKo2BA==", "license": "MIT", "resolved": "https://registry.npmjs.org/micromatch/-/micromatch-4.0.8.tgz", "version": "4.0.8"}, "node_modules/mime": {"bin": {"mime": "cli.js"}, "dev": true, "engines": {"node": ">=4.0.0"}, "integrity": "sha512-USPkMeET31rOMiarsBNIHZKLGgvKc/LrjofAnBlOttf5ajRvqiRA8QsenbcooctK6d6Ts6aqZXBA+XbkKthiQg==", "license": "MIT", "resolved": "https://registry.npmjs.org/mime/-/mime-2.6.0.tgz", "version": "2.6.0"}, "node_modules/mime-db": {"dev": true, "engines": {"node": ">= 0.6"}, "integrity": "sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==", "license": "MIT", "resolved": "https://registry.npmjs.org/mime-db/-/mime-db-1.52.0.tgz", "version": "1.52.0"}, "node_modules/mime-types": {"dependencies": {"mime-db": "1.52.0"}, "dev": true, "engines": {"node": ">= 0.6"}, "integrity": "sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==", "license": "MIT", "resolved": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.35.tgz", "version": "2.1.35"}, "node_modules/mimic-fn": {"engines": {"node": ">=6"}, "integrity": "sha512-OqbOk5oEQeAZ8WXWydlu9HJjz9WVdEIvamMCcXmuqUYjTknH/sqsWvhQ3vgwKFRR1HpjvNBKQ37nbJgYzGqGcg==", "license": "MIT", "resolved": "https://registry.npmjs.org/mimic-fn/-/mimic-fn-2.1.0.tgz", "version": "2.1.0"}, "node_modules/mimic-response": {"dev": true, "engines": {"node": ">=4"}, "integrity": "sha512-j5EctnkH7amfV/q5Hgmoal1g2QHFJRraOtmx0JpIqkxhBhI/lJSl1nMpQ45hVarwNETOoWEimndZ4QK0RHxuxQ==", "license": "MIT", "resolved": "https://registry.npmjs.org/mimic-response/-/mimic-response-1.0.1.tgz", "version": "1.0.1"}, "node_modules/minimatch": {"dependencies": {"brace-expansion": "^2.0.1"}, "engines": {"node": ">=10"}, "integrity": "sha512-lKwV/1brpG6mBUFHtb7NUmtABCb2WZZmm2wNiOA5hAb8VdCS4B3dtMWyvcoViccwAW/COERjXLt0zP1zXUN26g==", "license": "ISC", "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-5.1.6.tgz", "version": "5.1.6"}, "node_modules/minimist": {"dev": true, "funding": {"url": "https://github.com/sponsors/ljharb"}, "integrity": "sha512-2yyAR8qBkN3YuheJanUpWC5U3bb5osDywNB8RzDVlDwDHbocAJveqqj1u8+SVD7jkWT4yvsHCpWqqWqAxb0zCA==", "license": "MIT", "resolved": "https://registry.npmjs.org/minimist/-/minimist-1.2.8.tgz", "version": "1.2.8"}, "node_modules/minipass": {"dev": true, "engines": {"node": ">=8"}, "integrity": "sha512-3FnjYuehv9k6ovOEbyOswadCDPX1piCfhV8ncmYtHOjuPwylVWsghTLo7rabjC3Rx5xD4HDx8Wm1xnMF7S5qFQ==", "license": "ISC", "resolved": "https://registry.npmjs.org/minipass/-/minipass-5.0.0.tgz", "version": "5.0.0"}, "node_modules/minizlib": {"dependencies": {"minipass": "^3.0.0", "yallist": "^4.0.0"}, "dev": true, "engines": {"node": ">= 8"}, "integrity": "sha512-bAxsR8BVfj60DWXHE3u30oHzfl4G7khkSuPW+qvpd7jFRHm7dLxOjUk1EHACJ/hxLY8phGJ0YhYHZo7jil7Qdg==", "license": "MIT", "resolved": "https://registry.npmjs.org/minizlib/-/minizlib-2.1.2.tgz", "version": "2.1.2"}, "node_modules/minizlib/node_modules/minipass": {"dependencies": {"yallist": "^4.0.0"}, "dev": true, "engines": {"node": ">=8"}, "integrity": "sha512-DxiNidxSEK+tHG6zOIklvNOwm3hvCrbUrdtzY74U6HKTJxvIDfOUL5W5P2Ghd3DTkhhKPYGqeNUIh5qcM4YBfw==", "license": "ISC", "resolved": "https://registry.npmjs.org/minipass/-/minipass-3.3.6.tgz", "version": "3.3.6"}, "node_modules/mkdirp": {"bin": {"mkdirp": "bin/cmd.js"}, "dev": true, "engines": {"node": ">=10"}, "integrity": "sha512-vVqVZQyf3WLx2Shd0qJ9xuvqgAyKPLAiqITEtqW0oIUjzo3PePDd6fW9iFz30ef7Ysp/oiWqbhszeGWW2T6Gzw==", "license": "MIT", "resolved": "https://registry.npmjs.org/mkdirp/-/mkdirp-1.0.4.tgz", "version": "1.0.4"}, "node_modules/ms": {"integrity": "sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==", "license": "MIT", "resolved": "https://registry.npmjs.org/ms/-/ms-2.1.3.tgz", "version": "2.1.3"}, "node_modules/mute-stream": {"integrity": "sha512-nnbWWOkoWyUsTjKrhgD0dcz22mdkSnpYqbEjIm2nhwhuxlSkpywJmBo8h0ZqJdkp73mb90SssHkN4rsRaBAfAA==", "license": "ISC", "resolved": "https://registry.npmjs.org/mute-stream/-/mute-stream-0.0.8.tgz", "version": "0.0.8"}, "node_modules/natural-compare": {"dev": true, "integrity": "sha512-OWND8ei3VtNC9h7V60qff3SVobHr996CTwgxubgyQYEpg290h9J0buyECNNJexkFm5sOajh5G116RYA1c8ZMSw==", "license": "MIT", "resolved": "https://registry.npmjs.org/natural-compare/-/natural-compare-1.4.0.tgz", "version": "1.4.0"}, "node_modules/node-addon-api": {"dev": true, "integrity": "sha512-ibPK3iA+vaY1eEjESkQkM0BbCqFOaZMiXRTtdB0u7b4djtY6JnsjvPdUHVMg6xQt3B8fpTTWHI9A+ADjM9frzg==", "license": "MIT", "optional": true, "resolved": "https://registry.npmjs.org/node-addon-api/-/node-addon-api-1.7.2.tgz", "version": "1.7.2"}, "node_modules/node-int64": {"dev": true, "integrity": "sha512-O5lz91xSOeoXP6DulyHfllpq+Eg00MWitZIbtPfoSEvqIHdl5gfcY6hYzDWnj0qD5tz52PI08u9qUvSVeUBeHw==", "license": "MIT", "resolved": "https://registry.npmjs.org/node-int64/-/node-int64-0.4.0.tgz", "version": "0.4.0"}, "node_modules/node-releases": {"dev": true, "integrity": "sha512-xxOWJsBKtzAq7DY0J+DTzuz58K8e7sJbdgwkbMWQe8UYB6ekmsQ45q0M/tJDsGaZmbC+l7n57UV8Hl5tHxO9uw==", "license": "MIT", "resolved": "https://registry.npmjs.org/node-releases/-/node-releases-2.0.19.tgz", "version": "2.0.19"}, "node_modules/normalize-path": {"engines": {"node": ">=0.10.0"}, "integrity": "sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA==", "license": "MIT", "resolved": "https://registry.npmjs.org/normalize-path/-/normalize-path-3.0.0.tgz", "version": "3.0.0"}, "node_modules/normalize-url": {"dev": true, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}, "integrity": "sha512-DlL+XwOy3NxAQ8xuC0okPgK46iuVNAK01YN7RueYBqqFeGsBjV9XmCAzAdgt+667bCl5kPh9EqKKDwnaPG1I7A==", "license": "MIT", "resolved": "https://registry.npmjs.org/normalize-url/-/normalize-url-6.1.0.tgz", "version": "6.1.0"}, "node_modules/npm-run-path": {"dependencies": {"path-key": "^3.0.0"}, "dev": true, "engines": {"node": ">=8"}, "integrity": "sha512-S48WzZW777zhNIrn7gxOlISNAqi9ZC/uQFnRdbeIHhZhCA6UqpkOT8T1G7BvfdgP4Er8gF4sUbaS0i7QvIfCWw==", "license": "MIT", "resolved": "https://registry.npmjs.org/npm-run-path/-/npm-run-path-4.0.1.tgz", "version": "4.0.1"}, "node_modules/object-keys": {"dev": true, "engines": {"node": ">= 0.4"}, "integrity": "sha512-NuAESUOUMrlIXOfHKzD6bpPu3tYt3xvjNdRIQ+FeT0lNb4K8WR70CaDxhuNguS2XG+GjkyMwOzsN5ZktImfhLA==", "license": "MIT", "optional": true, "resolved": "https://registry.npmjs.org/object-keys/-/object-keys-1.1.1.tgz", "version": "1.1.1"}, "node_modules/on-exit-leak-free": {"engines": {"node": ">=14.0.0"}, "integrity": "sha512-0eJJY6hXLGf1udHwfNftBqH+g73EU4B504nZeKpz1sYRKafAghwxEJunB2O7rDZkL4PGfsMVnTXZ2EjibbqcsA==", "license": "MIT", "resolved": "https://registry.npmjs.org/on-exit-leak-free/-/on-exit-leak-free-2.1.2.tgz", "version": "2.1.2"}, "node_modules/once": {"dependencies": {"wrappy": "1"}, "integrity": "sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w==", "license": "ISC", "resolved": "https://registry.npmjs.org/once/-/once-1.4.0.tgz", "version": "1.4.0"}, "node_modules/onetime": {"dependencies": {"mimic-fn": "^2.1.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}, "integrity": "sha512-kbpaSSGJTWdAY5KPVeMOKXSrPtr8C8C7wodJbcsd51jRnmD+GZu8Y0VoU6Dm5Z4vWr0Ig/1NKuWRKf7j5aaYSg==", "license": "MIT", "resolved": "https://registry.npmjs.org/onetime/-/onetime-5.1.2.tgz", "version": "5.1.2"}, "node_modules/ora": {"dependencies": {"bl": "^4.1.0", "chalk": "^4.1.0", "cli-cursor": "^3.1.0", "cli-spinners": "^2.5.0", "is-interactive": "^1.0.0", "is-unicode-supported": "^0.1.0", "log-symbols": "^4.1.0", "strip-ansi": "^6.0.0", "wcwidth": "^1.0.1"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}, "integrity": "sha512-5b6Y85tPxZZ7QytO+BQzysW31HJku27cRIlkbAXaNx+BdcVi+LlRFmVXzeF6a7JCwJpyw5c4b+YSVImQIrBpuQ==", "license": "MIT", "resolved": "https://registry.npmjs.org/ora/-/ora-5.4.1.tgz", "version": "5.4.1"}, "node_modules/os-tmpdir": {"engines": {"node": ">=0.10.0"}, "integrity": "sha512-D2FR03Vir7FIu45XBY20mTb+/ZSWB00sjU9jdQXt83gDrI4Ztz5Fs7/yy74g2N5SVQY4xY1qDr4rNddwYRVX0g==", "license": "MIT", "resolved": "https://registry.npmjs.org/os-tmpdir/-/os-tmpdir-1.0.2.tgz", "version": "1.0.2"}, "node_modules/p-cancelable": {"dev": true, "engines": {"node": ">=8"}, "integrity": "sha512-BZOr3nRQHOntUjTrH8+Lh54smKHoHyur8We1V8DSMVrl5A2malOOwuJRnKRDjSnkoeBh4at6BwEnb5I7Jl31wg==", "license": "MIT", "resolved": "https://registry.npmjs.org/p-cancelable/-/p-cancelable-2.1.1.tgz", "version": "2.1.1"}, "node_modules/p-limit": {"dependencies": {"yocto-queue": "^0.1.0"}, "dev": true, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}, "integrity": "sha512-TYOanM3wGwNGsZN2cVTYPArw454xnXj5qmWF1bEoAc4+cU/ol7GVh7odevjp1FNHduHc3KZMcFduxU5Xc6uJRQ==", "license": "MIT", "resolved": "https://registry.npmjs.org/p-limit/-/p-limit-3.1.0.tgz", "version": "3.1.0"}, "node_modules/p-locate": {"dependencies": {"p-limit": "^2.2.0"}, "dev": true, "engines": {"node": ">=8"}, "integrity": "sha512-R79ZZ/0wAxKGu3oYMlz8jy/kbhsNrS7SKZ7PxEHBgJ5+F2mtFW2fK2cOtBh1cHYkQsbzFV7I+EoRKe6Yt0oK7A==", "license": "MIT", "resolved": "https://registry.npmjs.org/p-locate/-/p-locate-4.1.0.tgz", "version": "4.1.0"}, "node_modules/p-locate/node_modules/p-limit": {"dependencies": {"p-try": "^2.0.0"}, "dev": true, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}, "integrity": "sha512-//88mFWSJx8lxCzwdAABTJL2MyWB12+eIY7MDL2SqLmAkeKU9qxRvWuSyTjm3FUmpBEMuFfckAIqEaVGUDxb6w==", "license": "MIT", "resolved": "https://registry.npmjs.org/p-limit/-/p-limit-2.3.0.tgz", "version": "2.3.0"}, "node_modules/p-try": {"dev": true, "engines": {"node": ">=6"}, "integrity": "sha512-R4nPAVTAU0B9D35/Gk3uJf/7XYbQcyohSKdvAxIRSNghFl4e71hVoGnBNQz9cWaXxO2I10KTC+3jMdvvoKw6dQ==", "license": "MIT", "resolved": "https://registry.npmjs.org/p-try/-/p-try-2.2.0.tgz", "version": "2.2.0"}, "node_modules/package-json-from-dist": {"dev": true, "integrity": "sha512-UEZIS3/by4OC8vL3P2dTXRETpebLI2NiI5vIrjaD/5UtrkFX/tNbwjTSRAGC/+7CAo2pIcBaRgWmcBBHcsaCIw==", "license": "BlueOak-1.0.0", "resolved": "https://registry.npmjs.org/package-json-from-dist/-/package-json-from-dist-1.0.1.tgz", "version": "1.0.1"}, "node_modules/parse-json": {"dependencies": {"@babel/code-frame": "^7.0.0", "error-ex": "^1.3.1", "json-parse-even-better-errors": "^2.3.0", "lines-and-columns": "^1.1.6"}, "dev": true, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}, "integrity": "sha512-ayCKvm/phCGxOkYRSCM82iDwct8/EonSEgCSxWxD7ve6jHggsFl4fZVQBPRNgQoKiuV/odhFrGzQXZwbifC8Rg==", "license": "MIT", "resolved": "https://registry.npmjs.org/parse-json/-/parse-json-5.2.0.tgz", "version": "5.2.0"}, "node_modules/path-exists": {"dev": true, "engines": {"node": ">=8"}, "integrity": "sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w==", "license": "MIT", "resolved": "https://registry.npmjs.org/path-exists/-/path-exists-4.0.0.tgz", "version": "4.0.0"}, "node_modules/path-is-absolute": {"dev": true, "engines": {"node": ">=0.10.0"}, "integrity": "sha512-AVbw3UJ2e9bq64vSaS9Am0fje1Pa8pbGqTTsmXfaIiMpnr5DlDhfJOuLj9Sf95ZPVDAUerDfEk88MPmPe7UCQg==", "license": "MIT", "resolved": "https://registry.npmjs.org/path-is-absolute/-/path-is-absolute-1.0.1.tgz", "version": "1.0.1"}, "node_modules/path-key": {"dev": true, "engines": {"node": ">=8"}, "integrity": "sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==", "license": "MIT", "resolved": "https://registry.npmjs.org/path-key/-/path-key-3.1.1.tgz", "version": "3.1.1"}, "node_modules/path-parse": {"dev": true, "integrity": "sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw==", "license": "MIT", "resolved": "https://registry.npmjs.org/path-parse/-/path-parse-1.0.7.tgz", "version": "1.0.7"}, "node_modules/path-scurry": {"dependencies": {"lru-cache": "^10.2.0", "minipass": "^5.0.0 || ^6.0.2 || ^7.0.0"}, "dev": true, "engines": {"node": ">=16 || 14 >=14.18"}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "integrity": "sha512-Xa4Nw17FS9ApQFJ9umLiJS4orGjm7ZzwUrwamcGQuHSzDyth9boKDaycYdDcZDuqYATXw4HFXgaqWTctW/v1HA==", "license": "BlueOak-1.0.0", "resolved": "https://registry.npmjs.org/path-scurry/-/path-scurry-1.11.1.tgz", "version": "1.11.1"}, "node_modules/path-scurry/node_modules/lru-cache": {"dev": true, "integrity": "sha512-JNAzZcXrCt42VGLuYz0zfAzDfAvJWW6AfYlDBQyDV5DClI2m5sAmK+OIO7s59XfsRsWHp02jAJrRadPRGTt6SQ==", "license": "ISC", "resolved": "https://registry.npmjs.org/lru-cache/-/lru-cache-10.4.3.tgz", "version": "10.4.3"}, "node_modules/pend": {"integrity": "sha512-F3asv42UuXchdzt+xXqfW1OGlVBe+mxa2mqI0pg5yAHZPvFmY3Y6drSf/GQ1A86WgWEN9Kzh/WrgKa6iGcHXLg==", "license": "MIT", "resolved": "https://registry.npmjs.org/pend/-/pend-1.2.0.tgz", "version": "1.2.0"}, "node_modules/picocolors": {"dev": true, "integrity": "sha512-xceH2snhtb5M9liqDsmEw56le376mTZkEX/jEb/RxNFyegNul7eNslCXP9FDj/Lcu0X8KEyMceP2ntpaHrDEVA==", "license": "ISC", "resolved": "https://registry.npmjs.org/picocolors/-/picocolors-1.1.1.tgz", "version": "1.1.1"}, "node_modules/picomatch": {"dev": true, "engines": {"node": ">=8.6"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}, "integrity": "sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==", "license": "MIT", "resolved": "https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz", "version": "2.3.1"}, "node_modules/pino": {"bin": {"pino": "bin.js"}, "dependencies": {"atomic-sleep": "^1.0.0", "fast-redact": "^3.1.1", "on-exit-leak-free": "^2.1.0", "pino-abstract-transport": "^2.0.0", "pino-std-serializers": "^7.0.0", "process-warning": "^5.0.0", "quick-format-unescaped": "^4.0.3", "real-require": "^0.2.0", "safe-stable-stringify": "^2.3.1", "sonic-boom": "^4.0.1", "thread-stream": "^3.0.0"}, "integrity": "sha512-vnMCM6xZTb1WDmLvtG2lE/2p+t9hDEIvTWJsu6FejkE62vB7gDhvzrpFR4Cw2to+9JNQxVnkAKVPA1KPB98vWg==", "license": "MIT", "resolved": "https://registry.npmjs.org/pino/-/pino-9.7.0.tgz", "version": "9.7.0"}, "node_modules/pino-abstract-transport": {"dependencies": {"split2": "^4.0.0"}, "integrity": "sha512-F63x5tizV6WCh4R6RHyi2Ml+M70DNRXt/+HANowMflpgGFMAym/VKm6G7ZOQRjqN7XbGxK1Lg9t6ZrtzOaivMw==", "license": "MIT", "resolved": "https://registry.npmjs.org/pino-abstract-transport/-/pino-abstract-transport-2.0.0.tgz", "version": "2.0.0"}, "node_modules/pino-std-serializers": {"integrity": "sha512-e906FRY0+tV27iq4juKzSYPbUj2do2X2JX4EzSca1631EB2QJQUqGbDuERal7LCtOpxl6x3+nvo9NPZcmjkiFA==", "license": "MIT", "resolved": "https://registry.npmjs.org/pino-std-serializers/-/pino-std-serializers-7.0.0.tgz", "version": "7.0.0"}, "node_modules/pirates": {"dev": true, "engines": {"node": ">= 6"}, "integrity": "sha512-TfySrs/5nm8fQJDcBDuUng3VOUKsd7S+zqvbOTiGXHfxX4wK31ard+hoNuvkicM/2YFzlpDgABOevKSsB4G/FA==", "license": "MIT", "resolved": "https://registry.npmjs.org/pirates/-/pirates-4.0.7.tgz", "version": "4.0.7"}, "node_modules/pkg-dir": {"dependencies": {"find-up": "^4.0.0"}, "dev": true, "engines": {"node": ">=8"}, "integrity": "sha512-HRDzbaKjC+AOWVXxAU/x54COGeIv9eb+6CkDSQoNTt4XyWoIJvuPsXizxu/Fr23EiekbtZwmh1IcIG/l/a10GQ==", "license": "MIT", "resolved": "https://registry.npmjs.org/pkg-dir/-/pkg-dir-4.2.0.tgz", "version": "4.2.0"}, "node_modules/plist": {"dependencies": {"@xmldom/xmldom": "^0.8.8", "base64-js": "^1.5.1", "xmlbuilder": "^15.1.1"}, "dev": true, "engines": {"node": ">=10.4.0"}, "integrity": "sha512-uysumyrvkUX0rX/dEVqt8gC3sTBzd4zoWfLeS29nb53imdaXVvLINYXTI2GNqzaMuvacNx4uJQ8+b3zXR0pkgQ==", "license": "MIT", "resolved": "https://registry.npmjs.org/plist/-/plist-3.1.0.tgz", "version": "3.1.0"}, "node_modules/pretty-format": {"dependencies": {"@jest/schemas": "^29.6.3", "ansi-styles": "^5.0.0", "react-is": "^18.0.0"}, "dev": true, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "integrity": "sha512-Pdlw/oPxN+aXdmM9R00JVC9WVFoCLTKJvDVLgmJ+qAffBMxsV85l/Lu7sNx4zSzPyoL2euImuEwHhOXdEgNFZQ==", "license": "MIT", "resolved": "https://registry.npmjs.org/pretty-format/-/pretty-format-29.7.0.tgz", "version": "29.7.0"}, "node_modules/pretty-format/node_modules/ansi-styles": {"dev": true, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}, "integrity": "sha512-Cxwpt2SfTzTtXcfOlzGEee8O+c+MmUgGrNiBcXnuWxuFJHe6a5Hz7qwhwe5OgaSYI0IJvkLqWX1ASG+cJOkEiA==", "license": "MIT", "resolved": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-5.2.0.tgz", "version": "5.2.0"}, "node_modules/process-nextick-args": {"integrity": "sha512-3ouUOpQhtgrbOa17J7+uxOTpITYWaGP7/AhoR3+A+/1e9skrzelGi/dXzEYyvbxubEF6Wn2ypscTKiKJFFn1ag==", "license": "MIT", "resolved": "https://registry.npmjs.org/process-nextick-args/-/process-nextick-args-2.0.1.tgz", "version": "2.0.1"}, "node_modules/process-warning": {"funding": [{"type": "github", "url": "https://github.com/sponsors/fastify"}, {"type": "opencollective", "url": "https://opencollective.com/fastify"}], "integrity": "sha512-a39t9ApHNx2L4+HBnQKqxxHNs1r7KF+Intd8Q/g1bUh6q0WIp9voPXJ/x0j+ZL45KF1pJd9+q2jLIRMfvEshkA==", "license": "MIT", "resolved": "https://registry.npmjs.org/process-warning/-/process-warning-5.0.0.tgz", "version": "5.0.0"}, "node_modules/progress": {"dev": true, "engines": {"node": ">=0.4.0"}, "integrity": "sha512-7PiHtLll5LdnKIMw100I+8xJXR5gW2QwWYkT6iJva0bXitZKa/XMrSbdmg3r2Xnaidz9Qumd0VPaMrZlF9V9sA==", "license": "MIT", "resolved": "https://registry.npmjs.org/progress/-/progress-2.0.3.tgz", "version": "2.0.3"}, "node_modules/promise-retry": {"dependencies": {"err-code": "^2.0.2", "retry": "^0.12.0"}, "dev": true, "engines": {"node": ">=10"}, "integrity": "sha512-y+WKFlBR8BGXnsNlIHFGPZmyDf3DFMoLhaflAnyZgV6rG6xu+JwesTo2Q9R6XwYmtmwAFCkAk3e35jEdoeh/3g==", "license": "MIT", "resolved": "https://registry.npmjs.org/promise-retry/-/promise-retry-2.0.1.tgz", "version": "2.0.1"}, "node_modules/prompts": {"dependencies": {"kleur": "^3.0.3", "sisteransi": "^1.0.5"}, "dev": true, "engines": {"node": ">= 6"}, "integrity": "sha512-NxNv/kLguCA7p3jE8oL2aEBsrJWgAakBpgmgK6lpPWV+WuOmY6r2/zbAVnP+T8bQlA0nzHXSJSJW0Hq7ylaD2Q==", "license": "MIT", "resolved": "https://registry.npmjs.org/prompts/-/prompts-2.4.2.tgz", "version": "2.4.2"}, "node_modules/pump": {"dependencies": {"end-of-stream": "^1.1.0", "once": "^1.3.1"}, "dev": true, "integrity": "sha512-todwxLMY7/heScKmntwQG8CXVkWUOdYxIvY2s0VWAAMh/nd8SoYiRaKjlr7+iCs984f2P8zvrfWcDDYVb73NfA==", "license": "MIT", "resolved": "https://registry.npmjs.org/pump/-/pump-3.0.3.tgz", "version": "3.0.3"}, "node_modules/punycode": {"dev": true, "engines": {"node": ">=6"}, "integrity": "sha512-vYt7UD1U9Wg6138shLtLOvdAu+8DsC/ilFtEVHcH+wydcSpNE20AfSOduf6MkRFahL5FY7X1oU7nKVZFtfq8Fg==", "license": "MIT", "resolved": "https://registry.npmjs.org/punycode/-/punycode-2.3.1.tgz", "version": "2.3.1"}, "node_modules/pure-rand": {"dev": true, "funding": [{"type": "individual", "url": "https://github.com/sponsors/dubzzz"}, {"type": "opencollective", "url": "https://opencollective.com/fast-check"}], "integrity": "sha512-b<PERSON><PERSON>awvoZoBYpp6yIoQtQXHZjmz35RSVHnUOTefl8Vcjr8snTPY1wnpSPMWekcFwbxI6gtmT7rSYPFvz71ldiOA==", "license": "MIT", "resolved": "https://registry.npmjs.org/pure-rand/-/pure-rand-6.1.0.tgz", "version": "6.1.0"}, "node_modules/quick-format-unescaped": {"integrity": "sha512-tYC1Q1hgyRuHgloV/YXs2w15unPVh8qfu/qCTfhTYamaw7fyhumKa2yGpdSo87vY32rIclj+4fWYQXUMs9EHvg==", "license": "MIT", "resolved": "https://registry.npmjs.org/quick-format-unescaped/-/quick-format-unescaped-4.0.4.tgz", "version": "4.0.4"}, "node_modules/quick-lru": {"dev": true, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}, "integrity": "sha512-WuyALRjWPDGtt/wzJiadO5AXY+8hZ80hVpe6MyivgraREW751X3SbhRvG3eLKOYN+8VEvqLcf3wdnt44Z4S4SA==", "license": "MIT", "resolved": "https://registry.npmjs.org/quick-lru/-/quick-lru-5.1.1.tgz", "version": "5.1.1"}, "node_modules/react-is": {"dev": true, "integrity": "sha512-/LLMVyas0ljjAtoYiPqYiL8VWXzUUdThrmU5+n20DZv+a+ClRoevUzw5JxU+Ieh5/c87ytoTBV9G1FiKfNJdmg==", "license": "MIT", "resolved": "https://registry.npmjs.org/react-is/-/react-is-18.3.1.tgz", "version": "18.3.1"}, "node_modules/read-config-file": {"dependencies": {"config-file-ts": "^0.2.4", "dotenv": "^9.0.2", "dotenv-expand": "^5.1.0", "js-yaml": "^4.1.0", "json5": "^2.2.0", "lazy-val": "^1.0.4"}, "dev": true, "engines": {"node": ">=12.0.0"}, "integrity": "sha512-M80lpCjnE6Wt6zb98DoW8WHR09nzMSpu8XHtPkiTHrJ5Az9CybfeQhTJ8D7saeBHpGhLPIVyA8lcL6ZmdKwY6Q==", "license": "MIT", "resolved": "https://registry.npmjs.org/read-config-file/-/read-config-file-6.3.2.tgz", "version": "6.3.2"}, "node_modules/readable-stream": {"dependencies": {"inherits": "^2.0.3", "string_decoder": "^1.1.1", "util-deprecate": "^1.0.1"}, "engines": {"node": ">= 6"}, "integrity": "sha512-9u/sniCrY3D5WdsERHzHE4G2YCXqoG5FTHUiCC4SIbr6XcLZBY05ya9EKjYek9O5xOAwjGq+1JdGBAS7Q9ScoA==", "license": "MIT", "resolved": "https://registry.npmjs.org/readable-stream/-/readable-stream-3.6.2.tgz", "version": "3.6.2"}, "node_modules/readdir-glob": {"dependencies": {"minimatch": "^5.1.0"}, "integrity": "sha512-v05I2k7xN8zXvPD9N+z/uhXPaj0sUFCe2rcWZIpBsqxfP7xXFQ0tipAd/wjj1YxWyWtUS5IDJpOG82JKt2EAVA==", "license": "Apache-2.0", "resolved": "https://registry.npmjs.org/readdir-glob/-/readdir-glob-1.1.3.tgz", "version": "1.1.3"}, "node_modules/real-require": {"engines": {"node": ">= 12.13.0"}, "integrity": "sha512-57frrGM/OCTLqLOAh0mhVA9VBMHd+9U7Zb2THMGdBUoZVOtGbJzjxsYGDJ3A9AYYCP4hn6y1TVbaOfzWtm5GFg==", "license": "MIT", "resolved": "https://registry.npmjs.org/real-require/-/real-require-0.2.0.tgz", "version": "0.2.0"}, "node_modules/require-directory": {"dev": true, "engines": {"node": ">=0.10.0"}, "integrity": "sha512-fGxEI7+wsG9xrvdjsrlmL22OMTTiHRwAMroiEeMgq8gzoLC/PQr7RsRDSTLUg/bZAZtF+TVIkHc6/4RIKrui+Q==", "license": "MIT", "resolved": "https://registry.npmjs.org/require-directory/-/require-directory-2.1.1.tgz", "version": "2.1.1"}, "node_modules/require-from-string": {"engines": {"node": ">=0.10.0"}, "integrity": "sha512-Xf0nWe6RseziFMu+Ap9biiUbmplq6S9/p+7w7YXP/JBHhrUDDUhwa+vANyubuqfZWTveU//DYVGsDG7RKL/vEw==", "license": "MIT", "resolved": "https://registry.npmjs.org/require-from-string/-/require-from-string-2.0.2.tgz", "version": "2.0.2"}, "node_modules/resolve": {"bin": {"resolve": "bin/resolve"}, "dependencies": {"is-core-module": "^2.16.0", "path-parse": "^1.0.7", "supports-preserve-symlinks-flag": "^1.0.0"}, "dev": true, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "integrity": "sha512-NPRy+/ncIMeDlTAsuqwKIiferiawhefFJtkNSW0qZJEqMEb+qBt/77B/jGeeek+F0uOeN05CDa6HXbbIgtVX4w==", "license": "MIT", "resolved": "https://registry.npmjs.org/resolve/-/resolve-1.22.10.tgz", "version": "1.22.10"}, "node_modules/resolve-alpn": {"dev": true, "integrity": "sha512-0a1F4l73/ZFZOakJnQ3FvkJ2+gSTQWz/r2KE5OdDY0TxPm5h4GkqkWWfM47T7HsbnOtcJVEF4epCVy6u7Q3K+g==", "license": "MIT", "resolved": "https://registry.npmjs.org/resolve-alpn/-/resolve-alpn-1.2.1.tgz", "version": "1.2.1"}, "node_modules/resolve-cwd": {"dependencies": {"resolve-from": "^5.0.0"}, "dev": true, "engines": {"node": ">=8"}, "integrity": "sha512-OrZaX2Mb+rJCpH/6CpSqt9xFVpN++x01XnN2ie9g6P5/3xelLAkXWVADpdz1IHD/KFfEXyE6V0U01OQ3UO2rEg==", "license": "MIT", "resolved": "https://registry.npmjs.org/resolve-cwd/-/resolve-cwd-3.0.0.tgz", "version": "3.0.0"}, "node_modules/resolve-from": {"dev": true, "engines": {"node": ">=8"}, "integrity": "sha512-qYg9KP24dD5qka9J47d0aVky0N+b4fTU89LN9iDnjB5waksiC49rvMB0PrUJQGoTmH50XPiqOvAjDfaijGxYZw==", "license": "MIT", "resolved": "https://registry.npmjs.org/resolve-from/-/resolve-from-5.0.0.tgz", "version": "5.0.0"}, "node_modules/resolve.exports": {"dev": true, "engines": {"node": ">=10"}, "integrity": "sha512-OcXjMsGdhL4XnbShKpAcSqPMzQoYkYyhbEaeSko47MjRP9NfEQMhZkXL1DoFlt9LWQn4YttrdnV6X2OiyzBi+A==", "license": "MIT", "resolved": "https://registry.npmjs.org/resolve.exports/-/resolve.exports-2.0.3.tgz", "version": "2.0.3"}, "node_modules/responselike": {"dependencies": {"lowercase-keys": "^2.0.0"}, "dev": true, "funding": {"url": "https://github.com/sponsors/sindresorhus"}, "integrity": "sha512-4gl03wn3hj1HP3yzgdI7d3lCkF95F21Pz4BPGvKHinyQzALR5CapwC8yIi0Rh58DEMQ/SguC03wFj2k0M/mHhw==", "license": "MIT", "resolved": "https://registry.npmjs.org/responselike/-/responselike-2.0.1.tgz", "version": "2.0.1"}, "node_modules/restore-cursor": {"dependencies": {"onetime": "^5.1.0", "signal-exit": "^3.0.2"}, "engines": {"node": ">=8"}, "integrity": "sha512-l+sSefzHpj5qimhFSE5a8nufZYAM3sBSVMAPtYkmC+4EH2anSGaEMXSD0izRQbu9nfyQ9y5JrVmp7E8oZrUjvA==", "license": "MIT", "resolved": "https://registry.npmjs.org/restore-cursor/-/restore-cursor-3.1.0.tgz", "version": "3.1.0"}, "node_modules/restore-cursor/node_modules/signal-exit": {"integrity": "sha512-wnD2ZE+l+SPC/uoS0vXeE9L1+0wuaMqKlfz9AMUo38JsyLSBWSFcHR1Rri62LZc12vLr1gb3jl7iwQhgwpAbGQ==", "license": "ISC", "resolved": "https://registry.npmjs.org/signal-exit/-/signal-exit-3.0.7.tgz", "version": "3.0.7"}, "node_modules/retry": {"dev": true, "engines": {"node": ">= 4"}, "integrity": "sha512-9LkiTwjUh6rT555DtE9rTX+BKByPfrMzEAtnlEtdEwr3Nkffwiihqe2bWADg+OQRjt9gl6ICdmB/ZFDCGAtSow==", "license": "MIT", "resolved": "https://registry.npmjs.org/retry/-/retry-0.12.0.tgz", "version": "0.12.0"}, "node_modules/roarr": {"dependencies": {"boolean": "^3.0.1", "detect-node": "^2.0.4", "globalthis": "^1.0.1", "json-stringify-safe": "^5.0.1", "semver-compare": "^1.0.0", "sprintf-js": "^1.1.2"}, "dev": true, "engines": {"node": ">=8.0"}, "integrity": "sha512-CHhPh+UNHD2GTXNYhPWLnU8ONHdI+5DI+4EYIAOaiD63rHeYlZvyh8P+in5999TTSFgUYuKUAjzRI4mdh/p+2A==", "license": "BSD-3-<PERSON><PERSON>", "optional": true, "resolved": "https://registry.npmjs.org/roarr/-/roarr-2.15.4.tgz", "version": "2.15.4"}, "node_modules/run-async": {"engines": {"node": ">=0.12.0"}, "integrity": "sha512-tvVnVv01b8c1RrA6Ep7JkStj85Guv/YrMcwqYQnwjsAS2cTmmPGBBjAjpCW7RrSodNSoE2/qg9O4bceNvUuDgQ==", "license": "MIT", "resolved": "https://registry.npmjs.org/run-async/-/run-async-2.4.1.tgz", "version": "2.4.1"}, "node_modules/rxjs": {"dependencies": {"tslib": "^2.1.0"}, "integrity": "sha512-dhKf903U/PQZY6boNNtAGdWbG85WAbjT/1xYoZIC7FAY0yWapOBQVsVrDl58W86//e1VpMNBtRV4MaXfdMySFA==", "license": "Apache-2.0", "resolved": "https://registry.npmjs.org/rxjs/-/rxjs-7.8.2.tgz", "version": "7.8.2"}, "node_modules/safe-buffer": {"funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "integrity": "sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==", "license": "MIT", "resolved": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.2.1.tgz", "version": "5.2.1"}, "node_modules/safe-stable-stringify": {"engines": {"node": ">=10"}, "integrity": "sha512-b3rppTKm9T+PsVCBEOUR46GWI7fdOs00VKZ1+9c1EWDaDMvjQc6tUwuFyIprgGgTcWoVHSKrU8H31ZHA2e0RHA==", "license": "MIT", "resolved": "https://registry.npmjs.org/safe-stable-stringify/-/safe-stable-stringify-2.5.0.tgz", "version": "2.5.0"}, "node_modules/safer-buffer": {"integrity": "sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg==", "license": "MIT", "resolved": "https://registry.npmjs.org/safer-buffer/-/safer-buffer-2.1.2.tgz", "version": "2.1.2"}, "node_modules/sanitize-filename": {"dependencies": {"truncate-utf8-bytes": "^1.0.0"}, "dev": true, "integrity": "sha512-y/52Mcy7aw3gRm7IrcGDFx/bCk4AhRh2eI9luHOQM86nZsqwiRkkq2GekHXBBD+SmPidc8i2PqtYZl+pWJ8Oeg==", "license": "WTFPL OR ISC", "resolved": "https://registry.npmjs.org/sanitize-filename/-/sanitize-filename-1.6.3.tgz", "version": "1.6.3"}, "node_modules/sax": {"integrity": "sha512-+aWOz7yVScEGoKNd4PA10LZ8sk0A/z5+nXQG5giUO5rprX9jgYsTdov9qCchZiPIZezbZH+jRut8nPodFAX4Jg==", "license": "ISC", "resolved": "https://registry.npmjs.org/sax/-/sax-1.4.1.tgz", "version": "1.4.1"}, "node_modules/semver": {"bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}, "integrity": "sha512-RF0Fw+rO5AMf9MAyaRXI4AV0Ulj5lMHqVxxdSgiVbixSCXoEmmX/jk0CuJw4+3SqroYO9VoUh+HcuJivvtJemA==", "license": "ISC", "resolved": "https://registry.npmjs.org/semver/-/semver-7.7.2.tgz", "version": "7.7.2"}, "node_modules/semver-compare": {"dev": true, "integrity": "sha512-YM3/ITh2MJ5MtzaM429anh+x2jiLVjqILF4m4oyQB18W7Ggea7BfqdH/wGMK7dDiMghv/6WG7znWMwUDzJiXow==", "license": "MIT", "optional": true, "resolved": "https://registry.npmjs.org/semver-compare/-/semver-compare-1.0.0.tgz", "version": "1.0.0"}, "node_modules/serialize-error": {"dependencies": {"type-fest": "^0.13.1"}, "dev": true, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}, "integrity": "sha512-8I8TjW5KMOKsZQTvoxjuSIa7foAwPWGOts+6o7sgjz41/qMD9VQHEDxi6PBvK2l0MXUmqZyNpUK+T2tQaaElvw==", "license": "MIT", "optional": true, "resolved": "https://registry.npmjs.org/serialize-error/-/serialize-error-7.0.1.tgz", "version": "7.0.1"}, "node_modules/shebang-command": {"dependencies": {"shebang-regex": "^3.0.0"}, "dev": true, "engines": {"node": ">=8"}, "integrity": "sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==", "license": "MIT", "resolved": "https://registry.npmjs.org/shebang-command/-/shebang-command-2.0.0.tgz", "version": "2.0.0"}, "node_modules/shebang-regex": {"dev": true, "engines": {"node": ">=8"}, "integrity": "sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==", "license": "MIT", "resolved": "https://registry.npmjs.org/shebang-regex/-/shebang-regex-3.0.0.tgz", "version": "3.0.0"}, "node_modules/signal-exit": {"dev": true, "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "integrity": "sha512-bzyZ1e88w9O1iNJbKnOlvYTrWPDl46O1bG0D3XInv+9tkPrxrN8jUUTiFlDkkmKWgn1M6CfIA13SuGqOa9Korw==", "license": "ISC", "resolved": "https://registry.npmjs.org/signal-exit/-/signal-exit-4.1.0.tgz", "version": "4.1.0"}, "node_modules/simple-update-notifier": {"dependencies": {"semver": "^7.5.3"}, "dev": true, "engines": {"node": ">=10"}, "integrity": "sha512-a2B9Y0KlNXl9u/vsW6sTIu9vGEpfKu2wRV6l1H3XEas/0gUIzGzBoP/IouTcUQbm9JWZLH3COxyn03TYlFax6w==", "license": "MIT", "resolved": "https://registry.npmjs.org/simple-update-notifier/-/simple-update-notifier-2.0.0.tgz", "version": "2.0.0"}, "node_modules/sisteransi": {"dev": true, "integrity": "sha512-bLGGlR1QxBcynn2d5YmDX4MGjlZvy2MRBDRNHLJ8VI6l6+9FUiyTFNJ0IveOSP0bcXgVDPRcfGqA0pjaqUpfVg==", "license": "MIT", "resolved": "https://registry.npmjs.org/sisteransi/-/sisteransi-1.0.5.tgz", "version": "1.0.5"}, "node_modules/slash": {"dev": true, "engines": {"node": ">=8"}, "integrity": "sha512-g9Q1haeby36OSStwb4ntCGGGaKsaVSjQ68fBxoQcutl5fS1vuY18H3wSt3jFyFtrkx+Kz0V1G85A4MyAdDMi2Q==", "license": "MIT", "resolved": "https://registry.npmjs.org/slash/-/slash-3.0.0.tgz", "version": "3.0.0"}, "node_modules/slice-ansi": {"dependencies": {"ansi-styles": "^4.0.0", "astral-regex": "^2.0.0", "is-fullwidth-code-point": "^3.0.0"}, "dev": true, "engines": {"node": ">=8"}, "integrity": "sha512-pSyv7bSTC7ig9Dcgbw9AuRNUb5k5V6oDudjZoMBSr13qpLBG7tB+zgCkARjq7xIUgdz5P1Qe8u+rSGdouOOIyQ==", "license": "MIT", "optional": true, "resolved": "https://registry.npmjs.org/slice-ansi/-/slice-ansi-3.0.0.tgz", "version": "3.0.0"}, "node_modules/smart-buffer": {"dev": true, "engines": {"node": ">= 6.0.0", "npm": ">= 3.0.0"}, "integrity": "sha512-94hK0Hh8rPqQl2xXc3HsaBoOXKV20MToPkcXvwbISWLEs+64sBq5kFgn2kJDHb1Pry9yrP0dxrCI9RRci7RXKg==", "license": "MIT", "optional": true, "resolved": "https://registry.npmjs.org/smart-buffer/-/smart-buffer-4.2.0.tgz", "version": "4.2.0"}, "node_modules/sonic-boom": {"dependencies": {"atomic-sleep": "^1.0.0"}, "integrity": "sha512-INb7TM37/mAcsGmc9hyyI6+QR3rR1zVRu36B0NeGXKnOOLiZOfER5SA+N7X7k3yUYRzLWafduTDvJAfDswwEww==", "license": "MIT", "resolved": "https://registry.npmjs.org/sonic-boom/-/sonic-boom-4.2.0.tgz", "version": "4.2.0"}, "node_modules/source-map": {"dev": true, "engines": {"node": ">=0.10.0"}, "integrity": "sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==", "license": "BSD-3-<PERSON><PERSON>", "resolved": "https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz", "version": "0.6.1"}, "node_modules/source-map-support": {"dependencies": {"buffer-from": "^1.0.0", "source-map": "^0.6.0"}, "dev": true, "integrity": "sha512-uBHU3L3czsIyYXKX88fdrGovxdSCoTGDRZ6SYXtSRxLZUzHg5P/66Ht6uoUlHu9EZod+inXhKo3qQgwXUT/y1w==", "license": "MIT", "resolved": "https://registry.npmjs.org/source-map-support/-/source-map-support-0.5.21.tgz", "version": "0.5.21"}, "node_modules/split2": {"engines": {"node": ">= 10.x"}, "integrity": "sha512-UcjcJOWknrNkF6PLX83qcHM6KHgVKNkV62Y8a5uYDVv9ydGQVwAHMKqHdJje1VTWpljG0WYpCDhrCdAOYH4TWg==", "license": "ISC", "resolved": "https://registry.npmjs.org/split2/-/split2-4.2.0.tgz", "version": "4.2.0"}, "node_modules/sprintf-js": {"dev": true, "integrity": "sha512-Oo+0REFV59/rz3gfJNKQiBlwfHaSESl1pcGyABQsnnIfWOFt6JNj5gCog2U6MLZ//IGYD+nA8nI+mTShREReaA==", "license": "BSD-3-<PERSON><PERSON>", "optional": true, "resolved": "https://registry.npmjs.org/sprintf-js/-/sprintf-js-1.1.3.tgz", "version": "1.1.3"}, "node_modules/stack-utils": {"dependencies": {"escape-string-regexp": "^2.0.0"}, "dev": true, "engines": {"node": ">=10"}, "integrity": "sha512-XlkWvfIm6RmsWtNJx+uqtKLS8eqFbxUg0ZzLXqY0caEy9l7hruX8IpiDnjsLavoBgqCCR71TqWO8MaXYheJ3RQ==", "license": "MIT", "resolved": "https://registry.npmjs.org/stack-utils/-/stack-utils-2.0.6.tgz", "version": "2.0.6"}, "node_modules/stack-utils/node_modules/escape-string-regexp": {"dev": true, "engines": {"node": ">=8"}, "integrity": "sha512-UpzcLCXolUWcNu5HtVMHYdXJjArjsF9C0aNnquZYY4uW/Vu0miy5YoWvbV345HauVvcAUnpRuhMMcqTcGOY2+w==", "license": "MIT", "resolved": "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-2.0.0.tgz", "version": "2.0.0"}, "node_modules/stat-mode": {"dev": true, "engines": {"node": ">= 6"}, "integrity": "sha512-jH9EhtKIjuXZ2cWxmXS8ZP80XyC3iasQxMDV8jzhNJpfDb7VbQLVW4Wvsxz9QZvzV+G4YoSfBUVKDOyxLzi/sg==", "license": "MIT", "resolved": "https://registry.npmjs.org/stat-mode/-/stat-mode-1.0.0.tgz", "version": "1.0.0"}, "node_modules/streamx": {"dependencies": {"fast-fifo": "^1.3.2", "text-decoder": "^1.1.0"}, "integrity": "sha512-znKXEBxfatz2GBNK02kRnCXjV+AA4kjZIUxeWSr3UGirZMJfTE9uiwKHobnbgxWyL/JWro8tTq+vOqAK1/qbSA==", "license": "MIT", "optionalDependencies": {"bare-events": "^2.2.0"}, "resolved": "https://registry.npmjs.org/streamx/-/streamx-2.22.1.tgz", "version": "2.22.1"}, "node_modules/string-length": {"dependencies": {"char-regex": "^1.0.2", "strip-ansi": "^6.0.0"}, "dev": true, "engines": {"node": ">=10"}, "integrity": "sha512-+l6rNN5fYHNhZZy41RXsYptCjA2Igmq4EG7kZAYFQI1E1VTXarr6ZPXBg6eq7Y6eK4FEhY6AJlyuFIb/v/S0VQ==", "license": "MIT", "resolved": "https://registry.npmjs.org/string-length/-/string-length-4.0.2.tgz", "version": "4.0.2"}, "node_modules/string-width": {"dependencies": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=8"}, "integrity": "sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==", "license": "MIT", "resolved": "https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz", "version": "4.2.3"}, "node_modules/string-width-cjs": {"dependencies": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}, "dev": true, "engines": {"node": ">=8"}, "integrity": "sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==", "license": "MIT", "name": "string-width", "resolved": "https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz", "version": "4.2.3"}, "node_modules/string_decoder": {"dependencies": {"safe-buffer": "~5.2.0"}, "integrity": "sha512-hkRX8U1WjJFd8LsDJ2yQ/wWWxaopEsABU1XfkM8A+j0+85JAGppt16cr1Whg6KIbb4okU6Mql6BOj+uup/wKeA==", "license": "MIT", "resolved": "https://registry.npmjs.org/string_decoder/-/string_decoder-1.3.0.tgz", "version": "1.3.0"}, "node_modules/strip-ansi": {"dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}, "integrity": "sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==", "license": "MIT", "resolved": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz", "version": "6.0.1"}, "node_modules/strip-ansi-cjs": {"dependencies": {"ansi-regex": "^5.0.1"}, "dev": true, "engines": {"node": ">=8"}, "integrity": "sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==", "license": "MIT", "name": "strip-ansi", "resolved": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz", "version": "6.0.1"}, "node_modules/strip-bom": {"dev": true, "engines": {"node": ">=8"}, "integrity": "sha512-3xurFv5tEgii33Zi8Jtp55wEIILR9eh34FAW00PZf+JnSsTmV/ioewSgQl97JHvgjoRGwPShsWm+IdrxB35d0w==", "license": "MIT", "resolved": "https://registry.npmjs.org/strip-bom/-/strip-bom-4.0.0.tgz", "version": "4.0.0"}, "node_modules/strip-final-newline": {"dev": true, "engines": {"node": ">=6"}, "integrity": "sha512-BrpvfNAE3dcvq7ll3xVumzjKjZQ5tI1sEUIKr3Uoks0XUl45St3FlatVqef9prk4jRDzhW6WZg+3bk93y6pLjA==", "license": "MIT", "resolved": "https://registry.npmjs.org/strip-final-newline/-/strip-final-newline-2.0.0.tgz", "version": "2.0.0"}, "node_modules/strip-json-comments": {"dev": true, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}, "integrity": "sha512-6fPc+R4ihwqP6N/aIv2f1gMH8lOVtWQHoqC4yK6oSDVVocumAsfCqjkXnqiYMhmMwS/mEHLp7Vehlt3ql6lEig==", "license": "MIT", "resolved": "https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-3.1.1.tgz", "version": "3.1.1"}, "node_modules/sumchecker": {"dependencies": {"debug": "^4.1.0"}, "dev": true, "engines": {"node": ">= 8.0"}, "integrity": "sha512-MvjXzkz/BOfyVDkG0oFOtBxHX2u3gKbMHIF/dXblZsgD3BWOFLmHovIpZY7BykJdAjcqRCBi1WYBNdEC9yI7vg==", "license": "Apache-2.0", "resolved": "https://registry.npmjs.org/sumchecker/-/sumchecker-3.0.1.tgz", "version": "3.0.1"}, "node_modules/supports-color": {"dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}, "integrity": "sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==", "license": "MIT", "resolved": "https://registry.npmjs.org/supports-color/-/supports-color-7.2.0.tgz", "version": "7.2.0"}, "node_modules/supports-preserve-symlinks-flag": {"dev": true, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "integrity": "sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==", "license": "MIT", "resolved": "https://registry.npmjs.org/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz", "version": "1.0.0"}, "node_modules/tar": {"dependencies": {"chownr": "^2.0.0", "fs-minipass": "^2.0.0", "minipass": "^5.0.0", "minizlib": "^2.1.1", "mkdirp": "^1.0.3", "yallist": "^4.0.0"}, "dev": true, "engines": {"node": ">=10"}, "integrity": "sha512-DZ4yORTwrbTj/7MZYq2w+/ZFdI6OZ/f9SFHR+71gIVUZhOQPHzVCLpvRnPgyaMpfWxxk/4ONva3GQSyNIKRv6A==", "license": "ISC", "resolved": "https://registry.npmjs.org/tar/-/tar-6.2.1.tgz", "version": "6.2.1"}, "node_modules/tar-stream": {"dependencies": {"b4a": "^1.6.4", "fast-fifo": "^1.2.0", "streamx": "^2.15.0"}, "integrity": "sha512-qJj60CXt7IU1Ffyc3NJMjh6EkuCFej46zUqJ4J7pqYlThyd9bO0XBTmcOIhSzZJVWfsLks0+nle/j538YAW9RQ==", "license": "MIT", "resolved": "https://registry.npmjs.org/tar-stream/-/tar-stream-3.1.7.tgz", "version": "3.1.7"}, "node_modules/temp-file": {"dependencies": {"async-exit-hook": "^2.0.1", "fs-extra": "^10.0.0"}, "dev": true, "integrity": "sha512-C5tjlC/HCtVUOi3KWVokd4vHVViOmGjtLwIh4MuzPo/nMYTV/p1urt3RnMz2IWXDdKEGJH3k5+KPxtqRsUYGtg==", "license": "MIT", "resolved": "https://registry.npmjs.org/temp-file/-/temp-file-3.4.0.tgz", "version": "3.4.0"}, "node_modules/temp-file/node_modules/fs-extra": {"dependencies": {"graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}, "dev": true, "engines": {"node": ">=12"}, "integrity": "sha512-oRXApq54ETRj4eMiFzGnHWGy+zo5raudjuxN0b8H7s/RU2oW0Wvsx9O0ACRN/kRq9E8Vu/ReskGB5o3ji+FzHQ==", "license": "MIT", "resolved": "https://registry.npmjs.org/fs-extra/-/fs-extra-10.1.0.tgz", "version": "10.1.0"}, "node_modules/temp-file/node_modules/jsonfile": {"dependencies": {"universalify": "^2.0.0"}, "dev": true, "integrity": "sha512-5dgndWOriYSm5cnYaJNhalLNDKOqFwyDB/rr1E9ZsGciGvKPs8R2xYGCacuf3z6K1YKDz182fd+fY3cn3pMqXQ==", "license": "MIT", "optionalDependencies": {"graceful-fs": "^4.1.6"}, "resolved": "https://registry.npmjs.org/jsonfile/-/jsonfile-6.1.0.tgz", "version": "6.1.0"}, "node_modules/temp-file/node_modules/universalify": {"dev": true, "engines": {"node": ">= 10.0.0"}, "integrity": "sha512-gptHNQghINnc/vTGIk0SOFGFNXw7JVrlRUtConJRlvaw6DuX0wO5Jeko9sWrMBhh+PsYAZ7oXAiOnf/UKogyiw==", "license": "MIT", "resolved": "https://registry.npmjs.org/universalify/-/universalify-2.0.1.tgz", "version": "2.0.1"}, "node_modules/test-exclude": {"dependencies": {"@istanbuljs/schema": "^0.1.2", "glob": "^7.1.4", "minimatch": "^3.0.4"}, "dev": true, "engines": {"node": ">=8"}, "integrity": "sha512-cAGWPIyOHU6zlmg88jwm7VRyXnMN7iV68OGAbYDk/Mh/xC/pzVPlQtY6ngoIH/5/tciuhGfvESU8GrHrcxD56w==", "license": "ISC", "resolved": "https://registry.npmjs.org/test-exclude/-/test-exclude-6.0.0.tgz", "version": "6.0.0"}, "node_modules/test-exclude/node_modules/brace-expansion": {"dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}, "dev": true, "integrity": "sha512-9T9UjW3r0UW5c1Q7GTwllptXwhvYmEzFhzMfZ9H7FQWt+uZePjZPjBP/W1ZEyZ1twGWom5/56TF4lPcqjnDHcg==", "license": "MIT", "resolved": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.12.tgz", "version": "1.1.12"}, "node_modules/test-exclude/node_modules/minimatch": {"dependencies": {"brace-expansion": "^1.1.7"}, "dev": true, "engines": {"node": "*"}, "integrity": "sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==", "license": "ISC", "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-3.1.2.tgz", "version": "3.1.2"}, "node_modules/text-decoder": {"dependencies": {"b4a": "^1.6.4"}, "integrity": "sha512-3/o9z3X0X0fTupwsYvR03pJ/DjWuqqrfwBgTQzdWDiQSm9KitAyz/9WqsT2JQW7KV2m+bC2ol/zqpW37NHxLaA==", "license": "Apache-2.0", "resolved": "https://registry.npmjs.org/text-decoder/-/text-decoder-1.2.3.tgz", "version": "1.2.3"}, "node_modules/thread-stream": {"dependencies": {"real-require": "^0.2.0"}, "integrity": "sha512-OqyPZ9u96VohAyMfJykzmivOrY2wfMSf3C5TtFJVgN+Hm6aj+voFhlK+kZEIv2FBh1X6Xp3DlnCOfEQ3B2J86A==", "license": "MIT", "resolved": "https://registry.npmjs.org/thread-stream/-/thread-stream-3.1.0.tgz", "version": "3.1.0"}, "node_modules/through": {"integrity": "sha512-w89qg7PI8wAdvX60bMDP+bFoD5Dvhm9oLheFp5O4a2QF0cSBGsBX4qZmadPMvVqlLJBBci+WqGGOAPvcDeNSVg==", "license": "MIT", "resolved": "https://registry.npmjs.org/through/-/through-2.3.8.tgz", "version": "2.3.8"}, "node_modules/tiny-typed-emitter": {"integrity": "sha512-qVtvMxeXbVej0cQWKqVSSAHmKZEHAvxdF8HEUBFWts8h+xEo5m/lEiPakuyZ3BnCBjOD8i24kzNOiOLLgsSxhA==", "license": "MIT", "resolved": "https://registry.npmjs.org/tiny-typed-emitter/-/tiny-typed-emitter-2.1.0.tgz", "version": "2.1.0"}, "node_modules/tmp": {"dev": true, "engines": {"node": ">=14.14"}, "integrity": "sha512-nZD7m9iCPC5g0pYmcaxogYKggSfLsdxl8of3Q/oIbqCqLLIO9IAF0GWjX1z9NZRHPiXv8Wex4yDCaZsgEw0Y8w==", "license": "MIT", "resolved": "https://registry.npmjs.org/tmp/-/tmp-0.2.3.tgz", "version": "0.2.3"}, "node_modules/tmp-promise": {"dependencies": {"tmp": "^0.2.0"}, "dev": true, "integrity": "sha512-RwM7MoPojPxsOBYnyd2hy0bxtIlVrihNs9pj5SUvY8Zz1sQcQG2tG1hSr8PDxfgEB8RNKDhqbIlroIarSNDNsQ==", "license": "MIT", "resolved": "https://registry.npmjs.org/tmp-promise/-/tmp-promise-3.0.3.tgz", "version": "3.0.3"}, "node_modules/tmpl": {"dev": true, "integrity": "sha512-3f0uOEAQwIqGuWW2MVzYg8fV/QNnc/IpuJNG837rLuczAaLVHslWHZQj4IGiEl5Hs3kkbhwL9Ab7Hrsmuj+Smw==", "license": "BSD-3-<PERSON><PERSON>", "resolved": "https://registry.npmjs.org/tmpl/-/tmpl-1.0.5.tgz", "version": "1.0.5"}, "node_modules/to-regex-range": {"dependencies": {"is-number": "^7.0.0"}, "dev": true, "engines": {"node": ">=8.0"}, "integrity": "sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==", "license": "MIT", "resolved": "https://registry.npmjs.org/to-regex-range/-/to-regex-range-5.0.1.tgz", "version": "5.0.1"}, "node_modules/truncate-utf8-bytes": {"dependencies": {"utf8-byte-length": "^1.0.1"}, "dev": true, "integrity": "sha512-95Pu1QXQvruGEhv62XCMO3Mm90GscOCClvrIUwCM0PYOXK3kaF3l3sIHxx71ThJfcbM2O5Au6SO3AWCSEfW4mQ==", "license": "WTFPL", "resolved": "https://registry.npmjs.org/truncate-utf8-bytes/-/truncate-utf8-bytes-1.0.2.tgz", "version": "1.0.2"}, "node_modules/tslib": {"integrity": "sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==", "license": "0BSD", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.8.1.tgz", "version": "2.8.1"}, "node_modules/type-detect": {"dev": true, "engines": {"node": ">=4"}, "integrity": "sha512-0fr/mIH1dlO+x7TlcMy+bIDqKPsw/70tVyeHW787goQjhmqaZe10uwLujubK9q9Lg6Fiho1KUKDYz0Z7k7g5/g==", "license": "MIT", "resolved": "https://registry.npmjs.org/type-detect/-/type-detect-4.0.8.tgz", "version": "4.0.8"}, "node_modules/type-fest": {"dev": true, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}, "integrity": "sha512-34R7HTnG0XIJcBSn5XhDd7nNFPRcXYRZrBB2O2jdKqYODldSzBAqzsWoZYYvduky73toYS/ESqxPvkDf/F0XMg==", "license": "(MIT OR CC0-1.0)", "optional": true, "resolved": "https://registry.npmjs.org/type-fest/-/type-fest-0.13.1.tgz", "version": "0.13.1"}, "node_modules/typescript": {"bin": {"tsc": "bin/tsc", "tsserver": "bin/tsserver"}, "dev": true, "engines": {"node": ">=14.17"}, "integrity": "sha512-p1diW6TqL9L07nNxvRMM7hMMw4c5XOo/1ibL4aAIGmSAt9slTE1Xgw5KWuof2uTOvCg9BY7ZRi+GaF+7sfgPeQ==", "license": "Apache-2.0", "resolved": "https://registry.npmjs.org/typescript/-/typescript-5.8.3.tgz", "version": "5.8.3"}, "node_modules/undici-types": {"dev": true, "integrity": "sha512-iwDZqg0QAGrg9Rav5H4n0M64c3mkR59cJ6wQp+7C4nI0gsmExaedaYLNO44eT4AtBBwjbTiGPMlt2Md0T9H9JQ==", "license": "MIT", "resolved": "https://registry.npmjs.org/undici-types/-/undici-types-6.21.0.tgz", "version": "6.21.0"}, "node_modules/universalify": {"dev": true, "engines": {"node": ">= 4.0.0"}, "integrity": "sha512-rBJeI5CXAlmy1pV+617WB9J63U6XcazHHF2f2dbJix4XzpUF0RS3Zbj0FGIOCAva5P/d/GBOYaACQ1w+0azUkg==", "license": "MIT", "resolved": "https://registry.npmjs.org/universalify/-/universalify-0.1.2.tgz", "version": "0.1.2"}, "node_modules/update-browserslist-db": {"bin": {"update-browserslist-db": "cli.js"}, "dependencies": {"escalade": "^3.2.0", "picocolors": "^1.1.1"}, "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/browserslist"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "integrity": "sha512-UxhIZQ+QInVdunkDAaiazvvT/+fXL5Osr0JZlJulepYu6Jd7qJtDZjlur0emRlT71EN3ScPoE7gvsuIKKNavKw==", "license": "MIT", "peerDependencies": {"browserslist": ">= 4.21.0"}, "resolved": "https://registry.npmjs.org/update-browserslist-db/-/update-browserslist-db-1.1.3.tgz", "version": "1.1.3"}, "node_modules/uri-js": {"dependencies": {"punycode": "^2.1.0"}, "dev": true, "integrity": "sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg==", "license": "BSD-2-<PERSON><PERSON>", "resolved": "https://registry.npmjs.org/uri-js/-/uri-js-4.4.1.tgz", "version": "4.4.1"}, "node_modules/utf8-byte-length": {"dev": true, "integrity": "sha512-Xn0w3MtiQ6zoz2vFyUVruaCL53O/DwUvkEeOvj+uulMm0BkUGYWmBYVyElqZaSLhY6ZD0ulfU3aBra2aVT4xfA==", "license": "(WTFPL OR MIT)", "resolved": "https://registry.npmjs.org/utf8-byte-length/-/utf8-byte-length-1.0.5.tgz", "version": "1.0.5"}, "node_modules/util-deprecate": {"integrity": "sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw==", "license": "MIT", "resolved": "https://registry.npmjs.org/util-deprecate/-/util-deprecate-1.0.2.tgz", "version": "1.0.2"}, "node_modules/v8-to-istanbul": {"dependencies": {"@jridgewell/trace-mapping": "^0.3.12", "@types/istanbul-lib-coverage": "^2.0.1", "convert-source-map": "^2.0.0"}, "dev": true, "engines": {"node": ">=10.12.0"}, "integrity": "sha512-kiGUalWN+rgBJ/1OHZsBtU4rXZOfj/7rKQxULKlIzwzQSvMJUUNgPwJEEh7gU6xEVxC0ahoOBvN2YI8GH6FNgA==", "license": "ISC", "resolved": "https://registry.npmjs.org/v8-to-istanbul/-/v8-to-istanbul-9.3.0.tgz", "version": "9.3.0"}, "node_modules/verror": {"dependencies": {"assert-plus": "^1.0.0", "core-util-is": "1.0.2", "extsprintf": "^1.2.0"}, "dev": true, "engines": {"node": ">=0.6.0"}, "integrity": "sha512-veufcmxri4e3XSrT0xwfUR7kguIkaxBeosDg00yDWhk49wdwkSUrvvsm7nc75e1PUyvIeZj6nS8VQRYz2/S4Xg==", "license": "MIT", "optional": true, "resolved": "https://registry.npmjs.org/verror/-/verror-1.10.1.tgz", "version": "1.10.1"}, "node_modules/walker": {"dependencies": {"makeerror": "1.0.12"}, "dev": true, "integrity": "sha512-ts/8E8l5b7kY0vlWLewOkDXMmPdLcVV4GmOQLyxuSswIJsweeFZtAsMF7k1Nszz+TYBQrlYRmzOnr398y1JemQ==", "license": "Apache-2.0", "resolved": "https://registry.npmjs.org/walker/-/walker-1.0.8.tgz", "version": "1.0.8"}, "node_modules/wcwidth": {"dependencies": {"defaults": "^1.0.3"}, "integrity": "sha512-XHPEwS0q6TaxcvG85+8EYkbiCux2XtWG2mkc47Ng2A77BQu9+DqIOJldST4HgPkuea7dvKSj5VgX3P1d4rW8Tg==", "license": "MIT", "resolved": "https://registry.npmjs.org/wcwidth/-/wcwidth-1.0.1.tgz", "version": "1.0.1"}, "node_modules/which": {"bin": {"node-which": "bin/node-which"}, "dependencies": {"isexe": "^2.0.0"}, "dev": true, "engines": {"node": ">= 8"}, "integrity": "sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==", "license": "ISC", "resolved": "https://registry.npmjs.org/which/-/which-2.0.2.tgz", "version": "2.0.2"}, "node_modules/wrap-ansi": {"dependencies": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}, "dev": true, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/wrap-ansi?sponsor=1"}, "integrity": "sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q==", "license": "MIT", "resolved": "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-7.0.0.tgz", "version": "7.0.0"}, "node_modules/wrap-ansi-cjs": {"dependencies": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}, "dev": true, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/wrap-ansi?sponsor=1"}, "integrity": "sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q==", "license": "MIT", "name": "wrap-ansi", "resolved": "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-7.0.0.tgz", "version": "7.0.0"}, "node_modules/wrappy": {"integrity": "sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ==", "license": "ISC", "resolved": "https://registry.npmjs.org/wrappy/-/wrappy-1.0.2.tgz", "version": "1.0.2"}, "node_modules/write-file-atomic": {"dependencies": {"imurmurhash": "^0.1.4", "signal-exit": "^3.0.7"}, "dev": true, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}, "integrity": "sha512-7KxauUdBmSdWnmpaGFg+ppNjKF8uNLry8LyzjauQDOVONfFLNKrKvQOxZ/VuTIcS/gge/YNahf5RIIQWTSarlg==", "license": "ISC", "resolved": "https://registry.npmjs.org/write-file-atomic/-/write-file-atomic-4.0.2.tgz", "version": "4.0.2"}, "node_modules/write-file-atomic/node_modules/signal-exit": {"dev": true, "integrity": "sha512-wnD2ZE+l+SPC/uoS0vXeE9L1+0wuaMqKlfz9AMUo38JsyLSBWSFcHR1Rri62LZc12vLr1gb3jl7iwQhgwpAbGQ==", "license": "ISC", "resolved": "https://registry.npmjs.org/signal-exit/-/signal-exit-3.0.7.tgz", "version": "3.0.7"}, "node_modules/xmlbuilder": {"dev": true, "engines": {"node": ">=8.0"}, "integrity": "sha512-yMqGBqtXyeN1e3TGYvgNgDVZ3j84W4cwkOXQswghol6APgZWaff9lnbvN7MHYJOiXsvGPXtjTYJEiC9J2wv9Eg==", "license": "MIT", "resolved": "https://registry.npmjs.org/xmlbuilder/-/xmlbuilder-15.1.1.tgz", "version": "15.1.1"}, "node_modules/y18n": {"dev": true, "engines": {"node": ">=10"}, "integrity": "sha512-0pfFzegeDWJHJIAmTLRP2DwHjdF5s7jo9tuztdQxAhINCdvS+3nGINqPd00AphqJR/0LhANUS6/+7SCb98YOfA==", "license": "ISC", "resolved": "https://registry.npmjs.org/y18n/-/y18n-5.0.8.tgz", "version": "5.0.8"}, "node_modules/yallist": {"dev": true, "integrity": "sha512-3wdGidZyq5PB084XLES5TpOSRA3wjXAlIWMhum2kRcv/41Sn2emQ0dycQW4uZXLejwKvg6EsvbdlVL+FYEct7A==", "license": "ISC", "resolved": "https://registry.npmjs.org/yallist/-/yallist-4.0.0.tgz", "version": "4.0.0"}, "node_modules/yargs": {"dependencies": {"cliui": "^8.0.1", "escalade": "^3.1.1", "get-caller-file": "^2.0.5", "require-directory": "^2.1.1", "string-width": "^4.2.3", "y18n": "^5.0.5", "yargs-parser": "^21.1.1"}, "dev": true, "engines": {"node": ">=12"}, "integrity": "sha512-7dSzzRQ++CKnNI/krKnYRV7JKKPUXMEh61soaHKg9mrWEhzFWhFnxPxGl+69cD1Ou63C13NUPCnmIcrvqCuM6w==", "license": "MIT", "resolved": "https://registry.npmjs.org/yargs/-/yargs-17.7.2.tgz", "version": "17.7.2"}, "node_modules/yargs-parser": {"dev": true, "engines": {"node": ">=12"}, "integrity": "sha512-tVpsJW7DdjecAiFpbIB1e3qxIQsE6NoPc5/eTdrbbIC4h0LVsWhnoa3g+m2HclBIujHzsxZ4VJVA+GUuc2/LBw==", "license": "ISC", "resolved": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-21.1.1.tgz", "version": "21.1.1"}, "node_modules/yauzl": {"dependencies": {"buffer-crc32": "~0.2.3", "pend": "~1.2.0"}, "engines": {"node": ">=12"}, "integrity": "sha512-Ow9nuGZE+qp1u4JIPvg+uCiUr7xGQWdff7JQSk5VGYTAZMDe2q8lxJ10ygv10qmSj031Ty/6FNJpLO4o1Sgc+w==", "license": "MIT", "resolved": "https://registry.npmjs.org/yauzl/-/yauzl-3.2.0.tgz", "version": "3.2.0"}, "node_modules/yocto-queue": {"dev": true, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}, "integrity": "sha512-rVksvsnNCdJ/ohGc6xgPwyN8eheCxsiLM8mxuE/t/mOVqJewPuO1miLpTHQiRgTKCLexL4MeAFVagts7HmNZ2Q==", "license": "MIT", "resolved": "https://registry.npmjs.org/yocto-queue/-/yocto-queue-0.1.0.tgz", "version": "0.1.0"}, "node_modules/zip-stream": {"dependencies": {"archiver-utils": "^4.0.1", "compress-commons": "^5.0.1", "readable-stream": "^3.6.0"}, "engines": {"node": ">= 12.0.0"}, "integrity": "sha512-LfOdrUvPB8ZoXtvOBz6DlNClfvi//b5d56mSWyJi7XbH/HfhOHfUhOqxhT/rUiR7yiktlunqRo+jY6y/cWC/5g==", "license": "MIT", "resolved": "https://registry.npmjs.org/zip-stream/-/zip-stream-5.0.2.tgz", "version": "5.0.2"}}, "requires": true, "version": "0.1.0"}