/**
 * Plugin API - Core Interface for Lifeboard Plugins
 *
 * This module provides the Plugin API that is injected into plugin contexts.
 * It exposes controlled access to Lifeboard functionality including the new CoreLogger.
 *
 * @version 1.0.0
 * <AUTHOR> Team
 */

const { factory: createLogger } = require('../logger/CoreLogger');
const log = createLogger('pluginAPI');

/**
 * Creates a logger instance for a plugin
 * @param {string} pluginName - The name of the plugin requesting logger
 * @returns {Object} Logger instance with CoreLogger functionality
 */
function getLogger(pluginName) {
    log.DEBUG('Creating logger for plugin', { pluginName });

    try {
        // Create component-specific logger using CoreLogger factory
        const pluginLogger = createLogger(`plugin:${pluginName}`);

        log.DEBUG('Plugin logger created successfully', {
            pluginName,
            component: pluginLogger.component
        });

        return {
            // Core logging methods
            DEBUG: (msg, meta = {}, corrId = null) => pluginLogger.DEBUG(msg, meta, corrId),
            INFO: (msg, meta = {}, corrId = null) => pluginLogger.INFO(msg, meta, corrId),
            WARN: (msg, meta = {}, corrId = null) => pluginLogger.WARN(msg, meta, corrId),
            ERROR: (msg, meta = {}, corrId = null) => pluginLogger.ERROR(msg, meta, corrId),
            FATAL: (msg, meta = {}, corrId = null) => pluginLogger.FATAL(msg, meta, corrId),

            // Utility methods
            getLevel: () => pluginLogger.getLevel(),
            getStats: () => pluginLogger.getStats(),

            // Backward compatibility with existing plugin patterns
            info: (msg, meta = {}) => pluginLogger.INFO(msg, meta),
            warn: (msg, meta = {}) => pluginLogger.WARN(msg, meta),
            error: (msg, error = null, meta = {}) => {
                const errorMeta = error ? { ...meta, error: error.message, stack: error.stack } : meta;
                pluginLogger.ERROR(msg, errorMeta);
            },
            debug: (msg, meta = {}) => pluginLogger.DEBUG(msg, meta),

            // Performance timing utilities
            startTimer: (operation) => {
                const startTime = Date.now();
                return {
                    stop: () => {
                        const duration = Date.now() - startTime;
                        pluginLogger.DEBUG('Operation completed', {
                            operation,
                            duration: `${duration}ms`
                        });
                        return duration;
                    }
                };
            },

            // Specialized logging methods for plugins
            logApiCall: async (endpoint, options = {}) => {
                const callId = `api_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
                pluginLogger.INFO('API call started', {
                    callId,
                    endpoint,
                    method: options.method || 'GET'
                });
                return callId;
            },

            logApiResponse: (callId, response, duration) => {
                pluginLogger.INFO('API call completed', {
                    callId,
                    status: response.status || 'unknown',
                    duration: `${duration}ms`,
                    success: response.ok !== false
                });
            },

            logUserAction: (action, context = {}) => {
                pluginLogger.INFO('User action', {
                    action,
                    ...context
                });
            },

            logDataProcessing: (operation, stats = {}) => {
                pluginLogger.INFO('Data processing', {
                    operation,
                    ...stats
                });
            }
        };

    } catch (error) {
        log.ERROR('Failed to create plugin logger', {
            pluginName,
            error: error.message,
            stack: error.stack
        });

        // Return fallback logger to prevent plugin failure
        return createFallbackLogger(pluginName);
    }
}

/**
 * Creates a fallback logger in case CoreLogger initialization fails
 * @param {string} pluginName - Plugin name for fallback logger
 * @returns {Object} Fallback logger implementation
 */
function createFallbackLogger(pluginName) {
    const prefix = `[Plugin:${pluginName}]`;

    return {
        DEBUG: (msg, meta = {}) => console.debug(prefix, 'DEBUG:', msg, meta),
        INFO: (msg, meta = {}) => console.info(prefix, 'INFO:', msg, meta),
        WARN: (msg, meta = {}) => console.warn(prefix, 'WARN:', msg, meta),
        ERROR: (msg, meta = {}) => console.error(prefix, 'ERROR:', msg, meta),
        FATAL: (msg, meta = {}) => console.error(prefix, 'FATAL:', msg, meta),

        // Backward compatibility
        info: (msg, meta = {}) => console.info(prefix, 'INFO:', msg, meta),
        warn: (msg, meta = {}) => console.warn(prefix, 'WARN:', msg, meta),
        error: (msg, error = null, meta = {}) => console.error(prefix, 'ERROR:', msg, error, meta),
        debug: (msg, meta = {}) => console.debug(prefix, 'DEBUG:', msg, meta),

        getLevel: () => 'DEBUG',
        getStats: () => ({ fallback: true }),

        startTimer: (operation) => {
            const startTime = Date.now();
            return {
                stop: () => {
                    const duration = Date.now() - startTime;
                    console.debug(prefix, 'DEBUG: Operation completed', { operation, duration: `${duration}ms` });
                    return duration;
                }
            };
        },

        logApiCall: async (endpoint, options = {}) => {
            const callId = `api_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
            console.info(prefix, 'INFO: API call started', { callId, endpoint, method: options.method || 'GET' });
            return callId;
        },

        logApiResponse: (callId, response, duration) => {
            console.info(prefix, 'INFO: API call completed', { callId, status: response.status, duration: `${duration}ms` });
        },

        logUserAction: (action, context = {}) => {
            console.info(prefix, 'INFO: User action', { action, ...context });
        },

        logDataProcessing: (operation, stats = {}) => {
            console.info(prefix, 'INFO: Data processing', { operation, ...stats });
        }
    };
}

/**
 * Initialize PluginAPI module
 */
function initialize() {
    log.INFO('PluginAPI module initialized', {
        module: 'core/pluginAPI',
        exports: ['getLogger']
    });
}

// Initialize on module load
initialize();

module.exports = {
    getLogger,
    initialize
};
