const crypto = require('crypto');

const VAULT_ENC_KEY = process.env.VAULT_ENC_KEY || 'default_vault_enc_key';
const ALGORITHM = 'aes-256-cbc';
const IV_LENGTH = 16; // For AES, this is always 16

/**
 * Encrypts a string using AES-256-CBC with a random IV
 * @param {string} text - The text to encrypt
 * @returns {string} - The encrypted text with IV prepended
 */
function encryptString(text) {
    if (typeof text !== 'string') {
        throw new TypeError('Input must be a string');
    }

    // Generate a random IV
    const iv = crypto.randomBytes(IV_LENGTH);

    // Create a key from the VAULT_ENC_KEY
    const key = crypto.scryptSync(VAULT_ENC_KEY, 'salt', 32);

    // Create cipher
    const cipher = crypto.createCipheriv(ALGORITHM, key, iv);

    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');

    // Prepend IV to the encrypted text
    return iv.toString('hex') + ':' + encrypted;
}

/**
 * Decrypts a string using AES-256-CBC
 * @param {string} encryptedText - The encrypted text with IV prepended
 * @returns {string} - The decrypted text
 */
function decryptString(encryptedText) {
    if (typeof encryptedText !== 'string') {
        throw new TypeError('Input must be a string');
    }

    // Split the IV and encrypted text
    const parts = encryptedText.split(':');
    if (parts.length !== 2) {
        throw new Error('Invalid encrypted data format');
    }

    const iv = Buffer.from(parts[0], 'hex');
    const encrypted = parts[1];

    // Create a key from the VAULT_ENC_KEY
    const key = crypto.scryptSync(VAULT_ENC_KEY, 'salt', 32);

    // Create decipher
    const decipher = crypto.createDecipheriv(ALGORITHM, key, iv);

    let decrypted = decipher.update(encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');

    return decrypted;
}

module.exports = {
    encryptString,
    decryptString
};
