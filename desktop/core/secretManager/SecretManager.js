/**
 * SecretManager - Centralized API Key Management
 *
 * This class provides a unified interface for storing and retrieving API keys
 * with support for both local storage (with encryption) and database storage.
 *
 * @version 1.0.0
 * <AUTHOR> Team
 */

const fs = require('fs').promises;
const path = require('path');
const { factory: createLogger } = require('../logger/CoreLogger');
const { encryptString, decryptString } = require('../pluginAPI/cryptoUtilities');
const { getClient, isAuthenticated } = require('../supabase/client');

const log = createLogger('secret-manager');

/**
 * SecretManager Class
 *
 * Handles API key storage and retrieval with fallback mechanisms:
 * 1. Primary: Database storage (when authenticated)
 * 2. Fallback: Local encrypted storage
 */
class SecretManager {
    constructor() {
        this.localStoragePath = path.join(process.cwd(), 'secure_storage.json');
        this.initialized = false;
        this.localCache = new Map();

        log.INFO('SecretManager initialized', {
            localStoragePath: this.localStoragePath
        });
    }

    /**
     * Initialize the SecretManager
     * @returns {Promise<void>}
     */
    async initialize() {
        if (this.initialized) {
            return;
        }

        try {
            // Load local storage if exists
            await this.loadLocalStorage();
            this.initialized = true;

            log.INFO('SecretManager initialization complete', {
                localCacheSize: this.localCache.size,
                hasLocalStorage: await this.hasLocalStorage()
            });
        } catch (error) {
            log.ERROR('SecretManager initialization failed', {
                error: error.message,
                stack: error.stack
            });
            throw error;
        }
    }

    /**
     * Store an API key
     * @param {string} pluginId - Plugin identifier
     * @param {string} keyName - Key name/identifier
     * @param {string} apiKey - The API key to store
     * @returns {Promise<string>} Storage result ID
     */
    async storeAPIKey(pluginId, keyName, apiKey) {
        try {
            log.INFO('Storing API key', {
                pluginId,
                keyName,
                keyLength: apiKey.length,
                hasKey: !!apiKey
            });

            // Try database storage first if authenticated
            if (await isAuthenticated()) {
                try {
                    const result = await this.storeToDatabase(pluginId, keyName, apiKey);
                    log.INFO('API key stored to database', {
                        pluginId,
                        keyName,
                        resultId: result
                    });
                    return result;
                } catch (dbError) {
                    log.WARN('Database storage failed, falling back to local storage', {
                        pluginId,
                        keyName,
                        error: dbError.message
                    });
                }
            }

            // Fallback to local storage
            const result = await this.storeToLocal(pluginId, keyName, apiKey);
            log.INFO('API key stored to local storage', {
                pluginId,
                keyName,
                resultId: result
            });
            return result;

        } catch (error) {
            log.ERROR('Failed to store API key', {
                pluginId,
                keyName,
                error: error.message,
                stack: error.stack
            });
            throw error;
        }
    }

    /**
     * Retrieve an API key
     * @param {string} pluginId - Plugin identifier
     * @param {string} keyName - Key name/identifier (optional)
     * @returns {Promise<string|Object>} The API key or keys object
     */
    async retrieveAPIKey(pluginId, keyName = null) {
        try {
            log.DEBUG('Retrieving API key', {
                pluginId,
                keyName,
                authenticated: await isAuthenticated()
            });

            // Try database retrieval first if authenticated
            if (await isAuthenticated()) {
                try {
                    const result = await this.retrieveFromDatabase(pluginId, keyName);
                    if (result) {
                        log.INFO('API key retrieved from database', {
                            pluginId,
                            keyName,
                            hasResult: !!result
                        });
                        return result;
                    }
                } catch (dbError) {
                    log.WARN('Database retrieval failed, falling back to local storage', {
                        pluginId,
                        keyName,
                        error: dbError.message
                    });
                }
            }

            // Fallback to local storage
            const result = await this.retrieveFromLocal(pluginId, keyName);
            log.INFO('API key retrieved from local storage', {
                pluginId,
                keyName,
                hasResult: !!result
            });
            return result;

        } catch (error) {
            log.ERROR('Failed to retrieve API key', {
                pluginId,
                keyName,
                error: error.message,
                stack: error.stack
            });
            throw error;
        }
    }

    /**
     * Update an API key
     * @param {string} pluginId - Plugin identifier
     * @param {string} keyName - Key name/identifier
     * @param {string} apiKey - The new API key
     * @returns {Promise<boolean>} Success status
     */
    async updateAPIKey(pluginId, keyName, apiKey) {
        try {
            log.INFO('Updating API key', {
                pluginId,
                keyName,
                keyLength: apiKey.length
            });

            // Try database update first if authenticated
            if (await isAuthenticated()) {
                try {
                    const result = await this.updateInDatabase(pluginId, keyName, apiKey);
                    if (result) {
                        log.INFO('API key updated in database', {
                            pluginId,
                            keyName
                        });
                        return result;
                    }
                } catch (dbError) {
                    log.WARN('Database update failed, falling back to local storage', {
                        pluginId,
                        keyName,
                        error: dbError.message
                    });
                }
            }

            // Fallback to local storage
            const result = await this.updateInLocal(pluginId, keyName, apiKey);
            log.INFO('API key updated in local storage', {
                pluginId,
                keyName,
                success: result
            });
            return result;

        } catch (error) {
            log.ERROR('Failed to update API key', {
                pluginId,
                keyName,
                error: error.message,
                stack: error.stack
            });
            throw error;
        }
    }

    /**
     * Delete an API key
     * @param {string} pluginId - Plugin identifier
     * @param {string} keyName - Key name/identifier
     * @returns {Promise<boolean>} Success status
     */
    async deleteAPIKey(pluginId, keyName) {
        try {
            log.INFO('Deleting API key', {
                pluginId,
                keyName
            });

            // Try database deletion first if authenticated
            if (await isAuthenticated()) {
                try {
                    const result = await this.deleteFromDatabase(pluginId, keyName);
                    if (result) {
                        log.INFO('API key deleted from database', {
                            pluginId,
                            keyName
                        });
                        return result;
                    }
                } catch (dbError) {
                    log.WARN('Database deletion failed, falling back to local storage', {
                        pluginId,
                        keyName,
                        error: dbError.message
                    });
                }
            }

            // Fallback to local storage
            const result = await this.deleteFromLocal(pluginId, keyName);
            log.INFO('API key deleted from local storage', {
                pluginId,
                keyName,
                success: result
            });
            return result;

        } catch (error) {
            log.ERROR('Failed to delete API key', {
                pluginId,
                keyName,
                error: error.message,
                stack: error.stack
            });
            throw error;
        }
    }

    // Database operations

    /**
     * Store API key to database
     * @private
     */
    async storeToDatabase(pluginId, keyName, apiKey) {
        const client = getClient();
        const { data, error } = await client.rpc('sp_create_api_key', {
            p_plugin_id: pluginId,
            p_key_name: keyName,
            p_raw_key: apiKey
        });

        if (error) {
            throw new Error(`Database store failed: ${error.message}`);
        }

        return data; // Returns the UUID
    }

    /**
     * Retrieve API key from database
     * @private
     */
    async retrieveFromDatabase(pluginId, keyName) {
        const client = getClient();
        const { data, error } = await client.rpc('sp_get_api_keys', {
            p_plugin_id: pluginId
        });

        if (error) {
            throw new Error(`Database retrieve failed: ${error.message}`);
        }

        if (!data || data.length === 0) {
            return null;
        }

        if (keyName) {
            const key = data.find(k => k.key_name === keyName);
            return key ? key.decrypted_key : null;
        }

        // Return all keys for plugin
        const keys = {};
        data.forEach(key => {
            keys[key.key_name || 'default'] = key.decrypted_key;
        });
        return keys;
    }

    /**
     * Update API key in database
     * @private
     */
    async updateInDatabase(pluginId, keyName, apiKey) {
        // First find the key ID
        const client = getClient();
        const { data: keys, error: getError } = await client.rpc('sp_get_api_keys', {
            p_plugin_id: pluginId
        });

        if (getError) {
            throw new Error(`Database get failed: ${getError.message}`);
        }

        const key = keys.find(k => k.key_name === keyName);
        if (!key) {
            return false;
        }

        const { data, error } = await client.rpc('sp_update_api_key', {
            p_api_key_id: key.id,
            p_raw_key: apiKey
        });

        if (error) {
            throw new Error(`Database update failed: ${error.message}`);
        }

        return data;
    }

    /**
     * Delete API key from database
     * @private
     */
    async deleteFromDatabase(pluginId, keyName) {
        // First find the key ID
        const client = getClient();
        const { data: keys, error: getError } = await client.rpc('sp_get_api_keys', {
            p_plugin_id: pluginId
        });

        if (getError) {
            throw new Error(`Database get failed: ${getError.message}`);
        }

        const key = keys.find(k => k.key_name === keyName);
        if (!key) {
            return false;
        }

        const { data, error } = await client.rpc('sp_delete_api_key', {
            p_api_key_id: key.id
        });

        if (error) {
            throw new Error(`Database delete failed: ${error.message}`);
        }

        return data;
    }

    // Local storage operations

    /**
     * Load local storage file
     * @private
     */
    async loadLocalStorage() {
        try {
            const data = await fs.readFile(this.localStoragePath, 'utf8');
            const parsed = JSON.parse(data);

            // Decrypt and load into cache
            for (const [key, encryptedValue] of Object.entries(parsed)) {
                try {
                    this.localCache.set(key, decryptString(encryptedValue));
                } catch (decryptError) {
                    log.WARN('Failed to decrypt local storage entry', {
                        key,
                        error: decryptError.message
                    });
                }
            }

            log.DEBUG('Local storage loaded', {
                entries: this.localCache.size
            });
        } catch (error) {
            if (error.code !== 'ENOENT') {
                log.ERROR('Failed to load local storage', {
                    error: error.message,
                    path: this.localStoragePath
                });
            }
        }
    }

    /**
     * Save local storage file
     * @private
     */
    async saveLocalStorage() {
        try {
            const toSave = {};

            // Encrypt all cache entries
            for (const [key, value] of this.localCache.entries()) {
                toSave[key] = encryptString(value);
            }

            await fs.writeFile(this.localStoragePath, JSON.stringify(toSave, null, 2));

            log.DEBUG('Local storage saved', {
                entries: this.localCache.size,
                path: this.localStoragePath
            });
        } catch (error) {
            log.ERROR('Failed to save local storage', {
                error: error.message,
                path: this.localStoragePath
            });
            throw error;
        }
    }

    /**
     * Store to local storage
     * @private
     */
    async storeToLocal(pluginId, keyName, apiKey) {
        const key = `${pluginId}_${keyName || 'default'}`;
        this.localCache.set(key, apiKey);
        await this.saveLocalStorage();
        return key;
    }

    /**
     * Retrieve from local storage
     * @private
     */
    async retrieveFromLocal(pluginId, keyName) {
        if (keyName) {
            const key = `${pluginId}_${keyName}`;
            return this.localCache.get(key) || null;
        }

        // Return all keys for plugin
        const keys = {};
        for (const [key, value] of this.localCache.entries()) {
            if (key.startsWith(`${pluginId}_`)) {
                const keyName = key.substring(pluginId.length + 1);
                keys[keyName] = value;
            }
        }
        return Object.keys(keys).length > 0 ? keys : null;
    }

    /**
     * Update in local storage
     * @private
     */
    async updateInLocal(pluginId, keyName, apiKey) {
        const key = `${pluginId}_${keyName || 'default'}`;
        if (this.localCache.has(key)) {
            this.localCache.set(key, apiKey);
            await this.saveLocalStorage();
            return true;
        }
        return false;
    }

    /**
     * Delete from local storage
     * @private
     */
    async deleteFromLocal(pluginId, keyName) {
        const key = `${pluginId}_${keyName || 'default'}`;
        if (this.localCache.has(key)) {
            this.localCache.delete(key);
            await this.saveLocalStorage();
            return true;
        }
        return false;
    }

    /**
     * Check if local storage file exists
     * @private
     */
    async hasLocalStorage() {
        try {
            await fs.access(this.localStoragePath);
            return true;
        } catch {
            return false;
        }
    }
}

// Export singleton instance
const secretManager = new SecretManager();

module.exports = {
    SecretManager,
    secretManager
};
