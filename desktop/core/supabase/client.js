/**
 * Supabase Client Configuration
 *
 * This module provides a centralized Supabase client for the desktop application.
 * It handles authentication and provides access to the database for API key storage.
 *
 * @version 1.0.0
 * <AUTHOR> Team
 */

const { createClient } = require('@supabase/supabase-js');
const { factory: createLogger } = require('../logger/CoreLogger');

const log = createLogger('supabase-client');

/**
 * Supabase client instance
 */
let supabaseClient = null;

/**
 * Initialize Supabase client
 * @returns {Object} Supabase client instance
 */
function initializeClient() {
    if (supabaseClient) {
        return supabaseClient;
    }

    const supabaseUrl = process.env.SUPABASE_PUBLIC_URL || 'http://localhost:8810';
    const supabaseAnonKey = process.env.ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyAgCiAgICAicm9sZSI6ICJhbm9uIiwKICAgICJpc3MiOiAic3VwYWJhc2UtZGVtbyIsCiAgICAiaWF0IjogMTY0MTc2OTIwMCwKICAgICJleHAiOiAxNzk5NTM1NjAwCn0.dc_X5iR_VP_qT0zsiyj_I_OZ2T9FtRU2BBNWN8Bu4GE';

    try {
        supabaseClient = createClient(supabaseUrl, supabaseAnonKey, {
            auth: {
                autoRefreshToken: true,
                persistSession: true,
                detectSessionInUrl: false
            },
            db: {
                schema: 'public'
            },
            global: {
                headers: {
                    'X-Client-Info': 'lifeboard-desktop-app'
                }
            }
        });

        log.INFO('Supabase client initialized successfully', {
            url: supabaseUrl,
            hasClient: !!supabaseClient
        });

        return supabaseClient;
    } catch (error) {
        log.ERROR('Failed to initialize Supabase client', {
            error: error.message,
            stack: error.stack,
            url: supabaseUrl
        });
        throw error;
    }
}

/**
 * Get the current Supabase client instance
 * @returns {Object} Supabase client instance
 */
function getClient() {
    if (!supabaseClient) {
        return initializeClient();
    }
    return supabaseClient;
}

/**
 * Check if client is authenticated
 * @returns {Promise<boolean>} True if authenticated
 */
async function isAuthenticated() {
    try {
        const client = getClient();
        const { data: { user } } = await client.auth.getUser();

        log.DEBUG('Authentication check', {
            authenticated: !!user,
            userId: user?.id || null
        });

        return !!user;
    } catch (error) {
        log.ERROR('Authentication check failed', {
            error: error.message,
            stack: error.stack
        });
        return false;
    }
}

/**
 * Get current user
 * @returns {Promise<Object|null>} User object or null
 */
async function getCurrentUser() {
    try {
        const client = getClient();
        const { data: { user }, error } = await client.auth.getUser();

        if (error) {
            log.ERROR('Failed to get current user', {
                error: error.message,
                code: error.code
            });
            return null;
        }

        log.DEBUG('Current user retrieved', {
            userId: user?.id || null,
            email: user?.email || null
        });

        return user;
    } catch (error) {
        log.ERROR('Error getting current user', {
            error: error.message,
            stack: error.stack
        });
        return null;
    }
}

/**
 * Sign in with email and password
 * @param {string} email - User email
 * @param {string} password - User password
 * @returns {Promise<Object>} Authentication result
 */
async function signIn(email, password) {
    try {
        const client = getClient();
        const { data, error } = await client.auth.signInWithPassword({
            email,
            password
        });

        if (error) {
            log.ERROR('Sign in failed', {
                error: error.message,
                code: error.code,
                email
            });
            throw error;
        }

        log.INFO('User signed in successfully', {
            userId: data.user?.id,
            email: data.user?.email
        });

        return data;
    } catch (error) {
        log.ERROR('Sign in error', {
            error: error.message,
            stack: error.stack,
            email
        });
        throw error;
    }
}

/**
 * Sign out current user
 * @returns {Promise<void>}
 */
async function signOut() {
    try {
        const client = getClient();
        const { error } = await client.auth.signOut();

        if (error) {
            log.ERROR('Sign out failed', {
                error: error.message,
                code: error.code
            });
            throw error;
        }

        log.INFO('User signed out successfully');
    } catch (error) {
        log.ERROR('Sign out error', {
            error: error.message,
            stack: error.stack
        });
        throw error;
    }
}

module.exports = {
    initializeClient,
    getClient,
    isAuthenticated,
    getCurrentUser,
    signIn,
    signOut
};
