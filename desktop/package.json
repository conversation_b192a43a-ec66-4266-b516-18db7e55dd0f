{"author": "Lifeboard Team", "bin": {"lifeboard-plugin": "src/cli/plugin-cli.js"}, "build": {"appId": "com.lifeboard.desktop", "directories": {"output": "dist"}, "extraResources": [{"filter": ["**/*"], "from": "../webui", "to": "webui"}], "files": ["src/**/*", "webui/**/*", "node_modules/**/*"], "linux": {"category": "Office", "target": [{"arch": ["x64"], "target": "AppImage"}]}, "mac": {"category": "public.app-category.productivity", "entitlements": "build/entitlements.mac.plist", "entitlementsInherit": "build/entitlements.mac.plist", "gatekeeperAssess": false, "hardenedRuntime": true, "target": [{"arch": ["x64", "arm64"], "target": "dmg"}]}, "nsis": {"allowToChangeInstallationDirectory": true, "oneClick": false}, "productName": "Lifeboard", "win": {"target": [{"arch": ["x64"], "target": "nsis"}]}}, "dependencies": {"@supabase/supabase-js": "^2.39.0", "ajv": "^8.12.0", "archiver": "^6.0.1", "chalk": "^4.1.2", "commander": "^11.1.0", "electron-log": "^5.0.1", "electron-updater": "^6.1.7", "inquirer": "^8.2.6", "ora": "^5.4.1", "pino": "^9.7.0", "semver": "^7.7.2", "yauzl": "^3.0.0"}, "description": "Lifeboard Desktop Application with Plugin Architecture", "devDependencies": {"@types/node": "^20.10.0", "electron": "^28.1.0", "electron-builder": "^24.9.1", "jest": "^29.7.0", "typescript": "^5.3.3"}, "homepage": "./", "license": "MIT", "main": "src/main.js", "name": "lifeboard-desktop", "private": true, "scripts": {"build": "electron-builder", "build-linux": "electron-builder --linux", "build-mac": "electron-builder --mac", "build-win": "electron-builder --win", "dist": "electron-builder --publish=never", "electron": "electron .", "electron-dev": "ELECTRON_IS_DEV=true electron .", "pack": "electron-builder --dir", "postinstall": "electron-builder install-app-deps", "test": "jest"}, "version": "0.1.0"}