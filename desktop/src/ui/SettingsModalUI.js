/**
 * SettingsModalUI.js
 * Modal interface for plugin settings configuration
 *
 * Provides tabbed interface for general settings, API keys, permissions, and advanced options
 */

const { factory: createLogger } = require('../../core/logger/CoreLogger');

/**
 * SettingsModalUI provides a comprehensive settings interface for plugins
 * Features tabbed interface, form validation, and settings persistence
 */
class SettingsModalUI {
  constructor(logDir) {
    this.activeModals = new Map();
    this.notifyRenderer = null;
    this.modalIdCounter = 0;
    
    if (logDir) {
      const { CoreLogger } = require('../../core/logger/CoreLogger');
      this.log = new CoreLogger({ 
        component: 'SettingsModalUI', 
        logDir: logDir, 
        setupProcessHandlers: false
      });
    } else {
      this.log = createLogger('SettingsModalUI');
    }
    
    this.initializeLogger();

    this.log.INFO('SettingsModalUI initialized', {
      activeModalsCount: this.activeModals.size,
      modalIdCounter: this.modalIdCounter
    });
  }

  /**
   * Initialize logging configuration
   * @private
   */
  initializeLogger() {
    this.log.INFO('SettingsModalUI logger initialized', {
      component: 'SettingsModalUI',
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Set notification function for renderer process
   * @param {Function} notifyFn - Function to notify renderer
   */
  setNotifyRenderer(notifyFn) {
    this.notifyRenderer = notifyFn;
    this.log.DEBUG('Renderer notification function set', {
      hasNotifyFunction: typeof notifyFn === 'function'
    });
  }

  /**
   * Generate unique modal ID
   * @returns {string} Unique modal identifier
   * @private
   */
  generateModalId() {
    return `settings-modal-${++this.modalIdCounter}-${Date.now()}`;
  }

  /**
   * Show settings modal for a plugin
   * @param {string} pluginId - Plugin identifier
   * @param {object} currentSettings - Current plugin settings
   * @param {object} pluginInfo - Plugin information
   * @returns {string} Modal ID
   */
  showSettingsModal(pluginId, currentSettings = {}, pluginInfo = {}) {
    this.log.INFO('Showing settings modal for plugin', {
      pluginId,
      hasCurrentSettings: Object.keys(currentSettings).length > 0,
      pluginName: pluginInfo.name
    });

    try {
      if (!pluginId || typeof pluginId !== 'string') {
        throw new Error('Plugin ID must be a non-empty string');
      }

      // Check if modal already exists for this plugin
      const existingModalId = this.findModalByPlugin(pluginId);
      if (existingModalId) {
        this.log.INFO('Settings modal already exists for plugin, focusing existing modal', {
          pluginId,
          existingModalId
        });
        this.focusModal(existingModalId);
        return existingModalId;
      }

      const modalId = this.generateModalId();

      const modalConfig = {
        id: modalId,
        pluginId,
        title: `${pluginInfo.name || pluginId} Settings`,
        width: 600,
        height: 500,
        resizable: true,
        closable: true,
        modal: true,
        className: 'plugin-settings-modal',
        tabs: this.generateTabs(pluginId, currentSettings, pluginInfo),
        currentSettings: { ...currentSettings },
        originalSettings: { ...currentSettings },
        pluginInfo: { ...pluginInfo },
        isDirty: false,
        activeTab: 'general',
        created: new Date().toISOString()
      };

      // Store modal configuration
      this.activeModals.set(modalId, modalConfig);

      // Notify renderer to show modal
      if (this.notifyRenderer) {
        this.notifyRenderer('settings-modal:show', {
          modalId,
          config: this.sanitizeModalConfig(modalConfig),
          timestamp: new Date().toISOString()
        });
      }

      this.log.INFO(`[SettingsModalUI] Created settings modal ${modalId} for plugin: ${pluginId}`, {
        modalId,
        pluginId
      });
      return modalId;
    } catch (error) {
      this.log.ERROR(`[SettingsModalUI] Failed to show settings modal for ${pluginId}:`, {
        pluginId,
        error: error.message,
        stack: error.stack
      });
      throw error;
    }
  }

  /**
   * Generate tab configuration for settings modal
   * @param {string} pluginId - Plugin identifier
   * @param {object} settings - Current settings
   * @param {object} pluginInfo - Plugin information
   * @returns {Array} Tab configuration array
   * @private
   */
  generateTabs(pluginId, settings, pluginInfo) {
    this.log.DEBUG(`[SettingsModalUI] Generating tabs for plugin: ${pluginId}`, { pluginId });

    const tabs = [];

    // General tab (always present)
    tabs.push({
      id: 'general',
      name: 'General',
      icon: 'settings',
      fields: this.generateGeneralFields(settings, pluginInfo)
    });

    // API Keys tab (if plugin has network permissions)
    if (pluginInfo.permissions && pluginInfo.permissions.includes('network')) {
      tabs.push({
        id: 'api-keys',
        name: 'API Keys',
        icon: 'key',
        fields: this.generateApiKeyFields(settings)
      });
    }

    // Permissions tab (always present)
    tabs.push({
      id: 'permissions',
      name: 'Permissions',
      icon: 'shield',
      fields: this.generatePermissionFields(pluginInfo)
    });

    // Advanced tab (if plugin has advanced settings)
    if (this.hasAdvancedSettings(settings, pluginInfo)) {
      tabs.push({
        id: 'advanced',
        name: 'Advanced',
        icon: 'cog',
        fields: this.generateAdvancedFields(settings, pluginInfo)
      });
    }

    this.log.DEBUG(`[SettingsModalUI] Generated ${tabs.length} tabs for plugin: ${pluginId}`, {
      pluginId,
      tabCount: tabs.length
    });
    return tabs;
  }

  /**
   * Generate general settings fields
   * @param {object} settings - Current settings
   * @param {object} pluginInfo - Plugin information
   * @returns {Array} Field configuration array
   * @private
   */
  generateGeneralFields(settings, pluginInfo) {
    const fields = [];

    // Display name
    fields.push({
      type: 'text',
      id: 'displayName',
      label: 'Display Name',
      value: settings.displayName || pluginInfo.name || '',
      placeholder: 'Enter display name',
      required: false
    });

    // Theme
    fields.push({
      type: 'select',
      id: 'theme',
      label: 'Theme',
      value: settings.theme || 'dark',
      options: [
        { value: 'dark', label: 'Dark' },
        { value: 'light', label: 'Light' },
        { value: 'auto', label: 'Auto' }
      ],
      required: false
    });

    // Notifications
    fields.push({
      type: 'checkbox',
      id: 'notifications',
      label: 'Enable notifications',
      value: settings.notifications !== false,
      description: 'Show notifications from this plugin'
    });

    // Auto-refresh
    fields.push({
      type: 'checkbox',
      id: 'autoRefresh',
      label: 'Enable auto-refresh',
      value: settings.autoRefresh !== false,
      description: 'Automatically refresh plugin data'
    });

    // Refresh interval
    if (settings.autoRefresh !== false) {
      fields.push({
        type: 'number',
        id: 'refreshInterval',
        label: 'Refresh Interval (seconds)',
        value: settings.refreshInterval || 3600,
        min: 60,
        max: 86400,
        step: 60,
        description: 'How often to refresh data (60-86400 seconds)'
      });
    }

    // Max cache size
    fields.push({
      type: 'number',
      id: 'maxCacheSize',
      label: 'Max Cache Size (MB)',
      value: settings.maxCacheSize || 100,
      min: 1,
      max: 1000,
      step: 1,
      description: 'Maximum cache size in megabytes'
    });

    return fields;
  }

  /**
   * Generate API key fields
   * @param {object} settings - Current settings
   * @returns {Array} Field configuration array
   * @private
   */
  generateApiKeyFields(settings) {
    const fields = [];
    const apiKeys = settings.apiKeys || {};

    // Common API key fields
    const commonKeys = ['openai', 'anthropic', 'google', 'azure'];

    commonKeys.forEach(keyName => {
      fields.push({
        type: 'password',
        id: `apiKey_${keyName}`,
        label: `${keyName.charAt(0).toUpperCase() + keyName.slice(1)} API Key`,
        value: apiKeys[keyName] || '',
        placeholder: `Enter ${keyName} API key`,
        sensitive: true,
        description: `API key for ${keyName} services`
      });
    });

    // Custom API keys section
    fields.push({
      type: 'section',
      id: 'custom-api-keys',
      label: 'Custom API Keys',
      description: 'Additional API keys for this plugin'
    });

    // Add button for custom keys
    fields.push({
      type: 'button',
      id: 'add-api-key',
      label: 'Add Custom API Key',
      action: 'add-custom-key',
      variant: 'secondary'
    });

    return fields;
  }

  /**
   * Generate permission fields
   * @param {object} pluginInfo - Plugin information
   * @returns {Array} Field configuration array
   * @private
   */
  generatePermissionFields(pluginInfo) {
    const fields = [];
    const permissions = pluginInfo.permissions || [];
    const grantedPermissions = pluginInfo.grantedPermissions || [];
    const requestedPermissions = pluginInfo.requestedPermissions || permissions;

    // Permission categories
    const permissionCategories = {
      workspace: {
        label: 'Workspace Access',
        description: 'Read and write workspace data',
        icon: 'folder'
      },
      network: {
        label: 'Network Access',
        description: 'Make HTTP requests and network calls',
        icon: 'globe'
      },
      filesystem: {
        label: 'File System Access',
        description: 'Read and write files on the system',
        icon: 'file'
      },
      system: {
        label: 'System Access',
        description: 'Access system notifications and clipboard',
        icon: 'monitor'
      }
    };

    Object.entries(permissionCategories).forEach(([permission, info]) => {
      const isRequested = requestedPermissions.includes(permission);
      const isGranted = grantedPermissions.includes(permission);

      if (isRequested) {
        fields.push({
          type: 'permission',
          id: `permission_${permission}`,
          label: info.label,
          description: info.description,
          icon: info.icon,
          permission: permission,
          granted: isGranted,
          required: permissions.includes(permission),
          canToggle: !permissions.includes(permission) // Can only toggle optional permissions
        });
      }
    });

    return fields;
  }

  /**
   * Generate advanced settings fields
   * @param {object} settings - Current settings
   * @param {object} pluginInfo - Plugin information
   * @returns {Array} Field configuration array
   * @private
   */
  generateAdvancedFields(settings, pluginInfo) {
    const fields = [];

    // Debug mode
    fields.push({
      type: 'checkbox',
      id: 'debugMode',
      label: 'Debug Mode',
      value: settings.debugMode || false,
      description: 'Enable verbose logging and debug information'
    });

    // Custom configuration
    if (settings.customConfig || pluginInfo.supportsCustomConfig) {
      fields.push({
        type: 'textarea',
        id: 'customConfig',
        label: 'Custom Configuration (JSON)',
        value: JSON.stringify(settings.customConfig || {}, null, 2),
        rows: 10,
        placeholder: '{\n  "key": "value"\n}',
        description: 'Custom JSON configuration for advanced users',
        validate: 'json'
      });
    }

    // Reset settings button
    fields.push({
      type: 'button',
      id: 'reset-settings',
      label: 'Reset to Defaults',
      action: 'reset-settings',
      variant: 'danger',
      description: 'Reset all settings to their default values'
    });

    return fields;
  }

  /**
   * Check if plugin has advanced settings
   * @param {object} settings - Current settings
   * @param {object} pluginInfo - Plugin information
   * @returns {boolean} True if has advanced settings
   * @private
   */
  hasAdvancedSettings(settings, pluginInfo) {
    return !!(
      settings.debugMode !== undefined ||
      settings.customConfig ||
      pluginInfo.supportsCustomConfig ||
      pluginInfo.hasAdvancedSettings
    );
  }

  /**
   * Sanitize modal config for renderer (remove sensitive data)
   * @param {object} modalConfig - Modal configuration
   * @returns {object} Sanitized config
   * @private
   */
  sanitizeModalConfig(modalConfig) {
    const sanitized = { ...modalConfig };

    // Remove sensitive settings data
    if (sanitized.currentSettings && sanitized.currentSettings.apiKeys) {
      sanitized.currentSettings = {
        ...sanitized.currentSettings,
        apiKeys: Object.keys(sanitized.currentSettings.apiKeys).reduce((acc, key) => {
          acc[key] = '***HIDDEN***';
          return acc;
        }, {})
      };
    }

    return sanitized;
  }

  /**
   * Find modal by plugin ID
   * @param {string} pluginId - Plugin identifier
   * @returns {string|null} Modal ID if found
   * @private
   */
  findModalByPlugin(pluginId) {
    for (const [modalId, config] of this.activeModals) {
      if (config.pluginId === pluginId) {
        return modalId;
      }
    }
    return null;
  }

  /**
   * Focus existing modal
   * @param {string} modalId - Modal identifier
   */
  focusModal(modalId) {
    this.log.DEBUG(`[SettingsModalUI] Focusing modal: ${modalId}`, { modalId });

    if (this.notifyRenderer) {
      this.notifyRenderer('settings-modal:focus', {
        modalId,
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * Close settings modal
   * @param {string} modalId - Modal identifier
   * @returns {boolean} True if closed successfully
   */
  closeModal(modalId) {
    this.log.INFO(`[SettingsModalUI] Closing settings modal: ${modalId}`, { modalId });

    try {
      if (!this.activeModals.has(modalId)) {
        this.log.WARN(`[SettingsModalUI] Modal not found: ${modalId}`, { modalId });
        return false;
      }

      const modalConfig = this.activeModals.get(modalId);

      // Check if settings are dirty
      if (modalConfig.isDirty) {
        // Notify renderer to show confirmation dialog
        if (this.notifyRenderer) {
          this.notifyRenderer('settings-modal:confirm-close', {
            modalId,
            timestamp: new Date().toISOString()
          });
        }
        return false; // Don't close yet, wait for confirmation
      }

      // Remove modal from active list
      this.activeModals.delete(modalId);

      // Notify renderer to close modal
      if (this.notifyRenderer) {
        this.notifyRenderer('settings-modal:close', {
          modalId,
          timestamp: new Date().toISOString()
        });
      }

      this.log.INFO(`[SettingsModalUI] Closed settings modal: ${modalId}`, { modalId });
      return true;
    } catch (error) {
      this.log.ERROR(`[SettingsModalUI] Failed to close modal ${modalId}:`, {
        modalId,
        error: error.message,
        stack: error.stack
      });
      return false;
    }
  }

  /**
   * Update settings in modal
   * @param {string} modalId - Modal identifier
   * @param {object} newSettings - Updated settings
   * @returns {boolean} True if updated successfully
   */
  updateSettings(modalId, newSettings) {
    this.log.DEBUG(`[SettingsModalUI] Updating settings for modal: ${modalId}`, { modalId });

    try {
      const modalConfig = this.activeModals.get(modalId);
      if (!modalConfig) {
        this.log.WARN(`[SettingsModalUI] Modal not found: ${modalId}`, { modalId });
        return false;
      }

      // Merge new settings
      modalConfig.currentSettings = {
        ...modalConfig.currentSettings,
        ...newSettings
      };

      // Check if settings are dirty
      modalConfig.isDirty = JSON.stringify(modalConfig.currentSettings) !==
                           JSON.stringify(modalConfig.originalSettings);

      // Notify renderer of settings update
      if (this.notifyRenderer) {
        this.notifyRenderer('settings-modal:settings-updated', {
          modalId,
          settings: this.sanitizeModalConfig(modalConfig).currentSettings,
          isDirty: modalConfig.isDirty,
          timestamp: new Date().toISOString()
        });
      }

      this.log.DEBUG(`[SettingsModalUI] Updated settings for modal: ${modalId}`, { modalId });
      return true;
    } catch (error) {
      this.log.ERROR(`[SettingsModalUI] Failed to update settings for modal ${modalId}:`, {
        modalId,
        error: error.message,
        stack: error.stack
      });
      return false;
    }
  }

  /**
   * Save settings from modal
   * @param {string} modalId - Modal identifier
   * @returns {object} Save operation result
   */
  saveSettings(modalId) {
    this.log.INFO(`[SettingsModalUI] Saving settings for modal: ${modalId}`, { modalId });

    try {
      const modalConfig = this.activeModals.get(modalId);
      if (!modalConfig) {
        const error = `Modal not found: ${modalId}`;
        this.log.ERROR(`[SettingsModalUI] ${error}`, { modalId, error });
        return { success: false, error };
      }

      // Validate settings before saving
      const validation = this.validateSettings(modalConfig.currentSettings);
      if (!validation.valid) {
        this.log.WARN(`[SettingsModalUI] Settings validation failed for modal ${modalId}:`, {
          modalId,
          errors: validation.errors
        });
        return { success: false, error: 'Settings validation failed', errors: validation.errors };
      }

      // Notify renderer to save settings (will be handled by plugin manager)
      if (this.notifyRenderer) {
        this.notifyRenderer('settings-modal:save-requested', {
          modalId,
          pluginId: modalConfig.pluginId,
          settings: modalConfig.currentSettings,
          timestamp: new Date().toISOString()
        });
      }

      // Mark as not dirty
      modalConfig.originalSettings = { ...modalConfig.currentSettings };
      modalConfig.isDirty = false;

      this.log.INFO(`[SettingsModalUI] Requested save for modal: ${modalId}`, { modalId });
      return { success: true, modalId };
    } catch (error) {
      this.log.ERROR(`[SettingsModalUI] Failed to save settings for modal ${modalId}:`, {
        modalId,
        error: error.message,
        stack: error.stack
      });
      return { success: false, error: error.message };
    }
  }

  /**
   * Validate settings
   * @param {object} settings - Settings to validate
   * @returns {object} Validation result
   * @private
   */
  validateSettings(settings) {
    const errors = [];

    // Validate refresh interval
    if (settings.refreshInterval !== undefined) {
      const interval = parseInt(settings.refreshInterval);
      if (isNaN(interval) || interval < 60 || interval > 86400) {
        errors.push('Refresh interval must be between 60 and 86400 seconds');
      }
    }

    // Validate cache size
    if (settings.maxCacheSize !== undefined) {
      const size = parseInt(settings.maxCacheSize);
      if (isNaN(size) || size < 1 || size > 1000) {
        errors.push('Cache size must be between 1 and 1000 MB');
      }
    }

    // Validate custom config JSON
    if (settings.customConfig && typeof settings.customConfig === 'string') {
      try {
        JSON.parse(settings.customConfig);
      } catch (e) {
        errors.push('Custom configuration must be valid JSON');
      }
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * Reset settings to defaults
   * @param {string} modalId - Modal identifier
   * @returns {boolean} True if reset successfully
   */
  resetSettings(modalId) {
    this.log.INFO(`[SettingsModalUI] Resetting settings for modal: ${modalId}`, { modalId });

    try {
      const modalConfig = this.activeModals.get(modalId);
      if (!modalConfig) {
        this.log.WARN(`[SettingsModalUI] Modal not found: ${modalId}`, { modalId });
        return false;
      }

      // Reset to default settings
      modalConfig.currentSettings = {};
      modalConfig.isDirty = true;

      // Regenerate tabs with default settings
      modalConfig.tabs = this.generateTabs(
        modalConfig.pluginId,
        modalConfig.currentSettings,
        modalConfig.pluginInfo
      );

      // Notify renderer of reset
      if (this.notifyRenderer) {
        this.notifyRenderer('settings-modal:reset', {
          modalId,
          config: this.sanitizeModalConfig(modalConfig),
          timestamp: new Date().toISOString()
        });
      }

      this.log.INFO(`[SettingsModalUI] Reset settings for modal: ${modalId}`, { modalId });
      return true;
    } catch (error) {
      this.log.ERROR(`[SettingsModalUI] Failed to reset settings for modal ${modalId}:`, {
        modalId,
        error: error.message,
        stack: error.stack
      });
      return false;
    }
  }

  /**
   * Get statistics for settings modals
   * @returns {object} Settings modal statistics
   */
  getStatistics() {
    this.log.DEBUG('[SettingsModalUI] Getting settings modal statistics', {});

    try {
      const totalModals = this.activeModals.size;
      let dirtyModals = 0;
      const modalsByPlugin = {};

      this.activeModals.forEach(config => {
        // TODO: Implement modal statistics logic here
      });

      this.log.DEBUG('[SettingsModalUI] Retrieved settings modal statistics', {
        totalModals,
        dirtyModals,
        modalsByPlugin
      });

      return {
        totalModals,
        dirtyModals,
        modalsByPlugin
      };
    } catch (error) {
      this.log.ERROR('[SettingsModalUI] Failed to get statistics:', {
        error: error.message,
        stack: error.stack
      });
      return {
        totalModals: 0,
        dirtyModals: 0,
        modalsByPlugin: {}
      };
    }
  }
}

module.exports = SettingsModalUI;
