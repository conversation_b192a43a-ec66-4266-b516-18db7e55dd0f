const { factory: createLogger } = require('../../core/logger/CoreLogger');

/**
 * ModalManager handles plugin-created modal dialogs and overlays
 * Provides secure management of modal windows with proper lifecycle
 */
class ModalManager {
  constructor(logDir) {
    this.modals = new Map(); // modalId -> modal data
    this.modalStack = []; // Stack for z-index management
    this.modalCounter = 0;
    this.maxModals = 10; // Prevent modal spam

    if (logDir) {
      const { CoreLogger } = require('../../core/logger/CoreLogger');
      this.log = new CoreLogger({ 
        component: 'ModalManager', 
        logDir: logDir, 
        setupProcessHandlers: false
      });
    } else {
      this.log = createLogger('ModalManager');
    }

    this.log.INFO('ModalManager initialized', {
      maxModals: this.maxModals
    });
  }

  /**
   * Create and show a modal
   * @param {string} pluginId - ID of the plugin creating the modal
   * @param {Object} modalConfig - Modal configuration
   * @returns {string} - Unique modal ID
   */
  showModal(pluginId, modalConfig) {
    if (!pluginId || !modalConfig) {
      throw new Error('Invalid parameters for modal creation');
    }

    // Enforce modal limit
    if (this.modals.size >= this.maxModals) {
      throw new Error(`Maximum number of modals (${this.maxModals}) reached`);
    }

    const modalId = `modal-${pluginId}-${++this.modalCounter}`;

    // Validate and sanitize modal configuration
    const sanitizedConfig = this.validateModalConfig(modalConfig);

    const modalData = {
      id: modalId,
      pluginId,
      config: sanitizedConfig,
      createdAt: new Date(),
      isOpen: true,
      zIndex: this.getNextZIndex()
    };

    this.modals.set(modalId, modalData);
    this.modalStack.push(modalId);

    this.log.INFO('Created modal for plugin', {
      modalId,
      pluginId,
      title: sanitizedConfig.title,
      modalCount: this.modals.size
    });

    // Notify renderer to show modal
    this.notifyRenderer('modal:show', {
      modalId,
      pluginId,
      config: sanitizedConfig,
      zIndex: modalData.zIndex
    });

    return modalId;
  }

  /**
   * Close a specific modal
   * @param {string} modalId - ID of the modal to close
   * @returns {boolean} - Success status
   */
  closeModal(modalId) {
    if (!this.modals.has(modalId)) {
      this.log.WARN('Modal not found for closing', {
        modalId,
        availableModals: Array.from(this.modals.keys())
      });
      return false;
    }

    const modalData = this.modals.get(modalId);
    modalData.isOpen = false;

    // Remove from stack
    const stackIndex = this.modalStack.indexOf(modalId);
    if (stackIndex > -1) {
      this.modalStack.splice(stackIndex, 1);
    }

    // Clean up after a delay to allow closing animation
    setTimeout(() => {
      this.modals.delete(modalId);
    }, 300);

    this.log.INFO('Closed modal from plugin', {
      modalId,
      pluginId: modalData.pluginId,
      remainingModals: this.modals.size
    });

    // Notify renderer to close modal
    this.notifyRenderer('modal:close', { modalId });

    return true;
  }

  /**
   * Close all modals for a specific plugin
   * @param {string} pluginId - ID of the plugin
   * @returns {number} - Number of modals closed
   */
  closePluginModals(pluginId) {
    let closedCount = 0;

    for (const [modalId, modalData] of this.modals) {
      if (modalData.pluginId === pluginId && modalData.isOpen) {
        this.closeModal(modalId);
        closedCount++;
      }
    }

    this.log.INFO('Closed modals for plugin', {
      pluginId,
      closedCount,
      remainingModals: this.modals.size
    });
    return closedCount;
  }

  /**
   * Close the topmost modal
   * @returns {boolean} - Success status
   */
  closeTopModal() {
    if (this.modalStack.length === 0) {
      return false;
    }

    const topModalId = this.modalStack[this.modalStack.length - 1];
    return this.closeModal(topModalId);
  }

  /**
   * Get modal data by ID
   * @param {string} modalId - ID of the modal
   * @returns {Object|null} - Modal data or null if not found
   */
  getModal(modalId) {
    return this.modals.get(modalId) || null;
  }

  /**
   * Get all open modals
   * @returns {Array} - Array of modal data
   */
  getOpenModals() {
    const openModals = [];

    for (const [modalId, modalData] of this.modals) {
      if (modalData.isOpen) {
        openModals.push({
          id: modalId,
          pluginId: modalData.pluginId,
          title: modalData.config.title,
          zIndex: modalData.zIndex,
          createdAt: modalData.createdAt
        });
      }
    }

    return openModals.sort((a, b) => a.zIndex - b.zIndex);
  }

  /**
   * Get modals for a specific plugin
   * @param {string} pluginId - ID of the plugin
   * @returns {Array} - Array of modal data
   */
  getPluginModals(pluginId) {
    const pluginModals = [];

    for (const [modalId, modalData] of this.modals) {
      if (modalData.pluginId === pluginId) {
        pluginModals.push({
          id: modalId,
          title: modalData.config.title,
          isOpen: modalData.isOpen,
          zIndex: modalData.zIndex,
          createdAt: modalData.createdAt
        });
      }
    }

    return pluginModals;
  }

  /**
   * Validate and sanitize modal configuration
   * @param {Object} config - Raw modal configuration
   * @returns {Object} - Sanitized configuration
   */
  validateModalConfig(config) {
    const sanitized = {
      title: this.sanitizeString(config.title) || 'Plugin Modal',
      content: config.content || '',
      width: this.clampNumber(config.width, 300, 1200, 600),
      height: this.clampNumber(config.height, 200, 800, 400),
      resizable: Boolean(config.resizable),
      closable: config.closable !== false, // Default to true
      modal: config.modal !== false, // Default to true
      className: this.sanitizeClassName(config.className),
      buttons: this.validateButtons(config.buttons || [])
    };

    return sanitized;
  }

  /**
   * Validate modal buttons
   * @param {Array} buttons - Array of button configurations
   * @returns {Array} - Sanitized button array
   */
  validateButtons(buttons) {
    if (!Array.isArray(buttons)) {
      return [];
    }

    return buttons.slice(0, 5).map((button, index) => ({
      id: `btn-${index}`,
      text: this.sanitizeString(button.text) || 'Button',
      variant: this.validateButtonVariant(button.variant),
      callback: typeof button.callback === 'function' ? button.callback : () => {}
    }));
  }

  /**
   * Validate button variant
   * @param {string} variant - Button style variant
   * @returns {string} - Valid variant
   */
  validateButtonVariant(variant) {
    const validVariants = ['primary', 'secondary', 'success', 'danger', 'warning', 'info'];
    return validVariants.includes(variant) ? variant : 'secondary';
  }

  /**
   * Sanitize string input
   * @param {string} input - Input string
   * @returns {string} - Sanitized string
   */
  sanitizeString(input) {
    if (typeof input !== 'string') {
      return '';
    }

    // Remove HTML tags and limit length
    return input.replace(/<[^>]*>/g, '').substring(0, 200);
  }

  /**
   * Sanitize CSS class name
   * @param {string} className - CSS class name
   * @returns {string} - Sanitized class name
   */
  sanitizeClassName(className) {
    if (typeof className !== 'string') {
      return '';
    }

    // Allow only valid CSS class characters
    return className.replace(/[^a-zA-Z0-9_-]/g, '').substring(0, 50);
  }

  /**
   * Clamp number to valid range
   * @param {number} value - Input value
   * @param {number} min - Minimum value
   * @param {number} max - Maximum value
   * @param {number} defaultValue - Default if invalid
   * @returns {number} - Clamped value
   */
  clampNumber(value, min, max, defaultValue) {
    const num = Number(value);
    if (isNaN(num)) {
      return defaultValue;
    }
    return Math.max(min, Math.min(max, num));
  }

  /**
   * Get next z-index for modal stacking
   * @returns {number} - Z-index value
   */
  getNextZIndex() {
    const baseZIndex = 1000;
    return baseZIndex + this.modalStack.length;
  }

  /**
   * Handle modal button click
   * @param {string} modalId - ID of the modal
   * @param {string} buttonId - ID of the button clicked
   * @returns {boolean} - Success status
   */
  handleButtonClick(modalId, buttonId) {
    const modalData = this.modals.get(modalId);
    if (!modalData || !modalData.isOpen) {
      return false;
    }

    const button = modalData.config.buttons.find(btn => btn.id === buttonId);
    if (!button) {
      return false;
    }

    try {
      this.log.DEBUG('Executing button callback', {
        modalId,
        buttonId
      });
      button.callback();
      return true;
    } catch (error) {
      this.log.ERROR('Error executing button callback', {
        modalId,
        buttonId,
        error: error.message,
        stack: error.stack
      });
      return false;
    }
  }

  /**
   * Clean up all modals (for shutdown)
   */
  cleanup() {
    const totalModals = this.modals.size;

    // Close all modals
    for (const modalId of this.modals.keys()) {
      this.closeModal(modalId);
    }

    this.modals.clear();
    this.modalStack.length = 0;
    this.modalCounter = 0;

    this.log.INFO('Cleaned up modals', {
      totalModals,
      remainingModals: this.modals.size
    });
  }

  /**
   * Set renderer notification function
   * @param {Function} notifyFn - Function to notify renderer process
   */
  setNotifyRenderer(notifyFn) {
    this.notifyRenderer = notifyFn || (() => {});
  }

  /**
   * Default notification function (no-op until set)
   */
  notifyRenderer() {
    // Will be replaced by setNotifyRenderer
  }

  /**
   * Get statistics about modals
   * @returns {Object} - Statistics object
   */
  getStats() {
    let openModals = 0;
    const pluginCounts = {};

    for (const [modalId, modalData] of this.modals) {
      if (modalData.isOpen) {
        openModals++;
      }

      pluginCounts[modalData.pluginId] = (pluginCounts[modalData.pluginId] || 0) + 1;
    }

    return {
      totalModals: this.modals.size,
      openModals,
      closedModals: this.modals.size - openModals,
      stackDepth: this.modalStack.length,
      pluginCount: Object.keys(pluginCounts).length,
      pluginCounts
    };
  }
}

module.exports = ModalManager;
