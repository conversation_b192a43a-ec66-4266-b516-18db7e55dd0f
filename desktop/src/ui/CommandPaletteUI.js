const { factory: createLogger } = require('../../core/logger/CoreLogger');

/**
 * CommandPaletteUI provides enhanced visual command palette functionality
 * Built on top of the existing CommandPalette with search and filtering
 */
class CommandPaletteUI {
  constructor(commandPalette, logDir) {
    this.commandPalette = commandPalette;
    this.isVisible = false;
    this.searchQuery = '';
    this.selectedIndex = 0;
    this.filteredCommands = [];
    this.categories = new Map(); // category -> commands
    this.hotkeys = new Map(); // hotkey -> commandId
    this.commandMetadata = new Map(); // commandId -> metadata
    
    if (logDir) {
      const { CoreLogger } = require('../../core/logger/CoreLogger');
      this.log = new CoreLogger({ 
        component: 'CommandPaletteUI', 
        logDir: logDir, 
        setupProcessHandlers: false
      });
    } else {
      this.log = createLogger('CommandPaletteUI');
    }

    this.log.INFO('CommandPaletteUI initialized', {
      component: 'CommandPaletteUI'
    });
  }

  /**
   * Show the command palette UI
   * @param {string} initialQuery - Optional initial search query
   */
  show(initialQuery = '') {
    if (this.isVisible) {
      return;
    }

    this.isVisible = true;
    this.searchQuery = initialQuery;
    this.selectedIndex = 0;

    // Get all available commands
    this.updateFilteredCommands();

    this.log.DEBUG('[CommandPaletteUI] Showing palette with query', {
      component: 'CommandPaletteUI',
      initialQuery: initialQuery
    });

    // Notify renderer to show palette
    this.notifyRenderer('command-palette:show', {
      commands: this.getDisplayCommands(),
      searchQuery: this.searchQuery,
      selectedIndex: this.selectedIndex,
      categories: this.getCategoryList()
    });
  }

  /**
   * Hide the command palette UI
   */
  hide() {
    if (!this.isVisible) {
      return;
    }

    this.isVisible = false;
    this.searchQuery = '';
    this.selectedIndex = 0;
    this.filteredCommands = [];

    this.log.DEBUG('[CommandPaletteUI] Hiding palette', {
      component: 'CommandPaletteUI'
    });

    // Notify renderer to hide palette
    this.notifyRenderer('command-palette:hide');
  }

  /**
   * Update search query and filter commands
   * @param {string} query - Search query
   */
  updateSearch(query) {
    this.searchQuery = query;
    this.selectedIndex = 0;
    this.updateFilteredCommands();

    // Notify renderer of updated results
    this.notifyRenderer('command-palette:update', {
      commands: this.getDisplayCommands(),
      searchQuery: this.searchQuery,
      selectedIndex: this.selectedIndex
    });
  }

  /**
   * Move selection up in the command list
   */
  selectPrevious() {
    if (this.filteredCommands.length === 0) {
      return;
    }

    this.selectedIndex = Math.max(0, this.selectedIndex - 1);

    this.notifyRenderer('command-palette:selection-changed', {
      selectedIndex: this.selectedIndex
    });
  }

  /**
   * Move selection down in the command list
   */
  selectNext() {
    if (this.filteredCommands.length === 0) {
      return;
    }

    this.selectedIndex = Math.min(this.filteredCommands.length - 1, this.selectedIndex + 1);

    this.notifyRenderer('command-palette:selection-changed', {
      selectedIndex: this.selectedIndex
    });
  }

  /**
   * Execute the currently selected command
   * @returns {boolean} - Success status
   */
  executeSelected() {
    if (this.filteredCommands.length === 0 || this.selectedIndex >= this.filteredCommands.length) {
      return false;
    }

    try {
      const selectedCommand = this.filteredCommands[this.selectedIndex];
      const success = this.commandPalette.executeCommand(selectedCommand.id);

      if (success) {
        this.hide(); // Close palette after successful execution
        this.log.INFO(`[CommandPaletteUI] Executed command: ${selectedCommand.id}`, {
          component: 'CommandPaletteUI',
          commandId: selectedCommand.id
        });
      }

      return success;
    } catch (error) {
      this.log.ERROR(`[CommandPaletteUI] Error executing selected command`, {
        component: 'CommandPaletteUI',
        error: error.message,
        stack: error.stack
      });
      return false;
    }
  }

  /**
   * Execute command by ID
   * @param {string} commandId - ID of the command to execute
   * @returns {boolean} - Success status
   */
  executeCommand(commandId) {
    try {
      const success = this.commandPalette.executeCommand(commandId);

      if (success) {
        this.hide();
        this.log.INFO(`[CommandPaletteUI] Executed command via ID: ${commandId}`, {
          component: 'CommandPaletteUI',
          commandId: commandId
        });
      }

      return success;
    } catch (error) {
      this.log.ERROR(`[CommandPaletteUI] Error executing command ${commandId}`, {
        component: 'CommandPaletteUI',
        commandId: commandId,
        error: error.message,
        stack: error.stack
      });
      return false;
    }
  }

  /**
   * Register command metadata for enhanced display
   * @param {string} commandId - Command ID
   * @param {Object} metadata - Command metadata
   */
  setCommandMetadata(commandId, metadata) {
    this.commandMetadata.set(commandId, {
      category: metadata.category || 'General',
      description: metadata.description || '',
      icon: metadata.icon || '',
      tags: metadata.tags || [],
      hotkey: metadata.hotkey || '',
      isVisible: metadata.isVisible !== false
    });

    // Register hotkey if provided
    if (metadata.hotkey) {
      this.hotkeys.set(metadata.hotkey, commandId);
    }

    // Update category mapping
    const category = metadata.category || 'General';
    if (!this.categories.has(category)) {
      this.categories.set(category, new Set());
    }
    this.categories.get(category).add(commandId);

    this.log.DEBUG(`[CommandPaletteUI] Set metadata for command ${commandId}`, {
      component: 'CommandPaletteUI',
      commandId: commandId
    });
  }

  /**
   * Remove command metadata
   * @param {string} commandId - Command ID
   */
  removeCommandMetadata(commandId) {
    const metadata = this.commandMetadata.get(commandId);

    if (metadata) {
      // Remove from category
      if (this.categories.has(metadata.category)) {
        this.categories.get(metadata.category).delete(commandId);

        // Clean up empty categories
        if (this.categories.get(metadata.category).size === 0) {
          this.categories.delete(metadata.category);
        }
      }

      // Remove hotkey
      if (metadata.hotkey) {
        this.hotkeys.delete(metadata.hotkey);
      }

      this.commandMetadata.delete(commandId);
    }
  }

  /**
   * Execute command by hotkey
   * @param {string} hotkey - Hotkey combination
   * @returns {boolean} - Success status
   */
  executeByHotkey(hotkey) {
    const commandId = this.hotkeys.get(hotkey);
    if (!commandId) {
      return false;
    }

    return this.executeCommand(commandId);
  }

  /**
   * Update filtered commands based on search query
   */
  updateFilteredCommands() {
    const allCommands = this.getAllCommands();

    if (!this.searchQuery.trim()) {
      this.filteredCommands = allCommands;
      return;
    }

    const query = this.searchQuery.toLowerCase();

    this.filteredCommands = allCommands.filter(command => {
      // Search in command ID, name, description, category, and tags
      const searchableText = [
        command.id,
        command.name,
        command.description,
        command.category,
        ...(command.tags || [])
      ].join(' ').toLowerCase();

      return searchableText.includes(query);
    }).sort((a, b) => {
      // Prioritize exact matches and starts-with matches
      const aScore = this.getSearchScore(a, query);
      const bScore = this.getSearchScore(b, query);
      return bScore - aScore;
    });
  }

  /**
   * Calculate search relevance score for a command
   * @param {Object} command - Command object
   * @param {string} query - Search query
   * @returns {number} - Relevance score
   */
  getSearchScore(command, query) {
    let score = 0;

    // Exact match in name gets highest score
    if (command.name.toLowerCase() === query) {
      score += 100;
    }

    // Name starts with query
    if (command.name.toLowerCase().startsWith(query)) {
      score += 50;
    }

    // Name contains query
    if (command.name.toLowerCase().includes(query)) {
      score += 25;
    }

    // Category match
    if (command.category.toLowerCase().includes(query)) {
      score += 10;
    }

    // Tag match
    for (const tag of command.tags || []) {
      if (tag.toLowerCase().includes(query)) {
        score += 5;
      }
    }

    return score;
  }

  /**
   * Get all commands with metadata for display
   * @returns {Array} - Array of enhanced command objects
   */
  getAllCommands() {
    const commands = this.commandPalette.listCommands();

    return commands.map(command => {
      const metadata = this.commandMetadata.get(command.id) || {};

      return {
        id: command.id,
        name: this.extractCommandName(command.id),
        description: metadata.description || '',
        category: metadata.category || 'General',
        icon: metadata.icon || '',
        tags: metadata.tags || [],
        hotkey: metadata.hotkey || '',
        isVisible: metadata.isVisible !== false,
        pluginId: this.extractPluginId(command.id)
      };
    }).filter(command => command.isVisible);
  }

  /**
   * Get commands formatted for display in renderer
   * @returns {Array} - Filtered and formatted commands
   */
  getDisplayCommands() {
    return this.filteredCommands.map((command, index) => ({
      ...command,
      isSelected: index === this.selectedIndex
    }));
  }

  /**
   * Get list of categories
   * @returns {Array} - Array of category names with command counts
   */
  getCategoryList() {
    const categoryList = [];

    for (const [category, commandIds] of this.categories) {
      categoryList.push({
        name: category,
        count: commandIds.size,
        commands: Array.from(commandIds)
      });
    }

    return categoryList.sort((a, b) => a.name.localeCompare(b.name));
  }

  /**
   * Filter commands by category
   * @param {string} category - Category name
   */
  filterByCategory(category) {
    if (!this.categories.has(category)) {
      this.filteredCommands = [];
      return;
    }

    const categoryCommandIds = this.categories.get(category);
    const allCommands = this.getAllCommands();

    this.filteredCommands = allCommands.filter(command =>
      categoryCommandIds.has(command.id)
    );

    this.selectedIndex = 0;

    // Notify renderer of updated results
    this.notifyRenderer('command-palette:update', {
      commands: this.getDisplayCommands(),
      searchQuery: this.searchQuery,
      selectedIndex: this.selectedIndex
    });
  }

  /**
   * Clear category filter and show all commands
   */
  clearFilter() {
    this.updateFilteredCommands();

    this.notifyRenderer('command-palette:update', {
      commands: this.getDisplayCommands(),
      searchQuery: this.searchQuery,
      selectedIndex: this.selectedIndex
    });
  }

  /**
   * Extract command name from command ID
   * @param {string} commandId - Full command ID (plugin:command)
   * @returns {string} - Human-readable command name
   */
  extractCommandName(commandId) {
    const parts = commandId.split(':');
    if (parts.length > 1) {
      // Convert kebab-case to title case
      return parts[1]
        .split('-')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ');
    }
    return commandId;
  }

  /**
   * Extract plugin ID from command ID
   * @param {string} commandId - Full command ID (plugin:command)
   * @returns {string} - Plugin ID
   */
  extractPluginId(commandId) {
    const parts = commandId.split(':');
    return parts.length > 1 ? parts[0] : 'core';
  }

  /**
   * Get command palette statistics
   * @returns {Object} - Statistics object
   */
  getStats() {
    const allCommands = this.getAllCommands();
    const visibleCommands = allCommands.filter(cmd => cmd.isVisible);

    return {
      totalCommands: allCommands.length,
      visibleCommands: visibleCommands.length,
      hiddenCommands: allCommands.length - visibleCommands.length,
      categories: this.categories.size,
      hotkeys: this.hotkeys.size,
      isVisible: this.isVisible,
      currentQuery: this.searchQuery,
      filteredResults: this.filteredCommands.length
    };
  }

  /**
   * Clean up command palette UI
   */
  cleanup() {
    this.hide();
    this.categories.clear();
    this.hotkeys.clear();
    this.commandMetadata.clear();
    this.filteredCommands = [];

    this.log.INFO('[CommandPaletteUI] Cleaned up', {
      component: 'CommandPaletteUI'
    });
  }

  /**
   * Set renderer notification function
   * @param {Function} notifyFn - Function to notify renderer process
   */
  setNotifyRenderer(notifyFn) {
    this.notifyRenderer = notifyFn || (() => {});
  }

  /**
   * Default notification function (no-op until set)
   */
  notifyRenderer() {
    // Will be replaced by setNotifyRenderer
  }
}

module.exports = CommandPaletteUI;
