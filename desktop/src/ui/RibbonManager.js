const { factory: createLogger } = require('../../core/logger/CoreLogger');

/**
 * RibbonManager handles plugin-contributed toolbar icons and buttons
 * Provides secure management of UI elements added by plugins
 */
class RibbonManager {
  constructor(logDir) {
    this.ribbonIcons = new Map(); // pluginId -> Set of icon data
    this.iconCounter = 0;
    this.isInitialized = false;
    
    if (logDir) {
      const { CoreLogger } = require('../../core/logger/CoreLogger');
      this.log = new CoreLogger({ 
        component: 'RibbonManager', 
        logDir: logDir, 
        setupProcessHandlers: false
      });
    } else {
      this.log = createLogger('RibbonManager');
    }

    this.log.INFO('RibbonManager initialized', {
      component: 'RibbonManager'
    });
  }

  /**
   * Register a ribbon icon for a plugin
   * @param {string} pluginId - ID of the plugin registering the icon
   * @param {string} icon - Icon identifier (font-awesome class, unicode, or image path)
   * @param {string} title - Tooltip text for the icon
   * @param {Function} callback - Function to execute when icon is clicked
   * @returns {string} - Unique icon ID for removal
   */
  addRibbonIcon(pluginId, icon, title, callback) {
    if (!pluginId || !icon || !title || typeof callback !== 'function') {
      throw new Error('Invalid parameters for ribbon icon registration');
    }

    const iconId = `ribbon-${pluginId}-${++this.iconCounter}`;

    const iconData = {
      id: iconId,
      pluginId,
      icon,
      title,
      callback,
      createdAt: new Date(),
      enabled: true
    };

    // Initialize plugin icon set if needed
    if (!this.ribbonIcons.has(pluginId)) {
      this.ribbonIcons.set(pluginId, new Set());
    }

    this.ribbonIcons.get(pluginId).add(iconData);

    this.log.INFO(`[RibbonManager] Registered icon ${iconId} for plugin ${pluginId}: ${title}`, {
      component: 'RibbonManager',
      pluginId: pluginId,
      iconId: iconId,
      title: title
    });

    // Notify renderer to update UI
    this.notifyRenderer('ribbon:icon-added', {
      iconId,
      pluginId,
      icon,
      title
    });

    return iconId;
  }

  /**
   * Remove a specific ribbon icon
   * @param {string} iconId - ID of the icon to remove
   * @returns {boolean} - Success status
   */
  removeRibbonIcon(iconId) {
    for (const [pluginId, icons] of this.ribbonIcons) {
      for (const iconData of icons) {
        if (iconData.id === iconId) {
          icons.delete(iconData);
          this.log.INFO(`[RibbonManager] Removed icon ${iconId} from plugin ${pluginId}`, {
            component: 'RibbonManager',
            pluginId: pluginId,
            iconId: iconId
          });

          // Notify renderer to update UI
          this.notifyRenderer('ribbon:icon-removed', { iconId });
          return true;
        }
      }
    }

    this.log.WARN(`[RibbonManager] Icon ${iconId} not found for removal`, {
      component: 'RibbonManager',
      iconId: iconId
    });
    return false;
  }

  /**
   * Remove all ribbon icons for a specific plugin
   * @param {string} pluginId - ID of the plugin
   * @returns {number} - Number of icons removed
   */
  removePluginIcons(pluginId) {
    if (!this.ribbonIcons.has(pluginId)) {
      return 0;
    }

    const icons = this.ribbonIcons.get(pluginId);
    const removedCount = icons.size;

    // Notify renderer to remove each icon
    for (const iconData of icons) {
      this.notifyRenderer('ribbon:icon-removed', { iconId: iconData.id });
    }

    this.ribbonIcons.delete(pluginId);
    this.log.INFO(`[RibbonManager] Removed ${removedCount} icons for plugin ${pluginId}`, {
      component: 'RibbonManager',
      pluginId: pluginId,
      removedCount: removedCount
    });

    return removedCount;
  }

  /**
   * Execute callback for a ribbon icon
   * @param {string} iconId - ID of the icon clicked
   * @returns {boolean} - Success status
   */
  executeIconCallback(iconId) {
    for (const [pluginId, icons] of this.ribbonIcons) {
      for (const iconData of icons) {
        if (iconData.id === iconId && iconData.enabled) {
          try {
            this.log.DEBUG(`[RibbonManager] Executing callback for icon ${iconId}`, {
              component: 'RibbonManager',
              iconId: iconId
            });
            iconData.callback();
            return true;
          } catch (error) {
            this.log.ERROR(`[RibbonManager] Error executing callback for icon ${iconId}`, {
              component: 'RibbonManager',
              iconId: iconId,
              error: error.message,
              stack: error.stack
            });
            return false;
          }
        }
      }
    }

    this.log.WARN(`[RibbonManager] No enabled icon found with ID ${iconId}`, {
      component: 'RibbonManager',
      iconId: iconId
    });
    return false;
  }

  /**
   * Get all ribbon icons for display
   * @returns {Array} - Array of icon data for renderer
   */
  getAllRibbonIcons() {
    const allIcons = [];

    for (const [pluginId, icons] of this.ribbonIcons) {
      for (const iconData of icons) {
        if (iconData.enabled) {
          allIcons.push({
            id: iconData.id,
            pluginId: iconData.pluginId,
            icon: iconData.icon,
            title: iconData.title,
            createdAt: iconData.createdAt
          });
        }
      }
    }

    // Sort by creation time for consistent ordering
    return allIcons.sort((a, b) => a.createdAt - b.createdAt);
  }

  /**
   * Enable or disable a ribbon icon
   * @param {string} iconId - ID of the icon
   * @param {boolean} enabled - Enable/disable state
   * @returns {boolean} - Success status
   */
  setIconEnabled(iconId, enabled) {
    for (const [pluginId, icons] of this.ribbonIcons) {
      for (const iconData of icons) {
        if (iconData.id === iconId) {
          iconData.enabled = enabled;
          this.log.INFO(`[RibbonManager] ${enabled ? 'Enabled' : 'Disabled'} icon ${iconId}`, {
            component: 'RibbonManager',
            iconId: iconId,
            enabled: enabled
          });

          // Notify renderer to update icon state
          this.notifyRenderer('ribbon:icon-state-changed', {
            iconId,
            enabled
          });
          return true;
        }
      }
    }

    return false;
  }

  /**
   * Get ribbon icons for a specific plugin
   * @param {string} pluginId - ID of the plugin
   * @returns {Array} - Array of icon data
   */
  getPluginIcons(pluginId) {
    if (!this.ribbonIcons.has(pluginId)) {
      return [];
    }

    return Array.from(this.ribbonIcons.get(pluginId)).map(iconData => ({
      id: iconData.id,
      icon: iconData.icon,
      title: iconData.title,
      enabled: iconData.enabled,
      createdAt: iconData.createdAt
    }));
  }

  /**
   * Clean up all ribbon icons (for shutdown)
   */
  cleanup() {
    const totalIcons = Array.from(this.ribbonIcons.values())
      .reduce((sum, icons) => sum + icons.size, 0);

    this.ribbonIcons.clear();
    this.log.INFO(`[RibbonManager] Cleaned up ${totalIcons} ribbon icons`, {
      component: 'RibbonManager',
      totalIcons: totalIcons
    });
  }

  /**
   * Set renderer notification function
   * @param {Function} notifyFn - Function to notify renderer process
   */
  setNotifyRenderer(notifyFn) {
    this.notifyRenderer = notifyFn || (() => {});
  }

  /**
   * Default notification function (no-op until set)
   */
  notifyRenderer() {
    // Will be replaced by setNotifyRenderer
  }

  /**
   * Get statistics about ribbon icons
   * @returns {Object} - Statistics object
   */
  getStats() {
    let totalIcons = 0;
    let enabledIcons = 0;
    const pluginCounts = {};

    for (const [pluginId, icons] of this.ribbonIcons) {
      pluginCounts[pluginId] = icons.size;
      totalIcons += icons.size;

      for (const iconData of icons) {
        if (iconData.enabled) {
          enabledIcons++;
        }
      }
    }

    return {
      totalIcons,
      enabledIcons,
      disabledIcons: totalIcons - enabledIcons,
      pluginCount: this.ribbonIcons.size,
      pluginCounts
    };
  }
}

module.exports = RibbonManager;
