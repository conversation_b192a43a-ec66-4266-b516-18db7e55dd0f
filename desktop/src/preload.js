const { contextBridge, ipc<PERSON><PERSON>er } = require('electron');

// Expose protected methods that allow the renderer process to use
// the ipc<PERSON>enderer without exposing the entire object
contextBridge.exposeInMainWorld('lifeboard', {
  // API version for compatibility checking
  apiVersion: '0.1.0',

  // Platform information
  platform: process.platform,

  // IPC communication
  ipc: {
    // Send messages to main process
    invoke: (channel, data) => {
      const validChannels = [
        'plugins:list',
        'plugins:enable',
        'plugins:disable',
        'plugins:get-states',
        'plugins:get-info',
        'plugins:reload',
        'commands:list',
        'commands:execute',
        'ribbon:list',
        'ribbon:click',
        'modal:list',
        'modal:close',
        'modal:button-click',
        'command-palette:show',
        'command-palette:hide',
        'command-palette:search',
        'command-palette:select-next',
        'command-palette:select-previous',
        'command-palette:execute-selected',
        // M5 Settings Management
        'settings:load',
        'settings:save',
        'settings:reset',
        'settings:show-modal',
        // M5 Registry Management
        'registry:get-all',
        'registry:get-stats',
        'registry:get-preferences',
        'registry:update-preferences',
        // M5 Plugin Management UI
        'plugin-management:set-filter',
        'plugin-management:set-search',
        'plugin-management:toggle-state',
        'plugin-management:show-settings',
        'plugin-management:bulk-operation',
        // M5 Settings Modal
        'settings-modal:update',
        'settings-modal:save',
        'settings-modal:reset',
        'settings-modal:close',
        // M6 Marketplace channels
        'marketplace:search',
        'marketplace:get-plugin-details',
        'marketplace:get-stats',
        'marketplace:install',
        'marketplace:uninstall',
        'marketplace:check-updates',
        'marketplace:get-installation-status',
        'package:create',
        'package:verify',
        'ui:stats',
        'app:version',
        'app:platform',
        'log:info',
        'log:error',
        'log:warn',
        'dialog:showError',
        'dialog:showMessage'
      ];

      if (validChannels.includes(channel)) {
        return ipcRenderer.invoke(channel, data);
      }
      throw new Error(`Invalid IPC channel: ${channel}`);
    },

    // Listen for messages from main process
    on: (channel, callback) => {
      const validChannels = [
        'plugin-event',
        'app-event',
        'workspace-event',
        'ribbon:icon-added',
        'ribbon:icon-removed',
        'ribbon:icon-state-changed',
        'modal:show',
        'modal:close',
        'command-palette:show',
        'command-palette:hide',
        'command-palette:update',
        'command-palette:selection-changed',
        // M5 Plugin Management Events
        'plugin-management:plugins-updated',
        'plugin-management:filter-changed',
        'plugin-management:search-updated',
        'plugin-management:state-toggle-requested',
        'plugin-management:settings-requested',
        'plugin-management:uninstall-requested',
        'plugin-management:reload-requested',
        'plugin-management:bulk-operation-requested',
        'plugin-management:cleared',
        // M5 Settings Modal Events
        'settings-modal:show',
        'settings-modal:close',
        'settings-modal:focus',
        'settings-modal:confirm-close',
        'settings-modal:settings-updated',
        'settings-modal:save-requested',
        'settings-modal:reset',
        // M6 Marketplace Events
        'marketplace:search-completed',
        'marketplace:plugin-details-loaded',
        'marketplace:installation-started',
        'marketplace:installation-progress',
        'marketplace:installation-completed',
        'marketplace:installation-failed',
        'marketplace:uninstallation-completed',
        'marketplace:updates-available'
      ];

      if (validChannels.includes(channel)) {
        ipcRenderer.on(channel, (event, ...args) => callback(...args));
      } else {
        throw new Error(`Invalid IPC channel: ${channel}`);
      }
    },

    // Remove listener
    removeListener: (channel, callback) => {
      ipcRenderer.removeListener(channel, callback);
    }
  },

  // Plugin API (M5 Enhanced)
  plugins: {
    // List all available plugins
    list: () => ipcRenderer.invoke('plugins:list'),

    // Enable a plugin
    enable: (pluginId) => ipcRenderer.invoke('plugins:enable', pluginId),

    // Disable a plugin
    disable: (pluginId) => ipcRenderer.invoke('plugins:disable', pluginId),

    // M5: Get plugin states
    getStates: () => ipcRenderer.invoke('plugins:get-states'),

    // M5: Get comprehensive plugin info
    getInfo: (pluginId) => ipcRenderer.invoke('plugins:get-info', pluginId),

    // M5: Reload a plugin
    reload: (pluginId) => ipcRenderer.invoke('plugins:reload', pluginId),

    // M5: Plugin registry
    registry: {
      getAll: () => ipcRenderer.invoke('registry:get-all'),
      getStats: () => ipcRenderer.invoke('registry:get-stats'),
      getPreferences: () => ipcRenderer.invoke('registry:get-preferences'),
      updatePreferences: (preferences) => ipcRenderer.invoke('registry:update-preferences', preferences)
    }
  },

  // App utilities
  app: {
    // Get app version
    getVersion: () => ipcRenderer.invoke('app:version'),

    // Get platform
    getPlatform: () => ipcRenderer.invoke('app:platform'),

    // Show error dialog
    showError: (title, content) => ipcRenderer.invoke('dialog:showError', title, content),

    // Show message dialog
    showMessage: (options) => ipcRenderer.invoke('dialog:showMessage', options)
  },

  // Logging utilities
  log: {
    info: (message) => ipcRenderer.invoke('log:info', message),
    error: (message) => ipcRenderer.invoke('log:error', message),
    warn: (message) => ipcRenderer.invoke('log:warn', message)
  },

  // Workspace API (placeholder for future implementation)
  workspace: {
    // Will contain panes, views, commands etc.
    // Implemented in milestone M3
    version: '0.1.0-placeholder'
  },

  // Command system - M3 Implementation
  commands: {
    // List all registered commands
    list: () => ipcRenderer.invoke('commands:list'),

    // Execute a specific command
    execute: (commandId) => ipcRenderer.invoke('commands:execute', commandId)
  },

  // M4 UI APIs

  // Ribbon Icon System
  ribbon: {
    // List all ribbon icons
    list: () => ipcRenderer.invoke('ribbon:list'),

    // Handle ribbon icon click
    click: (iconId) => ipcRenderer.invoke('ribbon:click', iconId),

    // Listen for ribbon events
    onIconAdded: (callback) => ipcRenderer.on('ribbon:icon-added', callback),
    onIconRemoved: (callback) => ipcRenderer.on('ribbon:icon-removed', callback),
    onIconStateChanged: (callback) => ipcRenderer.on('ribbon:icon-state-changed', callback)
  },

  // Modal System
  modal: {
    // List open modals
    list: () => ipcRenderer.invoke('modal:list'),

    // Close a modal
    close: (modalId) => ipcRenderer.invoke('modal:close', modalId),

    // Handle modal button click
    buttonClick: (modalId, buttonId) => ipcRenderer.invoke('modal:button-click', modalId, buttonId),

    // Listen for modal events
    onShow: (callback) => ipcRenderer.on('modal:show', callback),
    onClose: (callback) => ipcRenderer.on('modal:close', callback)
  },

  // Command Palette UI
  commandPalette: {
    // Show command palette
    show: (query) => ipcRenderer.invoke('command-palette:show', query),

    // Hide command palette
    hide: () => ipcRenderer.invoke('command-palette:hide'),

    // Update search query
    search: (query) => ipcRenderer.invoke('command-palette:search', query),

    // Navigation
    selectNext: () => ipcRenderer.invoke('command-palette:select-next'),
    selectPrevious: () => ipcRenderer.invoke('command-palette:select-previous'),

    // Execute selected command
    executeSelected: () => ipcRenderer.invoke('command-palette:execute-selected'),

    // Listen for command palette events
    onShow: (callback) => ipcRenderer.on('command-palette:show', callback),
    onHide: (callback) => ipcRenderer.on('command-palette:hide', callback),
    onUpdate: (callback) => ipcRenderer.on('command-palette:update', callback),
    onSelectionChanged: (callback) => ipcRenderer.on('command-palette:selection-changed', callback)
  },

  // M5: Plugin Settings Management
  settings: {
    // Load plugin settings
    load: (pluginId, schema) => ipcRenderer.invoke('settings:load', pluginId, schema),

    // Save plugin settings
    save: (pluginId, settings, schema) => ipcRenderer.invoke('settings:save', pluginId, settings, schema),

    // Reset plugin settings
    reset: (pluginId) => ipcRenderer.invoke('settings:reset', pluginId),

    // Show settings modal
    showModal: (pluginId) => ipcRenderer.invoke('settings:show-modal', pluginId)
  },

  // M5: Plugin Management UI
  pluginManagement: {
    // Set filter for plugin display
    setFilter: (filter) => ipcRenderer.invoke('plugin-management:set-filter', filter),

    // Set search query
    setSearch: (query) => ipcRenderer.invoke('plugin-management:set-search', query),

    // Toggle plugin state
    toggleState: (pluginId) => ipcRenderer.invoke('plugin-management:toggle-state', pluginId),

    // Show plugin settings
    showSettings: (pluginId) => ipcRenderer.invoke('plugin-management:show-settings', pluginId),

    // Bulk operations
    bulkOperation: (operation, pluginIds) => ipcRenderer.invoke('plugin-management:bulk-operation', operation, pluginIds),

    // Listen for events
    onPluginsUpdated: (callback) => ipcRenderer.on('plugin-management:plugins-updated', callback),
    onFilterChanged: (callback) => ipcRenderer.on('plugin-management:filter-changed', callback),
    onSearchUpdated: (callback) => ipcRenderer.on('plugin-management:search-updated', callback),
    onStateToggleRequested: (callback) => ipcRenderer.on('plugin-management:state-toggle-requested', callback),
    onSettingsRequested: (callback) => ipcRenderer.on('plugin-management:settings-requested', callback),
    onUninstallRequested: (callback) => ipcRenderer.on('plugin-management:uninstall-requested', callback),
    onReloadRequested: (callback) => ipcRenderer.on('plugin-management:reload-requested', callback),
    onBulkOperationRequested: (callback) => ipcRenderer.on('plugin-management:bulk-operation-requested', callback)
  },

  // M5: Settings Modal UI
  settingsModal: {
    // Update settings in modal
    update: (modalId, settings) => ipcRenderer.invoke('settings-modal:update', modalId, settings),

    // Save settings from modal
    save: (modalId) => ipcRenderer.invoke('settings-modal:save', modalId),

    // Reset settings in modal
    reset: (modalId) => ipcRenderer.invoke('settings-modal:reset', modalId),

    // Close settings modal
    close: (modalId) => ipcRenderer.invoke('settings-modal:close', modalId),

    // Listen for events
    onShow: (callback) => ipcRenderer.on('settings-modal:show', callback),
    onClose: (callback) => ipcRenderer.on('settings-modal:close', callback),
    onFocus: (callback) => ipcRenderer.on('settings-modal:focus', callback),
    onConfirmClose: (callback) => ipcRenderer.on('settings-modal:confirm-close', callback),
    onSettingsUpdated: (callback) => ipcRenderer.on('settings-modal:settings-updated', callback),
    onSaveRequested: (callback) => ipcRenderer.on('settings-modal:save-requested', callback),
    onReset: (callback) => ipcRenderer.on('settings-modal:reset', callback)
  },

  // UI Statistics and State (M5 Enhanced)
  ui: {
    // Get UI statistics
    getStats: () => ipcRenderer.invoke('ui:stats')
  },

  // Event system (placeholder)
  events: {
    // Will contain event bus functionality
    // Implemented in milestone M3
    version: '0.1.0-placeholder'
  },

  // M6: Marketplace APIs
  marketplace: {
    // Search for plugins
    search: (query, filters) => ipcRenderer.invoke('marketplace:search', query, filters),

    // Get detailed plugin information
    getPluginDetails: (pluginId) => ipcRenderer.invoke('marketplace:get-plugin-details', pluginId),

    // Get marketplace statistics
    getStats: () => ipcRenderer.invoke('marketplace:get-stats'),

    // Install a plugin
    install: (pluginId, options) => ipcRenderer.invoke('marketplace:install', pluginId, options),

    // Uninstall a plugin
    uninstall: (pluginId, options) => ipcRenderer.invoke('marketplace:uninstall', pluginId, options),

    // Check for updates
    checkUpdates: (pluginId) => ipcRenderer.invoke('marketplace:check-updates', pluginId),

    // Get installation status
    getInstallationStatus: (pluginId) => ipcRenderer.invoke('marketplace:get-installation-status', pluginId),

    // Listen for marketplace events
    onSearchCompleted: (callback) => ipcRenderer.on('marketplace:search-completed', callback),
    onPluginDetailsLoaded: (callback) => ipcRenderer.on('marketplace:plugin-details-loaded', callback),
    onInstallationStarted: (callback) => ipcRenderer.on('marketplace:installation-started', callback),
    onInstallationProgress: (callback) => ipcRenderer.on('marketplace:installation-progress', callback),
    onInstallationCompleted: (callback) => ipcRenderer.on('marketplace:installation-completed', callback),
    onInstallationFailed: (callback) => ipcRenderer.on('marketplace:installation-failed', callback),
    onUninstallationCompleted: (callback) => ipcRenderer.on('marketplace:uninstallation-completed', callback),
    onUpdatesAvailable: (callback) => ipcRenderer.on('marketplace:updates-available', callback)
  },

  // M6: Package Management APIs
  packages: {
    // Create a plugin package
    create: (sourcePath, options) => ipcRenderer.invoke('package:create', sourcePath, options),

    // Verify a plugin package
    verify: (packagePath, expectedMetadata) => ipcRenderer.invoke('package:verify', packagePath, expectedMetadata)
  }
});

// Log that preload script has loaded
console.log('Lifeboard preload script loaded, API version:', '0.1.0');
