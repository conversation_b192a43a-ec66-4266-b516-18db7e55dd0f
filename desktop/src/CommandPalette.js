// CommandPalette.js
// Manages command registration and execution

class CommandPalette {
  constructor() {
    this.commands = new Map();
  }

  /**
   * Registers a new command.
   * @param {string} id - Unique identifier for the command.
   * @param {function} handler - Function to execute when the command is invoked.
   */
  registerCommand(id, handler) {
    this.commands.set(id, handler);
  }

  /**
   * Executes a registered command.
   * @param {string} id - Identifier of the command to execute.
   */
  executeCommand(id) {
    const command = this.commands.get(id);
    if (command) {
      command();
    } else {
      console.warn(`Command not found: ${id}`);
    }
  }

  /**
   * Lists all registered commands.
   * @returns {string[]} a list of command identifiers.
   */
  listCommands() {
    return Array.from(this.commands.keys());
  }
}

module.exports = CommandPalette;
