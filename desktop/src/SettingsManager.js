// SettingsManager.js
// Core module for plugin settings management with JSON-schema validation

const fs = require('fs');
const path = require('path');
const os = require('os');
const { factory: createLogger } = require('../core/logger/CoreLogger');
const Ajv = require('ajv');

/**
 * SettingsManager handles plugin settings with JSON-schema validation
 * Manages settings files located in $APPDATA/lifeboard/plugins/<id>/settings.json
 */
class SettingsManager {
  constructor(logDir) {
    this.ajv = new Ajv({ allErrors: true, removeAdditional: true });
    this.settingsCache = new Map();
    
    if (logDir) {
      const { CoreLogger } = require('../core/logger/CoreLogger');
      this.log = new CoreLogger({ 
        component: 'SettingsManager', 
        logDir: logDir, 
        setupProcessHandlers: false
      });
    } else {
      this.log = createLogger('settings-manager');
    }
    
    this.initializeLogger();
    this.initializeBaseDirectory();

    this.log.INFO('SettingsManager initialized', {
      baseDir: this.baseDir
    });
  }

  /**
   * Initialize logging configuration
   * @private
   */
  initializeLogger() {
    // Configure logging to follow the project's logging rules
    const logDir = path.join(process.cwd(), 'logs');
    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir, { recursive: true });
    }

    this.log.INFO('SettingsManager logger initialized', {
      baseDir: this.baseDir
    });
  }

  /**
   * Initialize the base directory for plugin settings
   * @private
   */
  initializeBaseDirectory() {
    this.baseDir = this.getAppDataDirectory();
    if (!fs.existsSync(this.baseDir)) {
      fs.mkdirSync(this.baseDir, { recursive: true });
      this.log.INFO('Created base directory', {
        baseDir: this.baseDir
      });
    }
  }

  /**
   * Get the APPDATA directory path for lifeboard plugin settings
   * @returns {string} The full path to the lifeboard plugins directory
   * @private
   */
  getAppDataDirectory() {
    let appDataPath;

    switch (process.platform) {
      case 'win32':
        appDataPath = process.env.APPDATA || path.join(os.homedir(), 'AppData', 'Roaming');
        // Normalize the APPDATA path to handle mixed separators
        appDataPath = path.normalize(appDataPath);
        break;
      case 'darwin':
        appDataPath = path.join(os.homedir(), 'Library', 'Application Support');
        break;
      default:
        appDataPath = process.env.XDG_CONFIG_HOME || path.join(os.homedir(), '.config');
        if (process.env.XDG_CONFIG_HOME) {
          appDataPath = path.normalize(appDataPath);
        }
        break;
    }

    const lifeboardPath = path.join(appDataPath, 'lifeboard', 'plugins');
    this.log.DEBUG('AppData directory', {
      lifeboardPath
    });
    return lifeboardPath;
  }

  /**
   * Get the settings file path for a plugin
   * @param {string} pluginId - The plugin identifier
   * @returns {string} The full path to the settings file
   * @private
   */
  getSettingsFilePath(pluginId) {
    if (!pluginId || typeof pluginId !== 'string') {
      throw new Error('Plugin ID must be a non-empty string');
    }

    const pluginDir = path.join(this.baseDir, pluginId);
    const settingsPath = path.join(pluginDir, 'settings.json');
    this.log.DEBUG('Settings file path for plugin', {
      pluginId,
      settingsPath
    });
    return settingsPath;
  }

  /**
   * Ensure the plugin directory exists
   * @param {string} pluginId - The plugin identifier
   * @private
   */
  ensurePluginDirectory(pluginId) {
    const pluginDir = path.join(this.baseDir, pluginId);
    if (!fs.existsSync(pluginDir)) {
      fs.mkdirSync(pluginDir, { recursive: true });
      this.log.INFO('Created plugin directory', {
        pluginId,
        pluginDir
      });
    }
  }

  /**
   * Apply default values from schema to settings object
   * @param {object} settings - The settings object to apply defaults to
   * @param {object} schema - The JSON schema containing default values
   * @returns {object} The settings object with defaults applied
   * @private
   */
  applyDefaults(settings, schema) {
    this.log.DEBUG('Applying defaults from schema', {
      hasSchema: !!schema
    });

    if (!schema || typeof schema !== 'object') {
      this.log.DEBUG('No schema provided, returning original settings', {
        settingsCount: Object.keys(settings).length
      });
      return settings;
    }

    const result = { ...settings };

    // Apply defaults from schema properties
    if (schema.properties) {
      Object.keys(schema.properties).forEach(key => {
        const property = schema.properties[key];
        if (property.default !== undefined && result[key] === undefined) {
          result[key] = property.default;
          this.log.DEBUG('Applied default value for property', {
            key,
            defaultValue: property.default
          });
        }
      });
    }

    // Apply top-level default if entire object is undefined
    if (Object.keys(result).length === 0 && schema.default !== undefined) {
      Object.assign(result, schema.default);
      this.log.DEBUG('Applied schema default', {
        schemaDefault: schema.default
      });
    }

    return result;
  }

  /**
   * Validate settings against JSON schema
   * @param {object} data - The data to validate
   * @param {object} schema - The JSON schema to validate against
   * @returns {boolean} True if valid, false otherwise
   * @private
   */
  validateSettings(data, schema) {
    if (!schema) {
      this.log.DEBUG('No schema provided, skipping validation', {
        settingsCount: Object.keys(settings).length
      });
      return true;
    }

    this.log.DEBUG('Validating settings against schema', {
      settingsCount: Object.keys(settings).length
    });

    const validate = this.ajv.compile(schema);
    const valid = validate(data);

    if (!valid) {
      this.log.ERROR('Schema validation failed', {
        validationErrors: validate.errors
      });
      validate.errors.forEach(error => {
        this.log.ERROR('Validation error detail', {
          path: error.instancePath,
          message: error.message,
          data: error.data
        });
      });
      return false;
    }

    this.log.DEBUG('Schema validation passed', {
      settingsCount: Object.keys(settings).length
    });
    return true;
  }

  /**
   * Load plugin settings from the settings file
   * @param {string} pluginId - The plugin identifier
   * @param {object} [schema] - Optional JSON schema for validation and defaults
   * @returns {object} The loaded settings object
   */
  load(pluginId, schema = null) {
    const corrId = `settings-load-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

    this.log.DEBUG('Settings load initiated', {
      pluginId,
      hasSchema: !!schema,
      corrId
    });

    try {
      if (!pluginId || typeof pluginId !== 'string') {
        throw new Error('Plugin ID must be a non-empty string');
      }

      const settingsPath = this.getSettingsFilePath(pluginId);
      let settings = {};

      // Check if settings file exists
      if (fs.existsSync(settingsPath)) {
        try {
          const fileContent = fs.readFileSync(settingsPath, 'utf8');
          this.log.DEBUG('Settings file read successfully', {
            pluginId,
            settingsPath,
            fileSize: fileContent.length,
            corrId
          });

          if (fileContent.trim()) {
            settings = JSON.parse(fileContent);
            this.log.INFO('Settings loaded from file', {
              pluginId,
              settingsKeys: Object.keys(settings).filter(key => key !== 'apiKey'),
              hasApiKey: !!settings.apiKey,
              corrId
            });
          } else {
            this.log.WARN('Settings file is empty, using defaults', {
              pluginId,
              settingsPath,
              corrId
            });
          }
        } catch (parseError) {
          this.log.ERROR('Settings file parse failed', {
            pluginId,
            settingsPath,
            error: parseError.message,
            corrId
          });
          throw new Error(`Malformed JSON in settings file: ${parseError.message}`);
        }
      } else {
        this.log.DEBUG('No settings file found, using defaults', {
          pluginId,
          settingsPath,
          corrId
        });
      }

      // Apply defaults from schema
      if (schema) {
        settings = this.applyDefaults(settings, schema);
      }

      // Validate against schema
      if (schema && !this.validateSettings(settings, schema)) {
        this.log.ERROR('Settings validation failed', {
          pluginId,
          corrId
        });
        throw new Error('Settings validation failed against provided schema');
      }

      // Cache the settings
      this.settingsCache.set(pluginId, settings);

      this.log.DEBUG('Settings load completed', {
        pluginId,
        cacheSize: this.settingsCache.size,
        corrId
      });

      return settings;
    } catch (error) {
      this.log.ERROR('Settings load failed', {
        pluginId,
        error: error.message,
        corrId
      });
      throw error;
    }
  }  /**
   * Save plugin settings to the settings file
   * @param {string} pluginId - The plugin identifier
   * @param {object} data - The settings data to save
   * @param {object} [schema] - Optional JSON schema for validation
   * @returns {boolean} True if saved successfully, false otherwise
   */
  save(pluginId, data, schema = null) {
    const corrId = `settings-save-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

    this.log.INFO('Settings save initiated', {
      pluginId,
      settingsKeys: Object.keys(data).filter(key => key !== 'apiKey'),
      hasApiKey: !!data.apiKey,
      hasSchema: !!schema,
      corrId
    });

    try {
      if (!pluginId || typeof pluginId !== 'string') {
        throw new Error('Plugin ID must be a non-empty string');
      }

      if (!data || typeof data !== 'object') {
        throw new Error('Settings data must be an object');
      }

      // Validate against schema before saving
      if (schema && !this.validateSettings(data, schema)) {
        this.log.ERROR('Settings validation failed', {
          pluginId,
          corrId
        });
        return false;
      }

      // Ensure plugin directory exists
      this.ensurePluginDirectory(pluginId);

      const settingsPath = this.getSettingsFilePath(pluginId);
      const jsonString = JSON.stringify(data, null, 2);
      fs.writeFileSync(settingsPath, jsonString, 'utf8');

      // Update cache
      this.settingsCache.set(pluginId, data);

      this.log.INFO('Settings saved successfully', {
        pluginId,
        settingsPath,
        corrId
      });

      this.log.DEBUG('Settings save details', {
        pluginId,
        settingsSize: jsonString.length,
        cacheSize: this.settingsCache.size,
        corrId
      });

      return true;
    } catch (error) {
      this.log.ERROR('Settings save failed', {
        pluginId,
        error: error.message,
        stack: error.stack,
        corrId
      });
      return false;
    }
  }

  /**
   * Clear cached settings for a plugin
   * @param {string} pluginId - The plugin identifier
   */
  clearCache(pluginId) {
    this.log.DEBUG('Clearing cache for plugin', {
      pluginId
    });
    this.settingsCache.delete(pluginId);
  }

  /**
   * Clear all cached settings
   */
  clearAllCache() {
    this.log.DEBUG('Clearing all settings cache');
    this.settingsCache.clear();
  }

  /**
   * Get the current cache size
   * @returns {number} The number of cached settings
   */
  getCacheSize() {
    return this.settingsCache.size;
  }

  /**
   * Check if settings file exists for a plugin
   * @param {string} pluginId - The plugin identifier
   * @returns {boolean} True if settings file exists
   */
  hasSettings(pluginId) {
    try {
      const settingsPath = this.getSettingsFilePath(pluginId);
      return fs.existsSync(settingsPath);
    } catch (error) {
      this.log.ERROR('Error checking settings existence for plugin', {
        pluginId,
        error: error.message,
        stack: error.stack
      });
      return false;
    }
  }

  /**
   * Delete settings file for a plugin
   * @param {string} pluginId - The plugin identifier
   * @returns {boolean} True if deleted successfully
   */
  deleteSettings(pluginId) {
    this.log.INFO('Deleting settings for plugin', {
      pluginId
    });

    try {
      const settingsPath = this.getSettingsFilePath(pluginId);

      if (fs.existsSync(settingsPath)) {
        fs.unlinkSync(settingsPath);
        this.log.INFO('Successfully deleted settings file', {
          pluginId,
          settingsPath
        });
      }

      // Clear from cache
      this.clearCache(pluginId);

      return true;
    } catch (error) {
      this.log.ERROR('Failed to delete settings for plugin', {
        pluginId,
        error: error.message,
        stack: error.stack
      });
      return false;
    }
  }
}

module.exports = SettingsManager;
