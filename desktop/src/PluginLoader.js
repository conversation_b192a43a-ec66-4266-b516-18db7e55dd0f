// PluginLoader.js

const { app } = require('electron');
const fs = require('fs');
const path = require('path');
const vm = require('vm');
const semver = require('semver');
const { getLogger: getPluginAPILogger } = require('../core/pluginAPI');

class PluginLoader {
  constructor(log, commandPalette, eventBus, ribbonManager, modalManager, commandPaletteUI, settingsManager, pluginManager = null) {
    this.log = log;
    this.commandPalette = commandPalette;
    this.eventBus = eventBus;
    this.ribbonManager = ribbonManager;
    this.modalManager = modalManager;
    this.commandPaletteUI = commandPaletteUI;
    this.settingsManager = settingsManager;
    this.pluginManager = pluginManager;
    this.plugins = new Map();
  }

  loadPlugins(pluginDirs) {
    this.log.INFO('Loading plugins...');

    pluginDirs.forEach(dir => {
      if (!fs.existsSync(dir)) {
        this.log.WARN(`Plugin directory does not exist`, { dir });
        return;
      }

      try {
        const entries = fs.readdirSync(dir, { withFileTypes: true });

        entries.forEach(entry => {
          if (entry.isDirectory()) {
            this.loadPluginFromDirectory(path.join(dir, entry.name));
          }
        });
      } catch (error) {
        this.log.ERROR(`Error scanning plugin directory`, {
          dir,
          error: error.message,
          stack: error.stack
        });
      }
    });

    this.log.INFO(`Plugin loading completed`, { pluginCount: this.plugins.size });
  }

  loadPluginFromDirectory(pluginDir) {
    const manifestPath = path.join(pluginDir, 'manifest.json');

    if (!fs.existsSync(manifestPath)) {
      this.log.DEBUG(`No manifest.json found in ${pluginDir}`, {
        component: 'PluginLoader',
        pluginDir: pluginDir
      });
      return;
    }

    try {
      const manifestContent = fs.readFileSync(manifestPath, 'utf8');
      const manifest = JSON.parse(manifestContent);

      if (!this.validateManifest(manifest)) {
        this.log.WARN(`Invalid manifest in ${pluginDir}`, {
          component: 'PluginLoader',
          pluginDir: pluginDir,
          error: error.message
        });
        return;
      }

      const appVersion = app.getVersion();
      if (manifest.minAppVersion && !semver.gte(appVersion, manifest.minAppVersion)) {
        this.log.WARN(`Plugin ${manifest.name} requires app version ${manifest.minAppVersion}, current: ${appVersion}`, {
          component: 'PluginLoader',
          pluginName: manifest.name,
          requiredVersion: manifest.minAppVersion,
          currentVersion: appVersion
        });
        return;
      }

      if (this.plugins.has(manifest.id)) {
        this.log.WARN(`Plugin ${manifest.id} is already loaded`, {
          component: 'PluginLoader',
          pluginId: manifest.id
        });
        return;
      }

      const mainPath = path.join(pluginDir, manifest.main);
      if (!fs.existsSync(mainPath)) {
        this.log.ERROR(`Main file not found for plugin ${manifest.id}: ${mainPath}`, {
          component: 'PluginLoader',
          pluginId: manifest.id,
          mainPath: mainPath
        });
        return;
      }

      try {
        const code = fs.readFileSync(mainPath, 'utf8');
        const pluginContext = this.createPluginContext(manifest, pluginDir);
        const pluginScript = new vm.Script(code, { filename: mainPath });

        pluginScript.runInContext(pluginContext);

        this.plugins.set(manifest.id, {
          manifest,
          context: pluginContext,
          directory: pluginDir,
          enabled: true,
          loadedAt: new Date()
        });

        this.log.INFO(`Successfully loaded plugin: ${manifest.name} v${manifest.version}`, {
          component: 'PluginLoader',
          pluginId: manifest.id,
          pluginName: manifest.name,
          version: manifest.version
        });

      } catch (error) {
        this.log.ERROR(`Error executing plugin ${manifest.id}`, {
          component: 'PluginLoader',
          pluginId: manifest.id,
          error: error.message,
          stack: error.stack
        });
      }

    } catch (error) {
      this.log.ERROR(`Error loading plugin from ${pluginDir}`, {
        component: 'PluginLoader',
        pluginDir: pluginDir,
        error: error.message,
        stack: error.stack
      });
    }
  }

  validateManifest(manifest) {
    const required = ['id', 'name', 'version', 'main'];
    if (!required.every(field => manifest[field])) {
      return false;
    }

    if (!semver.valid(manifest.version)) {
      this.log.WARN(`Invalid version format in manifest: ${manifest.version}`, {
        manifest: manifest.version,
        pluginId: manifest.id
      });
      return false;
    }

    if (manifest.minAppVersion && !semver.valid(manifest.minAppVersion)) {
      this.log.WARN(`Invalid minAppVersion format in manifest: ${manifest.minAppVersion}`, {
        minAppVersion: manifest.minAppVersion,
        pluginId: manifest.id
      });
      return false;
    }

    return true;
  }

  createPluginContext(manifest, pluginDir) {
    const pluginAPI = this.createPluginAPI(manifest, pluginDir);

    const sandbox = {
      console: {
        log: (...args) => this.log.INFO(`[Plugin:${manifest.id}]`, { message: args.join(' ') }),
        error: (...args) => this.log.ERROR(`[Plugin:${manifest.id}]`, { message: args.join(' ') }),
        warn: (...args) => this.log.WARN(`[Plugin:${manifest.id}]`, { message: args.join(' ') }),
        info: (...args) => this.log.INFO(`[Plugin:${manifest.id}]`, { message: args.join(' ') })
      },
      require: this.createSafeRequire(pluginDir, manifest.permissions || []),
      process: {
        env: process.env,
        platform: process.platform,
        version: process.version
      },
      Buffer,
      setTimeout,
      clearTimeout,
      setInterval,
      clearInterval,
      PluginAPI: pluginAPI,
      global: {},
      exports: {},
      module: { exports: {} }
    };

    return vm.createContext(sandbox);
  }

  createPluginAPI(manifest, pluginDir) {
    this.log.DEBUG('Creating Plugin API', { pluginId: manifest.id, pluginDir });

    return {
      app: {
        getName: () => 'Lifeboard',
        getVersion: () => app.getVersion(),
        getPlatform: () => process.platform
      },
      workspace: {
        version: '0.1.0-placeholder'
      },
      logger: getPluginAPILogger(manifest.id),
      commands: {
        register: (commandId, handler) => {
          const fullCommandId = `${manifest.id}:${commandId}`;
          this.commandPalette.registerCommand(fullCommandId, handler);
          this.log.INFO(`[Plugin:${manifest.id}] Registered command: ${commandId}`, {
            component: 'PluginLoader',
            pluginId: manifest.id,
            commandId: commandId
          });
        },
        execute: (commandId) => {
          this.commandPalette.executeCommand(`${manifest.id}:${commandId}`);
        },
        setMetadata: (commandId, metadata) => {
          const fullCommandId = `${manifest.id}:${commandId}`;
          this.commandPaletteUI.setCommandMetadata(fullCommandId, metadata);
          this.log.DEBUG(`[Plugin:${manifest.id}] Set metadata for command: ${commandId}`, {
            component: 'PluginLoader',
            pluginId: manifest.id,
            commandId: commandId
          });
        }
      },
      events: {
        on: (event, handler) => {
          this.eventBus.addPluginListener(manifest.id, event, handler);
        },
        emit: (event, data) => {
          this.eventBus.emitPluginEvent(manifest.id, event, data);
        },
        off: (event, handler) => {
          this.eventBus.removeListener(event, handler);
        }
      },
      ui: {
        addRibbonIcon: (icon, title, callback) => {
          return this.ribbonManager.addRibbonIcon(manifest.id, icon, title, callback);
        },
        removeRibbonIcon: (iconId) => {
          return this.ribbonManager.removeRibbonIcon(iconId);
        },
        showModal: (config) => {
          return this.modalManager.showModal(manifest.id, config);
        },
        closeModal: (modalId) => {
          return this.modalManager.closeModal(modalId);
        }
      },
      storage: {
        loadData: () => this.loadPluginData(manifest.id),
        saveData: (data) => this.savePluginData(manifest.id, data),
        loadSettings: (schema) => this.settingsManager.load(manifest.id, schema),
        saveSettings: (data, schema) => this.settingsManager.save(manifest.id, data, schema),
        resetSettings: () => this.resetPluginSettings(manifest.id)
      },
      network: {
        fetch: manifest.permissions && manifest.permissions.includes('network')
          ? this.createNetworkFetch()
          : undefined
      },
      manifest: {
        id: manifest.id,
        name: manifest.name,
        version: manifest.version,
        permissions: manifest.permissions || []
      }
    };
  }

  createSafeRequire(pluginDir, permissions) {
    const originalRequire = require;

    return function(moduleName) {
      const allowedModules = [
        'path', 'fs', 'util', 'crypto', 'os',
        'events', 'stream', 'querystring', 'url'
      ];

      if (permissions.includes('network')) {
        allowedModules.push('http', 'https', 'net', 'dns');
      }

      if (permissions.includes('filesystem')) {
        allowedModules.push('fs', 'path');
      }

      const blockedModules = [
        'child_process', 'cluster', 'process', 'vm',
        'electron', 'node-gyp'
      ];

      if (blockedModules.includes(moduleName)) {
        throw new Error(`Module '${moduleName}' is not allowed in plugins`);
      }

      if (moduleName.startsWith('./') || moduleName.startsWith('../')) {
        const resolvedPath = path.resolve(pluginDir, moduleName);
        if (!resolvedPath.startsWith(pluginDir)) {
          throw new Error('Plugin cannot require files outside its directory');
        }
        return originalRequire(resolvedPath);
      }

      if (allowedModules.includes(moduleName)) {
        return originalRequire(moduleName);
      }

      throw new Error(`Module '${moduleName}' is not allowed in plugins`);
    };
  }

  createNetworkFetch() {
    try {
      const fetch = require('node-fetch');
      return fetch;
    } catch (error) {
      this.log.WARN('node-fetch not available, providing placeholder network API', {
        component: 'PluginLoader'
      });
      return function() {
        throw new Error('Network functionality requires node-fetch to be installed');
      };
    }
  }

  // Methods to be implemented by PluginManager
  loadPluginData(pluginId) {
    if (this.pluginManager) {
      return this.pluginManager.loadPluginData(pluginId);
    }
    throw new Error('loadPluginData method should be implemented by PluginManager');
  }

  savePluginData(pluginId, data) {
    if (this.pluginManager) {
      return this.pluginManager.savePluginData(pluginId, data);
    }
    throw new Error('savePluginData method should be implemented by PluginManager');
  }

  resetPluginSettings(pluginId) {
    if (this.pluginManager) {
      return this.pluginManager.resetPluginSettings(pluginId);
    }
    throw new Error('resetPluginSettings method should be implemented by PluginManager');
  }

  getPlugins() {
    return this.plugins;
  }

  getPlugin(pluginId) {
    return this.plugins.get(pluginId);
  }

  hasPlugin(pluginId) {
    return this.plugins.has(pluginId);
  }

  removePlugin(pluginId) {
    return this.plugins.delete(pluginId);
  }

  reloadPlugin(pluginId) {
    if (!this.plugins.has(pluginId)) {
      this.log.WARN(`Cannot reload plugin ${pluginId}: not found`, { pluginId });
      return false;
    }

    const plugin = this.plugins.get(pluginId);
    const pluginDir = plugin.directory;

    this.plugins.delete(pluginId);
    this.loadPluginFromDirectory(pluginDir);

    this.log.INFO(`Reloaded plugin: ${pluginId}`, { pluginId });
    return true;
  }
}

module.exports = { PluginLoader };
