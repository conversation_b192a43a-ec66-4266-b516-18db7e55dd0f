#!/usr/bin/env node

/**
 * Lifeboard Plugin CLI - Command-line interface for plugin marketplace operations
 *
 * Provides comprehensive CLI functionality including:
 * - Plugin search and discovery
 * - Plugin installation and uninstallation
 * - Package creation and publishing
 * - Update management
 * - Plugin information and listing
 */

const fs = require('fs').promises;
const path = require('path');
const { program } = require('commander');
const chalk = require('chalk');
const inquirer = require('inquirer');
const ora = require('ora');

// Import marketplace managers
const MarketplaceManager = require('../marketplace/MarketplaceManager');
const PackageManager = require('../marketplace/PackageManager');

class PluginCLI {
    /**
     * Initialize the plugin CLI
     */
    constructor() {
        this.marketplace = null;
        this.packageManager = null;
        this.logger = this._createLogger();

        // CLI configuration
        this.config = {
            pluginDir: path.join(process.env.APPDATA || process.env.HOME, 'lifeboard', 'plugins'),
            outputDir: path.join(process.cwd(), 'packages'),
            verbose: false,
            interactive: true
        };
    }

    /**
     * Initialize CLI and marketplace managers
     */
    async initialize() {
        try {
            // Initialize marketplace manager
            this.marketplace = new MarketplaceManager({
                pluginDir: this.config.pluginDir,
                logger: this.logger
            });
            await this.marketplace.initialize();

            // Initialize package manager
            this.packageManager = new PackageManager({
                outputDir: this.config.outputDir,
                logger: this.logger
            });
            await this.packageManager.initialize();

            return true;
        } catch (error) {
            this.logger.error('Failed to initialize CLI:', error.message);
            throw error;
        }
    }

    /**
     * Set up CLI commands and options
     */
    setupCommands() {
        program
            .name('lifeboard-plugin')
            .description('Lifeboard Plugin Marketplace CLI')
            .version('1.0.0')
            .option('-v, --verbose', 'verbose output')
            .option('--plugin-dir <dir>', 'plugin installation directory')
            .option('--output-dir <dir>', 'package output directory')
            .hook('preAction', (thisCommand) => {
                const opts = thisCommand.opts();
                this.config.verbose = opts.verbose || false;
                this.config.pluginDir = opts.pluginDir || this.config.pluginDir;
                this.config.outputDir = opts.outputDir || this.config.outputDir;
            });

        // Search command
        program
            .command('search [query]')
            .description('search for plugins in the marketplace')
            .option('-c, --category <category>', 'filter by category')
            .option('--verified', 'show only verified plugins')
            .option('--limit <number>', 'limit number of results', '20')
            .action(async (query, options) => {
                await this.searchCommand(query, options);
            });

        // Install command
        program
            .command('install <plugin>')
            .description('install a plugin from the marketplace')
            .option('-f, --force', 'force reinstall if already installed')
            .option('-y, --yes', 'skip confirmation prompts')
            .action(async (plugin, options) => {
                await this.installCommand(plugin, options);
            });

        // Uninstall command
        program
            .command('uninstall <plugin>')
            .description('uninstall a plugin')
            .option('--backup-settings', 'backup plugin settings before removal')
            .option('-y, --yes', 'skip confirmation prompts')
            .action(async (plugin, options) => {
                await this.uninstallCommand(plugin, options);
            });

        // List command
        program
            .command('list')
            .alias('ls')
            .description('list installed plugins')
            .option('--updates', 'show available updates')
            .action(async (options) => {
                await this.listCommand(options);
            });

        // Info command
        program
            .command('info <plugin>')
            .description('show detailed information about a plugin')
            .action(async (plugin) => {
                await this.infoCommand(plugin);
            });

        // Update command
        program
            .command('update [plugin]')
            .description('update plugin(s) to latest version')
            .option('-a, --all', 'update all plugins')
            .option('-y, --yes', 'skip confirmation prompts')
            .action(async (plugin, options) => {
                await this.updateCommand(plugin, options);
            });

        // Package command
        program
            .command('package <source>')
            .description('create a plugin package from source directory')
            .option('-o, --output <dir>', 'output directory for package')
            .option('--sign', 'digitally sign the package')
            .action(async (source, options) => {
                await this.packageCommand(source, options);
            });

        // Registry command
        program
            .command('registry')
            .description('show marketplace registry information')
            .action(async () => {
                await this.registryCommand();
            });

        return program;
    }

    /**
     * Search for plugins in the marketplace
     */
    async searchCommand(query = '', options = {}) {
        const spinner = ora('Searching marketplace...').start();

        try {
            await this.initialize();

            const filters = {
                category: options.category,
                verified: options.verified
            };

            const plugins = await this.marketplace.searchPlugins(query, filters);
            spinner.stop();

            if (plugins.length === 0) {
                console.log(chalk.yellow('No plugins found matching your search criteria.'));
                return;
            }

            const limit = parseInt(options.limit, 10) || 20;
            const displayPlugins = plugins.slice(0, limit);

            console.log(chalk.blue(`\n📦 Found ${plugins.length} plugin(s)${plugins.length > limit ? ` (showing first ${limit})` : ''}:\n`));

            for (const plugin of displayPlugins) {
                const verified = plugin.verified ? chalk.green('✓') : chalk.gray('○');
                const category = plugin.category ? chalk.cyan(`[${plugin.category}]`) : '';
                const downloads = plugin.downloadCount ? chalk.gray(`${plugin.downloadCount} downloads`) : '';

                console.log(`${verified} ${chalk.bold(plugin.name)} ${chalk.gray(`v${plugin.version}`)} ${category}`);
                console.log(`   ${plugin.description || 'No description available'}`);
                console.log(`   ${chalk.gray(`by ${plugin.author || 'Unknown'}`)} ${downloads}`);
                console.log();
            }

            if (plugins.length > limit) {
                console.log(chalk.gray(`... and ${plugins.length - limit} more. Use --limit to see more results.`));
            }

        } catch (error) {
            spinner.stop();
            console.error(chalk.red(`❌ Search failed: ${error.message}`));
            process.exit(1);
        }
    }

    /**
     * Install a plugin from the marketplace
     */
    async installCommand(pluginId, options = {}) {
        const spinner = ora(`Installing ${pluginId}...`).start();

        try {
            await this.initialize();

            // Get plugin details
            const plugin = await this.marketplace.getPluginDetails(pluginId);
            spinner.stop();

            // Check if already installed
            if (plugin.isInstalled && !options.force) {
                console.log(chalk.yellow(`⚠️  Plugin ${pluginId} is already installed (v${plugin.installedVersion})`));

                if (plugin.hasUpdate) {
                    console.log(chalk.blue(`💡 Update available: v${plugin.version}`));
                    console.log(chalk.gray('Use --force to reinstall or run "lifeboard-plugin update" to update.'));
                }
                return;
            }

            // Show plugin information
            console.log(chalk.blue(`\n📦 Installing ${plugin.name} v${plugin.version}`));
            console.log(`   ${plugin.description || 'No description available'}`);
            console.log(`   ${chalk.gray(`by ${plugin.author || 'Unknown'}`)}`);

            if (plugin.permissions && plugin.permissions.length > 0) {
                console.log(`   ${chalk.yellow('⚠️  Permissions:')} ${plugin.permissions.join(', ')}`);
            }

            // Confirm installation unless --yes flag is used
            if (!options.yes && this.config.interactive) {
                const { confirm } = await inquirer.prompt([{
                    type: 'confirm',
                    name: 'confirm',
                    message: 'Do you want to continue with the installation?',
                    default: true
                }]);

                if (!confirm) {
                    console.log(chalk.gray('Installation cancelled.'));
                    return;
                }
            }

            // Start installation
            spinner.start('Installing plugin...');

            const result = await this.marketplace.installPlugin(pluginId, {
                force: options.force
            });

            spinner.stop();

            if (result.success) {
                console.log(chalk.green(`✅ Successfully installed ${pluginId} v${result.version}`));
                console.log(chalk.gray(`   Installed to: ${result.installPath}`));
            } else {
                console.error(chalk.red(`❌ Installation failed: ${result.error}`));
            }

        } catch (error) {
            spinner.stop();
            console.error(chalk.red(`❌ Installation failed: ${error.message}`));
            process.exit(1);
        }
    }

    /**
     * Uninstall a plugin
     */
    async uninstallCommand(pluginId, options = {}) {
        try {
            await this.initialize();

            // Confirm uninstallation unless --yes flag is used
            if (!options.yes && this.config.interactive) {
                const { confirm } = await inquirer.prompt([{
                    type: 'confirm',
                    name: 'confirm',
                    message: `Are you sure you want to uninstall ${pluginId}?`,
                    default: false
                }]);

                if (!confirm) {
                    console.log(chalk.gray('Uninstallation cancelled.'));
                    return;
                }
            }

            const spinner = ora(`Uninstalling ${pluginId}...`).start();

            const result = await this.marketplace.uninstallPlugin(pluginId, {
                backupSettings: options.backupSettings
            });

            spinner.stop();

            if (result.success) {
                console.log(chalk.green(`✅ Successfully uninstalled ${pluginId}`));
                if (result.settingsBackup) {
                    console.log(chalk.blue('💾 Settings have been backed up.'));
                }
            } else {
                console.error(chalk.red(`❌ Uninstallation failed: ${result.error}`));
            }

        } catch (error) {
            console.error(chalk.red(`❌ Uninstallation failed: ${error.message}`));
            process.exit(1);
        }
    }

    /**
     * List installed plugins
     */
    async listCommand(options = {}) {
        const spinner = ora('Loading installed plugins...').start();

        try {
            await this.initialize();

            let updates = [];
            if (options.updates) {
                updates = await this.marketplace.checkForUpdates();
            }

            spinner.stop();

            // Get marketplace stats for context
            const stats = await this.marketplace.getMarketplaceStats();

            console.log(chalk.blue(`\n📊 Plugin Status (${stats.installedPlugins} installed / ${stats.totalPlugins} available):\n`));

            if (stats.installedPlugins === 0) {
                console.log(chalk.gray('No plugins installed.'));
                console.log(chalk.blue('💡 Use "lifeboard-plugin search" to find plugins to install.'));
                return;
            }

            // TODO: Get actual installed plugin list with details
            // For now, we'll use a placeholder implementation
            console.log(chalk.gray('Installed plugin listing not yet implemented.'));
            console.log(chalk.blue('💡 This feature will be available in the next update.'));

            if (updates.length > 0) {
                console.log(chalk.yellow(`\n🔄 ${updates.length} update(s) available:`));
                for (const update of updates) {
                    console.log(`   ${update.pluginId}: ${update.currentVersion} → ${update.availableVersion}`);
                }
                console.log(chalk.blue('\n💡 Use "lifeboard-plugin update" to install updates.'));
            }

        } catch (error) {
            spinner.stop();
            console.error(chalk.red(`❌ Failed to list plugins: ${error.message}`));
            process.exit(1);
        }
    }

    /**
     * Show detailed plugin information
     */
    async infoCommand(pluginId) {
        const spinner = ora(`Loading plugin information...`).start();

        try {
            await this.initialize();

            const plugin = await this.marketplace.getPluginDetails(pluginId);
            spinner.stop();

            const verified = plugin.verified ? chalk.green('✓ Verified') : chalk.gray('○ Unverified');
            const installed = plugin.isInstalled ? chalk.green('✓ Installed') : chalk.gray('○ Not installed');

            console.log(chalk.blue(`\n📦 ${plugin.name} v${plugin.version}`));
            console.log(`   ${plugin.description || 'No description available'}`);
            console.log(`   ${chalk.gray(`by ${plugin.author || 'Unknown'}`)}`);
            console.log();

            console.log(`   Status: ${installed} ${verified}`);
            if (plugin.isInstalled) {
                console.log(`   Installed Version: ${plugin.installedVersion}`);
                if (plugin.hasUpdate) {
                    console.log(chalk.yellow(`   Update Available: v${plugin.version}`));
                }
            }
            console.log();

            if (plugin.category) {
                console.log(`   Category: ${chalk.cyan(plugin.category)}`);
            }

            if (plugin.tags && plugin.tags.length > 0) {
                console.log(`   Tags: ${plugin.tags.map(tag => chalk.cyan(tag)).join(', ')}`);
            }

            if (plugin.permissions && plugin.permissions.length > 0) {
                console.log(`   Permissions: ${chalk.yellow(plugin.permissions.join(', '))}`);
            }

            if (plugin.minAppVersion) {
                console.log(`   Minimum App Version: ${plugin.minAppVersion}`);
            }

            if (plugin.size) {
                const sizeKB = Math.round(plugin.size / 1024);
                console.log(`   Package Size: ${sizeKB} KB`);
            }

            if (plugin.downloadCount) {
                console.log(`   Downloads: ${plugin.downloadCount.toLocaleString()}`);
            }

            if (plugin.lastUpdated) {
                console.log(`   Last Updated: ${new Date(plugin.lastUpdated).toLocaleDateString()}`);
            }

            console.log();

        } catch (error) {
            spinner.stop();
            console.error(chalk.red(`❌ Failed to get plugin info: ${error.message}`));
            process.exit(1);
        }
    }

    /**
     * Update plugins to latest versions
     */
    async updateCommand(pluginId, options = {}) {
        const spinner = ora('Checking for updates...').start();

        try {
            await this.initialize();

            const updates = await this.marketplace.checkForUpdates(pluginId);
            spinner.stop();

            if (updates.length === 0) {
                console.log(chalk.green('✅ All plugins are up to date.'));
                return;
            }

            console.log(chalk.blue(`\n🔄 ${updates.length} update(s) available:\n`));

            for (const update of updates) {
                console.log(`   ${update.pluginId}: ${update.currentVersion} → ${chalk.green(update.availableVersion)}`);
                if (update.updateInfo && update.updateInfo !== 'No changelog available') {
                    console.log(`   ${chalk.gray(update.updateInfo)}`);
                }
                console.log();
            }

            // Confirm updates unless --yes flag is used
            if (!options.yes && this.config.interactive) {
                const { confirm } = await inquirer.prompt([{
                    type: 'confirm',
                    name: 'confirm',
                    message: 'Do you want to install these updates?',
                    default: true
                }]);

                if (!confirm) {
                    console.log(chalk.gray('Updates cancelled.'));
                    return;
                }
            }

            // Install updates
            for (const update of updates) {
                const updateSpinner = ora(`Updating ${update.pluginId}...`).start();
                try {
                    await this.marketplace.installPlugin(update.pluginId, { force: true });
                    updateSpinner.succeed(`Updated ${update.pluginId} to v${update.availableVersion}`);
                } catch (error) {
                    updateSpinner.fail(`Failed to update ${update.pluginId}: ${error.message}`);
                }
            }

            console.log(chalk.green('\n✅ Updates completed.'));

        } catch (error) {
            spinner.stop();
            console.error(chalk.red(`❌ Update check failed: ${error.message}`));
            process.exit(1);
        }
    }

    /**
     * Create a plugin package
     */
    async packageCommand(sourcePath, options = {}) {
        const spinner = ora('Creating package...').start();

        try {
            await this.initialize();

            const outputDir = options.output || this.config.outputDir;
            this.packageManager.outputDir = outputDir;

            const result = await this.packageManager.createPackage(sourcePath, {
                sign: options.sign
            });

            spinner.stop();

            if (result.success) {
                console.log(chalk.green(`✅ Package created successfully:`));
                console.log(`   📦 Package: ${result.packagePath}`);
                console.log(`   📄 Metadata: ${result.metadataPath}`);
                console.log(`   🏷️  Version: ${result.metadata.version}`);
                console.log(`   📊 Size: ${Math.round(result.metadata.packageInfo.size / 1024)} KB`);
                console.log(`   📁 Files: ${result.metadata.packageInfo.files}`);

                if (result.metadata.packageInfo.signature) {
                    console.log(`   🔐 Signed: Yes`);
                }

                console.log(`   🔑 Hash: ${result.metadata.packageInfo.sha256.substring(0, 16)}...`);
            }

        } catch (error) {
            spinner.stop();
            console.error(chalk.red(`❌ Package creation failed: ${error.message}`));
            process.exit(1);
        }
    }

    /**
     * Show marketplace registry information
     */
    async registryCommand() {
        const spinner = ora('Loading registry information...').start();

        try {
            await this.initialize();

            const stats = await this.marketplace.getMarketplaceStats();
            const registry = await this.marketplace.getRegistry();

            spinner.stop();

            console.log(chalk.blue('\n📊 Marketplace Registry Information:\n'));

            console.log(`   Total Plugins: ${chalk.bold(stats.totalPlugins)}`);
            console.log(`   Verified Plugins: ${chalk.green(stats.verifiedPlugins)}`);
            console.log(`   Installed Plugins: ${chalk.blue(stats.installedPlugins)}`);

            if (stats.categories && stats.categories.length > 0) {
                console.log(`   Categories: ${stats.categories.map(cat => chalk.cyan(cat)).join(', ')}`);
            }

            if (stats.lastUpdated) {
                console.log(`   Last Updated: ${new Date(stats.lastUpdated).toLocaleString()}`);
            }

            if (registry.version) {
                console.log(`   Registry Version: ${registry.version}`);
            }

            console.log();

        } catch (error) {
            spinner.stop();
            console.error(chalk.red(`❌ Failed to load registry info: ${error.message}`));
            process.exit(1);
        }
    }

    /**
     * Create a logger instance
     * @private
     */
    _createLogger() {
        return {
            info: (...args) => {
                if (this.config.verbose) {
                    console.log(chalk.blue('[INFO]'), ...args);
                }
            },
            warn: (...args) => {
                if (this.config.verbose) {
                    console.log(chalk.yellow('[WARN]'), ...args);
                }
            },
            error: (...args) => {
                if (this.config.verbose) {
                    console.log(chalk.red('[ERROR]'), ...args);
                }
            },
            debug: (...args) => {
                if (this.config.verbose) {
                    console.log(chalk.gray('[DEBUG]'), ...args);
                }
            }
        };
    }
}

// Main execution
async function main() {
    const cli = new PluginCLI();
    const program = cli.setupCommands();

    try {
        await program.parseAsync(process.argv);
    } catch (error) {
        console.error(chalk.red('❌ CLI Error:', error.message));
        process.exit(1);
    }
}

// Run CLI if this file is executed directly
if (require.main === module) {
    main().catch(error => {
        console.error(chalk.red('❌ Unexpected error:', error.message));
        process.exit(1);
    });
}

module.exports = PluginCLI;
