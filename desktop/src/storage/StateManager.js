/**
 * StateManager.js
 * Manages plugin lifecycle states with persistence and validation
 *
 * Handles plugin states: discovered, installing, installed, enabled, disabled,
 * updating, error, uninstalling
 */

const fs = require('fs');
const path = require('path');
const { EventEmitter } = require('events');
const { factory: createLogger } = require('../../core/logger/CoreLogger');

/**
 * StateManager handles plugin lifecycle states with persistence
 * Manages state files located in $APPDATA/lifeboard/plugins/<id>/state.json
 */
class StateManager extends EventEmitter {
  constructor(baseDirectory, logDir) {
    super();
    this.baseDir = baseDirectory;
    this.stateCache = new Map();
    
    if (logDir) {
      const { CoreLogger } = require('../../core/logger/CoreLogger');
      this.log = new CoreLogger({ 
        component: 'StateManager', 
        logDir: logDir, 
        setupProcessHandlers: false
      });
    } else {
      this.log = createLogger('StateManager');
    }
    
    this.stateTransitions = this.initializeStateTransitions();
    this.initializeLogger();

    this.log.INFO('StateManager initialized', {
      baseDir: this.baseDir,
      stateTransitionsCount: this.stateTransitions.size
    });
  }

  /**
   * Initialize logging configuration
   * @private
   */
  initializeLogger() {
    this.log.INFO('StateManager logger initialized', {
      component: 'StateManager',
      baseDir: this.baseDir
    });
  }

  /**
   * Initialize valid state transitions
   * @returns {Map} Map of valid state transitions
   * @private
   */
  initializeStateTransitions() {
    const transitions = new Map([
      ['discovered', ['installing']],
      ['installing', ['installed', 'error']],
      ['installed', ['enabled', 'uninstalling']],
      ['enabled', ['disabled', 'updating', 'uninstalling']],
      ['disabled', ['enabled', 'uninstalling']],
      ['updating', ['enabled', 'error']],
      ['error', ['disabled', 'uninstalling']],
      ['uninstalling', ['discovered']]
    ]);

    this.log.DEBUG('Initialized state transitions', {
      transitionCount: transitions.size
    });
    return transitions;
  }

  /**
   * Get the state file path for a plugin
   * @param {string} pluginId - The plugin identifier
   * @returns {string} The full path to the state file
   * @private
   */
  getStateFilePath(pluginId) {
    if (!pluginId || typeof pluginId !== 'string') {
      throw new Error('Plugin ID must be a non-empty string');
    }

    const pluginDir = path.join(this.baseDir, pluginId);
    const statePath = path.join(pluginDir, 'state.json');
    this.log.DEBUG('State file path for plugin', {
      pluginId,
      statePath
    });
    return statePath;
  }

  /**
   * Ensure the plugin directory exists
   * @param {string} pluginId - The plugin identifier
   * @private
   */
  ensurePluginDirectory(pluginId) {
    const pluginDir = path.join(this.baseDir, pluginId);
    if (!fs.existsSync(pluginDir)) {
      fs.mkdirSync(pluginDir, { recursive: true });
      this.log.INFO('Created plugin directory', {
        pluginId,
        pluginDir
      });
    }
  }

  /**
   * Create default state object for a plugin
   * @param {string} pluginId - The plugin identifier
   * @param {string} version - The plugin version
   * @param {string} initialState - The initial state
   * @returns {object} Default state object
   * @private
   */
  createDefaultState(pluginId, version, initialState = 'discovered') {
    const now = new Date().toISOString();

    return {
      pluginId,
      version,
      state: initialState,
      lastEnabled: null,
      lastDisabled: null,
      enabledCount: 0,
      createdAt: now,
      lastModified: now,
      statistics: {
        totalCommands: 0,
        totalEvents: 0,
        lastActivity: null
      },
      permissions: {
        granted: [],
        requested: [],
        denied: []
      }
    };
  }

  /**
   * Validate state transition
   * @param {string} currentState - Current plugin state
   * @param {string} newState - Desired new state
   * @returns {boolean} True if transition is valid
   * @private
   */
  isValidTransition(currentState, newState) {
    const validTransitions = this.stateTransitions.get(currentState);
    if (!validTransitions) {
      this.log.ERROR(`[StateManager] Unknown current state: ${currentState}`, {
        component: 'StateManager',
        currentState: currentState
      });
      return false;
    }

    const isValid = validTransitions.includes(newState);
    this.log.DEBUG(`[StateManager] State transition ${currentState} -> ${newState}: ${isValid ? 'valid' : 'invalid'}`, {
      component: 'StateManager',
      currentState: currentState,
      newState: newState,
      isValid: isValid
    });
    return isValid;
  }

  /**
   * Load plugin state from file
   * @param {string} pluginId - The plugin identifier
   * @returns {object} The plugin state object
   */
  getPluginState(pluginId) {
    this.log.DEBUG(`[StateManager] Getting state for plugin: ${pluginId}`, {
      component: 'StateManager',
      pluginId: pluginId
    });

    try {
      if (!pluginId || typeof pluginId !== 'string') {
        throw new Error('Plugin ID must be a non-empty string');
      }

      // Check cache first
      if (this.stateCache.has(pluginId)) {
        const cachedState = this.stateCache.get(pluginId);
        this.log.DEBUG(`[StateManager] Retrieved state from cache for ${pluginId}`, {
          component: 'StateManager',
          pluginId: pluginId
        });
        return cachedState;
      }

      const statePath = this.getStateFilePath(pluginId);
      let state = null;

      // Check if state file exists
      if (fs.existsSync(statePath)) {
        try {
          const fileContent = fs.readFileSync(statePath, 'utf8');
          this.log.DEBUG(`[StateManager] Read state file: ${statePath}`, {
            component: 'StateManager',
            statePath: statePath
          });

          if (fileContent.trim()) {
            state = JSON.parse(fileContent);
            this.log.DEBUG(`[StateManager] Parsed state for ${pluginId}: ${state.state}`, {
              component: 'StateManager',
              pluginId: pluginId,
              state: state.state
            });
          } else {
            this.log.WARN(`[StateManager] State file is empty: ${statePath}`, {
              component: 'StateManager',
              statePath: statePath
            });
          }
        } catch (parseError) {
          this.log.ERROR(`[StateManager] Failed to parse state file ${statePath}`, {
            component: 'StateManager',
            statePath: statePath,
            error: parseError.message,
            stack: parseError.stack
          });
          throw new Error(`Malformed JSON in state file: ${parseError.message}`);
        }
      }

      // If no state found, create default
      if (!state) {
        state = this.createDefaultState(pluginId, '0.0.0', 'disabled');
        this.log.INFO('*** DEBUG: No state file found for plugin, creating default disabled state ***', {
          pluginId,
          state: state.state,
          stackTrace: new Error().stack
        });
      }

      // Cache the state
      this.stateCache.set(pluginId, state);

      this.log.DEBUG(`[StateManager] Successfully loaded state for plugin: ${pluginId}`, {
        component: 'StateManager',
        pluginId: pluginId
      });
      return state;
    } catch (error) {
      this.log.ERROR(`[StateManager] Failed to get state for plugin ${pluginId}`, {
        component: 'StateManager',
        pluginId: pluginId,
        error: error.message,
        stack: error.stack
      });
      throw error;
    }
  }

  /**
   * Save plugin state to file
   * @param {string} pluginId - The plugin identifier
   * @param {object} stateData - The state data to save
   * @returns {boolean} True if saved successfully
   */
  setPluginState(pluginId, stateData) {
    this.log.INFO('*** DEBUG: Setting state for plugin ***', {
      pluginId,
      newState: stateData?.state,
      previousState: this.stateCache.get(pluginId)?.state,
      stackTrace: new Error().stack
    });

    try {
      if (!pluginId || typeof pluginId !== 'string') {
        throw new Error('Plugin ID must be a non-empty string');
      }

      if (!stateData || typeof stateData !== 'object') {
        throw new Error('State data must be an object');
      }

      // Get current state for transition validation
      const currentState = this.getPluginState(pluginId);

      // Validate state transition if state is changing
      if (stateData.state && stateData.state !== currentState.state) {
        if (!this.isValidTransition(currentState.state, stateData.state)) {
          throw new Error(`Invalid state transition from ${currentState.state} to ${stateData.state}`);
        }
      }

      // Merge with existing state data
      const updatedState = {
        ...currentState,
        ...stateData,
        lastModified: new Date().toISOString()
      };

      // Update timestamps based on state changes
      if (stateData.state === 'enabled' && currentState.state !== 'enabled') {
        updatedState.lastEnabled = new Date().toISOString();
        updatedState.enabledCount = (currentState.enabledCount || 0) + 1;
      } else if (stateData.state === 'disabled' && currentState.state === 'enabled') {
        updatedState.lastDisabled = new Date().toISOString();
      }

      // Ensure plugin directory exists
      this.ensurePluginDirectory(pluginId);

      const statePath = this.getStateFilePath(pluginId);
      const jsonString = JSON.stringify(updatedState, null, 2);

      fs.writeFileSync(statePath, jsonString, 'utf8');

      // Update cache
      this.stateCache.set(pluginId, updatedState);

      // Emit state change event
      this.emit('stateChanged', {
        pluginId,
        oldState: currentState.state,
        newState: updatedState.state,
        timestamp: updatedState.lastModified
      });

      this.log.INFO('Successfully saved state for plugin', {
        pluginId,
        state: updatedState.state,
        statePath
      });
      this.log.DEBUG(`[StateManager] State transition: ${currentState.state} -> ${updatedState.state}`, {
        component: 'StateManager',
        oldState: currentState.state,
        newState: updatedState.state
      });

      return true;
    } catch (error) {
      this.log.ERROR(`[StateManager] Failed to set state for plugin ${pluginId}`, {
        component: 'StateManager',
        pluginId: pluginId,
        error: error.message,
        stack: error.stack
      });
      this.emit('error', {
        pluginId,
        error: error.message,
        timestamp: new Date().toISOString()
      });
      return false;
    }
  }

  /**
   * Enable a plugin (transition to enabled state)
   * @param {string} pluginId - The plugin identifier
   * @returns {boolean} True if enabled successfully
   */
  enablePlugin(pluginId) {
    this.log.INFO('Enabling plugin', {
      pluginId,
      currentState: this.stateCache.get(pluginId)
    });

    try {
      const currentState = this.getPluginState(pluginId);

      if (currentState.state === 'enabled') {
        this.log.INFO('Plugin is already enabled', {
          pluginId,
          currentState: this.stateCache.get(pluginId)
        });
        return true;
      }

      return this.setPluginState(pluginId, { state: 'enabled' });
    } catch (error) {
      this.log.ERROR(`[StateManager] Failed to enable plugin ${pluginId}`, {
        component: 'StateManager',
        pluginId: pluginId,
        error: error.message,
        stack: error.stack
      });
      return false;
    }
  }

  /**
   * Disable a plugin (transition to disabled state)
   * @param {string} pluginId - The plugin identifier
   * @returns {boolean} True if disabled successfully
   */
  disablePlugin(pluginId) {
    this.log.INFO('Disabling plugin', {
      pluginId,
      currentState: this.stateCache.get(pluginId)
    });

    try {
      const currentState = this.getPluginState(pluginId);

      if (currentState.state === 'disabled') {
        this.log.INFO('Plugin is already disabled', {
          pluginId,
          currentState: this.stateCache.get(pluginId)
        });
        return true;
      }

      return this.setPluginState(pluginId, { state: 'disabled' });
    } catch (error) {
      this.log.ERROR(`[StateManager] Failed to disable plugin ${pluginId}`, {
        component: 'StateManager',
        pluginId: pluginId,
        error: error.message,
        stack: error.stack
      });
      return false;
    }
  }

  /**
   * Get all plugin states
   * @returns {Array} Array of all plugin states
   */
  getAllPluginStates() {
    this.log.DEBUG('[StateManager] Getting all plugin states', {
      component: 'StateManager'
    });

    try {
      const states = [];

      // Read all plugin directories
      if (fs.existsSync(this.baseDir)) {
        const pluginDirs = fs.readdirSync(this.baseDir, { withFileTypes: true });

        pluginDirs.forEach(dir => {
          if (dir.isDirectory()) {
            try {
              const state = this.getPluginState(dir.name);
              states.push(state);
            } catch (error) {
              this.log.ERROR(`[StateManager] Failed to load state for plugin ${dir.name}`, {
                component: 'StateManager',
                pluginId: dir.name,
                error: error.message,
                stack: error.stack
              });
            }
          }
        });
      }

      this.log.DEBUG(`[StateManager] Retrieved ${states.length} plugin states`, {
        component: 'StateManager',
        stateCount: states.length
      });
      return states;
    } catch (error) {
      this.log.ERROR('[StateManager] Failed to get all plugin states', {
        component: 'StateManager',
        error: error.message,
        stack: error.stack
      });
      return [];
    }
  }

  /**
   * Update plugin statistics
   * @param {string} pluginId - The plugin identifier
   * @param {object} stats - Statistics to update
   * @returns {boolean} True if updated successfully
   */
  updateStatistics(pluginId, stats) {
    this.log.DEBUG(`[StateManager] Updating statistics for plugin: ${pluginId}`, { pluginId });

    try {
      const currentState = this.getPluginState(pluginId);
      const updatedStats = {
        ...currentState.statistics,
        ...stats,
        lastActivity: new Date().toISOString()
      };

      return this.setPluginState(pluginId, { statistics: updatedStats });
    } catch (error) {
      this.log.ERROR(`[StateManager] Failed to update statistics for plugin ${pluginId}:`, {
        pluginId,
        error: error.message,
        stack: error.stack
      });
      return false;
    }
  }

  /**
   * Update plugin permissions
   * @param {string} pluginId - The plugin identifier
   * @param {object} permissions - Permissions to update
   * @returns {boolean} True if updated successfully
   */
  updatePermissions(pluginId, permissions) {
    this.log.DEBUG(`[StateManager] Updating permissions for plugin: ${pluginId}`, { pluginId });

    try {
      const currentState = this.getPluginState(pluginId);
      const updatedPermissions = {
        ...currentState.permissions,
        ...permissions
      };

      return this.setPluginState(pluginId, { permissions: updatedPermissions });
    } catch (error) {
      this.log.ERROR(`[StateManager] Failed to update permissions for plugin ${pluginId}:`, {
        pluginId,
        error: error.message,
        stack: error.stack
      });
      return false;
    }
  }

  /**
   * Clear cached state for a plugin
   * @param {string} pluginId - The plugin identifier
   */
  clearCache(pluginId) {
    this.log.DEBUG(`[StateManager] Clearing cache for plugin: ${pluginId}`, { pluginId });
    this.stateCache.delete(pluginId);
  }

  /**
   * Clear all cached states
   */
  clearAllCache() {
    this.log.DEBUG('[StateManager] Clearing all state cache', { cacheSize: this.stateCache.size });
    this.stateCache.clear();
  }

  /**
   * Get the current cache size
   * @returns {number} The number of cached states
   */
  getCacheSize() {
    return this.stateCache.size;
  }

  /**
   * Check if state file exists for a plugin
   * @param {string} pluginId - The plugin identifier
   * @returns {boolean} True if state file exists
   */
  hasState(pluginId) {
    try {
      const statePath = this.getStateFilePath(pluginId);
      return fs.existsSync(statePath);
    } catch (error) {
      this.log.ERROR(`[StateManager] Error checking state existence for plugin ${pluginId}:`, {
        pluginId,
        error: error.message,
        stack: error.stack
      });
      return false;
    }
  }

  /**
   * Delete state file for a plugin
   * @param {string} pluginId - The plugin identifier
   * @returns {boolean} True if deleted successfully
   */
  deleteState(pluginId) {
    this.log.INFO('Deleting state for plugin', {
      pluginId,
      currentState: this.stateCache.get(pluginId)
    });

    try {
      const statePath = this.getStateFilePath(pluginId);

      if (fs.existsSync(statePath)) {
        fs.unlinkSync(statePath);
        this.log.INFO('Successfully deleted state file', {
          pluginId,
          statePath
        });
      }

      // Clear from cache
      this.clearCache(pluginId);

      return true;
    } catch (error) {
      this.log.ERROR(`[StateManager] Failed to delete state for plugin ${pluginId}:`, {
        pluginId,
        error: error.message,
        stack: error.stack
      });
      return false;
    }
  }
}

module.exports = StateManager;
