// EventBus.js
// Plugin event system using Node.js EventEmitter

const { EventEmitter } = require('events');

class PluginEventBus extends EventEmitter {
  constructor() {
    super();
    this.setMaxListeners(100); // Allow more listeners for plugins
    this.pluginListeners = new Map(); // Track listeners by plugin
  }

  /**
   * Register an event listener for a specific plugin
   * @param {string} pluginId - Plugin identifier
   * @param {string} event - Event name
   * @param {function} handler - Event handler function
   */
  addPluginListener(pluginId, event, handler) {
    if (!this.pluginListeners.has(pluginId)) {
      this.pluginListeners.set(pluginId, []);
    }

    this.pluginListeners.get(pluginId).push({ event, handler });
    this.on(event, handler);
  }

  /**
   * Remove all listeners for a specific plugin
   * @param {string} pluginId - Plugin identifier
   */
  removePluginListeners(pluginId) {
    if (this.pluginListeners.has(pluginId)) {
      const listeners = this.pluginListeners.get(pluginId);
      listeners.forEach(({ event, handler }) => {
        this.removeListener(event, handler);
      });
      this.pluginListeners.delete(pluginId);
    }
  }

  /**
   * Emit a lifecycle event
   * @param {string} event - Lifecycle event name
   * @param {object} data - Event data
   */
  emitLifecycleEvent(event, data) {
    this.emit(`lifecycle:${event}`, data);
  }

  /**
   * Emit a plugin-specific event
   * @param {string} pluginId - Plugin identifier
   * @param {string} event - Event name
   * @param {object} data - Event data
   */
  emitPluginEvent(pluginId, event, data) {
    this.emit(`plugin:${pluginId}:${event}`, data);
  }
}

module.exports = PluginEventBus;
