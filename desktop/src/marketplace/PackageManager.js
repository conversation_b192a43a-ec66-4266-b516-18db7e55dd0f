/**
 * Package Manager - Plugin package creation and signing system
 *
 * Provides comprehensive package management functionality including:
 * - Plugin package creation with ZIP compression
 * - Digital signature generation and verification
 * - Package metadata and manifest validation
 * - SHA256 hash calculation and verification
 * - Development vs production package handling
 */

const fs = require('fs').promises;
const path = require('path');
const crypto = require('crypto');
const archiver = require('archiver');
const semver = require('semver');

class PackageManager {
    /**
     * Initialize the package manager
     * @param {Object} options Configuration options
     * @param {string} options.outputDir Package output directory
     * @param {Object} options.signingConfig Digital signing configuration
     * @param {Object} options.logger Logger instance
     */
    constructor(options = {}) {
        this.outputDir = options.outputDir || path.join(process.cwd(), 'packages');
        this.signingConfig = options.signingConfig || {};
        this.logger = options.logger || console;

        // Package configuration
        this.config = {
            compressionLevel: 9,
            includeDevFiles: process.env.NODE_ENV === 'development',
            requireSigning: process.env.NODE_ENV === 'production',
            manifestRequired: true,
            validateDependencies: true
        };

        // File patterns to exclude from packages
        this.excludePatterns = [
            '**/.DS_Store',
            '**/Thumbs.db',
            '**/*.tmp',
            '**/*.log',
            '**/node_modules/**',
            '**/.git/**',
            '**/.gitignore',
            '**/package-lock.json',
            '**/yarn.lock'
        ];

        // Development-only file patterns
        this.devOnlyPatterns = [
            '**/test/**',
            '**/tests/**',
            '**/*.test.js',
            '**/*.spec.js',
            '**/README.md',
            '**/CHANGELOG.md',
            '**/.eslintrc*',
            '**/tsconfig.json'
        ];

        this.logger.info('[PackageManager] Initialized', {
            outputDir: this.outputDir,
            requireSigning: this.config.requireSigning
        });
    }

    /**
     * Initialize package manager and create necessary directories
     */
    async initialize() {
        try {
            await fs.mkdir(this.outputDir, { recursive: true });
            this.logger.info('[PackageManager] Directories initialized');
            return true;
        } catch (error) {
            this.logger.error('[PackageManager] Failed to initialize directories', { error: error.message });
            throw new Error(`Package manager initialization failed: ${error.message}`);
        }
    }

    /**
     * Create a plugin package from a source directory
     * @param {string} sourcePath Path to plugin source directory
     * @param {Object} options Packaging options
     * @returns {Object} Package creation result
     */
    async createPackage(sourcePath, options = {}) {
        try {
            this.logger.info('[PackageManager] Starting package creation', { sourcePath });

            // Validate source directory
            const manifest = await this._validateSourceDirectory(sourcePath);

            // Create package filename
            const packageName = `${manifest.id}-${manifest.version}.zip`;
            const packagePath = path.join(this.outputDir, packageName);

            // Remove existing package if it exists
            try {
                await fs.unlink(packagePath);
                this.logger.debug('[PackageManager] Removed existing package', { packagePath });
            } catch {
                // Package doesn't exist, which is fine
            }

            // Create ZIP package
            const packageInfo = await this._createZipPackage(sourcePath, packagePath, manifest, options);

            // Calculate package hash
            const packageHash = await this._calculatePackageHash(packagePath);

            // Generate digital signature if required
            let signature = null;
            if (this.config.requireSigning || options.sign) {
                signature = await this._signPackage(packagePath, packageHash);
            }

            // Create package metadata
            const metadata = {
                id: manifest.id,
                name: manifest.name,
                version: manifest.version,
                description: manifest.description,
                author: manifest.author,
                category: manifest.category,
                tags: manifest.tags || [],
                permissions: manifest.permissions || [],
                minAppVersion: manifest.minAppVersion,
                packageInfo: {
                    filename: packageName,
                    size: packageInfo.size,
                    files: packageInfo.files,
                    sha256: packageHash,
                    signature: signature,
                    createdAt: new Date().toISOString(),
                    compressionRatio: packageInfo.compressionRatio
                },
                verified: !!signature,
                downloadUrl: null // To be set by marketplace
            };

            // Write package metadata
            const metadataPath = path.join(this.outputDir, `${manifest.id}-${manifest.version}.json`);
            await fs.writeFile(metadataPath, JSON.stringify(metadata, null, 2));

            const result = {
                success: true,
                packagePath,
                metadataPath,
                metadata,
                manifest
            };

            this.logger.info('[PackageManager] Package creation completed', {
                packageName,
                size: packageInfo.size,
                files: packageInfo.files,
                signed: !!signature
            });

            return result;

        } catch (error) {
            this.logger.error('[PackageManager] Package creation failed', {
                sourcePath,
                error: error.message
            });
            throw new Error(`Package creation failed: ${error.message}`);
        }
    }

    /**
     * Verify a plugin package
     * @param {string} packagePath Path to package file
     * @param {Object} expectedMetadata Expected package metadata
     * @returns {Object} Verification result
     */
    async verifyPackage(packagePath, expectedMetadata = {}) {
        try {
            this.logger.info('[PackageManager] Starting package verification', { packagePath });

            // Check if package exists
            try {
                await fs.access(packagePath);
            } catch {
                throw new Error(`Package file not found: ${packagePath}`);
            }

            // Calculate actual hash
            const actualHash = await this._calculatePackageHash(packagePath);

            // Verify hash if provided
            if (expectedMetadata.sha256 && expectedMetadata.sha256 !== actualHash) {
                throw new Error(`Package hash verification failed: expected ${expectedMetadata.sha256}, got ${actualHash}`);
            }

            // Verify digital signature if provided
            let signatureValid = null;
            if (expectedMetadata.signature) {
                signatureValid = await this._verifySignature(packagePath, actualHash, expectedMetadata.signature);
                if (!signatureValid) {
                    throw new Error('Digital signature verification failed');
                }
            }

            // Get package file statistics
            const stats = await fs.stat(packagePath);

            const result = {
                valid: true,
                packagePath,
                size: stats.size,
                hash: actualHash,
                signatureValid,
                verifiedAt: new Date().toISOString()
            };

            this.logger.info('[PackageManager] Package verification completed', result);
            return result;

        } catch (error) {
            this.logger.error('[PackageManager] Package verification failed', {
                packagePath,
                error: error.message
            });

            return {
                valid: false,
                error: error.message,
                verifiedAt: new Date().toISOString()
            };
        }
    }

    /**
     * Extract package information without full extraction
     * @param {string} packagePath Path to package file
     * @returns {Object} Package information
     */
    async getPackageInfo(packagePath) {
        try {
            // Calculate package hash and size
            const [hash, stats] = await Promise.all([
                this._calculatePackageHash(packagePath),
                fs.stat(packagePath)
            ]);

            // TODO: Read manifest from ZIP without full extraction
            // For now, we'll return basic info
            const info = {
                size: stats.size,
                hash,
                lastModified: stats.mtime.toISOString(),
                // manifest: null // Would be extracted from ZIP
            };

            return info;
        } catch (error) {
            this.logger.error('[PackageManager] Failed to get package info', {
                packagePath,
                error: error.message
            });
            throw error;
        }
    }

    /**
     * Generate a marketplace registry entry for a package
     * @param {Object} metadata Package metadata
     * @param {Object} registryConfig Registry configuration
     * @returns {Object} Registry entry
     */
    generateRegistryEntry(metadata, registryConfig = {}) {
        const registryEntry = {
            id: metadata.id,
            name: metadata.name,
            version: metadata.version,
            description: metadata.description,
            author: metadata.author,
            category: metadata.category || 'Utility',
            tags: metadata.tags || [],
            permissions: metadata.permissions || [],
            minAppVersion: metadata.minAppVersion,
            verified: metadata.verified || false,

            // Package information
            downloadUrl: registryConfig.baseUrl ?
                `${registryConfig.baseUrl}/packages/${metadata.packageInfo.filename}` :
                null,
            sha256: metadata.packageInfo.sha256,
            signature: metadata.packageInfo.signature,
            size: metadata.packageInfo.size,

            // Marketplace metadata
            downloadCount: 0,
            rating: 0,
            reviews: 0,
            lastUpdated: metadata.packageInfo.createdAt,

            // Optional marketplace fields
            homepage: metadata.homepage,
            repository: metadata.repository,
            changelog: metadata.changelog,
            screenshots: metadata.screenshots || [],
            license: metadata.license
        };

        this.logger.debug('[PackageManager] Generated registry entry', {
            id: registryEntry.id,
            version: registryEntry.version
        });

        return registryEntry;
    }

    /**
     * Batch create packages from multiple source directories
     * @param {Array} sourcePaths Array of source directory paths
     * @param {Object} options Packaging options
     * @returns {Array} Array of package creation results
     */
    async createBatchPackages(sourcePaths, options = {}) {
        const results = [];

        this.logger.info('[PackageManager] Starting batch package creation', {
            count: sourcePaths.length
        });

        for (const sourcePath of sourcePaths) {
            try {
                const result = await this.createPackage(sourcePath, options);
                results.push(result);
            } catch (error) {
                results.push({
                    success: false,
                    sourcePath,
                    error: error.message
                });
            }
        }

        const successful = results.filter(r => r.success).length;
        const failed = results.length - successful;

        this.logger.info('[PackageManager] Batch package creation completed', {
            total: results.length,
            successful,
            failed
        });

        return results;
    }

    // Private helper methods

    /**
     * Validate source directory and load manifest
     * @private
     */
    async _validateSourceDirectory(sourcePath) {
        // Check if source directory exists
        try {
            const stats = await fs.stat(sourcePath);
            if (!stats.isDirectory()) {
                throw new Error(`Source path is not a directory: ${sourcePath}`);
            }
        } catch (error) {
            throw new Error(`Source directory not accessible: ${error.message}`);
        }

        // Load and validate manifest
        const manifestPath = path.join(sourcePath, 'manifest.json');
        try {
            const manifestData = await fs.readFile(manifestPath, 'utf8');
            const manifest = JSON.parse(manifestData);

            // Validate required fields
            const requiredFields = ['id', 'name', 'version', 'main'];
            for (const field of requiredFields) {
                if (!manifest[field]) {
                    throw new Error(`Missing required manifest field: ${field}`);
                }
            }

            // Validate version format
            if (!semver.valid(manifest.version)) {
                throw new Error(`Invalid version format: ${manifest.version}`);
            }

            // Check if main file exists
            const mainPath = path.join(sourcePath, manifest.main);
            try {
                await fs.access(mainPath);
            } catch {
                throw new Error(`Main file not found: ${manifest.main}`);
            }

            return manifest;

        } catch (error) {
            if (error.code === 'ENOENT') {
                throw new Error('manifest.json not found in source directory');
            }
            throw error;
        }
    }

    /**
     * Create ZIP package from source directory
     * @private
     */
    async _createZipPackage(sourcePath, packagePath, manifest, options) {
        return new Promise((resolve, reject) => {
            const output = require('fs').createWriteStream(packagePath);
            const archive = archiver('zip', {
                zlib: { level: this.config.compressionLevel }
            });

            let totalSize = 0;
            let compressedSize = 0;
            let fileCount = 0;

            // Handle archiver events
            archive.on('error', reject);

            archive.on('entry', (entry) => {
                if (entry.type === 'file') {
                    totalSize += entry.stats.size;
                    fileCount++;
                }
            });

            output.on('close', () => {
                compressedSize = archive.pointer();
                const compressionRatio = totalSize > 0 ? (compressedSize / totalSize) : 1;

                resolve({
                    size: compressedSize,
                    originalSize: totalSize,
                    files: fileCount,
                    compressionRatio: Math.round(compressionRatio * 100) / 100
                });
            });

            output.on('error', reject);

            // Pipe archive data to the file
            archive.pipe(output);

            // Build exclude patterns
            const excludePatterns = [...this.excludePatterns];
            if (!this.config.includeDevFiles) {
                excludePatterns.push(...this.devOnlyPatterns);
            }

            // Add source directory to archive
            archive.glob('**/*', {
                cwd: sourcePath,
                ignore: excludePatterns,
                dot: false
            });

            // Finalize the archive
            archive.finalize();
        });
    }

    /**
     * Calculate SHA256 hash of package file
     * @private
     */
    async _calculatePackageHash(packagePath) {
        const data = await fs.readFile(packagePath);
        return crypto.createHash('sha256').update(data).digest('hex');
    }

    /**
     * Generate digital signature for package
     * @private
     */
    async _signPackage(packagePath, packageHash) {
        // TODO: Implement actual digital signature generation
        // This would typically use a private key to sign the package hash

        if (!this.signingConfig.privateKey) {
            this.logger.warn('[PackageManager] Digital signing requested but no private key configured');
            return null;
        }

        try {
            // Placeholder for actual signing implementation
            const signature = crypto
                .createHash('sha256')
                .update(packageHash + this.signingConfig.privateKey)
                .digest('hex');

            this.logger.debug('[PackageManager] Package signature generated');
            return signature;

        } catch (error) {
            this.logger.error('[PackageManager] Failed to sign package', { error: error.message });
            throw new Error(`Package signing failed: ${error.message}`);
        }
    }

    /**
     * Verify digital signature of package
     * @private
     */
    async _verifySignature(packagePath, packageHash, signature) {
        // TODO: Implement actual digital signature verification
        // This would typically use a public key to verify the signature

        if (!this.signingConfig.publicKey) {
            this.logger.warn('[PackageManager] Signature verification requested but no public key configured');
            return false;
        }

        try {
            // Placeholder for actual verification implementation
            const expectedSignature = crypto
                .createHash('sha256')
                .update(packageHash + this.signingConfig.privateKey) // In real implementation, would use public key
                .digest('hex');

            const isValid = signature === expectedSignature;
            this.logger.debug('[PackageManager] Signature verification completed', { isValid });
            return isValid;

        } catch (error) {
            this.logger.error('[PackageManager] Failed to verify signature', { error: error.message });
            return false;
        }
    }
}

module.exports = PackageManager;
