/**
 * Marketplace Manager - Plugin discovery and installation system
 *
 * Provides comprehensive marketplace functionality including:
 * - Plugin discovery from central registry
 * - Package download and verification
 * - Installation and dependency management
 * - Update checking and management
 * - Security verification with signed packages
 */

const fs = require('fs').promises;
const path = require('path');
const crypto = require('crypto');
const https = require('https');
const { pipeline } = require('stream/promises');
const yauzl = require('yauzl');
const semver = require('semver');

class MarketplaceManager {
    /**
     * Initialize the marketplace manager
     * @param {Object} options Configuration options
     * @param {string} options.pluginDir Plugin installation directory
     * @param {string} options.cacheDir Cache directory for downloads
     * @param {string} options.registryUrl Marketplace registry URL
     * @param {Object} options.logger Logger instance
     */
    constructor(options = {}) {
        this.pluginDir = options.pluginDir || path.join(process.env.APPDATA || process.env.HOME, 'lifeboard', 'plugins');
        this.cacheDir = options.cacheDir || path.join(this.pluginDir, '.cache');
        this.registryUrl = options.registryUrl || 'https://marketplace.lifeboard.app/registry.json';
        this.logger = options.logger || console;

        // Marketplace configuration
        this.config = {
            maxDownloadSize: 50 * 1024 * 1024, // 50MB max
            downloadTimeout: 30000, // 30 seconds
            verificationRequired: true,
            allowDevelopmentPackages: process.env.NODE_ENV === 'development'
        };

        // Plugin registry cache
        this.registryCache = null;
        this.registryCacheExpiry = null;
        this.cacheValidityPeriod = 30 * 60 * 1000; // 30 minutes

        // Installation state tracking
        this.activeInstallations = new Map();
        this.installationQueue = [];

        this.logger.info('[MarketplaceManager] Initialized', {
            pluginDir: this.pluginDir,
            cacheDir: this.cacheDir,
            registryUrl: this.registryUrl
        });
    }

    /**
     * Initialize marketplace directories and cache
     */
    async initialize() {
        try {
            // Ensure directories exist
            await fs.mkdir(this.pluginDir, { recursive: true });
            await fs.mkdir(this.cacheDir, { recursive: true });

            this.logger.info('[MarketplaceManager] Directories initialized');
            return true;
        } catch (error) {
            this.logger.error('[MarketplaceManager] Failed to initialize directories', { error: error.message });
            throw new Error(`Marketplace initialization failed: ${error.message}`);
        }
    }

    /**
     * Fetch and cache the plugin registry
     * @param {boolean} forceRefresh Force refresh of cached registry
     * @returns {Object} Plugin registry data
     */
    async getRegistry(forceRefresh = false) {
        const now = Date.now();

        // Return cached registry if valid and not forcing refresh
        if (!forceRefresh && this.registryCache && this.registryCacheExpiry && now < this.registryCacheExpiry) {
            this.logger.debug('[MarketplaceManager] Using cached registry');
            return this.registryCache;
        }

        try {
            this.logger.info('[MarketplaceManager] Fetching registry from marketplace', { url: this.registryUrl });

            const registryData = await this._downloadJson(this.registryUrl);

            // Validate registry structure
            if (!registryData.plugins || !Array.isArray(registryData.plugins)) {
                throw new Error('Invalid registry format: missing plugins array');
            }

            // Cache the registry
            this.registryCache = registryData;
            this.registryCacheExpiry = now + this.cacheValidityPeriod;

            this.logger.info('[MarketplaceManager] Registry fetched successfully', {
                pluginCount: registryData.plugins.length,
                version: registryData.version || 'unknown'
            });

            return registryData;
        } catch (error) {
            this.logger.error('[MarketplaceManager] Failed to fetch registry', { error: error.message });

            // Return cached registry if available
            if (this.registryCache) {
                this.logger.warn('[MarketplaceManager] Using stale cached registry');
                return this.registryCache;
            }

            throw new Error(`Failed to fetch marketplace registry: ${error.message}`);
        }
    }

    /**
     * Search for plugins in the marketplace
     * @param {string} query Search query
     * @param {Object} filters Search filters
     * @returns {Array} Array of matching plugins
     */
    async searchPlugins(query = '', filters = {}) {
        try {
            const registry = await this.getRegistry();
            let plugins = registry.plugins;

            // Apply text search
            if (query.trim()) {
                const searchTerm = query.toLowerCase();
                plugins = plugins.filter(plugin =>
                    plugin.name.toLowerCase().includes(searchTerm) ||
                    plugin.description?.toLowerCase().includes(searchTerm) ||
                    plugin.tags?.some(tag => tag.toLowerCase().includes(searchTerm)) ||
                    plugin.author?.toLowerCase().includes(searchTerm)
                );
            }

            // Apply filters
            if (filters.category) {
                plugins = plugins.filter(plugin => plugin.category === filters.category);
            }

            if (filters.verified !== undefined) {
                plugins = plugins.filter(plugin => plugin.verified === filters.verified);
            }

            if (filters.minVersion) {
                plugins = plugins.filter(plugin =>
                    semver.gte(plugin.version, filters.minVersion)
                );
            }

            // Sort by relevance and popularity
            plugins.sort((a, b) => {
                // Verified plugins first
                if (a.verified !== b.verified) {
                    return b.verified ? 1 : -1;
                }

                // Then by download count
                return (b.downloadCount || 0) - (a.downloadCount || 0);
            });

            this.logger.debug('[MarketplaceManager] Search completed', {
                query,
                filters,
                resultCount: plugins.length
            });

            return plugins;
        } catch (error) {
            this.logger.error('[MarketplaceManager] Search failed', { error: error.message });
            throw new Error(`Plugin search failed: ${error.message}`);
        }
    }

    /**
     * Get detailed information about a specific plugin
     * @param {string} pluginId Plugin identifier
     * @returns {Object} Detailed plugin information
     */
    async getPluginDetails(pluginId) {
        try {
            const registry = await this.getRegistry();
            const plugin = registry.plugins.find(p => p.id === pluginId);

            if (!plugin) {
                throw new Error(`Plugin not found: ${pluginId}`);
            }

            // Check if plugin is already installed
            const isInstalled = await this._isPluginInstalled(pluginId);
            const installedVersion = isInstalled ? await this._getInstalledVersion(pluginId) : null;

            return {
                ...plugin,
                isInstalled,
                installedVersion,
                hasUpdate: isInstalled && semver.gt(plugin.version, installedVersion),
                canUpdate: isInstalled && semver.gt(plugin.version, installedVersion)
            };
        } catch (error) {
            this.logger.error('[MarketplaceManager] Failed to get plugin details', {
                pluginId,
                error: error.message
            });
            throw error;
        }
    }

    /**
     * Install a plugin from the marketplace
     * @param {string} pluginId Plugin identifier
     * @param {Object} options Installation options
     * @returns {Object} Installation result
     */
    async installPlugin(pluginId, options = {}) {
        try {
            // Check if installation is already in progress
            if (this.activeInstallations.has(pluginId)) {
                throw new Error(`Installation already in progress for ${pluginId}`);
            }

            // Get plugin details
            const plugin = await this.getPluginDetails(pluginId);

            // Check if already installed and not forcing reinstall
            if (plugin.isInstalled && !options.force) {
                throw new Error(`Plugin ${pluginId} is already installed`);
            }

            // Start installation tracking
            this.activeInstallations.set(pluginId, {
                startTime: Date.now(),
                status: 'starting',
                progress: 0
            });

            this.logger.info('[MarketplaceManager] Starting plugin installation', {
                pluginId,
                version: plugin.version,
                force: options.force
            });

            // Update installation status
            this._updateInstallationStatus(pluginId, 'downloading', 10);

            // Download the plugin package
            const packagePath = await this._downloadPackage(plugin);

            this._updateInstallationStatus(pluginId, 'verifying', 40);

            // Verify package integrity and signature
            await this._verifyPackage(packagePath, plugin);

            this._updateInstallationStatus(pluginId, 'extracting', 60);

            // Extract and install the plugin
            const installPath = await this._extractPackage(packagePath, pluginId);

            this._updateInstallationStatus(pluginId, 'configuring', 80);

            // Validate the installed plugin
            await this._validateInstalledPlugin(installPath, plugin);

            this._updateInstallationStatus(pluginId, 'completed', 100);

            // Clean up
            await fs.unlink(packagePath).catch(() => {}); // Ignore cleanup errors
            this.activeInstallations.delete(pluginId);

            const result = {
                success: true,
                pluginId,
                version: plugin.version,
                installPath,
                installedAt: new Date().toISOString()
            };

            this.logger.info('[MarketplaceManager] Plugin installation completed', result);
            return result;

        } catch (error) {
            // Clean up on error
            this.activeInstallations.delete(pluginId);

            this.logger.error('[MarketplaceManager] Plugin installation failed', {
                pluginId,
                error: error.message
            });

            throw new Error(`Installation failed for ${pluginId}: ${error.message}`);
        }
    }

    /**
     * Uninstall a plugin
     * @param {string} pluginId Plugin identifier
     * @param {Object} options Uninstallation options
     * @returns {Object} Uninstallation result
     */
    async uninstallPlugin(pluginId, options = {}) {
        try {
            const pluginPath = path.join(this.pluginDir, pluginId);

            // Check if plugin exists
            try {
                await fs.access(pluginPath);
            } catch {
                throw new Error(`Plugin ${pluginId} is not installed`);
            }

            this.logger.info('[MarketplaceManager] Starting plugin uninstallation', { pluginId });

            // Backup settings if requested
            let settingsBackup = null;
            if (options.backupSettings) {
                const settingsPath = path.join(pluginPath, 'settings.json');
                try {
                    settingsBackup = await fs.readFile(settingsPath, 'utf8');
                } catch {
                    // Settings file doesn't exist, no backup needed
                }
            }

            // Remove plugin directory
            await fs.rm(pluginPath, { recursive: true, force: true });

            const result = {
                success: true,
                pluginId,
                uninstalledAt: new Date().toISOString(),
                settingsBackup
            };

            this.logger.info('[MarketplaceManager] Plugin uninstallation completed', result);
            return result;

        } catch (error) {
            this.logger.error('[MarketplaceManager] Plugin uninstallation failed', {
                pluginId,
                error: error.message
            });
            throw error;
        }
    }

    /**
     * Check for plugin updates
     * @param {string|null} pluginId Specific plugin to check, or null for all
     * @returns {Array} Available updates
     */
    async checkForUpdates(pluginId = null) {
        try {
            const registry = await this.getRegistry();
            const updates = [];

            // Get list of installed plugins
            const installedPlugins = pluginId ? [pluginId] : await this._getInstalledPlugins();

            for (const id of installedPlugins) {
                const installedVersion = await this._getInstalledVersion(id);
                const marketplacePlugin = registry.plugins.find(p => p.id === id);

                if (marketplacePlugin && semver.gt(marketplacePlugin.version, installedVersion)) {
                    updates.push({
                        pluginId: id,
                        currentVersion: installedVersion,
                        availableVersion: marketplacePlugin.version,
                        updateInfo: marketplacePlugin.changelog || 'No changelog available'
                    });
                }
            }

            this.logger.info('[MarketplaceManager] Update check completed', {
                checkedPlugins: installedPlugins.length,
                availableUpdates: updates.length
            });

            return updates;
        } catch (error) {
            this.logger.error('[MarketplaceManager] Update check failed', { error: error.message });
            throw error;
        }
    }

    /**
     * Get installation status for a plugin
     * @param {string} pluginId Plugin identifier
     * @returns {Object|null} Installation status or null if not installing
     */
    getInstallationStatus(pluginId) {
        return this.activeInstallations.get(pluginId) || null;
    }

    /**
     * Get marketplace statistics
     * @returns {Object} Marketplace statistics
     */
    async getMarketplaceStats() {
        try {
            const registry = await this.getRegistry();
            const installedPlugins = await this._getInstalledPlugins();

            const stats = {
                totalPlugins: registry.plugins.length,
                installedPlugins: installedPlugins.length,
                verifiedPlugins: registry.plugins.filter(p => p.verified).length,
                categories: [...new Set(registry.plugins.map(p => p.category).filter(Boolean))],
                lastUpdated: registry.lastUpdated || null
            };

            return stats;
        } catch (error) {
            this.logger.error('[MarketplaceManager] Failed to get marketplace stats', { error: error.message });
            throw error;
        }
    }

    // Private helper methods

    /**
     * Download JSON data from URL
     * @private
     */
    async _downloadJson(url) {
        return new Promise((resolve, reject) => {
            const request = https.get(url, {
                timeout: this.config.downloadTimeout,
                headers: {
                    'User-Agent': 'Lifeboard-Plugin-Manager/1.0'
                }
            }, (response) => {
                if (response.statusCode !== 200) {
                    reject(new Error(`HTTP ${response.statusCode}: ${response.statusMessage}`));
                    return;
                }

                let data = '';
                response.on('data', chunk => data += chunk);
                response.on('end', () => {
                    try {
                        resolve(JSON.parse(data));
                    } catch (error) {
                        reject(new Error(`Invalid JSON response: ${error.message}`));
                    }
                });
            });

            request.on('error', reject);
            request.on('timeout', () => {
                request.destroy();
                reject(new Error('Download timeout'));
            });
        });
    }

    /**
     * Download a plugin package
     * @private
     */
    async _downloadPackage(plugin) {
        const packagePath = path.join(this.cacheDir, `${plugin.id}-${plugin.version}.zip`);

        // Check if already cached
        try {
            await fs.access(packagePath);
            this.logger.debug('[MarketplaceManager] Using cached package', { packagePath });
            return packagePath;
        } catch {
            // Package not cached, download it
        }

        return new Promise((resolve, reject) => {
            const request = https.get(plugin.downloadUrl, {
                timeout: this.config.downloadTimeout,
                headers: {
                    'User-Agent': 'Lifeboard-Plugin-Manager/1.0'
                }
            }, async (response) => {
                if (response.statusCode !== 200) {
                    reject(new Error(`Download failed: HTTP ${response.statusCode}`));
                    return;
                }

                const contentLength = parseInt(response.headers['content-length'], 10);
                if (contentLength > this.config.maxDownloadSize) {
                    reject(new Error(`Package too large: ${contentLength} bytes`));
                    return;
                }

                try {
                    const writeStream = require('fs').createWriteStream(packagePath);
                    await pipeline(response, writeStream);
                    resolve(packagePath);
                } catch (error) {
                    reject(error);
                }
            });

            request.on('error', reject);
            request.on('timeout', () => {
                request.destroy();
                reject(new Error('Download timeout'));
            });
        });
    }

    /**
     * Verify package integrity and signature
     * @private
     */
    async _verifyPackage(packagePath, plugin) {
        // Calculate package hash
        const packageData = await fs.readFile(packagePath);
        const actualHash = crypto.createHash('sha256').update(packageData).digest('hex');

        // Verify hash if provided
        if (plugin.sha256 && plugin.sha256 !== actualHash) {
            throw new Error(`Package verification failed: hash mismatch`);
        }

        // TODO: Implement digital signature verification
        if (this.config.verificationRequired && plugin.signature) {
            this.logger.debug('[MarketplaceManager] Digital signature verification not yet implemented');
        }

        this.logger.debug('[MarketplaceManager] Package verification completed', {
            expectedHash: plugin.sha256,
            actualHash
        });
    }

    /**
     * Extract plugin package
     * @private
     */
    async _extractPackage(packagePath, pluginId) {
        const installPath = path.join(this.pluginDir, pluginId);

        // Remove existing installation
        await fs.rm(installPath, { recursive: true, force: true });
        await fs.mkdir(installPath, { recursive: true });

        return new Promise((resolve, reject) => {
            yauzl.open(packagePath, { lazyEntries: true }, (err, zipfile) => {
                if (err) {
                    reject(err);
                    return;
                }

                zipfile.readEntry();

                zipfile.on('entry', async (entry) => {
                    if (/\/$/.test(entry.fileName)) {
                        // Directory entry
                        const dirPath = path.join(installPath, entry.fileName);
                        await fs.mkdir(dirPath, { recursive: true });
                        zipfile.readEntry();
                    } else {
                        // File entry
                        zipfile.openReadStream(entry, async (err, readStream) => {
                            if (err) {
                                reject(err);
                                return;
                            }

                            const filePath = path.join(installPath, entry.fileName);
                            await fs.mkdir(path.dirname(filePath), { recursive: true });

                            const writeStream = require('fs').createWriteStream(filePath);

                            try {
                                await pipeline(readStream, writeStream);
                                zipfile.readEntry();
                            } catch (error) {
                                reject(error);
                            }
                        });
                    }
                });

                zipfile.on('end', () => resolve(installPath));
                zipfile.on('error', reject);
            });
        });
    }

    /**
     * Validate installed plugin
     * @private
     */
    async _validateInstalledPlugin(installPath, plugin) {
        // Check manifest exists
        const manifestPath = path.join(installPath, 'manifest.json');
        try {
            const manifestData = await fs.readFile(manifestPath, 'utf8');
            const manifest = JSON.parse(manifestData);

            // Verify plugin ID matches
            if (manifest.id !== plugin.id) {
                throw new Error(`Plugin ID mismatch: expected ${plugin.id}, got ${manifest.id}`);
            }

            // Verify main file exists
            const mainPath = path.join(installPath, manifest.main);
            await fs.access(mainPath);

        } catch (error) {
            throw new Error(`Plugin validation failed: ${error.message}`);
        }
    }

    /**
     * Check if plugin is installed
     * @private
     */
    async _isPluginInstalled(pluginId) {
        try {
            const pluginPath = path.join(this.pluginDir, pluginId);
            await fs.access(pluginPath);
            return true;
        } catch {
            return false;
        }
    }

    /**
     * Get installed version of a plugin
     * @private
     */
    async _getInstalledVersion(pluginId) {
        try {
            const manifestPath = path.join(this.pluginDir, pluginId, 'manifest.json');
            const manifestData = await fs.readFile(manifestPath, 'utf8');
            const manifest = JSON.parse(manifestData);
            return manifest.version;
        } catch {
            return '0.0.0';
        }
    }

    /**
     * Get list of installed plugins
     * @private
     */
    async _getInstalledPlugins() {
        try {
            const entries = await fs.readdir(this.pluginDir, { withFileTypes: true });
            const plugins = [];

            for (const entry of entries) {
                if (entry.isDirectory() && !entry.name.startsWith('.')) {
                    const manifestPath = path.join(this.pluginDir, entry.name, 'manifest.json');
                    try {
                        await fs.access(manifestPath);
                        plugins.push(entry.name);
                    } catch {
                        // Not a valid plugin directory
                    }
                }
            }

            return plugins;
        } catch {
            return [];
        }
    }

    /**
     * Update installation status
     * @private
     */
    _updateInstallationStatus(pluginId, status, progress) {
        const installation = this.activeInstallations.get(pluginId);
        if (installation) {
            installation.status = status;
            installation.progress = progress;
            installation.lastUpdate = Date.now();
        }
    }
}

module.exports = MarketplaceManager;
