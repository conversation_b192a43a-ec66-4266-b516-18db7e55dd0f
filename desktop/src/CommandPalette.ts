// CommandPalette.ts
// Manages command registration and execution

class CommandPalette {
  private commands: Map<string, () => void> = new Map();

  /**
   * Registers a new command.
   * @param id - Unique identifier for the command.
   * @param handler - Function to execute when the command is invoked.
   */
  registerCommand(id: string, handler: () => void): void {
    this.commands.set(id, handler);
  }

  /**
   * Executes a registered command.
   * @param id - Identifier of the command to execute.
   */
  executeCommand(id: string): void {
    const command = this.commands.get(id);
    if (command) {
      command();
    } else {
      console.warn(`Command not found: ${id}`);
    }
  }

  /**
   * Lists all registered commands.
   * @returns a list of command identifiers.
   */
  listCommands(): string[] {
    return Array.from(this.commands.keys());
  }
}

export default CommandPalette;
