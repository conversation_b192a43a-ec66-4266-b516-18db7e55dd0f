#!/usr/bin/env node

/**
 * Development script for Lifeboard Desktop
 * This script sets up the development environment and starts the Electron app
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('🚀 Starting Lifeboard Desktop Development Environment');

// Check if web UI is running
async function checkWebUI() {
  try {
    const response = await fetch('http://localhost:9820');
    return response.ok;
  } catch (error) {
    return false;
  }
}

// Start the web UI if not running
function startWebUI() {
  console.log('📡 Starting web UI server...');

  const webUIProcess = spawn('docker', [
    'compose', '-f', '../docker-compose.web-ui.yml',
    '-p', 'lifeboard', '--profile', 'webui', 'up', '-d'
  ], {
    cwd: __dirname,
    stdio: 'inherit'
  });

  return new Promise((resolve, reject) => {
    webUIProcess.on('close', (code) => {
      if (code === 0) {
        console.log('✅ Web UI started successfully');
        resolve();
      } else {
        reject(new Error(`Web UI failed to start with code ${code}`));
      }
    });
  });
}

// Start Electron in development mode
function startElectron() {
  console.log('⚡ Starting Electron application...');

  const electronProcess = spawn('npm', ['run', 'electron-dev'], {
    cwd: __dirname,
    stdio: 'inherit',
    env: { ...process.env, ELECTRON_IS_DEV: 'true' }
  });

  electronProcess.on('close', (code) => {
    console.log(`Electron exited with code ${code}`);
    process.exit(code);
  });
}

// Main development workflow
async function main() {
  try {
    // Check if dependencies are installed
    if (!fs.existsSync(path.join(__dirname, 'node_modules'))) {
      console.log('📦 Installing dependencies...');
      const installProcess = spawn('npm', ['install'], {
        cwd: __dirname,
        stdio: 'inherit'
      });

      await new Promise((resolve, reject) => {
        installProcess.on('close', (code) => {
          if (code === 0) resolve();
          else reject(new Error(`npm install failed with code ${code}`));
        });
      });
    }

    // Check if web UI is running
    const webUIRunning = await checkWebUI();

    if (!webUIRunning) {
      console.log('⚠️  Web UI not detected, starting it...');
      await startWebUI();

      // Wait a moment for the web UI to fully start
      console.log('⏳ Waiting for web UI to be ready...');
      await new Promise(resolve => setTimeout(resolve, 3000));
    } else {
      console.log('✅ Web UI already running');
    }

    // Start Electron
    startElectron();

  } catch (error) {
    console.error('❌ Development setup failed:', error.message);
    process.exit(1);
  }
}

// Handle process signals
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down development environment...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Shutting down development environment...');
  process.exit(0);
});

// Run the development script
main();
