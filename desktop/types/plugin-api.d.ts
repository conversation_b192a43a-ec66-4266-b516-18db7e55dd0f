// Type definitions for Lifeboard plugins - Phase 10 M4

declare namespace PluginAPI {
  interface App {
    getName(): string;
    getVersion(): string;
    getPlatform(): string;
  }

  interface Workspace {
    version: string;
    createLeaf(): WorkspaceLeaf;
  }

  interface WorkspaceLeaf {
    id: string;
    setContent(content: string): void;
    setTitle(title: string): void;
    close(): void;
  }

  interface CommandMetadata {
    category?: string;
    description?: string;
    icon?: string;
    tags?: string[];
    hotkey?: string;
    isVisible?: boolean;
  }

  interface Commands {
    register(commandId: string, handler: Function): void;
    execute(commandId: string): void;
    setMetadata(commandId: string, metadata: CommandMetadata): void;
  }

  interface Events {
    on(event: string, handler: Function): void;
    emit(event: string, data?: any): void;
    off(event: string, handler: Function): void;
  }

  interface ModalButton {
    text: string;
    variant?: 'primary' | 'secondary' | 'success' | 'danger' | 'warning' | 'info';
    callback: Function;
  }

  interface ModalConfig {
    title: string;
    content?: string;
    width?: number;
    height?: number;
    resizable?: boolean;
    closable?: boolean;
    modal?: boolean;
    className?: string;
    buttons?: ModalButton[];
  }

  interface UI {
    /**
     * Add a ribbon icon to the toolbar
     * @param icon - Icon identifier (font-awesome class, unicode, or image path)
     * @param title - Tooltip text for the icon
     * @param callback - Function to execute when icon is clicked
     * @returns Unique icon ID for removal
     */
    addRibbonIcon(icon: string, title: string, callback: Function): string;

    /**
     * Remove a ribbon icon from the toolbar
     * @param iconId - ID of the icon to remove
     * @returns Success status
     */
    removeRibbonIcon(iconId: string): boolean;

    /**
     * Show a modal dialog
     * @param config - Modal configuration
     * @returns Unique modal ID
     */
    showModal(config: ModalConfig): string;

    /**
     * Close a specific modal
     * @param modalId - ID of the modal to close
     * @returns Success status
     */
    closeModal(modalId: string): boolean;
  }

  interface Storage {
    loadData(): object;
    saveData(data: object): boolean;
  }

  interface Network {
    fetch?: Function;
  }

  interface Manifest {
    id: string;
    name: string;
    version: string;
    permissions: string[];
  }

  interface Main {
    app: App;
    workspace: Workspace;
    commands: Commands;
    events: Events;
    ui: UI;
    storage: Storage;
    network: Network;
    manifest: Manifest;
  }
}

// Global PluginAPI available in plugin context
declare const PluginAPI: PluginAPI.Main;

export { PluginAPI };
