# 🧪 M5 Plugin Management UI - Testing Guide

## 🚀 Quick Start

You can test the M5 plugin management features in two ways:

### Option 1: Full Electron App (Recommended)
```bash
cd /Users/<USER>/code/lifeboard-supabase/desktop
npm run electron-dev
```

### Option 2: Web Browser Preview
```bash
# Open the plugins.html file directly in your browser
open /Users/<USER>/code/lifeboard-supabase/webui/plugins.html
```

---

## 🔍 M5 Features You Can Test

### 1. **Plugin Dashboard Overview**
- **Statistics Panel**: View total, enabled, disabled, and error plugin counts
- **Real-time Updates**: Watch stats update as you enable/disable plugins
- **Visual Status Indicators**: Color-coded plugin states (green=enabled, gray=disabled, red=error)

### 2. **Plugin Filtering & Search**
- **Filter Buttons**: Click "All", "Enabled", "Disabled", or "Error" to filter plugins
- **Search Functionality**: Type in the search box to find plugins by name or description
- **Real-time Filtering**: See results update instantly as you type

### 3. **Plugin State Management**
- **Toggle Switches**: Click the switch next to each plugin to enable/disable it
- **Visual Feedback**: Watch plugin cards change appearance when disabled
- **State Persistence**: Plugin states are maintained during your session

### 4. **Settings Management (M5 Core Feature)**
- **Settings Modals**: Click "⚙️ Settings" on any plugin to open its configuration
- **Tabbed Interface**: Different settings tabs based on plugin type
- **Form Validation**: Try changing settings and saving them
- **Plugin-Specific Settings**:
  - **Limitless**: Theme, notifications, API key
  - **Analytics**: Dashboard layout, refresh interval
  - **Weather**: API key configuration with error handling

### 5. **Plugin Actions**
- **Reload Plugin**: Click "🔄 Reload" to simulate plugin restart
- **Uninstall Plugin**: Click "🗑️ Remove" to simulate plugin removal
- **Error Handling**: Try fixing the Weather plugin's configuration error

### 6. **M5 API Testing**
- **API Test Button**: Click "🧪 Test M5 API" to test the plugin management APIs
- **Console Logging**: Open browser DevTools to see M5 API calls and responses
- **Notification System**: See toast notifications for all plugin actions

---

## 🎮 Interactive Test Scenarios

### Scenario 1: Enable/Disable Plugins
1. **Start with Mixed States**: Limitless (enabled), Analytics (disabled), Weather (error)
2. **Toggle Analytics**: Click the switch to enable Analytics plugin
3. **Watch Updates**: Notice the statistics panel updates automatically
4. **Toggle Limitless**: Disable the Limitless plugin and see visual changes

### Scenario 2: Configure Plugin Settings
1. **Open Limitless Settings**: Click "⚙️ Settings" on the Limitless plugin
2. **Change Theme**: Switch from "Dark" to "Light" theme
3. **Update Notifications**: Toggle the notifications checkbox
4. **Save Settings**: Click "Save Settings" and see confirmation

### Scenario 3: Fix Plugin Errors
1. **Identify Error**: Notice the Weather plugin has a configuration error
2. **Fix Configuration**: Click "🔧 Fix Settings" on the Weather plugin
3. **Add API Key**: Enter a dummy API key in the settings modal
4. **Save and Reload**: Save settings and click "🔄 Retry"

### Scenario 4: Search and Filter
1. **Search Test**: Type "AI" in search box - only Limitless should show
2. **Filter Test**: Click "Error" filter - only Weather plugin should show
3. **Clear and Reset**: Clear search and click "All" to see all plugins

### Scenario 5: Test API Integration
1. **Open DevTools**: Press F12 to open browser console
2. **Click API Test**: Click "🧪 Test M5 API" button
3. **Review Logs**: See simulated M5 API calls in the console
4. **Real vs Demo**: Compare behavior in Electron vs browser

---

## 🔧 Technical Details

### M5 APIs Demonstrated
- **Plugin State Management**: `plugins.getStates()`, `plugins.enable()`, `plugins.disable()`
- **Settings Storage**: `settings.load()`, `settings.save()`, `settings.reset()`
- **Plugin Registry**: `registry.getAll()`, `registry.getStats()`
- **Plugin Management UI**: Filtering, search, bulk operations

### Real vs Simulated Features
- **In Browser**: All features work with simulated data
- **In Electron**: Connected to real M5 backend systems
- **Console Output**: Shows actual vs simulated API calls

### Data Structures
```javascript
// Plugin State Structure
{
  limitless: {
    enabled: true,
    settings: { theme: 'dark', notifications: true, apiKey: '' }
  },
  analytics: {
    enabled: false,
    settings: { dashboardLayout: 'grid', refreshInterval: 300 }
  },
  weather: {
    enabled: false,
    error: 'API key not configured',
    settings: { apiKey: '', location: 'auto' }
  }
}
```

---

## 🎯 Key M5 Features to Validate

### ✅ Settings Storage System
- **Persistent Configuration**: Plugin settings saved per-plugin
- **Schema Validation**: Settings validated before saving
- **Default Values**: Proper defaults applied when settings missing
- **Encryption Support**: Sensitive data (API keys) handled securely

### ✅ State Management System
- **Lifecycle States**: discovered → installing → enabled → disabled
- **State Transitions**: Valid state changes enforced
- **State Persistence**: Plugin states maintained across sessions
- **Error Handling**: Proper error state management

### ✅ Plugin Management UI
- **Visual Dashboard**: Comprehensive plugin overview
- **Real-time Updates**: UI updates reflect state changes
- **Filtering & Search**: Find plugins quickly
- **Bulk Operations**: Manage multiple plugins at once

### ✅ Settings Modal System
- **Tabbed Interface**: Organized settings by category
- **Form Validation**: Prevent invalid configurations
- **Plugin-Specific**: Different settings per plugin type
- **Save/Cancel/Reset**: Full settings lifecycle

---

## 🚨 Troubleshooting

### If Electron App Won't Start
```bash
cd /Users/<USER>/code/lifeboard-supabase/desktop
npm install
npm run electron-dev
```

### If Features Don't Work
1. **Check Console**: Open DevTools and look for JavaScript errors
2. **Check Browser**: Try in Chrome/Safari for best compatibility
3. **Check File Path**: Ensure you're opening the correct plugins.html file

### If M5 APIs Don't Respond
- **In Browser**: APIs are simulated - check console for demo data
- **In Electron**: APIs connect to real backend - check plugin manager logs

---

## 🏆 Success Metrics

After testing, you should be able to:
- ✅ Toggle plugin states and see visual feedback
- ✅ Filter and search plugins effectively
- ✅ Open and interact with settings modals
- ✅ See real-time statistics updates
- ✅ Understand the difference between demo and real modes
- ✅ Experience the full M5 plugin management workflow

**🎉 Congratulations!** You're now experiencing the full M5 Plugin Management system with enhanced settings storage and state management!
