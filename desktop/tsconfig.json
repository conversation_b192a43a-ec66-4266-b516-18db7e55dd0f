{"compilerOptions": {"allowSyntheticDefaultImports": true, "declaration": true, "declarationMap": true, "emitDecoratorMetadata": true, "esModuleInterop": true, "experimentalDecorators": true, "forceConsistentCasingInFileNames": true, "lib": ["ES2020", "DOM"], "module": "CommonJS", "moduleResolution": "node", "outDir": "./dist", "rootDir": "./src", "skipLibCheck": true, "sourceMap": true, "strict": true, "target": "ES2020"}, "exclude": ["node_modules", "dist", "plugins"], "include": ["src/**/*", "types/**/*"]}