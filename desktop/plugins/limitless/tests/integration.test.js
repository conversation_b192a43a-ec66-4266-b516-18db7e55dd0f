/**
 * Integration Tests for Limitless Plugin
 *
 * Tests the complete plugin functionality including:
 * - Plugin initialization and lifecycle
 * - End-to-end data flow from API to storage
 * - UI command registration and execution
 * - Error handling and recovery
 * - Performance and resource usage
 */

const { LimitlessPlugin } = require('../main');

// Mock the PluginAPI
const mockPluginAPI = {
  manifest: {
    id: 'limitless',
    name: 'Limitless AI Integration',
    version: '1.0.0',
    permissions: ['network', 'storage', 'workspace']
  },
  storage: {
    loadData: jest.fn(),
    saveData: jest.fn()
  },
  network: {
    fetch: jest.fn()
  },
  commands: {
    register: jest.fn(),
    setMetadata: jest.fn()
  },
  ui: {
    addRibbonIcon: jest.fn(),
    showModal: jest.fn(),
    removeRibbonIcon: jest.fn()
  },
  logger: {
    debug: jest.fn(),
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
    logLifecycle: jest.fn(),
    logUserAction: jest.fn(),
    logDataProcessing: jest.fn(),
    logSync: jest.fn(),
    logApiCallStart: jest.fn().mockResolvedValue('call_123'),
    logApiCallEnd: jest.fn(),
    startTimer: jest.fn(() => ({ stop: jest.fn().mockResolvedValue(50) }))
  }
};

describe('Limitless Plugin Integration', () => {
  let plugin;

  beforeEach(() => {
    jest.clearAllMocks();

    // Reset plugin API mocks
    mockPluginAPI.storage.loadData.mockReturnValue({});
    mockPluginAPI.storage.saveData.mockReturnValue(true);
    mockPluginAPI.ui.addRibbonIcon.mockReturnValue('ribbon-icon-1');

    // Create a new plugin instance for each test
    plugin = new LimitlessPlugin(mockPluginAPI);
  });

  describe('Plugin Initialization', () => {
    test('should initialize plugin successfully', async () => {
      // Initialize plugin
      await plugin.initialize();

      expect(plugin.isInitialized).toBe(true);
      expect(mockPluginAPI.commands.register).toHaveBeenCalled();
      expect(mockPluginAPI.ui.addRibbonIcon).toHaveBeenCalled();
    });

    test('should register all expected commands', async () => {
      await plugin.initialize();

      const expectedCommands = [
        'limitless-configure',
        'limitless-sync-now',
        'limitless-sync-status',
        'limitless-view-data',
        'limitless-validate-key',
        'limitless-clear-data'
      ];

      expectedCommands.forEach(command => {
        expect(mockPluginAPI.commands.register).toHaveBeenCalledWith(
          command,
          expect.any(Function)
        );
      });
    });

    test('should set up UI elements', async () => {
      await plugin.initialize();

      expect(mockPluginAPI.ui.addRibbonIcon).toHaveBeenCalledWith(
        'brain',
        'Limitless AI Integration',
        expect.any(Function)
      );
    });

    test('should handle initialization errors gracefully', async () => {
      // Mock a failure in UI setup
      mockPluginAPI.ui.addRibbonIcon.mockImplementation(() => {
        throw new Error('UI setup failed');
      });

      // Should not throw, but should handle error internally
      await expect(plugin.initialize()).resolves.not.toThrow();
    });
  });

  describe('API Key Management', () => {
    beforeEach(async () => {
      await plugin.initialize();
    });

    test('should validate API key when configured', async () => {
      // Mock API key in storage
      mockPluginAPI.storage.loadData.mockReturnValue({
        apiKey: 'test-api-key-123'
      });

      // Mock successful API response
      mockPluginAPI.network.fetch.mockResolvedValue({
        ok: true,
        json: jest.fn().mockResolvedValue({
          data: { lifelogs: [] },
          meta: { lifelogs: { count: 0 } }
        })
      });

      await plugin.validateAPIKey();

      expect(mockPluginAPI.network.fetch).toHaveBeenCalledWith(
        expect.stringContaining('api.limitless.ai'),
        expect.objectContaining({
          headers: expect.objectContaining({
            'X-API-Key': 'test-api-key-123'
          })
        })
      );
    });

    test('should handle API key validation failure', async () => {
      mockPluginAPI.storage.loadData.mockReturnValue({
        apiKey: 'invalid-key'
      });

      mockPluginAPI.network.fetch.mockResolvedValue({
        ok: false,
        status: 401,
        statusText: 'Unauthorized',
        text: jest.fn().mockResolvedValue('Invalid API key')
      });

      await plugin.validateAPIKey();

      // Should handle error gracefully without throwing
      expect(mockPluginAPI.network.fetch).toHaveBeenCalled();
    });

    test('should handle missing API key', async () => {
      mockPluginAPI.storage.loadData.mockReturnValue({});

      await plugin.validateAPIKey();

      // Should not make API call when no key is present
      expect(mockPluginAPI.network.fetch).not.toHaveBeenCalled();
    });
  });

  describe('Data Synchronization', () => {
    beforeEach(async () => {
      await plugin.initialize();
    });

    test('should perform manual sync successfully', async () => {
      // Setup API key
      mockPluginAPI.storage.loadData.mockReturnValue({
        apiKey: 'test-api-key',
        syncInterval: 3600,
        autoSync: true
      });

      // Mock successful lifelog fetch
      const mockLifelogs = [
        {
          id: 'lifelog-1',
          title: 'Test Meeting',
          markdown: 'Meeting about project goals',
          startTime: '2025-01-01T10:00:00Z',
          endTime: '2025-01-01T11:00:00Z',
          contents: []
        }
      ];

      mockPluginAPI.network.fetch.mockResolvedValue({
        ok: true,
        json: jest.fn().mockResolvedValue({
          data: { lifelogs: mockLifelogs },
          meta: {
            lifelogs: {
              count: 1,
              nextCursor: null
            }
          }
        })
      });

      await plugin.performManualSync();

      expect(mockPluginAPI.network.fetch).toHaveBeenCalled();
      expect(mockPluginAPI.storage.saveData).toHaveBeenCalled();
    });

    test('should handle sync errors gracefully', async () => {
      mockPluginAPI.storage.loadData.mockReturnValue({
        apiKey: 'test-api-key'
      });

      mockPluginAPI.network.fetch.mockRejectedValue(new Error('Network error'));

      // Should not throw error
      await expect(plugin.performManualSync()).resolves.not.toThrow();
    }, 15000);

    test('should prevent concurrent syncs', async () => {
      mockPluginAPI.storage.loadData.mockReturnValue({
        apiKey: 'test-api-key'
      });

      // Mock slow API response
      let resolvePromise;
      const slowPromise = new Promise(resolve => {
        resolvePromise = resolve;
      });

      mockPluginAPI.network.fetch.mockReturnValue(slowPromise);

      // Start first sync
      const sync1Promise = plugin.performManualSync();

      // Try to start second sync immediately
      const sync2Promise = plugin.performManualSync();

      // Resolve the API call
      resolvePromise({
        ok: true,
        json: jest.fn().mockResolvedValue({
          data: { lifelogs: [] },
          meta: { lifelogs: { count: 0, nextCursor: null } }
        })
      });

      await Promise.all([sync1Promise, sync2Promise]);

      // Second sync should have been skipped
      expect(mockPluginAPI.network.fetch).toHaveBeenCalledTimes(1);
    });
  });

  describe('Data Processing Pipeline', () => {
    beforeEach(async () => {
      await plugin.initialize();
    });

    test('should process complete data pipeline', async () => {
      mockPluginAPI.storage.loadData.mockReturnValue({
        apiKey: 'test-api-key'
      });

      const mockLifelogs = [
        {
          id: 'lifelog-1',
          title: 'Team Discussion',
          markdown: 'Discussed project goals and feeling optimistic about success',
          startTime: '2025-01-01T10:00:00Z',
          endTime: '2025-01-01T10:30:00Z',
          contents: [
            {
              type: 'heading1',
              content: 'Team Discussion',
              speakerName: 'Alice',
              speakerIdentifier: 'user'
            }
          ]
        }
      ];

      mockPluginAPI.network.fetch.mockResolvedValue({
        ok: true,
        json: jest.fn().mockResolvedValue({
          data: { lifelogs: mockLifelogs },
          meta: {
            lifelogs: {
              count: 1,
              nextCursor: null
            }
          }
        })
      });

      await plugin.performManualSync();

      // Verify storage was called with processed data (AI enhancement disabled)
      expect(mockPluginAPI.storage.saveData).toHaveBeenCalledWith(
        expect.objectContaining({
          posts: expect.arrayContaining([
            expect.objectContaining({
              id: 'limitless-lifelog-1',
              title: 'Team Discussion',
              tags: expect.arrayContaining(['limitless', 'lifelog'])
            })
          ])
        })
      );
    });

    test('should handle data processing errors', async () => {
      mockPluginAPI.storage.loadData.mockReturnValue({
        apiKey: 'test-api-key'
      });

      // Mock API response with malformed data
      mockPluginAPI.network.fetch.mockResolvedValue({
        ok: true,
        json: jest.fn().mockResolvedValue({
          data: { lifelogs: [{ /* malformed lifelog */ }] },
          meta: { lifelogs: { count: 1, nextCursor: null } }
        })
      });

      // Should handle processing errors gracefully
      await expect(plugin.performManualSync()).resolves.not.toThrow();
    });
  });

  describe('Command Execution', () => {
    beforeEach(async () => {
      await plugin.initialize();
    });

    test('should execute configuration command', async () => {
      const configureCommand = mockPluginAPI.commands.register.mock.calls
        .find(call => call[0] === 'limitless-configure')[1];

      // Should not throw when executed
      await expect(configureCommand()).resolves.not.toThrow();
    });

    test('should execute sync command', async () => {
      mockPluginAPI.storage.loadData.mockReturnValue({
        apiKey: 'test-api-key'
      });

      mockPluginAPI.network.fetch.mockResolvedValue({
        ok: true,
        json: jest.fn().mockResolvedValue({
          data: { lifelogs: [] },
          meta: { lifelogs: { count: 0, nextCursor: null } }
        })
      });

      const syncCommand = mockPluginAPI.commands.register.mock.calls
        .find(call => call[0] === 'limitless-sync-now')[1];

      await expect(syncCommand()).resolves.not.toThrow();
      expect(mockPluginAPI.network.fetch).toHaveBeenCalled();
    });

    test('should execute data viewing command', async () => {
      mockPluginAPI.storage.loadData.mockReturnValue({
        posts: [{ id: 'test-post', title: 'Test' }]
      });

      const viewDataCommand = mockPluginAPI.commands.register.mock.calls
        .find(call => call[0] === 'limitless-view-data')[1];

      await expect(viewDataCommand()).resolves.not.toThrow();
    });

    test('should execute clear data command', async () => {
      const clearDataCommand = mockPluginAPI.commands.register.mock.calls
        .find(call => call[0] === 'limitless-clear-data')[1];

      await expect(clearDataCommand()).resolves.not.toThrow();
      expect(mockPluginAPI.storage.saveData).toHaveBeenCalledWith({});
    });
  });

  describe('UI Interactions', () => {
    beforeEach(async () => {
      await plugin.initialize();
    });

    test('should handle ribbon icon click', async () => {
      const ribbonCallback = mockPluginAPI.ui.addRibbonIcon.mock.calls[0][2];

      // Should not throw when ribbon icon is clicked
      await expect(ribbonCallback()).resolves.not.toThrow();
    });

    test('should show main menu modal', async () => {
      mockPluginAPI.storage.loadData.mockReturnValue({
        apiKey: 'test-key',
        autoSync: true
      });

      await plugin.showMainMenu();

      expect(mockPluginAPI.ui.showModal).toHaveBeenCalledWith(
        expect.objectContaining({
          title: 'Limitless AI Integration',
          content: expect.stringMatching(/API Key:.*Configured/)
        })
      );
    });
  });

  describe('Error Handling and Recovery', () => {
    beforeEach(async () => {
      await plugin.initialize();
    });

    test('should handle storage failures gracefully', async () => {
      mockPluginAPI.storage.saveData.mockReturnValue(false);
      mockPluginAPI.storage.loadData.mockImplementation(() => {
        throw new Error('Storage unavailable');
      });

      // Should not crash when storage fails
      await expect(plugin.clearStoredData()).resolves.not.toThrow();
    });

    test('should handle network failures gracefully', async () => {
      mockPluginAPI.storage.loadData.mockReturnValue({
        apiKey: 'test-key'
      });

      mockPluginAPI.network.fetch.mockRejectedValue(new Error('Network unavailable'));

      await expect(plugin.performManualSync()).resolves.not.toThrow();
    });

    test('should handle API rate limiting', async () => {
      mockPluginAPI.storage.loadData.mockReturnValue({
        apiKey: 'test-key'
      });

      mockPluginAPI.network.fetch.mockResolvedValue({
        ok: false,
        status: 429,
        statusText: 'Too Many Requests',
        text: jest.fn().mockResolvedValue('Rate limit exceeded')
      });

      await expect(plugin.performManualSync()).resolves.not.toThrow();
    });
  });

  describe('Plugin Lifecycle', () => {
    test('should cleanup properly when disabled', async () => {
      await plugin.initialize();

      // Perform cleanup
      await plugin.cleanup();

      expect(plugin.isInitialized).toBe(false);
      expect(mockPluginAPI.ui.removeRibbonIcon).toHaveBeenCalled();
    });

    test('should provide status information', async () => {
      await plugin.initialize();

      const status = plugin.getStatus();

      expect(status).toEqual({
        initialized: true,
        version: '1.0.0',
        permissions: ['network', 'storage', 'workspace'],
        syncStatus: expect.any(Object),
        lastActivity: expect.any(String)
      });
    });

    test('should export correct module interface', () => {
      const pluginModule = require('../main');

      expect(pluginModule).toHaveProperty('LimitlessPlugin');
      expect(typeof pluginModule.LimitlessPlugin).toBe('function');
    });
  });

  describe('Performance and Resource Usage', () => {
    test('should initialize within reasonable time', async () => {
      const startTime = Date.now();

      await plugin.initialize();
      const initTime = Date.now() - startTime;

      // Should initialize within 500ms
      expect(initTime).toBeLessThan(500);
      expect(plugin.isInitialized).toBe(true);
    });

    test('should handle large data sets efficiently', async () => {
      await plugin.initialize();

      // Mock API with large dataset
      const largeDataset = Array.from({ length: 100 }, (_, i) => ({
        id: `lifelog-${i}`,
        title: `Log ${i}`,
        markdown: 'Sample content',
        startTime: new Date().toISOString(),
        endTime: new Date().toISOString(),
        contents: []
      }));

      mockPluginAPI.storage.loadData.mockReturnValue({
        apiKey: 'test-key'
      });

      mockPluginAPI.network.fetch.mockResolvedValue({
        ok: true,
        json: jest.fn().mockResolvedValue({
          data: { lifelogs: largeDataset },
          meta: { lifelogs: { count: 100, nextCursor: null } }
        })
      });

      const startTime = Date.now();
      await plugin.performManualSync();
      const processTime = Date.now() - startTime;

      // Should process 100 records within 5 seconds
      expect(processTime).toBeLessThan(5000);
      expect(mockPluginAPI.storage.saveData).toHaveBeenCalled();
    });
  });

  afterEach(async () => {
    if (plugin) {
      try {
        await plugin.cleanup();
      } catch (error) {
        // Ignore cleanup errors in tests
      }
    }
  });
});
