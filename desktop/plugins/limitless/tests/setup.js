/**
 * Jest Test Setup
 *
 * Global test configuration and mocks for Limitless plugin tests
 */

// Mock console methods to reduce noise in tests
global.console = {
  ...console,
  log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn()
};

// Mock global timers
jest.setTimeout(10000); // 10 second timeout for tests

// Mock Date for consistent testing
const mockDate = new Date('2025-01-01T12:00:00Z');
global.Date.now = jest.fn(() => mockDate.getTime());

// Global test utilities
global.testUtils = {
  // Create mock API response
  createMockApiResponse: (data, options = {}) => ({
    ok: options.ok !== false,
    status: options.status || 200,
    statusText: options.statusText || 'OK',
    json: jest.fn().mockResolvedValue(data),
    text: jest.fn().mockResolvedValue(options.text || ''),
    ...options
  }),

  // Create mock lifelog data
  createMockLifelog: (id = 'test-lifelog', overrides = {}) => ({
    id,
    title: `Test Lifelog ${id}`,
    markdown: `# Test Content\n\nThis is test content for ${id}`,
    startTime: '2025-01-01T10:00:00Z',
    endTime: '2025-01-01T10:30:00Z',
    contents: [
      {
        type: 'heading1',
        content: 'Test Content',
        speakerName: 'Test User',
        speakerIdentifier: 'user'
      }
    ],
    ...overrides
  }),

  // Create mock plugin API
  createMockPluginAPI: (overrides = {}) => ({
    manifest: {
      id: 'limitless',
      name: 'Limitless AI Integration',
      version: '1.0.0',
      permissions: ['network', 'storage', 'workspace']
    },
    storage: {
      loadData: jest.fn().mockReturnValue({}),
      saveData: jest.fn().mockReturnValue(true)
    },
    network: {
      fetch: jest.fn()
    },
    commands: {
      register: jest.fn(),
      setMetadata: jest.fn()
    },
    ui: {
      addRibbonIcon: jest.fn().mockReturnValue('ribbon-icon-1'),
      showModal: jest.fn(),
      removeRibbonIcon: jest.fn()
    },
    ...overrides
  }),

  // Wait for async operations
  waitFor: (ms = 100) => new Promise(resolve => setTimeout(resolve, ms)),

  // Reset all mocks
  resetAllMocks: () => {
    jest.clearAllMocks();
    jest.clearAllTimers();
  }
};

// Clean up after each test
afterEach(() => {
  jest.clearAllMocks();
});

// Clean up after all tests
afterAll(() => {
  jest.restoreAllMocks();
});
