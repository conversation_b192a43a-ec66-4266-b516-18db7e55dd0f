/**
 * Unit Tests for Limitless API Client
 *
 * Tests all API communication functionality including:
 * - API key validation
 * - Lifelog fetching with pagination
 * - Error handling and retry logic
 * - Rate limiting compliance
 * - Data transformation accuracy
 */

const LimitlessAPI = require('../src/limitless-api');

// Mock dependencies
const mockLogger = {
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
  debug: jest.fn(),
  startTimer: jest.fn(() => ({ stop: jest.fn().mockResolvedValue(100) })),
  logApiCallStart: jest.fn().mockResolvedValue('call_123'),
  logApiCallEnd: jest.fn(),
  logSync: jest.fn()
};

const mockAPI = {
  manifest: { id: 'limitless' },
  network: { fetch: jest.fn() }
};

// Mock timers for sleep tests
jest.useFakeTimers();

describe('LimitlessAPI', () => {
  let limitlessAPI;

  beforeEach(() => {
    jest.clearAllMocks();
    jest.clearAllTimers();
    limitlessAPI = new LimitlessAPI(mockAPI, mockLogger);
  });

  afterEach(() => {
    jest.runOnlyPendingTimers();
  });

  describe('constructor', () => {
    test('should initialize with correct default values', () => {
      expect(limitlessAPI.baseURL).toBe('https://api.limitless.ai');
      expect(limitlessAPI.version).toBe('v1');
      expect(limitlessAPI.rateLimitDelay).toBe(1000);
      expect(limitlessAPI.maxRetries).toBe(3);
    });
  });

  describe('validateAPIKey', () => {
    test('should validate a correct API key', async () => {
      const mockResponse = {
        ok: true,
        json: jest.fn().mockResolvedValue({
          data: { lifelogs: [] },
          meta: { lifelogs: { count: 0 } }
        })
      };

      mockAPI.network.fetch.mockResolvedValue(mockResponse);

      const result = await limitlessAPI.validateAPIKey('valid-key-123');

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(mockLogger.info).toHaveBeenCalledWith('Starting API key validation');
      expect(mockLogger.info).toHaveBeenCalledWith('API key validation successful');
    });

    test('should reject invalid API key', async () => {
      const mockResponse = {
        ok: false,
        status: 401,
        statusText: 'Unauthorized',
        text: jest.fn().mockResolvedValue('Invalid API key')
      };

      mockAPI.network.fetch.mockResolvedValue(mockResponse);

      const result = await limitlessAPI.validateAPIKey('invalid-key');

      expect(result.success).toBe(false);
      expect(result.error).toContain('API validation failed: 401 Unauthorized');
      expect(mockLogger.warn).toHaveBeenCalledWith('API key validation failed', expect.any(Object));
    });

    test('should reject empty API key', async () => {
      const result = await limitlessAPI.validateAPIKey('');

      expect(result.success).toBe(false);
      expect(result.error).toBe('API key is required and must be a non-empty string');
      expect(mockLogger.warn).toHaveBeenCalledWith('Invalid API key format provided');
    });
    test('should handle network errors', async () => {
      mockAPI.network.fetch.mockRejectedValue(new Error('Network timeout'));

      // Mock the sleep function to avoid actual delays during retries
      const originalSleep = limitlessAPI.sleep;
      limitlessAPI.sleep = jest.fn().mockResolvedValue();

      const result = await limitlessAPI.validateAPIKey('test-key');

      expect(result.success).toBe(false);
      expect(result.error).toContain('Network error during validation');
      expect(mockLogger.error).toHaveBeenCalledWith('API key validation error', expect.any(Error));

      // Restore original sleep
      limitlessAPI.sleep = originalSleep;
    }, 15000);
  });

  describe('fetchLifelogs', () => {
    test('should fetch lifelogs successfully', async () => {
      const mockLifelogs = [
        {
          id: 'lifelog-1',
          title: 'Test Lifelog',
          markdown: 'Test content',
          startTime: '2025-01-01T10:00:00Z',
          endTime: '2025-01-01T10:30:00Z',
          contents: []
        }
      ];

      const mockResponse = {
        ok: true,
        json: jest.fn().mockResolvedValue({
          data: { lifelogs: mockLifelogs },
          meta: {
            lifelogs: {
              count: 1,
              nextCursor: null
            }
          }
        })
      };

      mockAPI.network.fetch.mockResolvedValue(mockResponse);

      const result = await limitlessAPI.fetchLifelogs({
        apiKey: 'test-key',
        limit: 10
      });

      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockLifelogs);
      expect(result.nextCursor).toBeNull();
      expect(mockLogger.info).toHaveBeenCalledWith('Starting lifelog fetch', expect.any(Object));
    });

    test('should handle pagination correctly', async () => {
      const mockResponse = {
        ok: true,
        json: jest.fn().mockResolvedValue({
          data: { lifelogs: [] },
          meta: {
            lifelogs: {
              count: 0,
              nextCursor: 'next-cursor-123'
            }
          }
        })
      };

      mockAPI.network.fetch.mockResolvedValue(mockResponse);

      const result = await limitlessAPI.fetchLifelogs({
        apiKey: 'test-key',
        cursor: 'cursor-123'
      });

      expect(result.nextCursor).toBe('next-cursor-123');
      expect(mockAPI.network.fetch).toHaveBeenCalledWith(
        expect.stringContaining('cursor=cursor-123'),
        expect.any(Object)
      );
    });

    test('should require API key', async () => {
      const result = await limitlessAPI.fetchLifelogs({});

      expect(result.success).toBe(false);
      expect(result.error).toBe('API key is required for fetching lifelogs');
    });

    test('should handle API errors', async () => {
      const mockResponse = {
        ok: false,
        status: 500,
        statusText: 'Internal Server Error',
        text: jest.fn().mockResolvedValue('Server error')
      };

      mockAPI.network.fetch.mockResolvedValue(mockResponse);

      const result = await limitlessAPI.fetchLifelogs({
        apiKey: 'test-key'
      });

      expect(result.success).toBe(false);
      expect(result.error).toContain('Failed to fetch lifelogs: 500');
    });
  });

  describe('fetchLifelogDetails', () => {
    test('should fetch lifelog details successfully', async () => {
      const mockLifelog = {
        id: 'lifelog-1',
        title: 'Detailed Lifelog',
        markdown: 'Detailed content',
        contents: [
          {
            type: 'heading1',
            content: 'Meeting Notes',
            speakerName: 'John',
            speakerIdentifier: 'user'
          }
        ]
      };

      const mockResponse = {
        ok: true,
        json: jest.fn().mockResolvedValue({
          data: { lifelog: mockLifelog }
        })
      };

      mockAPI.network.fetch.mockResolvedValue(mockResponse);

      const result = await limitlessAPI.fetchLifelogDetails('lifelog-1', 'test-key');

      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockLifelog);
      expect(mockLogger.info).toHaveBeenCalledWith('Starting lifelog detail fetch', expect.any(Object));
    });

    test('should require both lifelog ID and API key', async () => {
      const result1 = await limitlessAPI.fetchLifelogDetails('', 'test-key');
      const result2 = await limitlessAPI.fetchLifelogDetails('lifelog-1', '');

      expect(result1.success).toBe(false);
      expect(result2.success).toBe(false);
      expect(result1.error).toBe('Lifelog ID and API key are required');
      expect(result2.error).toBe('Lifelog ID and API key are required');
    });
  });

  describe('syncData', () => {
    test('should perform full data sync', async () => {
      const mockLifelogs = [
        { id: 'lifelog-1', title: 'Sync Test 1' },
        { id: 'lifelog-2', title: 'Sync Test 2' }
      ];

      // Mock first batch response
      const mockResponse1 = {
        ok: true,
        json: jest.fn().mockResolvedValue({
          data: { lifelogs: mockLifelogs },
          meta: {
            lifelogs: {
              count: 2,
              nextCursor: 'cursor-2'
            }
          }
        })
      };

      // Mock second batch response (empty - end of data)
      const mockResponse2 = {
        ok: true,
        json: jest.fn().mockResolvedValue({
          data: { lifelogs: [] },
          meta: {
            lifelogs: {
              count: 0,
              nextCursor: null
            }
          }
        })
      };

      mockAPI.network.fetch
        .mockResolvedValueOnce(mockResponse1)
        .mockResolvedValueOnce(mockResponse2);

      // Mock the sleep function to avoid actual delays
      const originalSleep = limitlessAPI.sleep;
      limitlessAPI.sleep = jest.fn().mockResolvedValue();

      const result = await limitlessAPI.syncData('test-key', null, {
        batchSize: 10,
        maxRecords: 100
      });

      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockLifelogs);
      expect(result.totalFetched).toBe(2);
      expect(result.truncated).toBe(false);
      expect(mockLogger.logSync).toHaveBeenCalledWith('start', expect.any(Object));
      expect(mockLogger.logSync).toHaveBeenCalledWith('complete', expect.any(Object));

      // Restore original sleep
      limitlessAPI.sleep = originalSleep;
    }, 15000);

    test('should handle incremental sync with lastSyncTime', async () => {
      const lastSyncTime = '2025-01-01T00:00:00Z';

      const mockResponse = {
        ok: true,
        json: jest.fn().mockResolvedValue({
          data: { lifelogs: [] },
          meta: { lifelogs: { count: 0, nextCursor: null } }
        })
      };

      mockAPI.network.fetch.mockResolvedValue(mockResponse);

      await limitlessAPI.syncData('test-key', lastSyncTime);

      // Check for URL-encoded version of the timestamp
      expect(mockAPI.network.fetch).toHaveBeenCalledWith(
        expect.stringContaining(`start=${encodeURIComponent(lastSyncTime)}`),
        expect.any(Object)
      );
    });

    test('should respect maxRecords limit', async () => {
      const mockLifelogs = Array.from({ length: 15 }, (_, i) => ({
        id: `lifelog-${i}`,
        title: `Test ${i}`
      }));

      const mockResponse = {
        ok: true,
        json: jest.fn().mockResolvedValue({
          data: { lifelogs: mockLifelogs },
          meta: {
            lifelogs: {
              count: 15,
              nextCursor: 'more-data'
            }
          }
        })
      };

      mockAPI.network.fetch.mockResolvedValue(mockResponse);

      const result = await limitlessAPI.syncData('test-key', null, {
        maxRecords: 10,
        batchSize: 15 // Set batch size larger than maxRecords to test truncation
      });

      expect(result.totalFetched).toBe(10); // Should be limited to maxRecords
      expect(result.truncated).toBe(true); // Should be truncated because we hit the limit
    });

    test('should handle sync errors gracefully', async () => {
      mockAPI.network.fetch.mockRejectedValue(new Error('Network failure'));

      const result = await limitlessAPI.syncData('test-key');

      expect(result.success).toBe(false);
      expect(result.error).toContain('Network failure');
      expect(mockLogger.logSync).toHaveBeenCalledWith('error', expect.any(Object));
    });
  });

  describe('makeRequest', () => {
    test('should make successful request', async () => {
      const mockResponse = { ok: true, status: 200 };
      mockAPI.network.fetch.mockResolvedValue(mockResponse);

      const result = await limitlessAPI.makeRequest('https://test.com');

      expect(result).toBe(mockResponse);
      expect(mockAPI.network.fetch).toHaveBeenCalledWith('https://test.com', {});
    });

    test('should retry on network errors', async () => {
      const networkError = new Error('network error');
      const mockResponse = { ok: true, status: 200 };

      mockAPI.network.fetch
        .mockRejectedValueOnce(networkError)
        .mockRejectedValueOnce(networkError)
        .mockResolvedValueOnce(mockResponse);

      // Mock the sleep function to avoid actual delays
      const originalSleep = limitlessAPI.sleep;
      limitlessAPI.sleep = jest.fn().mockResolvedValue();

      const result = await limitlessAPI.makeRequest('https://test.com');

      expect(result).toBe(mockResponse);
      expect(mockAPI.network.fetch).toHaveBeenCalledTimes(3);
      expect(mockLogger.warn).toHaveBeenCalledWith('Request failed, retrying', expect.any(Object));

      // Restore original sleep
      limitlessAPI.sleep = originalSleep;
    }, 10000);

    test('should fail after max retries', async () => {
      const networkError = new Error('persistent network error');
      mockAPI.network.fetch.mockRejectedValue(networkError);

      // Mock the sleep function to avoid actual delays
      const originalSleep = limitlessAPI.sleep;
      limitlessAPI.sleep = jest.fn().mockResolvedValue();

      await expect(limitlessAPI.makeRequest('https://test.com')).rejects.toThrow('persistent network error');
      expect(mockAPI.network.fetch).toHaveBeenCalledTimes(4); // 1 initial + 3 retries

      // Restore original sleep
      limitlessAPI.sleep = originalSleep;
    }, 10000);

    test('should throw error if fetch not available', async () => {
      const apiWithoutFetch = { ...mockAPI, network: {} };
      const limitlessAPINoFetch = new LimitlessAPI(apiWithoutFetch, mockLogger);

      // Temporarily remove global fetch to simulate unavailable environment
      const originalFetch = global.fetch;
      delete global.fetch;

      try {
        await expect(limitlessAPINoFetch.makeRequest('https://test.com')).rejects.toThrow(/Network access not available|fetch is not defined/);
      } finally {
        // Restore global fetch
        if (originalFetch) {
          global.fetch = originalFetch;
        }
      }
    });
  });

  describe('shouldRetry', () => {
    test('should retry on retryable errors', () => {
      const networkError = new Error('network error occurred');
      const timeoutError = new Error('request timeout');
      const dnsError = new Error('dns lookup failed');

      expect(limitlessAPI.shouldRetry(networkError)).toBe(true);
      expect(limitlessAPI.shouldRetry(timeoutError)).toBe(true);
      expect(limitlessAPI.shouldRetry(dnsError)).toBe(true);
    });

    test('should not retry on non-retryable errors', () => {
      const authError = new Error('authentication failed');
      const validationError = new Error('invalid input');

      expect(limitlessAPI.shouldRetry(authError)).toBe(false);
      expect(limitlessAPI.shouldRetry(validationError)).toBe(false);
    });
  });

  describe('sanitizeUrl', () => {
    test('should sanitize sensitive parameters', () => {
      const url = 'https://api.test.com/data?key=secret123&other=value';
      const sanitized = limitlessAPI.sanitizeUrl(url);

      expect(sanitized).toContain('key=%5BREDACTED%5D');
      expect(sanitized).toContain('other=value');
      expect(sanitized).not.toContain('secret123');
    });

    test('should handle invalid URLs', () => {
      const result = limitlessAPI.sanitizeUrl('not-a-url');
      expect(result).toBe('[INVALID_URL]');
    });
  });

  describe('sleep', () => {
    test('should resolve after specified time', async () => {
      const sleepPromise = limitlessAPI.sleep(50);

      // Fast-forward time by 50ms
      jest.advanceTimersByTime(50);

      await expect(sleepPromise).resolves.toBeUndefined();
    });
  });

  describe('getApiStats', () => {
    test('should return correct API statistics', () => {
      const stats = limitlessAPI.getApiStats();

      expect(stats).toEqual({
        baseURL: 'https://api.limitless.ai',
        version: 'v1',
        rateLimitDelay: 1000,
        maxRetries: 3,
        retryDelay: 2000
      });
    });
  });
});

// Cleanup after all tests
afterAll(() => {
  jest.useRealTimers();
});

// Integration test with actual API structure
describe('LimitlessAPI Integration', () => {
  beforeAll(() => {
    jest.useRealTimers();
  });

  afterAll(() => {
    jest.useFakeTimers();
  });

  test('should handle real API response structure', async () => {
    const mockRealResponse = {
      ok: true,
      json: jest.fn().mockResolvedValue({
        "data": {
          "lifelogs": [
            {
              "id": "lifelog_abc123",
              "title": "Morning standup discussion",
              "markdown": "# Meeting Notes\n\nDiscussed project timeline and deliverables...",
              "startTime": "2025-01-01T09:00:00.000Z",
              "endTime": "2025-01-01T09:30:00.000Z",
              "contents": [
                {
                  "type": "heading1",
                  "content": "Meeting Notes",
                  "startTime": "2025-01-01T09:00:00.000Z",
                  "endTime": "2025-01-01T09:00:05.000Z",
                  "startOffsetMs": 0,
                  "endOffsetMs": 5000,
                  "children": [],
                  "speakerName": "John Doe",
                  "speakerIdentifier": "user"
                }
              ]
            }
          ]
        },
        "meta": {
          "lifelogs": {
            "nextCursor": "cursor_xyz789",
            "count": 1
          }
        }
      })
    };

    mockAPI.network.fetch.mockResolvedValue(mockRealResponse);
    const limitlessAPI = new LimitlessAPI(mockAPI, mockLogger);

    const result = await limitlessAPI.fetchLifelogs({
      apiKey: 'test-key',
      limit: 1
    });

    expect(result.success).toBe(true);
    expect(result.data[0]).toHaveProperty('id', 'lifelog_abc123');
    expect(result.data[0]).toHaveProperty('title', 'Morning standup discussion');
    expect(result.data[0]).toHaveProperty('markdown');
    expect(result.data[0]).toHaveProperty('contents');
    expect(result.nextCursor).toBe('cursor_xyz789');
    expect(result.meta.count).toBe(1);
  });
});
