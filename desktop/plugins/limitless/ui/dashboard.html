<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Limitless AI Dashboard</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="limitless-settings-container">
        <header class="settings-header">
            <h1>Limitless AI Data Dashboard</h1>
            <p>Overview of your imported lifelogs and generated content from Limitless AI.</p>
        </header>

        <div class="settings-content">
            <!-- Statistics Overview -->
            <section class="settings-section">
                <h2>Statistics Overview</h2>

                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">📊</div>
                        <div class="stat-content">
                            <h3>Total Lifelogs</h3>
                            <span class="stat-value" id="totalLifelogs">0</span>
                            <span class="stat-label">imported records</span>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon">📝</div>
                        <div class="stat-content">
                            <h3>Generated Posts</h3>
                            <span class="stat-value" id="generatedPosts">0</span>
                            <span class="stat-label">AI-enhanced posts</span>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon">🔄</div>
                        <div class="stat-content">
                            <h3>Last Sync</h3>
                            <span class="stat-value" id="lastSyncTime">Never</span>
                            <span class="stat-label">synchronization</span>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon">⏱️</div>
                        <div class="stat-content">
                            <h3>Total Duration</h3>
                            <span class="stat-value" id="totalDuration">0h 0m</span>
                            <span class="stat-label">conversation time</span>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Recent Activity -->
            <section class="settings-section">
                <h2>Recent Activity</h2>

                <div class="activity-header">
                    <div class="activity-controls">
                        <select id="activityFilter" class="activity-filter">
                            <option value="all">All Activity</option>
                            <option value="today">Today</option>
                            <option value="week">This Week</option>
                            <option value="month">This Month</option>
                        </select>

                        <button id="refreshActivity" class="btn btn-secondary">
                            <span class="btn-icon">🔄</span>
                            Refresh
                        </button>
                    </div>
                </div>

                <div class="activity-list" id="recentActivity">
                    <div class="activity-item placeholder">
                        <div class="activity-icon">📭</div>
                        <div class="activity-content">
                            <div class="activity-title">No recent activity</div>
                            <div class="activity-description">
                                Import your first lifelog data to see activity here.
                            </div>
                        </div>
                        <div class="activity-time">--</div>
                    </div>
                </div>
            </section>

            <!-- Data Insights -->
            <section class="settings-section">
                <h2>Data Insights</h2>

                <div class="insights-grid">
                    <div class="insight-card">
                        <h3>Top Conversation Topics</h3>
                        <div class="topic-list" id="topTopics">
                            <div class="topic-item">
                                <span class="topic-name">No data available</span>
                                <span class="topic-count">--</span>
                            </div>
                        </div>
                    </div>

                    <div class="insight-card">
                        <h3>Conversation Patterns</h3>
                        <div class="pattern-list" id="conversationPatterns">
                            <div class="pattern-item">
                                <div class="pattern-label">Most Active Hour</div>
                                <div class="pattern-value" id="mostActiveHour">--:--</div>
                            </div>
                            <div class="pattern-item">
                                <div class="pattern-label">Average Duration</div>
                                <div class="pattern-value" id="avgDuration">-- min</div>
                            </div>
                            <div class="pattern-item">
                                <div class="pattern-label">Weekly Total</div>
                                <div class="pattern-value" id="weeklyTotal">-- hours</div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Data Management -->
            <section class="settings-section">
                <h2>Data Management</h2>

                <div class="management-grid">
                    <div class="management-card">
                        <div class="management-icon">💾</div>
                        <div class="management-content">
                            <h3>Data Export</h3>
                            <p>Export your lifelog data and generated posts for backup or analysis.</p>
                            <button id="exportDataBtn" class="btn btn-secondary">
                                <span class="btn-icon">📤</span>
                                Export Data
                            </button>
                        </div>
                    </div>

                    <div class="management-card">
                        <div class="management-icon">🔍</div>
                        <div class="management-content">
                            <h3>Search & Filter</h3>
                            <p>Find specific conversations or posts using advanced search.</p>
                            <button id="searchDataBtn" class="btn btn-secondary">
                                <span class="btn-icon">🔎</span>
                                Search Data
                            </button>
                        </div>
                    </div>

                    <div class="management-card warning">
                        <div class="management-icon">🗑️</div>
                        <div class="management-content">
                            <h3>Clear Data</h3>
                            <p>Remove all imported lifelog data and generated posts.</p>
                            <button id="clearDataBtn" class="btn btn-danger">
                                <span class="btn-icon">🗑️</span>
                                Clear All Data
                            </button>
                        </div>
                    </div>
                </div>
            </section>
        </div>

        <!-- Action Buttons -->
        <div class="settings-actions">
            <div class="actions-left">
                <button id="syncNowBtn" class="btn btn-primary">
                    <span class="btn-icon">🔄</span>
                    Sync Now
                </button>
                <button id="viewSettingsBtn" class="btn btn-secondary">
                    <span class="btn-icon">⚙️</span>
                    Settings
                </button>
            </div>

            <div class="actions-right">
                <button id="refreshDashboard" class="btn btn-secondary">
                    <span class="btn-icon">🔄</span>
                    Refresh Dashboard
                </button>
            </div>
        </div>
    </div>

    <script src="../src/dashboard-ui.js"></script>
</body>
</html>
