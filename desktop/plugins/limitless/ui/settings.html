<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Limitless AI Settings</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="limitless-settings-container">
        <header class="settings-header">
            <h1>Limitless AI Integration Settings</h1>
            <p>Configure your Limitless AI pendant integration for secure, local data processing.</p>
        </header>

        <div class="settings-content">
            <!-- API Configuration Section -->
            <section class="settings-section">
                <h2>API Configuration</h2>

                <div class="form-group">
                    <label for="apiKey">
                        <span class="label-text">Limitless AI API Key</span>
                        <span class="label-hint">Your personal API key from Limitless AI Developer settings</span>
                    </label>
                    <div class="input-with-button">
                        <input
                            type="password"
                            id="apiKey"
                            placeholder="Enter your Limitless AI API Key"
                            autocomplete="off"
                        >
                        <button id="validateBtn" class="btn btn-secondary">Validate</button>
                        <button id="showKeyBtn" class="btn btn-icon" type="button" title="Show/Hide API Key">
                            👁️
                        </button>
                    </div>
                    <div class="validation-status" id="validationStatus"></div>
                </div>
            </section>

            <!-- Sync Settings Section -->
            <section class="settings-section">
                <h2>Synchronization Settings</h2>

                <div class="form-group">
                    <label for="autoSync">
                        <input type="checkbox" id="autoSync" checked>
                        <span class="checkbox-label">Enable automatic synchronization</span>
                    </label>
                    <div class="form-hint">Automatically fetch new lifelogs at regular intervals</div>
                </div>

                <div class="form-group">
                    <label for="syncInterval">
                        <span class="label-text">Sync Interval</span>
                        <span class="label-hint">How often to check for new data (in hours)</span>
                    </label>
                    <div class="input-range-group">
                        <input
                            type="range"
                            id="syncInterval"
                            min="1"
                            max="24"
                            value="6"
                            step="1"
                        >
                        <span class="range-value" id="syncIntervalValue">6 hours</span>
                    </div>
                </div>

                <div class="form-group">
                    <label for="maxRecords">
                        <span class="label-text">Maximum Records</span>
                        <span class="label-hint">Maximum number of lifelogs to store locally</span>
                    </label>
                    <input
                        type="number"
                        id="maxRecords"
                        min="100"
                        max="10000"
                        value="1000"
                        step="100"
                    >
                </div>

                <div class="form-group">
                    <label for="timezone">
                        <span class="label-text">Timezone</span>
                        <span class="label-hint">Timezone for date/time operations</span>
                    </label>
                    <select id="timezone">
                        <option value="UTC">UTC</option>
                        <option value="America/New_York">Eastern Time</option>
                        <option value="America/Chicago">Central Time</option>
                        <option value="America/Denver">Mountain Time</option>
                        <option value="America/Los_Angeles">Pacific Time</option>
                        <option value="Europe/London">London</option>
                        <option value="Europe/Paris">Paris</option>
                        <option value="Asia/Tokyo">Tokyo</option>
                        <option value="Asia/Shanghai">Shanghai</option>
                        <option value="Australia/Sydney">Sydney</option>
                    </select>
                </div>
            </section>

            <!-- Status Section -->
            <section class="settings-section">
                <h2>Status</h2>

                <div class="status-grid">
                    <div class="status-card">
                        <h3>API Connection</h3>
                        <div class="status-indicator" id="apiStatus">
                            <span class="status-dot unknown"></span>
                            <span class="status-text">Unknown</span>
                        </div>
                    </div>

                    <div class="status-card">
                        <h3>Last Sync</h3>
                        <div class="status-info" id="lastSyncInfo">
                            <span class="status-text">Never</span>
                        </div>
                    </div>

                    <div class="status-card">
                        <h3>Auto Sync</h3>
                        <div class="status-indicator" id="autoSyncStatus">
                            <span class="status-dot disabled"></span>
                            <span class="status-text">Disabled</span>
                        </div>
                    </div>

                    <div class="status-card">
                        <h3>Stored Posts</h3>
                        <div class="status-info" id="storedPostsCount">
                            <span class="status-text">0</span>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Advanced Settings Section -->
            <section class="settings-section">
                <h2>Advanced Settings</h2>

                <div class="form-group">
                    <label for="enableDebugLogging">
                        <input type="checkbox" id="enableDebugLogging">
                        <span class="checkbox-label">Enable debug logging</span>
                    </label>
                    <div class="form-hint">Log detailed debug information (may impact performance)</div>
                </div>

                <div class="form-group">
                    <label for="aiEnhancement">
                        <input type="checkbox" id="aiEnhancement" checked>
                        <span class="checkbox-label">Enable AI content enhancement</span>
                    </label>
                    <div class="form-hint">Add AI-generated insights, themes, and tags to posts</div>
                </div>
            </section>
        </div>

        <!-- Action Buttons -->
        <div class="settings-actions">
            <div class="actions-left">
                <button id="syncNowBtn" class="btn btn-secondary">
                    <span class="btn-icon">🔄</span>
                    Sync Now
                </button>
                <button id="testConnectionBtn" class="btn btn-secondary">
                    <span class="btn-icon">🔍</span>
                    Test Connection
                </button>
            </div>

            <div class="actions-right">
                <button id="resetBtn" class="btn btn-danger">Reset to Defaults</button>
                <button id="saveBtn" class="btn btn-primary">Save Settings</button>
            </div>
        </div>

        <!-- Sync Progress -->
        <div class="sync-progress" id="syncProgress" style="display: none;">
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div class="progress-text" id="progressText">Initializing sync...</div>
        </div>
    </div>

    <script src="../src/limitless-api.js"></script>
    <script src="../src/settings-ui.js"></script>
</body>
</html>
