/**
 * Settings UI JavaScript
 *
 * Handles all user interactions in the Limitless AI settings interface,
 * including API key validation, form management, and real-time status updates.
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */

const Logger = require('./logger');

class LimitlessSettingsUI {
  /**
   * Creates a new LimitlessSettingsUI instance
   */
  constructor() {
    this.pluginAPI = null;
    this.limitlessAPI = null;
    this.currentSettings = {};
    this.validationInProgress = false;
    this.logger = null;

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.initialize());
    } else {
      this.initialize();
    }
  }

  /**
   * Initializes the settings UI
   *
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      // Get plugin API reference from parent window
      this.pluginAPI = window.parent?.pluginAPI || window.pluginAPI;

      if (!this.pluginAPI) {
        console.error('Plugin API not available');
        this.showError('Plugin API not available. Please reload the plugin.');
        return;
      }

      // Prefer file logger if possible
      try {
        this.logger = new Logger(this.pluginAPI);
        // Test file logger immediately
        await this.logger.info('Logger test: File logger initialized and should write to /logs');
        console.log('LimitlessSettingsUI: File logger initialized. Logs should appear in /logs.');
      } catch (e) {
        console.error('LimitlessSettingsUI: Failed to initialize file logger:', e);
        // Fallback to pluginAPI.getLogger or console
        this.logger = this.pluginAPI.getLogger ? this.pluginAPI.getLogger('limitless-settings-ui') : null;
        if (this.logger && this.logger.info) {
          this.logger.info('Logger fallback: Using pluginAPI.getLogger or console', { error: e.message });
        } else {
          console.warn('Logger fallback: Using console only. File logging unavailable.');
        }
      }

      if (this.logger && this.logger.info) {
        this.logger.info('Limitless Settings UI initializing', {
          userAgent: navigator.userAgent,
          url: window.location.href,
          timestamp: new Date().toISOString()
        });
      } else if (this.logger && this.logger.INFO) {
        this.logger.INFO('Limitless Settings UI initializing', {
          userAgent: navigator.userAgent,
          url: window.location.href,
          timestamp: new Date().toISOString()
        });
      } else {
        console.log('Limitless Settings UI initializing', {
          userAgent: navigator.userAgent,
          url: window.location.href,
          timestamp: new Date().toISOString()
        });
      }

      // Load current settings
      await this.loadSettings();

      // Set up event listeners
      this.setupEventListeners();

      // Update UI with current settings
      this.updateUI();

      if (this.logger) {
        this.logger.INFO('Limitless Settings UI initialized successfully', {
          settingsCount: Object.keys(this.currentSettings).length,
          hasApiKey: !!this.currentSettings.apiKey
        });
      } else {
        console.log('Limitless Settings UI initialized successfully');
      }

    } catch (error) {
      if (this.logger) {
        this.logger.ERROR('Failed to initialize settings UI', {
          error: error.message,
          stack: error.stack
        });
      } else {
        console.error('Failed to initialize settings UI:', error);
      }
      this.showError('Failed to initialize settings interface.');
    }
  }

  /**
   * Sets up all event listeners for UI elements
   */
  setupEventListeners() {
    // API Key validation
    const validateBtn = document.getElementById('validateBtn');
    if (validateBtn) {
      validateBtn.addEventListener('click', () => this.validateAPIKey());
    }

    // Show/Hide API key
    const showKeyBtn = document.getElementById('showKeyBtn');
    if (showKeyBtn) {
      showKeyBtn.addEventListener('click', () => this.toggleAPIKeyVisibility());
    }

    // Auto-sync checkbox
    const autoSyncCheckbox = document.getElementById('autoSync');
    if (autoSyncCheckbox) {
      autoSyncCheckbox.addEventListener('change', () => this.handleAutoSyncChange());
    }

    // Sync interval range
    const syncIntervalRange = document.getElementById('syncInterval');
    if (syncIntervalRange) {
      syncIntervalRange.addEventListener('input', () => this.handleSyncIntervalChange());
    }

    // Action buttons
    const syncNowBtn = document.getElementById('syncNowBtn');
    if (syncNowBtn) {
      syncNowBtn.addEventListener('click', () => this.performManualSync());
    }

    const testConnectionBtn = document.getElementById('testConnectionBtn');
    if (testConnectionBtn) {
      testConnectionBtn.addEventListener('click', () => this.testConnection());
    }

    const saveBtn = document.getElementById('saveBtn');
    if (saveBtn) {
      saveBtn.addEventListener('click', () => this.saveSettings());
    }

    const resetBtn = document.getElementById('resetBtn');
    if (resetBtn) {
      resetBtn.addEventListener('click', () => this.resetSettings());
    }

    // API Key input validation
    const apiKeyInput = document.getElementById('apiKey');
    if (apiKeyInput) {
      apiKeyInput.addEventListener('input', () => this.handleAPIKeyInput());
      apiKeyInput.addEventListener('paste', () => {
        // Clear validation status when pasting
        setTimeout(() => this.clearValidationStatus(), 100);
      });
    }
  }

  /**
   * Loads current settings from plugin storage
   *
   * @returns {Promise<void>}
   */
  async loadSettings() {
    try {
      this.currentSettings = this.pluginAPI.storage.loadData() || {};

      // Set defaults for missing settings
      this.currentSettings = {
        apiKey: '',
        syncInterval: 6,
        autoSync: true,
        maxRecords: 1000,
        timezone: 'UTC',
        enableDebugLogging: false,
        aiEnhancement: true,
        ...this.currentSettings
      };

      if (this.logger) {
        this.logger.INFO('Settings loaded successfully', {
          settingsCount: Object.keys(this.currentSettings).length,
          hasApiKey: !!this.currentSettings.apiKey,
          syncInterval: this.currentSettings.syncInterval,
          autoSync: this.currentSettings.autoSync
        });
      }

    } catch (error) {
      if (this.logger) {
        this.logger.ERROR('Failed to load settings', {
          error: error.message,
          stack: error.stack
        });
      } else {
        console.error('Failed to load settings:', error);
      }
      this.showError('Failed to load current settings.');
    }
  }

  /**
   * Updates the UI with current settings values
   */
  updateUI() {
    try {
      // API Key (show masked)
      const apiKeyInput = document.getElementById('apiKey');
      if (apiKeyInput && this.currentSettings.apiKey) {
        apiKeyInput.value = this.currentSettings.apiKey;
      }

      // Sync interval
      const syncIntervalInput = document.getElementById('syncInterval');
      const syncIntervalValue = document.getElementById('syncIntervalValue');
      if (syncIntervalInput) {
        syncIntervalInput.value = this.currentSettings.syncInterval;
        if (syncIntervalValue) {
          const hours = this.currentSettings.syncInterval;
          syncIntervalValue.textContent = `${hours} hour${hours !== 1 ? 's' : ''}`;
        }
      }

      // Auto sync checkbox
      const autoSyncCheckbox = document.getElementById('autoSync');
      if (autoSyncCheckbox) {
        autoSyncCheckbox.checked = this.currentSettings.autoSync;
      }

      // Max records
      const maxRecordsInput = document.getElementById('maxRecords');
      if (maxRecordsInput) {
        maxRecordsInput.value = this.currentSettings.maxRecords;
      }

      // Timezone
      const timezoneSelect = document.getElementById('timezone');
      if (timezoneSelect) {
        timezoneSelect.value = this.currentSettings.timezone;
      }

      // Debug logging
      const debugLoggingCheckbox = document.getElementById('enableDebugLogging');
      if (debugLoggingCheckbox) {
        debugLoggingCheckbox.checked = this.currentSettings.enableDebugLogging;
      }

      // AI enhancement
      const aiEnhancementCheckbox = document.getElementById('aiEnhancement');
      if (aiEnhancementCheckbox) {
        aiEnhancementCheckbox.checked = this.currentSettings.aiEnhancement;
      }

      // Update status indicators
      this.updateStatusIndicators();

    } catch (error) {
      console.error('Failed to update UI:', error);
    }
  }

  /**
   * Updates status indicators in the UI
   */
  updateStatusIndicators() {
    // API Status
    const apiStatus = document.getElementById('apiStatus');
    if (apiStatus) {
      const hasApiKey = !!this.currentSettings.apiKey;
      const statusDot = apiStatus.querySelector('.status-dot');
      const statusText = apiStatus.querySelector('.status-text');

      if (hasApiKey) {
        statusDot.className = 'status-dot connected';
        statusText.textContent = 'Connected';
      } else {
        statusDot.className = 'status-dot unknown';
        statusText.textContent = 'Not configured';
      }
    }

    // Auto Sync Status
    const autoSyncStatus = document.getElementById('autoSyncStatus');
    if (autoSyncStatus) {
      const statusDot = autoSyncStatus.querySelector('.status-dot');
      const statusText = autoSyncStatus.querySelector('.status-text');

      if (this.currentSettings.autoSync) {
        statusDot.className = 'status-dot enabled';
        statusText.textContent = 'Enabled';
      } else {
        statusDot.className = 'status-dot disabled';
        statusText.textContent = 'Disabled';
      }
    }
  }

  /**
   * Handles API key input changes
   */
  handleAPIKeyInput() {
    const apiKeyInput = document.getElementById('apiKey');
    if (apiKeyInput) {
      const apiKey = apiKeyInput.value.trim();

      // Clear previous validation status
      this.clearValidationStatus();

      // Enable/disable validate button
      const validateBtn = document.getElementById('validateBtn');
      if (validateBtn) {
        validateBtn.disabled = !apiKey || this.validationInProgress;
      }
    }
  }

  /**
   * Validates the API key with Limitless AI
   *
   * @returns {Promise<void>}
   */
  async validateAPIKey() {
    const apiKeyInput = document.getElementById('apiKey');
    const validateBtn = document.getElementById('validateBtn');
    const validationStatus = document.getElementById('validationStatus');

    if (!apiKeyInput || this.validationInProgress) {
      return;
    }

    const apiKey = apiKeyInput.value.trim();
    if (!apiKey) {
      this.showValidationError('Please enter an API key');
      return;
    }

    try {
      this.validationInProgress = true;

      // Update UI to show validation in progress
      if (validateBtn) {
        validateBtn.disabled = true;
        validateBtn.textContent = 'Validating...';
      }

      if (validationStatus) {
        validationStatus.innerHTML = '<span class="validating">🔄 Validating API key...</span>';
      }

      // Create API client instance for validation
      // LimitlessAPI should be available from the included script
      if (typeof LimitlessAPI === 'undefined') {
        throw new Error('Limitless API client not available');
      }

      // Create a logger adapter that matches the expected interface
      const loggerAdapter = this.createLoggerAdapter();
      const limitlessAPI = new LimitlessAPI(this.pluginAPI, loggerAdapter);
      const result = await limitlessAPI.validateAPIKey(apiKey);

      if (result.success) {
        this.showValidationSuccess('API key is valid and ready to use!');

        // Update settings with validated key
        this.currentSettings.apiKey = apiKey;
        this.updateStatusIndicators();

      } else {
        const errorMessage = result.error || 'API key validation failed';
        this.showValidationError(errorMessage);
      }

    } catch (error) {
      console.error('API key validation error:', error);
      this.showValidationError('Failed to validate API key. Please check your connection.');

    } finally {
      this.validationInProgress = false;

      // Reset validate button
      if (validateBtn) {
        validateBtn.disabled = false;
        validateBtn.textContent = 'Validate';
      }
    }
  }

  /**
   * Shows validation success message
   *
   * @param {string} message - Success message to display
   */
  showValidationSuccess(message) {
    const validationStatus = document.getElementById('validationStatus');
    if (validationStatus) {
      validationStatus.innerHTML = `<span class="validation-success">✅ ${message}</span>`;
    }
  }

  /**
   * Shows validation error message
   *
   * @param {string} message - Error message to display
   */
  showValidationError(message) {
    console.log('Limitless Settings: Showing validation error:', message);

    const validationStatus = document.getElementById('validationStatus');
    if (validationStatus) {
      validationStatus.innerHTML = `<span class="validation-error">❌ ${message}</span>`;
      validationStatus.style.display = 'block';
      validationStatus.style.backgroundColor = '#fee';
      validationStatus.style.border = '1px solid #f00';
      validationStatus.style.padding = '10px';
      validationStatus.style.borderRadius = '4px';
      validationStatus.style.marginTop = '10px';
      console.log('Limitless Settings: Updated inline validation status');
    }

    // Also show as modal for better visibility - BUG FIX
    console.log('Limitless Settings: Displaying error modal for validation failure');
    this.showErrorModal('API Key Validation Failed',
      `${message}\n\nThe API key was not saved. Please correct the key and try again.`);
  }

  /**
   * Clears validation status display
   */
  clearValidationStatus() {
    const validationStatus = document.getElementById('validationStatus');
    if (validationStatus) {
      validationStatus.innerHTML = '';
    }
  }

  /**
   * Toggles API key visibility
   */
  toggleAPIKeyVisibility() {
    const apiKeyInput = document.getElementById('apiKey');
    const showKeyBtn = document.getElementById('showKeyBtn');

    if (apiKeyInput && showKeyBtn) {
      const isPassword = apiKeyInput.type === 'password';
      apiKeyInput.type = isPassword ? 'text' : 'password';
      showKeyBtn.textContent = isPassword ? '🙈' : '👁️';
      showKeyBtn.title = isPassword ? 'Hide API Key' : 'Show API Key';
    }
  }

  /**
   * Handles auto-sync checkbox change
   */
  handleAutoSyncChange() {
    const autoSyncCheckbox = document.getElementById('autoSync');
    if (autoSyncCheckbox) {
      this.currentSettings.autoSync = autoSyncCheckbox.checked;
      this.updateStatusIndicators();
    }
  }

  /**
   * Handles sync interval range change
   */
  handleSyncIntervalChange() {
    const syncIntervalInput = document.getElementById('syncInterval');
    const syncIntervalValue = document.getElementById('syncIntervalValue');

    if (syncIntervalInput) {
      const hours = parseInt(syncIntervalInput.value);
      this.currentSettings.syncInterval = hours;

      if (syncIntervalValue) {
        syncIntervalValue.textContent = `${hours} hour${hours !== 1 ? 's' : ''}`;
      }
    }
  }

  /**
   * Performs manual sync
   *
   * @returns {Promise<void>}
   */
  async performManualSync() {
    const syncNowBtn = document.getElementById('syncNowBtn');
    const syncProgress = document.getElementById('syncProgress');
    const progressText = document.getElementById('progressText');

    try {
      // Validate API key exists
      if (!this.currentSettings.apiKey) {
        this.showError('Please configure and validate your API key first.');
        return;
      }

      // Update UI for sync in progress
      if (syncNowBtn) {
        syncNowBtn.disabled = true;
        syncNowBtn.innerHTML = '<span class="btn-icon">⏳</span> Syncing...';
      }

      if (syncProgress) {
        syncProgress.style.display = 'block';
      }

      if (progressText) {
        progressText.textContent = 'Starting synchronization...';
      }

      // Call plugin's sync functionality
      if (this.pluginAPI.commands) {
        await this.pluginAPI.commands.execute('limitless-sync-now');

        if (progressText) {
          progressText.textContent = 'Synchronization completed successfully!';
        }

        // Update last sync time
        this.updateLastSyncTime();

      } else {
        throw new Error('Plugin commands not available');
      }

    } catch (error) {
      console.error('Manual sync failed:', error);
      this.showError('Synchronization failed. Please check your API key and connection.');

      if (progressText) {
        progressText.textContent = 'Synchronization failed.';
      }

    } finally {
      // Reset UI
      if (syncNowBtn) {
        syncNowBtn.disabled = false;
        syncNowBtn.innerHTML = '<span class="btn-icon">🔄</span> Sync Now';
      }

      // Hide progress after delay
      setTimeout(() => {
        if (syncProgress) {
          syncProgress.style.display = 'none';
        }
      }, 3000);
    }
  }

  /**
   * Tests connection to Limitless API
   *
   * @returns {Promise<void>}
   */
  async testConnection() {
    const testConnectionBtn = document.getElementById('testConnectionBtn');

    try {
      if (!this.currentSettings.apiKey) {
        this.showError('Please configure your API key first.');
        return;
      }

      if (testConnectionBtn) {
        testConnectionBtn.disabled = true;
        testConnectionBtn.innerHTML = '<span class="btn-icon">⏳</span> Testing...';
      }

      // Perform API key validation as connection test
      await this.validateAPIKey();

    } catch (error) {
      console.error('Connection test failed:', error);
      this.showError('Connection test failed.');

    } finally {
      if (testConnectionBtn) {
        testConnectionBtn.disabled = false;
        testConnectionBtn.innerHTML = '<span class="btn-icon">🔍</span> Test Connection';
      }
    }
  }

  /**
   * Saves current settings
   *
   * @returns {Promise<void>}
   */
  async saveSettings() {
    const saveBtn = document.getElementById('saveBtn');
    const sessionId = `save_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    try {
      if (this.logger) {
        this.logger.INFO('Starting save process', { sessionId });
      } else {
        console.log('Limitless Settings: Starting save process');
      }

      // Collect all form values
      const formData = this.collectFormData();

      if (this.logger) {
        this.logger.INFO('Form data collected', {
          sessionId,
          hasApiKey: !!formData.apiKey,
          apiKeyLength: formData.apiKey?.length || 0,
          syncInterval: formData.syncInterval,
          autoSync: formData.autoSync
        });
      } else {
        console.log('Limitless Settings: Form data collected', {
          hasApiKey: !!formData.apiKey,
          apiKeyLength: formData.apiKey?.length || 0,
          syncInterval: formData.syncInterval,
          autoSync: formData.autoSync
        });
      }

      // Validate required fields
      if (!formData.apiKey) {
        this.showError('API key is required.');
        return;
      }

      if (saveBtn) {
        saveBtn.disabled = true;
        saveBtn.textContent = 'Validating & Saving...';
      }

      // Clear any previous validation status
      this.clearValidationStatus();

      // First validate the API key with Limitless API
      if (this.logger) {
        this.logger.INFO('Starting API key validation', { sessionId });
      } else {
        console.log('Limitless Settings: Starting API key validation');
      }

      const validationStatus = document.getElementById('validationStatus');
      if (validationStatus) {
        validationStatus.innerHTML = '<span class="validating">🔄 Validating API key before saving...</span>';
      }

      // Create API client instance for validation
      // LimitlessAPI should be available from the included script
      if (typeof LimitlessAPI === 'undefined') {
        const errorMsg = 'LimitlessAPI class not available - settings validation will be skipped';
        if (this.logger) {
          this.logger.ERROR(errorMsg, { sessionId });
        } else {
          console.error('Limitless Settings:', errorMsg);
        }
        this.showValidationError('API validation unavailable. Settings will be saved without validation.');

        // Still save the settings but warn the user
        const success = this.pluginAPI.storage.saveData(formData);
        if (success) {
          this.currentSettings = formData;
          this.showSuccess('Settings saved (validation skipped - API client unavailable)');
          this.updateStatusIndicators();
        } else {
          throw new Error('Failed to save settings to storage');
        }
        return;
      }

      if (this.logger) {
        this.logger.DEBUG('Creating LimitlessAPI instance', { sessionId });
      } else {
        console.log('Limitless Settings: Creating LimitlessAPI instance');
      }

      let limitlessAPI;
      try {
        // Create a logger adapter that matches the expected interface
        const loggerAdapter = this.createLoggerAdapter();
        limitlessAPI = new LimitlessAPI(this.pluginAPI, loggerAdapter);
      } catch (apiError) {
        const errorMsg = `Failed to create LimitlessAPI instance: ${apiError.message}`;
        if (this.logger) {
          this.logger.ERROR(errorMsg, { sessionId, error: apiError });
        } else {
          console.error('Limitless Settings:', errorMsg, apiError);
        }
        this.showValidationError('API client initialization failed. Please try again.');
        return;
      }

      if (this.logger) {
        this.logger.DEBUG('Calling validateAPIKey', { sessionId });
      } else {
        console.log('Limitless Settings: Calling validateAPIKey');
      }

      let validationResult;
      try {
        validationResult = await limitlessAPI.validateAPIKey(formData.apiKey);
      } catch (validationError) {
        const errorMsg = `API validation threw an error: ${validationError.message}`;
        if (this.logger) {
          this.logger.ERROR(errorMsg, { sessionId, error: validationError });
        } else {
          console.error('Limitless Settings:', errorMsg, validationError);
        }
        this.showValidationError('API validation failed due to an unexpected error. Please check your connection and try again.');
        return;
      }

      if (this.logger) {
        this.logger.INFO('API validation result', {
          sessionId,
          success: validationResult.success,
          hasError: !!validationResult.error
        });
      } else {
        console.log('Limitless Settings: API validation result', {
          success: validationResult.success,
          hasError: !!validationResult.error
        });
      }

      if (!validationResult.success) {
        // API key validation failed - show error and don't save
        const errorMessage = validationResult.error || 'API key validation failed';

        if (this.logger) {
          this.logger.WARN('API key validation failed, settings NOT saved', {
            sessionId,
            error: errorMessage,
            apiKeyLength: formData.apiKey?.length || 0
          });
        } else {
          console.warn('Limitless Settings: API key validation failed, settings NOT saved', {
            error: errorMessage,
            apiKeyLength: formData.apiKey?.length || 0
          });
        }

        this.showValidationError(errorMessage);
        return;
      }

      // API key is valid, clear validation status and proceed with save
      this.showValidationSuccess('API key validated successfully!');

      if (saveBtn) {
        saveBtn.textContent = 'Saving...';
      }

      // Save to plugin storage
      const success = this.pluginAPI.storage.saveData(formData);

      if (success) {
        this.currentSettings = formData;
        this.showSuccess('Settings saved successfully!');
        this.updateStatusIndicators();

        if (this.logger) {
          this.logger.INFO('Settings saved successfully', {
            sessionId,
            settingsCount: Object.keys(formData).length,
            hasApiKey: !!formData.apiKey,
            syncInterval: formData.syncInterval
          });
        }

        // Clear validation status after successful save
        setTimeout(() => {
          this.clearValidationStatus();
        }, 3000);
      } else {
        throw new Error('Failed to save settings to storage');
      }

    } catch (error) {
      if (this.logger) {
        this.logger.ERROR('Failed to save settings', {
          sessionId,
          error: error.message,
          stack: error.stack
        });
      } else {
        console.error('Failed to save settings:', error);
      }
      this.showError('Failed to save settings. Please try again.');

      // Clear validation status on error
      this.clearValidationStatus();

    } finally {
      if (saveBtn) {
        saveBtn.disabled = false;
        saveBtn.textContent = 'Save Settings';
      }
    }
  }

  /**
   * Collects all form data
   *
   * @returns {Object} Form data object
   */
  collectFormData() {
    const apiKeyInput = document.getElementById('apiKey');
    const syncIntervalInput = document.getElementById('syncInterval');
    const autoSyncCheckbox = document.getElementById('autoSync');
    const maxRecordsInput = document.getElementById('maxRecords');
    const timezoneSelect = document.getElementById('timezone');
    const debugLoggingCheckbox = document.getElementById('enableDebugLogging');
    const aiEnhancementCheckbox = document.getElementById('aiEnhancement');

    return {
      apiKey: apiKeyInput?.value.trim() || '',
      syncInterval: parseInt(syncIntervalInput?.value) || 6,
      autoSync: autoSyncCheckbox?.checked || false,
      maxRecords: parseInt(maxRecordsInput?.value) || 1000,
      timezone: timezoneSelect?.value || 'UTC',
      enableDebugLogging: debugLoggingCheckbox?.checked || false,
      aiEnhancement: aiEnhancementCheckbox?.checked || true
    };
  }

  /**
   * Resets settings to defaults
   *
   * @returns {Promise<void>}
   */
  async resetSettings() {
    const resetBtn = document.getElementById('resetBtn');

    try {
      // Confirm reset action
      const confirmed = confirm('Are you sure you want to reset all settings to defaults? This action cannot be undone.');
      if (!confirmed) {
        return;
      }

      if (resetBtn) {
        resetBtn.disabled = true;
        resetBtn.textContent = 'Resetting...';
      }

      // Reset to default settings
      this.currentSettings = {
        apiKey: '',
        syncInterval: 6,
        autoSync: true,
        maxRecords: 1000,
        timezone: 'UTC',
        enableDebugLogging: false,
        aiEnhancement: true
      };

      // Save defaults and update UI
      const success = this.pluginAPI.storage.saveData(this.currentSettings);

      if (success) {
        this.updateUI();
        this.clearValidationStatus();
        this.showSuccess('Settings reset to defaults successfully!');
      } else {
        throw new Error('Failed to reset settings');
      }

    } catch (error) {
      console.error('Failed to reset settings:', error);
      this.showError('Failed to reset settings. Please try again.');

    } finally {
      if (resetBtn) {
        resetBtn.disabled = false;
        resetBtn.textContent = 'Reset to Defaults';
      }
    }
  }

  /**
   * Updates the last sync time display
   */
  updateLastSyncTime() {
    const lastSyncInfo = document.getElementById('lastSyncInfo');
    if (lastSyncInfo) {
      const now = new Date();
      const statusText = lastSyncInfo.querySelector('.status-text');
      if (statusText) {
        statusText.textContent = now.toLocaleString();
      }
    }
  }

  /**
   * Shows success message
   *
   * @param {string} message - Success message to display
   */
  showSuccess(message) {
    // You could implement a toast notification or status bar here
    console.log('Success:', message);
    alert(message); // Temporary implementation
  }

  /**
   * Creates a logger adapter that matches the LimitlessAPI expected interface
   *
   * @returns {Object} Logger adapter with async methods
   */
  createLoggerAdapter() {
    return {
      startTimer: (name) => ({
        stop: async () => {
          // Return a mock duration for timing
          return 100;
        }
      }),
      info: async (message, data) => {
        if (this.logger) {
          this.logger.INFO(message, data);
        } else {
          console.log(`[INFO] ${message}`, data || '');
        }
      },
      warn: async (message, data) => {
        if (this.logger) {
          this.logger.WARN(message, data);
        } else {
          console.warn(`[WARN] ${message}`, data || '');
        }
      },
      error: async (message, error) => {
        if (this.logger) {
          this.logger.ERROR(message, error);
        } else {
          console.error(`[ERROR] ${message}`, error || '');
        }
      },
      logApiCallStart: async (url, options) => {
        const callId = `call_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        if (this.logger) {
          this.logger.DEBUG('API call started', { callId, url, options });
        } else {
          console.log(`[API] Starting call ${callId} to: ${url}`, options);
        }
        return callId;
      },
      logApiCallEnd: async (callId, response, duration) => {
        if (this.logger) {
          this.logger.DEBUG('API call completed', {
            callId,
            status: response.status,
            statusText: response.statusText,
            duration
          });
        } else {
          console.log(`[API] Call ${callId} completed in ${duration}ms - Status: ${response.status}`);
        }
      }
    };
  }

  /**
   * Shows error modal with dismiss capability
   *
   * @param {string} title - Modal title
   * @param {string} message - Error message to display
   */
  showErrorModal(title, message) {
    // Create modal overlay
    const overlay = document.createElement('div');
    overlay.className = 'error-modal-overlay';
    overlay.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 10000;
    `;

    // Create modal content
    const modal = document.createElement('div');
    modal.className = 'error-modal';
    modal.style.cssText = `
      background: white;
      border-radius: 8px;
      padding: 24px;
      max-width: 500px;
      width: 90%;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
      position: relative;
    `;

    modal.innerHTML = `
      <div style="display: flex; align-items: flex-start; gap: 16px; margin-bottom: 20px;">
        <div style="color: #dc2626; font-size: 24px; flex-shrink: 0;">⚠️</div>
        <div style="flex: 1;">
          <h3 style="margin: 0 0 8px 0; color: #1f2937; font-size: 18px; font-weight: 600;">${title}</h3>
          <p style="margin: 0; color: #6b7280; line-height: 1.5;">${message}</p>
        </div>
      </div>
      <div style="display: flex; justify-content: flex-end; gap: 12px;">
        <button class="dismiss-btn" style="
          background: #dc2626;
          color: white;
          border: none;
          padding: 8px 16px;
          border-radius: 6px;
          font-weight: 500;
          cursor: pointer;
          font-size: 14px;
        ">Dismiss</button>
      </div>
    `;

    // Add dismiss functionality
    const dismissBtn = modal.querySelector('.dismiss-btn');
    const dismiss = () => {
      document.body.removeChild(overlay);
    };

    dismissBtn.addEventListener('click', dismiss);
    overlay.addEventListener('click', (e) => {
      if (e.target === overlay) dismiss();
    });

    // Add keyboard support
    const handleKeydown = (e) => {
      if (e.key === 'Escape') {
        dismiss();
        document.removeEventListener('keydown', handleKeydown);
      }
    };
    document.addEventListener('keydown', handleKeydown);

    overlay.appendChild(modal);
    document.body.appendChild(overlay);

    // Focus the dismiss button
    dismissBtn.focus();
  }

  /**
   * Shows error message
   *
   * @param {string} message - Error message to display
   */
  showError(message) {
    this.showErrorModal('Error', message);
  }
}

// Initialize settings UI when script loads
new LimitlessSettingsUI();
ntin
import { storeAPIKey } from '../../core/pluginAPI/secureStorage';

// In saveSettings function:
await storeAPIKey('limitless', formData.apiKey);
