/**
 * Limitless API Client
 *
 * Handles all API communication with Limitless AI services including:
 * - API key validation
 * - Lifelog fetching with pagination
 * - Individual lifelog detail retrieval
 * - Incremental data synchronization
 * - Rate limiting and error handling
 *
 * @class LimitlessAPI
 */

class LimitlessAPI {
  /**
   * Creates a new LimitlessAPI instance
   *
   * @param {Object} api - The Plugin API instance
   * @param {Object} logger - The Logger instance
   */
  constructor(api, logger) {
    this.api = api;
    this.logger = logger;
    this.baseURL = 'https://api.limitless.ai';
    this.version = 'v1';
    this.rateLimitDelay = 1000; // 1 second between requests
    this.maxRetries = 3;
    this.retryDelay = 2000; // 2 seconds initial retry delay
  }

  /**
   * Validates an API key by making a test request
   *
   * @param {string} apiKey - The API key to validate
   * @returns {Promise<Object>} Validation result with success flag and details
   */
  async validateAPIKey(apiKey) {
    const timer = this.logger.startTimer('API key validation');

    try {
      await this.logger.info('Starting API key validation');

      if (!apiKey || typeof apiKey !== 'string' || apiKey.trim().length === 0) {
        await this.logger.warn('Invalid API key format provided');
        return {
          success: false,
          error: 'API key is required and must be a non-empty string'
        };
      }

      // Test the API key with a minimal request
      const endpoint = `${this.baseURL}/${this.version}/lifelogs`;
      const callId = await this.logger.logApiCallStart(endpoint, {
        method: 'GET',
        purpose: 'API key validation'
      });

      const response = await this.makeRequest(endpoint, {
        method: 'GET',
        headers: {
          'X-API-Key': apiKey,
          'Content-Type': 'application/json'
        },
        // Limit to 1 record for validation
        searchParams: { limit: 1 }
      });

      const duration = await timer.stop();
      await this.logger.logApiCallEnd(callId, response, duration);

      if (response.ok) {
        await this.logger.info('API key validation successful');
        return {
          success: true,
          data: await response.json()
        };
      } else {
        const errorText = await response.text();
        await this.logger.warn('API key validation failed', {
          status: response.status,
          statusText: response.statusText,
          error: errorText
        });

        return {
          success: false,
          error: `API validation failed: ${response.status} ${response.statusText}`,
          details: errorText
        };
      }

    } catch (error) {
      await timer.stop();
      await this.logger.error('API key validation error', error);
      return {
        success: false,
        error: `Network error during validation: ${error.message}`
      };
    }
  }

  /**
   * Fetches lifelogs with pagination support
   *
   * @param {Object} options - Fetch options
   * @param {string} options.apiKey - The API key
   * @param {string} options.cursor - Pagination cursor
   * @param {number} options.limit - Number of records to fetch
   * @param {string} options.timezone - Timezone for date operations
   * @param {string} options.startDate - Start date filter (YYYY-MM-DD)
   * @param {string} options.endDate - End date filter (YYYY-MM-DD)
   * @param {boolean} options.includeMarkdown - Include markdown content
   * @param {boolean} options.includeHeadings - Include headings
   * @returns {Promise<Object>} Fetch result with lifelogs and pagination info
   */
  async fetchLifelogs(options = {}) {
    const {
      apiKey,
      cursor,
      limit = 10,
      timezone = 'UTC',
      startDate,
      endDate,
      includeMarkdown = true,
      includeHeadings = true
    } = options;

    const timer = this.logger.startTimer('Fetch lifelogs');

    try {
      await this.logger.info('Starting lifelog fetch', {
        cursor: cursor ? '[PRESENT]' : '[NONE]',
        limit,
        timezone,
        startDate,
        endDate,
        includeMarkdown,
        includeHeadings
      });

      if (!apiKey) {
        throw new Error('API key is required for fetching lifelogs');
      }

      // Build query parameters
      const searchParams = new URLSearchParams({
        limit: limit.toString(),
        includeMarkdown: includeMarkdown.toString(),
        includeHeadings: includeHeadings.toString()
      });

      if (cursor) {
        searchParams.append('cursor', cursor);
      }

      if (timezone) {
        searchParams.append('timezone', timezone);
      }

      if (startDate) {
        searchParams.append('start', startDate);
      }

      if (endDate) {
        searchParams.append('end', endDate);
      }

      const endpoint = `${this.baseURL}/${this.version}/lifelogs`;
      const url = `${endpoint}?${searchParams.toString()}`;

      const callId = await this.logger.logApiCallStart(url, {
        method: 'GET',
        purpose: 'Fetch lifelogs'
      });

      const response = await this.makeRequest(url, {
        method: 'GET',
        headers: {
          'X-API-Key': apiKey,
          'Content-Type': 'application/json'
        }
      });

      const duration = await timer.stop();
      await this.logger.logApiCallEnd(callId, response, duration);

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to fetch lifelogs: ${response.status} ${response.statusText} - ${errorText}`);
      }

      const data = await response.json();

      await this.logger.info('Lifelog fetch completed', {
        recordCount: data.data?.lifelogs?.length || 0,
        hasNextCursor: !!data.meta?.lifelogs?.nextCursor,
        totalCount: data.meta?.lifelogs?.count || 0
      });

      return {
        success: true,
        data: data.data.lifelogs || [],
        meta: data.meta?.lifelogs || {},
        nextCursor: data.meta?.lifelogs?.nextCursor || null
      };

    } catch (error) {
      await timer.stop();
      await this.logger.error('Lifelog fetch error', error, { options });
      return {
        success: false,
        error: error.message,
        data: [],
        meta: {},
        nextCursor: null
      };
    }
  }

  /**
   * Fetches detailed information for a specific lifelog
   *
   * @param {string} lifelogId - The lifelog ID
   * @param {string} apiKey - The API key
   * @param {Object} options - Additional options
   * @returns {Promise<Object>} Lifelog details
   */
  async fetchLifelogDetails(lifelogId, apiKey, options = {}) {
    const {
      includeMarkdown = true,
      includeHeadings = true
    } = options;

    const timer = this.logger.startTimer('Fetch lifelog details');

    try {
      await this.logger.info('Starting lifelog detail fetch', {
        lifelogId,
        includeMarkdown,
        includeHeadings
      });

      if (!lifelogId || !apiKey) {
        throw new Error('Lifelog ID and API key are required');
      }

      // Build query parameters
      const searchParams = new URLSearchParams({
        includeMarkdown: includeMarkdown.toString(),
        includeHeadings: includeHeadings.toString()
      });

      const endpoint = `${this.baseURL}/${this.version}/lifelogs/${lifelogId}`;
      const url = `${endpoint}?${searchParams.toString()}`;

      const callId = await this.logger.logApiCallStart(url, {
        method: 'GET',
        purpose: 'Fetch lifelog details'
      });

      const response = await this.makeRequest(url, {
        method: 'GET',
        headers: {
          'X-API-Key': apiKey,
          'Content-Type': 'application/json'
        }
      });

      const duration = await timer.stop();
      await this.logger.logApiCallEnd(callId, response, duration);

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to fetch lifelog details: ${response.status} ${response.statusText} - ${errorText}`);
      }

      const data = await response.json();

      await this.logger.info('Lifelog detail fetch completed', {
        lifelogId,
        hasContent: !!data.data?.lifelog?.contents
      });

      return {
        success: true,
        data: data.data.lifelog
      };

    } catch (error) {
      await timer.stop();
      await this.logger.error('Lifelog detail fetch error', error, { lifelogId });
      return {
        success: false,
        error: error.message,
        data: null
      };
    }
  }

  /**
   * Synchronizes data incrementally since the last sync time
   *
   * @param {string} apiKey - The API key
   * @param {string|null} lastSyncTime - ISO timestamp of last sync
   * @param {Object} options - Sync options
   * @returns {Promise<Object>} Sync result with fetched data
   */
  async syncData(apiKey, lastSyncTime = null, options = {}) {
    const {
      timezone = 'UTC',
      batchSize = 10,
      maxRecords = 1000
    } = options;

    const timer = this.logger.startTimer('Data sync');
    const allLifelogs = [];
    let cursor = null;
    let totalFetched = 0;

    try {
      await this.logger.logSync('start', {
        lastSyncTime,
        timezone,
        batchSize,
        maxRecords
      });

      if (!apiKey) {
        throw new Error('API key is required for data sync');
      }

      // Fetch data in batches
      do {
        const fetchOptions = {
          apiKey,
          cursor,
          limit: Math.min(batchSize, maxRecords - totalFetched), // Don't fetch more than needed
          timezone
        };

        // If we have a last sync time, use it as start date
        if (lastSyncTime) {
          fetchOptions.startDate = lastSyncTime;
        }

        const result = await this.fetchLifelogs(fetchOptions);

        if (!result.success) {
          throw new Error(`Sync batch failed: ${result.error}`);
        }

        // Add records but respect max limit
        const recordsToAdd = result.data.slice(0, maxRecords - totalFetched);
        allLifelogs.push(...recordsToAdd);
        totalFetched += recordsToAdd.length;
        cursor = result.nextCursor;

        await this.logger.logSync('progress', {
          batchSize: recordsToAdd.length,
          totalFetched,
          hasMoreData: !!cursor
        });

        // Check if we hit the limit
        if (totalFetched >= maxRecords) {
          break;
        }

        // Rate limiting
        if (cursor) {
          await this.sleep(this.rateLimitDelay);
        }

      } while (cursor);

      const duration = await timer.stop();

      await this.logger.logSync('complete', {
        totalFetched,
        duration: `${duration}ms`,
        truncated: totalFetched >= maxRecords
      });

      return {
        success: true,
        data: allLifelogs,
        totalFetched,
        truncated: totalFetched >= maxRecords,
        syncTime: new Date().toISOString()
      };

    } catch (error) {
      await timer.stop();
      await this.logger.logSync('error', { error: error.message });
      await this.logger.error('Data sync error', error);

      return {
        success: false,
        error: error.message,
        data: allLifelogs, // Return partial data if any was fetched
        totalFetched,
        syncTime: new Date().toISOString()
      };
    }
  }

  /**
   * Makes an HTTP request with retry logic and error handling
   *
   * @param {string} url - The request URL
   * @param {Object} options - Request options
   * @returns {Promise<Response>} The fetch response
   */
  async makeRequest(url, options = {}) {
    const { retries = 0 } = options;

    try {
      // Use the plugin's network fetch if available
      let fetchFunction = this.api.network?.fetch;

      if (!fetchFunction) {
        // Try to use global fetch if available
        if (typeof fetch !== 'undefined') {
          fetchFunction = fetch;
        } else {
          throw new Error('Network access not available - missing fetch function');
        }
      }

      const response = await fetchFunction(url, options);
      return response;

    } catch (error) {
      // Retry logic for network errors
      if (retries < this.maxRetries && this.shouldRetry(error)) {
        await this.logger.warn('Request failed, retrying', {
          url: this.sanitizeUrl(url),
          attempt: retries + 1,
          maxRetries: this.maxRetries,
          error: error.message
        });

        await this.sleep(this.retryDelay * Math.pow(2, retries)); // Exponential backoff
        return this.makeRequest(url, { ...options, retries: retries + 1 });
      }

      throw error;
    }
  }

  /**
   * Determines if a request should be retried based on the error
   *
   * @param {Error} error - The error that occurred
   * @returns {boolean} Whether to retry the request
   */
  shouldRetry(error) {
    // Retry on network errors, timeouts, and certain HTTP status codes
    const retryableMessages = [
      'network error',
      'timeout',
      'connection refused',
      'dns lookup failed'
    ];

    const errorMessage = error.message.toLowerCase();
    return retryableMessages.some(msg => errorMessage.includes(msg));
  }

  /**
   * Sanitizes a URL for logging (removes sensitive query parameters)
   *
   * @param {string} url - The URL to sanitize
   * @returns {string} Sanitized URL
   */
  sanitizeUrl(url) {
    try {
      const urlObj = new URL(url);
      // Remove any sensitive query parameters
      const sensitiveParams = ['key', 'token', 'secret'];
      sensitiveParams.forEach(param => {
        if (urlObj.searchParams.has(param)) {
          urlObj.searchParams.set(param, '[REDACTED]');
        }
      });
      return urlObj.toString();
    } catch {
      return '[INVALID_URL]';
    }
  }

  /**
   * Sleep utility for rate limiting and retries
   *
   * @param {number} ms - Milliseconds to sleep
   * @returns {Promise<void>}
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Gets API usage statistics
   *
   * @returns {Object} API usage statistics
   */
  getApiStats() {
    // This could be enhanced to track actual usage statistics
    return {
      baseURL: this.baseURL,
      version: this.version,
      rateLimitDelay: this.rateLimitDelay,
      maxRetries: this.maxRetries,
      retryDelay: this.retryDelay
    };
  }
}

module.exports = LimitlessAPI;
