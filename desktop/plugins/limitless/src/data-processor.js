/**
 * Data Processor
 *
 * Transforms Limitless data into Lifeboard-compatible format. Includes methods for processing lifelogs
 * into posts, generating AI content based on transformed data, and storing / retrieving data in the local format.
 * Handles the filtering, tagging, and organizing data before storing them in the local database.
 *
 * @class DataProcessor
 */

const fs = require('fs');
const path = require('path');

class DataProcessor {
  /**
   * Creates a new DataProcessor instance
   *
   * @param {Object} api - The Plugin API instance
   * @param {Object} logger - The Logger instance
   */
  constructor(api, logger) {
    this.api = api;
    this.logger = logger;
    this.storageDir = this.getStorageDirectory();
  }

  /**
   * Processes lifelogs into Lifeboard posts
   *
   * @param {Array} lifelogs - Array of lifelogs to be processed
   * @returns {Promise<Array>} Processed lifelogs in Lifeboard format
   */
  async processLifelogs(lifelogs) {
    try {
      await this.logger.logDataProcessing('start', {
        totalLifelogs: lifelogs.length
      });

      const posts = lifelogs.map((lifelog) => this.transformLifelogToPost(lifelog));

      await this.logger.logDataProcessing('complete', {
        postsCreated: posts.length
      });

      return posts;

    } catch (error) {
      await this.logger.error('Failed to process lifelogs', error, {
        totalLifelogs: lifelogs.length
      });
      return [];
    }
  }

  /**
   * Transforms a single lifelog to a Lifeboard post
   *
   * @param {Object} lifelog - The lifelog to transform
   * @returns {Object} The transformed Lifeboard post
   */
  transformLifelogToPost(lifelog) {
    return {
      id: `limitless-${lifelog.id}`,
      title: lifelog.title,
      content: lifelog.markdown,
      timestamp: lifelog.startTime,
      tags: ['limitless', 'lifelog'],
      metadata: {
        source: 'limitless',
        originalId: lifelog.id,
        duration: this.calculateDuration(lifelog.startTime, lifelog.endTime),
        speakers: this.extractSpeakers(lifelog.contents)
      }
    };
  }

  /**
   * Calculates the duration of a lifelog
   *
   * @param {string|Date} startTime - The start time of the lifelog
   * @param {string|Date} endTime - The end time of the lifelog
   * @returns {number} Duration in milliseconds
   */
  calculateDuration(startTime, endTime) {
    const start = new Date(startTime);
    const end = new Date(endTime);
    return end - start;
  }

  /**
   * Extracts speaker names from lifelog contents
   *
   * @param {Array<Object>} contents - The contents array of lifelog
   * @returns {Array<string>} Array of speaker names
   */
  extractSpeakers(contents) {
    const speakers = new Set();
    contents.forEach((content) => {
      if (content.speakerName) {
        speakers.add(content.speakerName);
      }
    });
    return Array.from(speakers);
  }

/*
   * Generates AI-enhanced posts from processed lifelog data - DISABLED
   * Future work will integrate real AI.
   *
   * @param {Array<Object>} processedData
   * @returns {Promise<Array<Object>>}
   *
  async generatePosts(processedData) {
    // AI integration is disabled for now
    return processedData;
  }*/

/*
   * Enhances a single post with AI-generated insights - DISABLED
   * Future work will integrate real AI.
   *
   * @param {Object} post
   * @returns {Promise<Object>} Post
   *
  async enhancePostWithAI(post) {
    return post;
  }*/

/*
   * Extracts key insights from lifelog content - DISABLED
   * Regex-based AI-style functionality commented out for future work.
   *
   * @param {string} content
   * @returns {Array<string>}
   *
  extractInsights(content) {
    return [];
  }*/

/*
   * Identifies themes in lifelog content - DISABLED
   * Regex-based AI-style functionality commented out for future work.
   *
   * @param {string} content
   * @returns {Array<string>}
   *
  identifyThemes(content) {
    return [];
  }*/

/*
   * Analyzes sentiment of lifelog content - DISABLED
   * Simple AI-style sentiment analysis commented out for future work.
   *
   * @param {string} content
   * @returns {string}
   *
  analyzeSentiment(content) {
    return 'neutral';
  }*/

/*
   * Generates a summary of long content - DISABLED
   * AI-style summarization commented out for future work.
   *
   * @param {string} content
   * @returns {string}
   *
  generateSummary(content) {
    return content.substring(0, 200) + '...'; // Simple truncation instead
  }*/

/*
   * Generates AI tags based on content analysis - DISABLED
   * AI-style tag generation commented out for future work.
   *
   * @param {string} content
   * @param {Array<string>} insights
   * @param {Array<string>} themes
   * @returns {Array<string>}
   *
  generateAITags(content, insights, themes) {
    return []; // Return empty tags for now
  }*/

  /**
   * Filters posts based on various criteria
   *
   * @param {Array<Object>} posts - Posts to filter
   * @param {Object} filters - Filter criteria
   * @returns {Array<Object>} Filtered posts
   */
  filterPosts(posts, filters = {}) {
    const {
      dateRange,
      tags,
      themes,
      sentiment,
      minDuration,
      maxDuration,
      speakers
    } = filters;

    return posts.filter(post => {
      // Date range filter
      if (dateRange) {
        const postDate = new Date(post.timestamp);
        if (dateRange.start && postDate < new Date(dateRange.start)) return false;
        if (dateRange.end && postDate > new Date(dateRange.end)) return false;
      }

      // Tags filter
      if (tags && tags.length > 0) {
        const hasRequiredTags = tags.some(tag => post.tags.includes(tag));
        if (!hasRequiredTags) return false;
      }

      // Themes filter
      if (themes && themes.length > 0 && post.themes) {
        const hasRequiredThemes = themes.some(theme => post.themes.includes(theme));
        if (!hasRequiredThemes) return false;
      }

      // Sentiment filter
      if (sentiment && post.sentiment !== sentiment) return false;

      // Duration filters
      if (minDuration && post.metadata.duration < minDuration) return false;
      if (maxDuration && post.metadata.duration > maxDuration) return false;

      // Speakers filter
      if (speakers && speakers.length > 0 && post.metadata.speakers) {
        const hasRequiredSpeakers = speakers.some(speaker =>
          post.metadata.speakers.includes(speaker)
        );
        if (!hasRequiredSpeakers) return false;
      }

      return true;
    });
  }

  /**
   * Stores processed posts in the local storage system
   *
   * @param {Array<Object>} posts - The posts to store
   * @returns {Promise<boolean>} Success status
   */
  async storeProcessedData(posts) {
    try {
      await this.logger.info('Storing processed posts', {
        postCount: posts.length
      });

      // Store in plugin storage using the API
      const storageData = {
        posts,
        lastUpdated: new Date().toISOString(),
        totalCount: posts.length
      };

      const success = this.api.storage.saveData(storageData);

      if (success) {
        await this.logger.info('Posts successfully stored in plugin storage', {
          postCount: posts.length
        });
      } else {
        throw new Error('Failed to save data to plugin storage');
      }

      return success;
    } catch (error) {
      await this.logger.error('Failed to store processed posts', error);
      return false;
    }
  }

  /**
   * Loads processed posts from local storage
   *
   * @returns {Promise<Array<Object>>} Loaded posts
   */
  async loadProcessedData() {
    try {
      await this.logger.info('Loading processed posts from storage');

      const data = this.api.storage.loadData();
      const posts = data?.posts || [];

      await this.logger.info('Posts loaded from storage', {
        postCount: posts.length,
        lastUpdated: data?.lastUpdated
      });

      return posts;
    } catch (error) {
      await this.logger.error('Failed to load processed posts', error);
      return [];
    }
  }

  /**
   * Gets the local storage directory path for processed data
   *
   * @returns {string} Path to the local storage directory
   */
  getStorageDirectory() {
    const storagePath = path.resolve(__dirname, '../../../../data');
    if (!fs.existsSync(storagePath)) {
      fs.mkdirSync(storagePath, { recursive: true });
    }
    return storagePath;
  }
}

module.exports = DataProcessor;
