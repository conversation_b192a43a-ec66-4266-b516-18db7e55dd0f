/**
 * Logger Utility for Limitless Plugin
 *
 * Provides comprehensive logging functionality for the Limitless AI integration plugin.
 * All logs are written to datetime-stamped files in the /logs directory at project root.
 *
 * Features:
 * - Multiple log levels (DEBUG, INFO, WARN, ERROR)
 * - Datetime-stamped log files
 * - Structured JSON logging
 * - Plugin-specific log identification
 * - Performance tracking
 * - Error context capture
 *
 * @class Logger
 */

const fs = require('fs');
const path = require('path');

class Logger {
  /**
   * Creates a new Logger instance
   *
   * @param {Object} api - The Plugin API instance
   */
  constructor(api) {
    this.api = api;
    this.pluginId = api.manifest.id;
    this.logDirectory = this.getLogDirectory();
    this.logFile = this.getLogFile();
    this.initializeLogging();
  }

  /**
   * Gets the logs directory path at project root
   *
   * @returns {string} The absolute path to the logs directory
   */
  getLogDirectory() {
    // Navigate to project root from plugin directory
    const projectRoot = path.resolve(__dirname, '../../../../');
    const logsDir = path.join(projectRoot, 'logs');

    // Ensure logs directory exists
    if (!fs.existsSync(logsDir)) {
      fs.mkdirSync(logsDir, { recursive: true });
    }

    return logsDir;
  }

  /**
   * Gets the datetime-stamped log file path
   *
   * @returns {string} The absolute path to today's log file
   */
  getLogFile() {
    const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD format
    const filename = `${today}-limitless.log`;
    return path.join(this.logDirectory, filename);
  }

  /**
   * Initializes the logging system
   *
   * Creates log directory and initial log entry
   */
  initializeLogging() {
    try {
      this.info('Logger initialized', {
        pluginId: this.pluginId,
        logFile: this.logFile,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Failed to initialize logger:', error);
    }
  }

  /**
   * Logs a debug message
   *
   * @param {string} message - The log message
   * @param {Object} data - Additional data to log
   */
  async debug(message, data = {}) {
    await this.writeLog('DEBUG', message, data);
  }

  /**
   * Logs an info message
   *
   * @param {string} message - The log message
   * @param {Object} data - Additional data to log
   */
  async info(message, data = {}) {
    await this.writeLog('INFO', message, data);
  }

  /**
   * Logs a warning message
   *
   * @param {string} message - The log message
   * @param {Object} data - Additional data to log
   */
  async warn(message, data = {}) {
    await this.writeLog('WARN', message, data);
  }

  /**
   * Logs an error message
   *
   * @param {string} message - The log message
   * @param {Error|null} error - The error object (if any)
   * @param {Object} data - Additional data to log
   */
  async error(message, error = null, data = {}) {
    const errorData = {
      ...data,
      error: error ? {
        message: error.message,
        stack: error.stack,
        name: error.name
      } : null
    };
    await this.writeLog('ERROR', message, errorData);
  }

  /**
   * Logs API call start
   *
   * @param {string} endpoint - The API endpoint being called
   * @param {Object} options - Request options
   * @returns {string} Call ID for tracking
   */
  async logApiCallStart(endpoint, options = {}) {
    const callId = this.generateCallId();
    await this.info('API call started', {
      callId,
      endpoint,
      method: options.method || 'GET',
      hasHeaders: !!options.headers,
      timestamp: new Date().toISOString()
    });
    return callId;
  }

  /**
   * Logs API call completion
   *
   * @param {string} callId - The call ID from logApiCallStart
   * @param {Object} response - The response object
   * @param {number} duration - Duration in milliseconds
   */
  async logApiCallEnd(callId, response, duration) {
    await this.info('API call completed', {
      callId,
      status: response.status || 'unknown',
      duration: `${duration}ms`,
      success: response.ok !== false,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Logs data processing operations
   *
   * @param {string} operation - The processing operation
   * @param {Object} stats - Processing statistics
   */
  async logDataProcessing(operation, stats = {}) {
    await this.info('Data processing', {
      operation,
      ...stats,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Logs sync operations
   *
   * @param {string} phase - The sync phase (start, progress, complete, error)
   * @param {Object} data - Sync data
   */
  async logSync(phase, data = {}) {
    await this.info(`Sync ${phase}`, {
      phase,
      ...data,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Logs user interactions
   *
   * @param {string} action - The user action
   * @param {Object} context - Action context
   */
  async logUserAction(action, context = {}) {
    await this.info('User action', {
      action,
      ...context,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Writes a log entry to the log file
   *
   * @param {string} level - The log level
   * @param {string} message - The log message
   * @param {Object} data - Additional data to log
   */
  async writeLog(level, message, data) {
    try {
      const timestamp = new Date().toISOString();
      const logEntry = {
        timestamp,
        level,
        plugin: this.pluginId,
        message,
        data: this.sanitizeData(data)
      };

      const logLine = JSON.stringify(logEntry) + '\n';

      // Append to log file
      await this.appendToLogFile(logLine);

      // Also output to console for development
      if (process.env.NODE_ENV === 'development') {
        console.log(`[${level}] ${this.pluginId}: ${message}`, data);
      }
    } catch (error) {
      console.error('Failed to write log entry:', error);
    }
  }

  /**
   * Appends content to the log file
   *
   * @param {string} content - The content to append
   */
  async appendToLogFile(content) {
    return new Promise((resolve, reject) => {
      fs.appendFile(this.logFile, content, 'utf8', (error) => {
        if (error) {
          reject(error);
        } else {
          resolve();
        }
      });
    });
  }

  /**
   * Sanitizes data for logging (removes sensitive information)
   *
   * @param {Object} data - The data to sanitize
   * @returns {Object} Sanitized data
   */
  sanitizeData(data) {
    if (!data || typeof data !== 'object') {
      return data;
    }

    const sanitized = { ...data };

    // Remove or mask sensitive fields
    const sensitiveFields = ['apiKey', 'password', 'token', 'secret', 'key'];

    for (const field of sensitiveFields) {
      if (sanitized[field]) {
        sanitized[field] = '[REDACTED]';
      }
    }

    // Recursively sanitize nested objects
    for (const key in sanitized) {
      if (typeof sanitized[key] === 'object' && sanitized[key] !== null) {
        sanitized[key] = this.sanitizeData(sanitized[key]);
      }
    }

    return sanitized;
  }

  /**
   * Generates a unique call ID for tracking
   *
   * @returns {string} A unique call ID
   */
  generateCallId() {
    return `call_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Creates a performance timer
   *
   * @param {string} operation - The operation being timed
   * @returns {Object} Timer object with stop method
   */
  startTimer(operation) {
    const startTime = Date.now();

    return {
      stop: async () => {
        const duration = Date.now() - startTime;
        await this.debug('Operation completed', {
          operation,
          duration: `${duration}ms`
        });
        return duration;
      }
    };
  }

  /**
   * Logs plugin lifecycle events
   *
   * @param {string} event - The lifecycle event
   * @param {Object} data - Event data
   */
  async logLifecycle(event, data = {}) {
    await this.info(`Plugin lifecycle: ${event}`, {
      event,
      ...data,
      timestamp: new Date().toISOString()
    });
  }
}

module.exports = Logger;
