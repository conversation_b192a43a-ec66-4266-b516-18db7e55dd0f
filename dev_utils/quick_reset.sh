#!/usr/bin/env bash
set -e

# =============================================================================
# Lifeboard Quick Reset Script
# =============================================================================
# Purpose: Fast Docker service reset without full data wipe
# Location: /dev_utils/quick_reset.sh
# Usage: ./quick_reset.sh
# =============================================================================

# Color codes
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
COMPOSE_PROJECT="lifeboard"

log() {
    echo -e "${BLUE}[$(date +'%H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}✅ $1${NC}"
}

error() {
    echo -e "${RED}❌ $1${NC}"
    exit 1
}

step() {
    echo -e "\n${PURPLE}🔄 $1${NC}"
}

check_and_start_docker() {
    step "Checking Docker daemon status"

    # Check if Docker daemon is running
    if docker info > /dev/null 2>&1; then
        success "Docker daemon is already running"
        return 0
    fi

    log "Docker daemon not running, starting Docker Desktop..."

    # Start Docker Desktop
    open -a Docker

    # Wait for Docker daemon to start
    log "Waiting for Docker daemon to start..."
    local max_attempts=60  # Wait up to 2 minutes
    local attempt=1

    while [[ $attempt -le $max_attempts ]]; do
        if docker info > /dev/null 2>&1; then
            success "Docker daemon is now running"
            # Give it a few more seconds to fully initialize
            sleep 3
            return 0
        fi

        if [[ $attempt -eq $max_attempts ]]; then
            error "Docker daemon failed to start after $max_attempts attempts. Please start Docker Desktop manually."
        fi

        log "Attempt $attempt/$max_attempts - waiting for Docker daemon..."
        sleep 2
        ((attempt++))
    done
}

main() {
    echo -e "${CYAN}"
    cat << 'EOF'
╔══════════════════════════════════════════╗
║         LIFEBOARD QUICK RESET            ║
║                                          ║
║    Fast Docker restart (preserves data)  ║
╚══════════════════════════════════════════╝
EOF
    echo -e "${NC}"

    cd "$PROJECT_ROOT" || exit 1

    check_and_start_docker

    step "Stopping all Docker services"
    docker compose -p $COMPOSE_PROJECT down --remove-orphans 2>/dev/null || true
    docker compose -p $COMPOSE_PROJECT -f docker-compose.web-ui.yml down --remove-orphans 2>/dev/null || true

    step "Starting Docker services"
    docker compose -p $COMPOSE_PROJECT up -d

    step "Starting Web UI"
    docker compose -p $COMPOSE_PROJECT -f docker-compose.web-ui.yml --profile webui up -d

    step "Waiting for services to be ready"
    sleep 5

    success "Quick reset complete!"
    echo -e "\n${CYAN}🌐 Web UI: http://localhost:9820${NC}"
    echo -e "${CYAN}📋 Plugins: http://localhost:9820/plugins.html${NC}"
    echo -e "\n${GREEN}Ready for testing!${NC}"
}

main "$@"
