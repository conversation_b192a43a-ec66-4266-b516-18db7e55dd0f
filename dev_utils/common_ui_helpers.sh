#!/usr/bin/env bash
# =============================================================================
# Lifeboard Development Common UI Helpers
# =============================================================================
# Purpose: Shared utility functions for development scripts
# Location: /dev_utils/common_ui_helpers.sh
# Usage: source "${SCRIPT_DIR}/common_ui_helpers.sh"
# =============================================================================

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# =============================================================================
# Logging Functions
# =============================================================================

log() {
    echo -e "${BLUE}[$(date +'%H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}✅ $1${NC}"
}

warn() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

error() {
    echo -e "${RED}❌ $1${NC}"
    exit 1
}

step() {
    echo -e "\n${PURPLE}🔄 $1${NC}"
}

# =============================================================================
# Electron Management Functions
# =============================================================================

start_electron_background() {
    step "Starting Electron application in background"

    # Ensure we have the required environment variables
    if [[ -z "$ELECTRON_DIR" ]]; then
        error "ELECTRON_DIR environment variable is not set"
    fi

    if [[ -z "$PROJECT_ROOT" ]]; then
        error "PROJECT_ROOT environment variable is not set"
    fi

    # Change to Electron directory
    if [[ ! -d "$ELECTRON_DIR" ]]; then
        error "Electron directory not found: $ELECTRON_DIR"
    fi

    log "Changing to Electron directory: $ELECTRON_DIR"
    cd "$ELECTRON_DIR" || error "Cannot change to Electron directory"

    # Ensure node_modules exists - this runs synchronously before background launch
    if [[ ! -d "node_modules" ]]; then
        log "Installing npm dependencies synchronously..."
        npm install || error "Failed to install npm dependencies"
        success "npm dependencies installed"
    else
        log "npm dependencies already installed"
    fi

    # Ensure logs directory exists before redirecting Electron output
    log "Creating logs directory: $PROJECT_ROOT/logs"
    if ! mkdir -p "$PROJECT_ROOT/logs"; then
        error "Failed to create logs directory: $PROJECT_ROOT/logs"
    fi

    # Generate timestamped log filename (logs live only in /logs)
    local log_file="$PROJECT_ROOT/logs/electron_$(date +%Y%m%d_%H%M%S).log"
    log "Electron output will be logged to: $log_file"

    log "Launching Electron in background mode..."

    # Start Electron in background and capture PID
    npm run electron-dev >> "$log_file" 2>&1 &
    local electron_pid=$!

    # Give it a moment to start
    sleep 2

    # Check if process is still running
    if kill -0 "$electron_pid" 2>/dev/null; then
        success "Electron launched in background (PID $electron_pid); tail the log for details."
        log "Log file: $log_file"
        log "To view logs in real-time: tail -f $log_file"
        return 0
    else
        error "Electron failed to start. Check log file: $log_file"
    fi
}

# =============================================================================
# Browser Management Functions
# =============================================================================

open_browser() {
    local url="${1:-http://localhost:9820}"

    step "Opening browser to $url"

    # Detect platform
    local platform
    platform=$(uname -s)

    case "$platform" in
        "Darwin")
            log "Detected macOS, using 'open' command"
            if command -v open >/dev/null 2>&1; then
                open "$url"
                success "Browser opened on macOS"
            else
                warn "open command not found on macOS"
                return 1
            fi
            ;;
        "Linux")
            log "Detected Linux, using 'xdg-open' command"
            if command -v xdg-open >/dev/null 2>&1; then
                xdg-open "$url"
                success "Browser opened on Linux"
            else
                warn "xdg-open command not found on Linux"
                return 1
            fi
            ;;
        "CYGWIN"*|"MINGW"*|"MSYS"*)
            log "Detected Windows, using 'start' command"
            if command -v start >/dev/null 2>&1; then
                start "$url"
                success "Browser opened on Windows"
            else
                warn "start command not found on Windows"
                return 1
            fi
            ;;
        *)
            warn "Unknown platform: $platform"
            warn "Please manually open: $url"
            return 1
            ;;
    esac
}

# =============================================================================
# CODE SMELL SELF-REVIEW COMMENTS
# =============================================================================
#
# POTENTIAL SHELLCHECK CONCERNS:
# 1. SC2086: Double quote variables to prevent word splitting (line 66)
# 2. SC2164: Use 'cd ... || exit' instead of 'cd ...' (line 66)
# 3. SC2181: Check exit code directly instead of $? (line 97)
# 4. SC2015: Note A && B || C is not if-then-else (line 97)
# 5. SC2034: Some color variables may be unused in standalone usage
# 6. SC2046: Quote process substitution to prevent word splitting (line 118)
# 7. SC2065: Consider using [[ ]] instead of [ ] for better compatibility
# 8. SC2236: Use -n instead of ! -z for clarity (line 52, 57)
#
# FUTURE REFACTOR IDEAS:
# 1. Add configurable log levels (DEBUG, INFO, WARN, ERROR)
# 2. Implement structured logging with JSON output option
# 3. Add timestamp formatting options (ISO8601, custom formats)
# 4. Create log rotation functionality
# 5. Add colored output toggle for CI/CD environments
# 6. Implement log file archiving and compression
# 7. Add performance timing functions (start_timer, end_timer)
# 8. Create progress bar/spinner functions for long operations
# 9. Add notification system (email, slack, webhook)
# 10. Implement proper error handling with stack traces
# 11. Add configuration file support for default behaviors
# 12. Create platform-specific optimizations
# 13. Add support for multiple output destinations
# 14. Implement log filtering and searching capabilities
# 15. Add health check utilities for services
#
# SECURITY CONSIDERATIONS:
# - Sanitize all log messages to prevent log injection
# - Validate file paths before writing logs
# - Implement proper file permissions for log files
# - Add rate limiting for log output
#
# MAINTAINABILITY IMPROVEMENTS:
# - Add comprehensive function documentation
# - Create unit tests for all utility functions
# - Implement consistent return codes
# - Add parameter validation for all functions
# - Create usage examples and documentation
# =============================================================================
