#!/bin/bash

# Log Analysis Utility
# Description: Provides analytical insights into the logs generated by the Lifeboard application

set -e

# Configuration
LOG_DIR="./logs"

# Function to analyze log entries by level of severity
analyze_by_severity() {
    echo "Analyzing logs by severity..."
    for file in "$LOG_DIR"/*; do
        if [ -f "$file" ]; then
            echo "
Log file: $file"
            grep -Po '"level":"\K[^"]+' "$file" | sort | uniq -c | sort -nr
        fi
    done
}

# Function to find the most common log messages
analyze_common_messages() {
    echo "Analyzing common log messages..."
    for file in "$LOG_DIR"/*; do
        if [ -f "$file" ]; then
            echo "
Log file: $file"
            grep -Po '"message":"\K[^"]+' "$file" | sort | uniq -c | sort -nr | head -10
        fi
    done
}

# Main menu for log analysis
main() {
    echo "Lifeboard Log Analysis Utility"
    echo "==================================="
    echo "1. Analyze logs by severity"
    echo "2. Analyze common log messages"
    echo "0. Exit"
    echo "==================================="
    read -r -p "Enter choice: " choice
    case "$choice" in
        1)
            analyze_by_severity
            ;;
        2)
            analyze_common_messages
            ;;
        0)
            echo "Exiting..."
            ;;
        *)
            echo "Invalid option. Try again."
            main
            ;;
    esac
}

main
