# Lifeboard Development Utilities

This directory contains scripts for managing the Lifeboard development environment.

## 🚀 Quick Start

```bash
# Full system reset (recommended for thorough testing)
./reset_and_start.sh

# Quick Docker restart (faster, preserves data)
./quick_reset.sh
```

## 📋 Available Scripts

### `reset_and_start.sh` - Complete System Reset

**Purpose**: Complete teardown and restart of the entire Lifeboard development environment.

**What it does**:
- ✅ Stops all Docker containers and services
- ✅ Kills processes on occupied ports (5432, 3000, 8810, 8811, 9820, etc.)
- ✅ Removes Docker networks and containers
- ✅ Optionally clears all data (database volumes, plugin settings)
- ✅ Starts all services fresh
- ✅ Launches Electron application

**Usage**:
```bash
# Full reset with confirmation prompt
./reset_and_start.sh

# Skip confirmation (dangerous!)
./reset_and_start.sh --no-confirm

# Reset but preserve all data
./reset_and_start.sh --keep-data

# Only restart Electron app (skip Docker)
./reset_and_start.sh --electron-only

# Show help
./reset_and_start.sh --help
```

**Options**:
- `--no-confirm` - Skip confirmation prompts (use with caution)
- `--keep-data` - Preserve database volumes and plugin data
- `--electron-only` - Only restart Electron app, skip Docker services
- `--verbose` - Show detailed command output
- `-h, --help` - Show help message

---

### `quick_reset.sh` - Fast Docker Restart

**Purpose**: Quick restart of Docker services without data destruction.

**What it does**:
- ✅ Stops Docker containers
- ✅ Restarts Docker services
- ✅ Preserves all data and volumes
- ✅ Fast execution (no confirmation needed)

**Usage**:
```bash
./quick_reset.sh
```

**When to use**:
- Quick iterations during development
- When you need to restart services but keep data
- When Docker containers are misbehaving
- For faster testing cycles

---

## 🎯 Testing Scenarios

### Scenario 1: Fresh Testing Environment
```bash
# Complete clean slate
./reset_and_start.sh --no-confirm
```

### Scenario 2: Quick Development Iteration
```bash
# Fast restart with data preservation
./quick_reset.sh
```

### Scenario 3: Electron-Only Testing
```bash
# Only restart Electron, keep Docker running
./reset_and_start.sh --electron-only
```

### Scenario 4: Data-Preserving Reset
```bash
# Reset services but keep all data
./reset_and_start.sh --keep-data
```

---

## 📊 Service URLs After Reset

Once the scripts complete, you can access:

### Web UI System
- **Main UI**: http://localhost:9820
- **Plugins Page**: http://localhost:9820/plugins.html
- **Index Page**: http://localhost:9820/index.html

### Electron System
- **Native App**: Desktop window that opens automatically
- **Plugin Commands**: Available via Command Palette (Cmd+Shift+P)
- **Settings**: Via plugin settings button or ribbon icon

### Backend Services
- **Database**: localhost:5543 (PostgreSQL)
- **REST API**: localhost:8810 (Supabase REST)
- **Auth**: Internal (healthy status check)
- **Realtime**: Internal (WebSocket connections)

---

## ⚠️ Important Notes

### Data Destruction Warning
```bash
# This WILL delete all your data:
./reset_and_start.sh

# This preserves data:
./reset_and_start.sh --keep-data
./quick_reset.sh
```

### Port Conflicts
The scripts automatically kill processes on these ports:
- `5432` - PostgreSQL
- `3000` - Supabase API / Next.js
- `8810` - Supabase REST API
- `8811` - Supabase Studio
- `9820` - Web UI
- `5543` - Custom PostgreSQL port
- `9810` - Custom Supabase port

### Dependencies Required
- Docker & Docker Compose
- Node.js & npm
- macOS (scripts are macOS-optimized)

---

## 🔧 Troubleshooting

### Script Won't Run
```bash
# Make sure scripts are executable
chmod +x *.sh

# Check if you're in the right directory
pwd  # Should be /path/to/lifeboard/dev_utils
```

### Docker Issues
```bash
# If Docker services won't start
docker system prune -f
docker volume prune -f

# Then try again
./reset_and_start.sh
```

### Port Conflicts
```bash
# Manually kill processes on a specific port
lsof -ti:9820 | xargs kill -9

# Or let the script handle it
./reset_and_start.sh
```

### Electron Won't Start
```bash
# Install/update npm dependencies
cd ../desktop
npm install

# Then restart
cd ../dev_utils
./reset_and_start.sh --electron-only
```

---

## 🚀 Plugin Testing Workflow

1. **Start with fresh environment**:
   ```bash
   ./reset_and_start.sh
   ```

2. **Test in Web UI**:
   - Open http://localhost:9820/plugins.html
   - Verify Limitless plugin appears
   - Test settings modal and API key validation

3. **Test in Electron**:
   - Use the desktop window that opens
   - Check plugin loads in logs
   - Test command palette and ribbon icons

4. **Quick iterations**:
   ```bash
   # For quick restarts during development
   ./quick_reset.sh
   ```

---

## 📝 Script Maintenance

### Adding New Ports
Edit `reset_and_start.sh` and update the `ports` array in `kill_port_processes()`.

### Adding New Compose Files
Edit the `other_composes` array in `stop_docker_services()`.

### Changing Default Behavior
Modify the default option variables at the top of `reset_and_start.sh`.

---

## 🎯 Best Practices

1. **Always use `--keep-data` during active development** unless you specifically need a clean database
2. **Use `quick_reset.sh` for most development iterations** - it's much faster
3. **Use full `reset_and_start.sh` when switching branches** or testing major changes
4. **Check the script output** for any service health warnings
5. **Wait for all services to be healthy** before starting tests

---

*Happy testing! 🎉*
