# Pre-commit configuration for code smell detection
# This file configures automated code quality checks that run before each commit
#
# Installation:
#   pip install pre-commit
#   pre-commit install
#
# Usage:
#   pre-commit run --all-files  # Run on all files
#   pre-commit run <hook-id>    # Run specific hook
#
# Based on Lifeboard code smell automation requirements

repos:
  # General formatting and syntax
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
      - id: trailing-whitespace
        name: "Remove trailing whitespace"
      - id: end-of-file-fixer
        name: "Ensure files end with newline"
      - id: check-yaml
        name: "Validate YAML syntax"
        args: [--allow-multiple-documents]
      - id: check-json
        name: "Validate JSON syntax"
      - id: check-toml
        name: "Validate TOML syntax"
      - id: check-xml
        name: "Validate XML syntax"
      - id: check-added-large-files
        name: "Check for large files"
        args: [--maxkb=1024]
      - id: check-case-conflict
        name: "Check for case conflicts"
      - id: check-merge-conflict
        name: "Check for merge conflicts"
      - id: check-symlinks
        name: "Check for broken symlinks"
      - id: debug-statements
        name: "Check for debug statements"
      - id: detect-private-key
        name: "Detect private keys"

  # Shell script linting
  - repo: https://github.com/shellcheck-py/shellcheck-py
    rev: v0.9.0.6
    hooks:
      - id: shellcheck
        name: "Shell script analysis"
        args: [--severity=warning, --shell=bash]
        types: [shell]

  # YAML linting
  - repo: https://github.com/adrienverge/yamllint
    rev: v1.32.0
    hooks:
      - id: yamllint
        name: "YAML linting"
        args: [-c=.yamllint.yml]

  # Markdown linting
  - repo: https://github.com/igorshubovych/markdownlint-cli
    rev: v0.37.0
    hooks:
      - id: markdownlint
        name: "Markdown linting"
        args: [--config=.markdownlint.yml]

  # Docker linting
  - repo: https://github.com/hadolint/hadolint
    rev: v2.12.0
    hooks:
      - id: hadolint-docker
        name: "Dockerfile linting"
        args: [--config=.hadolint.yaml]

  # Secret detection
  - repo: https://github.com/Yelp/detect-secrets
    rev: v1.4.0
    hooks:
      - id: detect-secrets
        name: "Secret detection"
        args: [--baseline=.secrets.baseline]
        exclude: package.lock.json

  # Git leaks detection
  - repo: https://github.com/zricethezav/gitleaks
    rev: v8.18.0
    hooks:
      - id: gitleaks
        name: "Git leaks detection"

  # Python code quality
  - repo: https://github.com/psf/black
    rev: 23.9.1
    hooks:
      - id: black
        name: "Python code formatting"
        language_version: python3

  - repo: https://github.com/pycqa/isort
    rev: 5.12.0
    hooks:
      - id: isort
        name: "Python import sorting"
        args: [--profile=black]

  - repo: https://github.com/pycqa/flake8
    rev: 6.1.0
    hooks:
      - id: flake8
        name: "Python linting"
        args: [--config=.flake8]

  - repo: https://github.com/pycqa/pylint
    rev: v3.0.0
    hooks:
      - id: pylint
        name: "Python code analysis"
        args: [--rcfile=.pylintrc]

  - repo: https://github.com/PyCQA/bandit
    rev: 1.7.5
    hooks:
      - id: bandit
        name: "Python security analysis"
        args: [-c=.bandit]

  # JavaScript/TypeScript code quality
  - repo: https://github.com/pre-commit/mirrors-eslint
    rev: v8.51.0
    hooks:
      - id: eslint
        name: "JavaScript/TypeScript linting"
        files: \.(js|jsx|ts|tsx)$
        types: [file]
        args: [--fix, --ext=.js,.jsx,.ts,.tsx]
        additional_dependencies:
          - eslint@8.51.0
          - "@typescript-eslint/eslint-plugin@6.7.4"
          - "@typescript-eslint/parser@6.7.4"
          - "eslint-config-airbnb@19.0.4"
          - "eslint-plugin-import@2.28.1"
          - "eslint-plugin-jsx-a11y@6.7.1"
          - "eslint-plugin-react@7.33.2"
          - "eslint-plugin-react-hooks@4.6.0"
          - "eslint-plugin-sonarjs@0.21.0"

  - repo: https://github.com/pre-commit/mirrors-prettier
    rev: v3.0.3
    hooks:
      - id: prettier
        name: "JavaScript/TypeScript formatting"
        files: \.(js|jsx|ts|tsx|json|css|md)$
        exclude: package-lock.json

  # Go code quality
  - repo: https://github.com/dnephin/pre-commit-golang
    rev: v0.5.1
    hooks:
      - id: go-fmt
        name: "Go formatting"
      - id: go-vet-mod
        name: "Go vet analysis"
      - id: go-mod-tidy
        name: "Go mod tidy"
      - id: golangci-lint-mod
        name: "Go comprehensive linting"
        args: [--config=.golangci.yml]

  # SQL linting
  - repo: https://github.com/sqlfluff/sqlfluff
    rev: 2.3.2
    hooks:
      - id: sqlfluff-lint
        name: "SQL linting"
        args: [--dialect=postgres, --config=.sqlfluff]
      - id: sqlfluff-fix
        name: "SQL auto-fix"
        args: [--dialect=postgres, --config=.sqlfluff]

  # Commit message linting
  - repo: https://github.com/commitizen-tools/commitizen
    rev: v3.10.0
    hooks:
      - id: commitizen
        name: "Commit message validation"
        stages: [commit-msg]

  # Custom code smell detection
  - repo: local
    hooks:
      - id: code-smell-detector
        name: "Custom code smell detection"
        entry: python3 tools/code_smell_detector.py
        language: system
        args: [--project=., --format=json]
        pass_filenames: false
        always_run: true

# Configuration for specific hooks
ci:
  autofix_commit_msg: |
    [pre-commit.ci] auto fixes from pre-commit.com hooks

    for more information, see https://pre-commit.ci
  autofix_prs: true
  autoupdate_branch: ''
  autoupdate_commit_msg: '[pre-commit.ci] pre-commit autoupdate'
  autoupdate_schedule: weekly
  skip: []
  submodules: false
