-- Bootstrap Implementation Validation Tests
-- Tests the db/bootstrap/00_roles.sql implementation

\echo 'Testing Bootstrap Implementation...'

-- Test 1: Verify all required roles exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_roles WHERE rolname = 'authenticator') THEN
        RAISE EXCEPTION 'Bootstrap FAILED: authenticator role not found';
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_roles WHERE rolname = 'anon') THEN
        RAISE EXCEPTION 'Bootstrap FAILED: anon role not found';
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_roles WHERE rolname = 'authenticated') THEN
        RAISE EXCEPTION 'Bootstrap FAILED: authenticated role not found';
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_roles WHERE rolname = 'service_role') THEN
        RAISE EXCEPTION 'Bootstrap FAILED: service_role role not found';
    END IF;

    RAISE NOTICE 'Bootstrap Test 1 PASSED: All required roles exist';
END
$$;

-- Test 2: Verify role login permissions
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_roles WHERE rolname = 'authenticator' AND rolcanlogin = true) THEN
        RAISE EXCEPTION 'Bootstrap FAILED: authenticator cannot login';
    END IF;

    IF EXISTS (SELECT 1 FROM pg_roles WHERE rolname = 'anon' AND rolcanlogin = true) THEN
        RAISE EXCEPTION 'Bootstrap FAILED: anon should not be able to login';
    END IF;

    RAISE NOTICE 'Bootstrap Test 2 PASSED: Login permissions correct';
END
$$;

-- Test 3: Verify role switching capabilities
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_auth_members WHERE roleid = (SELECT oid FROM pg_roles WHERE rolname = 'anon') AND member = (SELECT oid FROM pg_roles WHERE rolname = 'authenticator')) THEN
        RAISE EXCEPTION 'Bootstrap FAILED: authenticator cannot switch to anon';
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_auth_members WHERE roleid = (SELECT oid FROM pg_roles WHERE rolname = 'authenticated') AND member = (SELECT oid FROM pg_roles WHERE rolname = 'authenticator')) THEN
        RAISE EXCEPTION 'Bootstrap FAILED: authenticator cannot switch to authenticated';
    END IF;

    RAISE NOTICE 'Bootstrap Test 3 PASSED: Role switching configured';
END
$$;

\echo 'Bootstrap Implementation Tests COMPLETED'
