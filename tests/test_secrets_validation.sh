#!/bin/bash

# Test: Secrets and Configuration Validation
# Description: Validates environment configuration, secret management, and security practices
# Author: AI Assistant
# Created: 2025-07-01

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/.." && pwd)"
LOG_DIR="${PROJECT_ROOT}/logs"
LOG_FILE="${LOG_DIR}/secrets_validation_$(date +%Y%m%d_%H%M%S).log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Ensure log directory exists
mkdir -p "${LOG_DIR}"

# Logging function
log() {
    local level="$1"
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${timestamp} [${level}] ${message}" | tee -a "${LOG_FILE}"
}

log_info() { log "INFO" "$@"; }
log_warn() { log "WARN" "$@"; }
log_error() { log "ERROR" "$@"; }
log_success() { log "SUCCESS" "$@"; }

# Test counters
TESTS_TOTAL=0
TESTS_PASSED=0
TESTS_FAILED=0

# Test result tracking
test_result() {
    local test_name="$1"
    local result="$2"
    local message="$3"

    TESTS_TOTAL=$((TESTS_TOTAL + 1))

    if [[ "$result" == "PASS" ]]; then
        TESTS_PASSED=$((TESTS_PASSED + 1))
        echo -e "${GREEN}✓ PASS${NC}: $test_name - $message"
        log_success "TEST PASS: $test_name - $message"
    else
        TESTS_FAILED=$((TESTS_FAILED + 1))
        echo -e "${RED}✗ FAIL${NC}: $test_name - $message"
        log_error "TEST FAIL: $test_name - $message"
    fi
}

# Check if file exists and is readable
check_file_exists() {
    local file_path="$1"
    local description="$2"

    if [[ -f "$file_path" && -r "$file_path" ]]; then
        test_result "File Existence: $description" "PASS" "File exists and is readable: $file_path"
        return 0
    else
        test_result "File Existence: $description" "FAIL" "File missing or unreadable: $file_path"
        return 1
    fi
}

# Test 1: Environment file validation
test_env_file_validation() {
    log_info "Starting environment file validation tests..."

    local env_file="${PROJECT_ROOT}/.env.local"

    # Check if .env.local exists
    if ! check_file_exists "$env_file" ".env.local"; then
        return 1
    fi

    # Validate required environment variables
    local required_vars=(
        "POSTGRES_HOST"
        "POSTGRES_DB"
        "POSTGRES_PASSWORD"
        "POSTGRES_PORT"
        "JWT_SECRET"
        "ANON_KEY"
        "SERVICE_ROLE_KEY"
        "API_EXTERNAL_URL"
        "SITE_URL"
        "SUPABASE_PUBLIC_URL"
    )

    local missing_vars=()

    for var in "${required_vars[@]}"; do
        if ! grep -q "^${var}=" "$env_file"; then
            missing_vars+=("$var")
        fi
    done

    if [[ ${#missing_vars[@]} -eq 0 ]]; then
        test_result "Required Variables" "PASS" "All required environment variables present"
    else
        test_result "Required Variables" "FAIL" "Missing variables: ${missing_vars[*]}"
    fi

    # Validate JWT secret format (should be hex string, 64 chars)
    local jwt_secret=$(grep "^JWT_SECRET=" "$env_file" | cut -d'=' -f2)
    if [[ ${#jwt_secret} -eq 64 && "$jwt_secret" =~ ^[a-fA-F0-9]+$ ]]; then
        test_result "JWT Secret Format" "PASS" "JWT secret has correct format (64 hex chars)"
    else
        test_result "JWT Secret Format" "FAIL" "JWT secret format invalid (expected 64 hex chars)"
    fi

    # Validate port numbers are numeric and in valid range
    local ports=("POSTGRES_PORT" "SUPABASE_PORT" "STUDIO_PORT" "KONG_HTTP_PORT" "KONG_HTTPS_PORT")
    local port_valid=true

    for port_var in "${ports[@]}"; do
        local port_val=$(grep "^${port_var}=" "$env_file" 2>/dev/null | cut -d'=' -f2 || echo "")
        if [[ -n "$port_val" ]]; then
            if ! [[ "$port_val" =~ ^[0-9]+$ ]] || [[ $port_val -lt 1 || $port_val -gt 65535 ]]; then
                port_valid=false
                break
            fi
        fi
    done

    if $port_valid; then
        test_result "Port Validation" "PASS" "All port numbers are valid"
    else
        test_result "Port Validation" "FAIL" "Invalid port number detected"
    fi
}

# Test 2: Secret leak detection
test_secret_leak_detection() {
    log_info "Starting secret leak detection tests..."

    # Define patterns that might indicate exposed secrets
    local secret_patterns=(
        "password.*=.*[^_][a-zA-Z0-9]"
        "secret.*=.*[a-zA-Z0-9]"
        "key.*=.*[a-zA-Z0-9]"
        "token.*=.*[a-zA-Z0-9]"
    )

    # Check common files that shouldn't contain secrets
    local files_to_check=(
        "README.md"
        "docker-compose.yml"
        "Dockerfile"
    )

    local leaks_found=false

    for file in "${files_to_check[@]}"; do
        local file_path="${PROJECT_ROOT}/$file"
        if [[ -f "$file_path" ]]; then
            for pattern in "${secret_patterns[@]}"; do
                if grep -i -E "$pattern" "$file_path" >/dev/null 2>&1; then
                    leaks_found=true
                    log_warn "Potential secret leak in $file: pattern '$pattern'"
                fi
            done
        fi
    done

    if ! $leaks_found; then
        test_result "Secret Leak Detection" "PASS" "No obvious secret leaks detected in common files"
    else
        test_result "Secret Leak Detection" "FAIL" "Potential secret leaks detected"
    fi

    # Check if .env.local is in .gitignore
    local gitignore="${PROJECT_ROOT}/.gitignore"
    if [[ -f "$gitignore" ]] && grep -q "\.env\.local" "$gitignore"; then
        test_result "Gitignore Protection" "PASS" ".env.local is properly ignored by git"
    else
        test_result "Gitignore Protection" "FAIL" ".env.local is not protected by .gitignore"
    fi
}

# Test 3: Database connection validation
test_database_connection() {
    log_info "Starting database connection validation tests..."

    # Source environment variables
    if [[ -f "${PROJECT_ROOT}/.env.local" ]]; then
        set -a
        source "${PROJECT_ROOT}/.env.local"
        set +a
    else
        test_result "Environment Loading" "FAIL" "Cannot load .env.local file"
        return 1
    fi

    # Test database connectivity (assuming Docker is running)
    if command -v docker >/dev/null 2>&1; then
        # Check if database container is running
        if docker ps --format "table {{.Names}}" | grep -qE "lifeboard-db-[0-9]+|lifeboard.*db|postgres"; then
            # Try to connect to database
            local db_url="postgresql://supabase_admin:${POSTGRES_PASSWORD}@localhost:${POSTGRES_PORT}/postgres"

            if command -v psql >/dev/null 2>&1; then
                if psql "$db_url" -c "SELECT 1;" >/dev/null 2>&1; then
                    test_result "Database Connection" "PASS" "Successfully connected to database"
                else
                    test_result "Database Connection" "FAIL" "Cannot connect to database"
                fi
            else
                test_result "Database Connection" "SKIP" "psql not available for connection test"
            fi
        else
            test_result "Database Container" "FAIL" "Database container not running"
        fi
    else
        test_result "Docker Availability" "FAIL" "Docker not available for container checks"
    fi
}

# Test 4: JWT validation
test_jwt_validation() {
    log_info "Starting JWT validation tests..."

    # Source environment variables
    if [[ -f "${PROJECT_ROOT}/.env.local" ]]; then
        set -a
        source "${PROJECT_ROOT}/.env.local"
        set +a
    fi

    # Validate JWT keys are proper JWT format
    local jwt_keys=("ANON_KEY" "SERVICE_ROLE_KEY")

    for key_var in "${jwt_keys[@]}"; do
        local key_value="${!key_var:-}"

        if [[ -n "$key_value" ]]; then
            # JWT should have 3 parts separated by dots
            local jwt_parts=$(echo "$key_value" | tr '.' '\n' | wc -l)

            if [[ $jwt_parts -eq 3 ]]; then
                test_result "JWT Format: $key_var" "PASS" "JWT has correct format (3 parts)"
            else
                test_result "JWT Format: $key_var" "FAIL" "JWT format invalid (expected 3 parts, got $jwt_parts)"
            fi
        else
            test_result "JWT Presence: $key_var" "FAIL" "JWT key not found or empty"
        fi
    done
}

# Test 5: URL and endpoint validation
test_url_validation() {
    log_info "Starting URL and endpoint validation tests..."

    # Source environment variables
    if [[ -f "${PROJECT_ROOT}/.env.local" ]]; then
        set -a
        source "${PROJECT_ROOT}/.env.local"
        set +a
    fi

    # Validate URL formats
    local url_vars=("API_EXTERNAL_URL" "SITE_URL" "SUPABASE_PUBLIC_URL")
    local url_regex='^https?://[a-zA-Z0-9][a-zA-Z0-9.-]*[a-zA-Z0-9](:[0-9]+)?(/.*)?$'

    for url_var in "${url_vars[@]}"; do
        local url_value="${!url_var:-}"

        if [[ -n "$url_value" ]]; then
            if [[ "$url_value" =~ $url_regex ]]; then
                test_result "URL Format: $url_var" "PASS" "URL format is valid"
            else
                test_result "URL Format: $url_var" "FAIL" "URL format is invalid"
            fi
        else
            test_result "URL Presence: $url_var" "FAIL" "URL variable not found or empty"
        fi
    done
}

# Test 6: Security configuration validation
test_security_configuration() {
    log_info "Starting security configuration validation tests..."

    # Source environment variables
    if [[ -f "${PROJECT_ROOT}/.env.local" ]]; then
        set -a
        source "${PROJECT_ROOT}/.env.local"
        set +a
    fi

    # Check JWT expiry is reasonable (not too long)
    local jwt_expiry="${JWT_EXPIRY:-0}"
    if [[ $jwt_expiry -gt 0 && $jwt_expiry -le 86400 ]]; then
        test_result "JWT Expiry Security" "PASS" "JWT expiry is reasonable (${jwt_expiry}s <= 24h)"
    else
        test_result "JWT Expiry Security" "FAIL" "JWT expiry too long or invalid (${jwt_expiry}s)"
    fi

    # Check if signup is appropriately configured for development
    local disable_signup="${DISABLE_SIGNUP:-false}"
    if [[ "$disable_signup" == "false" ]]; then
        test_result "Signup Configuration" "PASS" "Signup enabled for development environment"
    else
        test_result "Signup Configuration" "WARN" "Signup disabled - verify this is intentional"
    fi

    # Check if anonymous users are disabled (good security practice)
    local enable_anon="${ENABLE_ANONYMOUS_USERS:-true}"
    if [[ "$enable_anon" == "false" ]]; then
        test_result "Anonymous Users" "PASS" "Anonymous users disabled (good security)"
    else
        test_result "Anonymous Users" "WARN" "Anonymous users enabled - review if necessary"
    fi
}

# Main execution
main() {
    echo -e "${BLUE}=== Lifeboard Secrets & Configuration Validation ===${NC}"
    echo -e "${BLUE}Started at: $(date)${NC}"
    echo ""

    log_info "Starting secrets and configuration validation test suite"

    # Run test suites
    test_env_file_validation
    test_secret_leak_detection
    test_database_connection
    test_jwt_validation
    test_url_validation
    test_security_configuration

    # Summary
    echo ""
    echo -e "${BLUE}=== Test Summary ===${NC}"
    echo -e "Total Tests: ${TESTS_TOTAL}"
    echo -e "${GREEN}Passed: ${TESTS_PASSED}${NC}"
    echo -e "${RED}Failed: ${TESTS_FAILED}${NC}"

    if [[ $TESTS_FAILED -eq 0 ]]; then
        echo -e "${GREEN}✓ All secrets and configuration tests passed!${NC}"
        log_success "All secrets and configuration validation tests completed successfully"
        exit 0
    else
        echo -e "${RED}✗ Some tests failed. Check the logs for details.${NC}"
        log_error "Some secrets and configuration validation tests failed"
        exit 1
    fi
}

# Execute main function
main "$@"
