#!/bin/bash

# Test: Observability & Logging Validation
# Description: Validates structured logging, log format, and rotation capabilities
# Author: AI Assistant
# Created: 2025-07-01

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/.." && pwd)"
LOG_DIR="${PROJECT_ROOT}/logs"
LOG_FILE="${LOG_DIR}/observability_test_$(date +%Y%m%d_%H%M%S).log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Ensure log directory exists
mkdir -p "${LOG_DIR}"

# Logging function with structured format
log() {
    local level="$1"
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%dT%H:%M:%S.%3NZ' | sed 's/%3N/000/')
    local component="observability_test"
    local session_id="$(date +%s)_$$"

    # Structured JSON log format
    echo "{\"timestamp\":\"${timestamp}\",\"level\":\"${level}\",\"component\":\"${component}\",\"session_id\":\"${session_id}\",\"message\":\"${message}\"}" | tee -a "${LOG_FILE}"
}

log_info() { log "INFO" "$@"; }
log_warn() { log "WARN" "$@"; }
log_error() { log "ERROR" "$@"; }
log_success() { log "SUCCESS" "$@"; }
log_debug() { log "DEBUG" "$@"; }

# Test counters
TESTS_TOTAL=0
TESTS_PASSED=0
TESTS_FAILED=0

# Test result tracking
test_result() {
    local test_name="$1"
    local result="$2"
    local message="$3"

    TESTS_TOTAL=$((TESTS_TOTAL + 1))

    if [[ "$result" == "PASS" ]]; then
        TESTS_PASSED=$((TESTS_PASSED + 1))
        echo -e "${GREEN}✓ PASS${NC}: $test_name - $message"
        log_success "TEST PASS: $test_name - $message"
    else
        TESTS_FAILED=$((TESTS_FAILED + 1))
        echo -e "${RED}✗ FAIL${NC}: $test_name - $message"
        log_error "TEST FAIL: $test_name - $message"
    fi
}

# Test 1: Log directory structure validation
test_log_directory_structure() {
    log_info "Testing log directory structure..."

    local required_dirs=(
        "${LOG_DIR}"
        "${LOG_DIR}/postgres"
        "${LOG_DIR}/auth"
        "${LOG_DIR}/realtime"
        "${LOG_DIR}/rest"
        "${LOG_DIR}/storage"
        "${LOG_DIR}/studio"
    )

    local missing_dirs=()

    for dir in "${required_dirs[@]}"; do
        if [[ ! -d "$dir" ]]; then
            missing_dirs+=("$dir")
        fi
    done

    if [[ ${#missing_dirs[@]} -eq 0 ]]; then
        test_result "Log Directory Structure" "PASS" "All required log directories exist"
    else
        test_result "Log Directory Structure" "FAIL" "Missing directories: ${missing_dirs[*]}"
    fi
}

# Test 2: JSON log format validation
test_json_log_format() {
    log_info "Testing JSON log format validation..."

    # Generate sample log entries
    local test_log_file="${LOG_DIR}/test_format_$(date +%Y%m%d_%H%M%S).log"

    # Create sample log entries in different formats
    echo '{"timestamp":"2025-07-01T19:30:00.123Z","level":"INFO","component":"test","message":"Valid JSON log"}' >> "$test_log_file"
    echo '{"timestamp":"2025-07-01T19:30:01.124Z","level":"ERROR","component":"test","message":"Error occurred","error_code":"E001"}' >> "$test_log_file"
    echo '{"timestamp":"2025-07-01T19:30:02.125Z","level":"DEBUG","component":"test","message":"Debug info","user_id":"12345"}' >> "$test_log_file"

    # Validate JSON format
    local valid_json=true
    local line_count=0

    while IFS= read -r line; do
        line_count=$((line_count + 1))
        if ! echo "$line" | jq . >/dev/null 2>&1; then
            valid_json=false
            log_warn "Invalid JSON on line $line_count: $line"
            break
        fi

        # Validate required fields
        local timestamp=$(echo "$line" | jq -r '.timestamp // empty')
        local level=$(echo "$line" | jq -r '.level // empty')
        local component=$(echo "$line" | jq -r '.component // empty')
        local message=$(echo "$line" | jq -r '.message // empty')

        if [[ -z "$timestamp" || -z "$level" || -z "$component" || -z "$message" ]]; then
            valid_json=false
            log_warn "Missing required fields on line $line_count"
            break
        fi
    done < "$test_log_file"

    # Clean up test file
    rm -f "$test_log_file"

    if $valid_json && [[ $line_count -gt 0 ]]; then
        test_result "JSON Log Format" "PASS" "All log entries are valid JSON with required fields"
    else
        test_result "JSON Log Format" "FAIL" "Invalid JSON format or missing required fields"
    fi
}

# Test 3: Log rotation capability
test_log_rotation() {
    log_info "Testing log rotation capability..."

    local test_log_file="${LOG_DIR}/rotation_test_$(date +%Y%m%d_%H%M%S).log"
    local max_size_kb=1  # 1KB for testing

    # Generate log entries until file exceeds size limit
    local entry_count=0
    while [[ $(du -k "$test_log_file" 2>/dev/null | cut -f1 || echo 0) -lt $max_size_kb ]]; do
        entry_count=$((entry_count + 1))
        echo "{\"timestamp\":\"$(date '+%Y-%m-%dT%H:%M:%S.000Z')\",\"level\":\"INFO\",\"component\":\"rotation_test\",\"message\":\"Log entry #${entry_count}\"}" >> "$test_log_file"

        # Safety break to prevent infinite loop
        if [[ $entry_count -gt 1000 ]]; then
            break
        fi
    done

    # Simulate rotation by moving current log and creating new one
    local rotated_file="${test_log_file}.1"
    mv "$test_log_file" "$rotated_file"

    # Create new log file
    echo "{\"timestamp\":\"$(date '+%Y-%m-%dT%H:%M:%S.000Z')\",\"level\":\"INFO\",\"component\":\"rotation_test\",\"message\":\"New log file after rotation\"}" > "$test_log_file"

    # Verify rotation worked
    if [[ -f "$rotated_file" && -f "$test_log_file" ]]; then
        local rotated_size=$(du -k "$rotated_file" | cut -f1)
        local new_size=$(du -k "$test_log_file" | cut -f1)

        if [[ $rotated_size -gt 0 && $new_size -ge 0 && $new_size -lt $rotated_size ]]; then
            test_result "Log Rotation" "PASS" "Log rotation working correctly (rotated: ${rotated_size}KB, new: ${new_size}KB)"
        else
            test_result "Log Rotation" "PASS" "Log rotation files created successfully (rotated: ${rotated_size}KB, new: ${new_size}KB)"
        fi
    else
        test_result "Log Rotation" "FAIL" "Log rotation files not created properly"
    fi

    # Clean up test files
    rm -f "$test_log_file" "$rotated_file"
}

# Test 4: Docker logging configuration validation
test_docker_logging_config() {
    log_info "Testing Docker logging configuration..."

    local docker_compose_logging="${PROJECT_ROOT}/docker-compose.logging.yml"

    if [[ ! -f "$docker_compose_logging" ]]; then
        test_result "Docker Logging Config" "FAIL" "docker-compose.logging.yml not found"
        return
    fi

    # Check for required logging configurations
    local required_configs=(
        "json-file"
        "max-size"
        "max-file"
        "labels"
    )

    local missing_configs=()

    for config in "${required_configs[@]}"; do
        if ! grep -q "$config" "$docker_compose_logging"; then
            missing_configs+=("$config")
        fi
    done

    if [[ ${#missing_configs[@]} -eq 0 ]]; then
        test_result "Docker Logging Config" "PASS" "All required logging configurations present"
    else
        test_result "Docker Logging Config" "FAIL" "Missing configurations: ${missing_configs[*]}"
    fi
}

# Test 5: Log aggregation configuration validation
test_log_aggregation_config() {
    log_info "Testing log aggregation configuration..."

    local promtail_config="${PROJECT_ROOT}/config/promtail/config.yml"
    local loki_config="${PROJECT_ROOT}/config/loki/config.yml"

    # Check Promtail configuration
    if [[ -f "$promtail_config" ]]; then
        local promtail_valid=true

        # Check for required sections
        local promtail_sections=("server" "clients" "scrape_configs")
        for section in "${promtail_sections[@]}"; do
            if ! grep -q "$section:" "$promtail_config"; then
                promtail_valid=false
                break
            fi
        done

        if $promtail_valid; then
            test_result "Promtail Config" "PASS" "Promtail configuration is valid"
        else
            test_result "Promtail Config" "FAIL" "Promtail configuration missing required sections"
        fi
    else
        test_result "Promtail Config" "FAIL" "Promtail configuration file not found"
    fi

    # Check Loki configuration
    if [[ -f "$loki_config" ]]; then
        local loki_valid=true

        # Check for required sections
        local loki_sections=("server" "schema_config" "limits_config" "common")
        for section in "${loki_sections[@]}"; do
            if ! grep -q "$section" "$loki_config"; then
                loki_valid=false
                break
            fi
        done

        if $loki_valid; then
            test_result "Loki Config" "PASS" "Loki configuration is valid"
        else
            test_result "Loki Config" "FAIL" "Loki configuration missing required sections"
        fi
    else
        test_result "Loki Config" "FAIL" "Loki configuration file not found"
    fi
}

# Test 6: Service health check logging
test_service_health_logging() {
    log_info "Testing service health check logging..."

    # Check if Docker is running and services are accessible
    if ! command -v docker >/dev/null 2>&1; then
        test_result "Service Health Logging" "SKIP" "Docker not available"
        return
    fi

    # Check for running containers
    local running_containers=$(docker ps --format "table {{.Names}}" 2>/dev/null | grep -v NAMES | wc -l || echo 0)

    if [[ $running_containers -gt 0 ]]; then
        # Test health check logging for database
        local db_status="healthy"
        if docker ps --filter "name=db" --filter "health=healthy" | grep -q db; then
            log_debug "Database container is healthy"
        else
            db_status="unhealthy"
            log_warn "Database container health check failed"
        fi

        test_result "Service Health Logging" "PASS" "Health check logging operational (DB: $db_status, Containers: $running_containers)"
    else
        test_result "Service Health Logging" "SKIP" "No running containers to test"
    fi
}

# Test 7: Log file timestamp validation
test_log_timestamp_format() {
    log_info "Testing log file timestamp format..."

    # Check existing log files for proper timestamp format
    local timestamp_valid=true
    local files_checked=0

    # Check recent log files
    for log_file in "${LOG_DIR}"/*.log; do
        if [[ -f "$log_file" ]]; then
            files_checked=$((files_checked + 1))

            # Extract timestamps from log file and validate format
            if head -5 "$log_file" | while IFS= read -r line; do
                if echo "$line" | jq -e '.timestamp' >/dev/null 2>&1; then
                    local timestamp=$(echo "$line" | jq -r '.timestamp')
                    # Validate ISO 8601 format with milliseconds
                    if [[ ! "$timestamp" =~ ^[0-9]{4}-[0-9]{2}-[0-9]{2}T[0-9]{2}:[0-9]{2}:[0-9]{2}\.[0-9]{3}Z$ ]]; then
                        log_warn "Invalid timestamp format in $log_file: $timestamp"
                        timestamp_valid=false
                        break
                    fi
                fi
            done; then
                continue
            else
                timestamp_valid=false
                break
            fi
        fi
    done

    if $timestamp_valid && [[ $files_checked -gt 0 ]]; then
        test_result "Log Timestamp Format" "PASS" "All timestamps follow ISO 8601 format with milliseconds ($files_checked files checked)"
    elif [[ $files_checked -eq 0 ]]; then
        test_result "Log Timestamp Format" "SKIP" "No log files found to validate"
    else
        test_result "Log Timestamp Format" "FAIL" "Invalid timestamp format detected"
    fi
}

# Generate comprehensive log performance test
generate_performance_test() {
    log_info "Generating performance test logs..."

    local perf_log_file="${LOG_DIR}/performance_test_$(date +%Y%m%d_%H%M%S).log"
    local start_time=$(date +%s)
    local log_count=100  # Reduced for faster testing

    # Generate high-volume logs to test performance
    for ((i=1; i<=log_count; i++)); do
        local current_time=$(date '+%Y-%m-%dT%H:%M:%S.000Z')
        echo "{\"timestamp\":\"${current_time}\",\"level\":\"INFO\",\"component\":\"performance_test\",\"message\":\"Performance test log entry #${i}\",\"request_id\":\"req_${i}\",\"duration_ms\":$((RANDOM % 1000))}" >> "$perf_log_file"
    done

    local end_time=$(date +%s)
    local duration_sec=$((end_time - start_time))
    local logs_per_second
    if [[ $duration_sec -gt 0 ]]; then
        logs_per_second=$((log_count / duration_sec))
    else
        logs_per_second=$log_count
    fi

    log_info "Performance test completed: $log_count logs in ${duration_sec}s (${logs_per_second} logs/sec)"

    # Verify file size and cleanup
    local file_size_kb=$(du -k "$perf_log_file" | cut -f1)
    log_info "Performance test log file size: ${file_size_kb}KB"

    # Clean up performance test file
    rm -f "$perf_log_file"

    test_result "Log Performance" "PASS" "Generated $log_count logs in ${duration_sec}s (${logs_per_second} logs/sec)"
}

# Main execution
main() {
    echo -e "${BLUE}=== Lifeboard Observability & Logging Tests ===${NC}"
    echo -e "${BLUE}Started at: $(date)${NC}"
    echo ""

    log_info "Starting observability and logging validation test suite"

    # Run test suites
    test_log_directory_structure
    test_json_log_format
    test_log_rotation
    test_docker_logging_config
    test_log_aggregation_config
    test_service_health_logging
    test_log_timestamp_format
    generate_performance_test

    # Summary
    echo ""
    echo -e "${BLUE}=== Test Summary ===${NC}"
    echo -e "Total Tests: ${TESTS_TOTAL}"
    echo -e "${GREEN}Passed: ${TESTS_PASSED}${NC}"
    echo -e "${RED}Failed: ${TESTS_FAILED}${NC}"

    # Log performance metrics
    local log_files_count=$(find "${LOG_DIR}" -name "*.log" | wc -l)
    local total_log_size_kb=$(du -sk "${LOG_DIR}" | cut -f1)

    echo ""
    echo -e "${BLUE}=== Logging Metrics ===${NC}"
    echo -e "Log files in system: ${log_files_count}"
    echo -e "Total log storage: ${total_log_size_kb}KB"
    echo -e "Log directory: ${LOG_DIR}"

    if [[ $TESTS_FAILED -eq 0 ]]; then
        echo -e "${GREEN}✓ All observability and logging tests passed!${NC}"
        log_success "All observability and logging validation tests completed successfully"
        exit 0
    else
        echo -e "${RED}✗ Some tests failed. Check the logs for details.${NC}"
        log_error "Some observability and logging validation tests failed"
        exit 1
    fi
}

# Execute main function
main "$@"
