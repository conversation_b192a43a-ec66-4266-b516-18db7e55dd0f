#!/bin/bash

# Test: CRUD API Integration Tests
# Description: End-to-end CRUD operations via Supabase API to validate full workflow
# Author: AI Assistant
# Created: 2025-07-02
# Phase: 5 - Health & Tests

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/.." && pwd)"
LOG_DIR="${PROJECT_ROOT}/logs"
LOG_FILE="${LOG_DIR}/crud_integration_$(date +%Y%m%d_%H%M%S).log"

# Load environment variables
if [[ -f "${PROJECT_ROOT}/.env.local" ]]; then
    set -a
    source "${PROJECT_ROOT}/.env.local"
    set +a
fi

# API Configuration
API_URL="${SUPABASE_PUBLIC_URL:-http://localhost:8810}"
# Using environment variables directly for API keys
# ANON_KEY and SERVICE_ROLE_KEY are loaded from .env.local

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Ensure log directory exists
mkdir -p "${LOG_DIR}"

# Logging function with structured format
log() {
    local level="$1"
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%dT%H:%M:%S.000Z')
    local component="crud_integration"
    local session_id="$(date +%s)_$$"

    # Structured JSON log format
    echo "{\"timestamp\":\"${timestamp}\",\"level\":\"${level}\",\"component\":\"${component}\",\"session_id\":\"${session_id}\",\"message\":\"${message}\"}" | tee -a "${LOG_FILE}"
}

log_info() { log "INFO" "$@"; }
log_warn() { log "WARN" "$@"; }
log_error() { log "ERROR" "$@"; }
log_success() { log "SUCCESS" "$@"; }
log_debug() { log "DEBUG" "$@"; }

# Test counters
TESTS_TOTAL=0
TESTS_PASSED=0
TESTS_FAILED=0

# Test result tracking
test_result() {
    local test_name="$1"
    local result="$2"
    local message="$3"
    local duration="${4:-N/A}"

    TESTS_TOTAL=$((TESTS_TOTAL + 1))

    if [[ "$result" == "PASS" ]]; then
        TESTS_PASSED=$((TESTS_PASSED + 1))
        echo -e "${GREEN}✓ PASS${NC}: $test_name - $message ${duration:+(${duration}ms)}"
        log_success "TEST PASS: $test_name - $message - Duration: ${duration}ms"
    else
        TESTS_FAILED=$((TESTS_FAILED + 1))
        echo -e "${RED}✗ FAIL${NC}: $test_name - $message ${duration:+(${duration}ms)}"
        log_error "TEST FAIL: $test_name - $message - Duration: ${duration}ms"
    fi
}

# Utility function to measure command execution time
measure_time() {
    local start_time=$(date +%s%3N)
    "$@"
    local exit_code=$?
    local end_time=$(date +%s%3N)
    local duration=$((end_time - start_time))
    echo "$duration"
    return $exit_code
}

# Utility function for API calls
api_call() {
    local method="$1"
    local endpoint="$2"
    local data="${3:-}"
    local auth_key="${4:-$ANON_KEY}"

    local curl_args=(
        -X "$method"
        -H "Content-Type: application/json"
        -H "apikey: $auth_key"
        -H "Authorization: Bearer $auth_key"
        -s
        -w "%{http_code}"
    )

    if [[ -n "$data" ]]; then
        curl_args+=(-d "$data")
    fi

    curl "${curl_args[@]}" "$API_URL$endpoint"
}

# Test 1: API connectivity and authentication
test_api_connectivity() {
    log_info "Testing API connectivity and authentication..."

    # Test basic API connectivity
    local duration
    local response
    if response=$(measure_time api_call "GET" "/rest/v1/" "" "$ANON_KEY" 2>/dev/null); then
        local http_code="${response: -3}"
        if [[ "$http_code" =~ ^[23][0-9][0-9]$ ]]; then
            test_result "API Connectivity" "PASS" "API endpoint accessible (HTTP $http_code)" "$duration"
        else
            test_result "API Connectivity" "FAIL" "API returned HTTP $http_code" "$duration"
            return
        fi
    else
        test_result "API Connectivity" "FAIL" "Failed to connect to API" "$duration"
        return
    fi

    # Test service role authentication
    if response=$(measure_time api_call "GET" "/rest/v1/" "" "$SERVICE_ROLE_KEY" 2>/dev/null); then
        local http_code="${response: -3}"
        if [[ "$http_code" =~ ^[23][0-9][0-9]$ ]]; then
            test_result "Service Role Auth" "PASS" "Service role authentication working (HTTP $http_code)" "$duration"
        else
            test_result "Service Role Auth" "FAIL" "Service role auth returned HTTP $http_code" "$duration"
        fi
    else
        test_result "Service Role Auth" "FAIL" "Failed to authenticate with service role" "$duration"
    fi
}

# Test 2: User management CRUD operations
test_user_crud_operations() {
    log_info "Testing user management CRUD operations..."

    local test_email="test.crud.$(date +%s)@lifeboard.test"
    local test_password="TestPassword123!"
    local user_id=""

    # CREATE: Sign up a new user
    local duration
    local signup_data="{\"email\":\"$test_email\",\"password\":\"$test_password\"}"
    local response

    if response=$(measure_time api_call "POST" "/auth/v1/signup" "$signup_data" "$ANON_KEY" 2>/dev/null); then
        local http_code="${response: -3}"
        local response_body="${response%???}"

        if [[ "$http_code" =~ ^[23][0-9][0-9]$ ]]; then
            user_id=$(echo "$response_body" | jq -r '.user.id // empty' 2>/dev/null || echo "")
            if [[ -n "$user_id" ]]; then
                test_result "User Creation" "PASS" "User created successfully (ID: ${user_id:0:8}...)" "$duration"
                log_debug "Created user with ID: $user_id"
            else
                test_result "User Creation" "FAIL" "User created but no ID returned" "$duration"
                return
            fi
        else
            test_result "User Creation" "FAIL" "User creation failed (HTTP $http_code)" "$duration"
            return
        fi
    else
        test_result "User Creation" "FAIL" "Failed to call signup API" "$duration"
        return
    fi

    # READ: Verify user exists (using service role)
    if [[ -n "$user_id" ]]; then
        if response=$(measure_time api_call "GET" "/auth/v1/admin/users/$user_id" "" "$SERVICE_ROLE_KEY" 2>/dev/null); then
            local http_code="${response: -3}"
            local response_body="${response%???}"

            if [[ "$http_code" =~ ^[23][0-9][0-9]$ ]]; then
                local retrieved_email=$(echo "$response_body" | jq -r '.email // empty' 2>/dev/null || echo "")
                if [[ "$retrieved_email" == "$test_email" ]]; then
                    test_result "User Read" "PASS" "User retrieved successfully" "$duration"
                else
                    test_result "User Read" "FAIL" "Retrieved user email mismatch" "$duration"
                fi
            else
                test_result "User Read" "FAIL" "User retrieval failed (HTTP $http_code)" "$duration"
            fi
        else
            test_result "User Read" "FAIL" "Failed to call user retrieval API" "$duration"
        fi

        # DELETE: Clean up test user
        if response=$(measure_time api_call "DELETE" "/auth/v1/admin/users/$user_id" "" "$SERVICE_ROLE_KEY" 2>/dev/null); then
            local http_code="${response: -3}"
            if [[ "$http_code" =~ ^[23][0-9][0-9]$ ]]; then
                test_result "User Deletion" "PASS" "User deleted successfully" "$duration"
                log_debug "Cleaned up test user: $user_id"
            else
                test_result "User Deletion" "FAIL" "User deletion failed (HTTP $http_code)" "$duration"
            fi
        else
            test_result "User Deletion" "FAIL" "Failed to call user deletion API" "$duration"
        fi
    fi
}

# Test 3: Tags CRUD operations
test_tags_crud_operations() {
    log_info "Testing tags CRUD operations..."

    local test_tag_name="test-crud-tag-$(date +%s)"
    local test_tag_color="#FF5733"
    local tag_id=""

    # CREATE: Insert a new tag
    local duration
    local tag_data="{\"name\":\"$test_tag_name\",\"color\":\"$test_tag_color\",\"description\":\"Test tag for CRUD operations\"}"
    local response

    if response=$(measure_time api_call "POST" "/rest/v1/tags" "$tag_data" "$SERVICE_ROLE_KEY" 2>/dev/null); then
        local http_code="${response: -3}"
        local response_body="${response%???}"

        if [[ "$http_code" =~ ^[23][0-9][0-9]$ ]]; then
            tag_id=$(echo "$response_body" | jq -r '.[0].id // .id // empty' 2>/dev/null || echo "")
            if [[ -n "$tag_id" ]]; then
                test_result "Tag Creation" "PASS" "Tag created successfully (ID: $tag_id)" "$duration"
                log_debug "Created tag with ID: $tag_id"
            else
                test_result "Tag Creation" "FAIL" "Tag created but no ID returned" "$duration"
                return
            fi
        else
            test_result "Tag Creation" "FAIL" "Tag creation failed (HTTP $http_code)" "$duration"
            return
        fi
    else
        test_result "Tag Creation" "FAIL" "Failed to call tag creation API" "$duration"
        return
    fi

    # READ: Retrieve the created tag
    if [[ -n "$tag_id" ]]; then
        if response=$(measure_time api_call "GET" "/rest/v1/tags?id=eq.$tag_id" "" "$SERVICE_ROLE_KEY" 2>/dev/null); then
            local http_code="${response: -3}"
            local response_body="${response%???}"

            if [[ "$http_code" =~ ^[23][0-9][0-9]$ ]]; then
                local retrieved_name=$(echo "$response_body" | jq -r '.[0].name // empty' 2>/dev/null || echo "")
                if [[ "$retrieved_name" == "$test_tag_name" ]]; then
                    test_result "Tag Read" "PASS" "Tag retrieved successfully" "$duration"
                else
                    test_result "Tag Read" "FAIL" "Retrieved tag name mismatch" "$duration"
                fi
            else
                test_result "Tag Read" "FAIL" "Tag retrieval failed (HTTP $http_code)" "$duration"
            fi
        else
            test_result "Tag Read" "FAIL" "Failed to call tag retrieval API" "$duration"
        fi

        # UPDATE: Modify the tag
        local updated_description="Updated test tag description"
        local update_data="{\"description\":\"$updated_description\"}"

        if response=$(measure_time api_call "PATCH" "/rest/v1/tags?id=eq.$tag_id" "$update_data" "$SERVICE_ROLE_KEY" 2>/dev/null); then
            local http_code="${response: -3}"
            if [[ "$http_code" =~ ^[23][0-9][0-9]$ ]]; then
                test_result "Tag Update" "PASS" "Tag updated successfully" "$duration"
            else
                test_result "Tag Update" "FAIL" "Tag update failed (HTTP $http_code)" "$duration"
            fi
        else
            test_result "Tag Update" "FAIL" "Failed to call tag update API" "$duration"
        fi

        # DELETE: Remove the test tag
        if response=$(measure_time api_call "DELETE" "/rest/v1/tags?id=eq.$tag_id" "" "$SERVICE_ROLE_KEY" 2>/dev/null); then
            local http_code="${response: -3}"
            if [[ "$http_code" =~ ^[23][0-9][0-9]$ ]]; then
                test_result "Tag Deletion" "PASS" "Tag deleted successfully" "$duration"
                log_debug "Cleaned up test tag: $tag_id"
            else
                test_result "Tag Deletion" "FAIL" "Tag deletion failed (HTTP $http_code)" "$duration"
            fi
        else
            test_result "Tag Deletion" "FAIL" "Failed to call tag deletion API" "$duration"
        fi
    fi
}

# Test 4: Posts CRUD operations
test_posts_crud_operations() {
    log_info "Testing posts CRUD operations..."

    # First, create a test user for the posts
    local test_email="test.posts.$(date +%s)@lifeboard.test"
    local test_password="TestPassword123!"
    local user_id=""
    local post_id=""

    # Create test user
    local signup_data="{\"email\":\"$test_email\",\"password\":\"$test_password\"}"
    local response

    if response=$(api_call "POST" "/auth/v1/signup" "$signup_data" "$ANON_KEY" 2>/dev/null); then
        local http_code="${response: -3}"
        local response_body="${response%???}"

        if [[ "$http_code" =~ ^[23][0-9][0-9]$ ]]; then
            user_id=$(echo "$response_body" | jq -r '.user.id // empty' 2>/dev/null || echo "")
            log_debug "Created test user for posts: $user_id"
        else
            test_result "Posts Test Setup" "FAIL" "Failed to create test user for posts"
            return
        fi
    else
        test_result "Posts Test Setup" "FAIL" "Failed to call signup API for posts test"
        return
    fi

    if [[ -z "$user_id" ]]; then
        test_result "Posts Test Setup" "FAIL" "No user ID returned for posts test"
        return
    fi

    # CREATE: Insert a new post
    local duration
    local post_data="{\"user_id\":\"$user_id\",\"title\":\"Test CRUD Post\",\"content\":\"This is a test post for CRUD operations\",\"post_type\":\"manual\"}"

    if response=$(measure_time api_call "POST" "/rest/v1/posts" "$post_data" "$SERVICE_ROLE_KEY" 2>/dev/null); then
        local http_code="${response: -3}"
        local response_body="${response%???}"

        if [[ "$http_code" =~ ^[23][0-9][0-9]$ ]]; then
            post_id=$(echo "$response_body" | jq -r '.[0].id // .id // empty' 2>/dev/null || echo "")
            if [[ -n "$post_id" ]]; then
                test_result "Post Creation" "PASS" "Post created successfully (ID: $post_id)" "$duration"
                log_debug "Created post with ID: $post_id"
            else
                test_result "Post Creation" "FAIL" "Post created but no ID returned" "$duration"
            fi
        else
            test_result "Post Creation" "FAIL" "Post creation failed (HTTP $http_code)" "$duration"
        fi
    else
        test_result "Post Creation" "FAIL" "Failed to call post creation API" "$duration"
    fi

    # READ: Retrieve the created post
    if [[ -n "$post_id" ]]; then
        if response=$(measure_time api_call "GET" "/rest/v1/posts?id=eq.$post_id" "" "$SERVICE_ROLE_KEY" 2>/dev/null); then
            local http_code="${response: -3}"
            local response_body="${response%???}"

            if [[ "$http_code" =~ ^[23][0-9][0-9]$ ]]; then
                local retrieved_title=$(echo "$response_body" | jq -r '.[0].title // empty' 2>/dev/null || echo "")
                if [[ "$retrieved_title" == "Test CRUD Post" ]]; then
                    test_result "Post Read" "PASS" "Post retrieved successfully" "$duration"
                else
                    test_result "Post Read" "FAIL" "Retrieved post title mismatch" "$duration"
                fi
            else
                test_result "Post Read" "FAIL" "Post retrieval failed (HTTP $http_code)" "$duration"
            fi
        else
            test_result "Post Read" "FAIL" "Failed to call post retrieval API" "$duration"
        fi

        # UPDATE: Modify the post
        local updated_content="Updated test post content"
        local update_data="{\"content\":\"$updated_content\"}"

        if response=$(measure_time api_call "PATCH" "/rest/v1/posts?id=eq.$post_id" "$update_data" "$SERVICE_ROLE_KEY" 2>/dev/null); then
            local http_code="${response: -3}"
            if [[ "$http_code" =~ ^[23][0-9][0-9]$ ]]; then
                test_result "Post Update" "PASS" "Post updated successfully" "$duration"
            else
                test_result "Post Update" "FAIL" "Post update failed (HTTP $http_code)" "$duration"
            fi
        else
            test_result "Post Update" "FAIL" "Failed to call post update API" "$duration"
        fi

        # DELETE: Remove the test post
        if response=$(measure_time api_call "DELETE" "/rest/v1/posts?id=eq.$post_id" "" "$SERVICE_ROLE_KEY" 2>/dev/null); then
            local http_code="${response: -3}"
            if [[ "$http_code" =~ ^[23][0-9][0-9]$ ]]; then
                test_result "Post Deletion" "PASS" "Post deleted successfully" "$duration"
                log_debug "Cleaned up test post: $post_id"
            else
                test_result "Post Deletion" "FAIL" "Post deletion failed (HTTP $http_code)" "$duration"
            fi
        else
            test_result "Post Deletion" "FAIL" "Failed to call post deletion API" "$duration"
        fi
    fi

    # Clean up test user
    if [[ -n "$user_id" ]]; then
        api_call "DELETE" "/auth/v1/admin/users/$user_id" "" "$SERVICE_ROLE_KEY" >/dev/null 2>&1
        log_debug "Cleaned up test user: $user_id"
    fi
}

# Test 5: Plugin configuration CRUD operations
test_plugins_crud_operations() {
    log_info "Testing plugin configuration CRUD operations..."

    local test_plugin_name="test-crud-plugin-$(date +%s)"
    local plugin_id=""

    # CREATE: Insert a new plugin configuration
    local duration
    local plugin_data="{\"name\":\"$test_plugin_name\",\"description\":\"Test plugin for CRUD operations\",\"is_enabled\":true,\"configuration\":{\"test_key\":\"test_value\"}}"
    local response

    if response=$(measure_time api_call "POST" "/rest/v1/plugins" "$plugin_data" "$SERVICE_ROLE_KEY" 2>/dev/null); then
        local http_code="${response: -3}"
        local response_body="${response%???}"

        if [[ "$http_code" =~ ^[23][0-9][0-9]$ ]]; then
            plugin_id=$(echo "$response_body" | jq -r '.[0].id // .id // empty' 2>/dev/null || echo "")
            if [[ -n "$plugin_id" ]]; then
                test_result "Plugin Creation" "PASS" "Plugin created successfully (ID: $plugin_id)" "$duration"
                log_debug "Created plugin with ID: $plugin_id"
            else
                test_result "Plugin Creation" "FAIL" "Plugin created but no ID returned" "$duration"
                return
            fi
        else
            test_result "Plugin Creation" "FAIL" "Plugin creation failed (HTTP $http_code)" "$duration"
            return
        fi
    else
        test_result "Plugin Creation" "FAIL" "Failed to call plugin creation API" "$duration"
        return
    fi

    # READ: Retrieve the created plugin
    if [[ -n "$plugin_id" ]]; then
        if response=$(measure_time api_call "GET" "/rest/v1/plugins?id=eq.$plugin_id" "" "$SERVICE_ROLE_KEY" 2>/dev/null); then
            local http_code="${response: -3}"
            local response_body="${response%???}"

            if [[ "$http_code" =~ ^[23][0-9][0-9]$ ]]; then
                local retrieved_name=$(echo "$response_body" | jq -r '.[0].name // empty' 2>/dev/null || echo "")
                if [[ "$retrieved_name" == "$test_plugin_name" ]]; then
                    test_result "Plugin Read" "PASS" "Plugin retrieved successfully" "$duration"
                else
                    test_result "Plugin Read" "FAIL" "Retrieved plugin name mismatch" "$duration"
                fi
            else
                test_result "Plugin Read" "FAIL" "Plugin retrieval failed (HTTP $http_code)" "$duration"
            fi
        else
            test_result "Plugin Read" "FAIL" "Failed to call plugin retrieval API" "$duration"
        fi

        # UPDATE: Modify the plugin configuration
        local update_data="{\"is_enabled\":false,\"configuration\":{\"test_key\":\"updated_value\",\"new_key\":\"new_value\"}}"

        if response=$(measure_time api_call "PATCH" "/rest/v1/plugins?id=eq.$plugin_id" "$update_data" "$SERVICE_ROLE_KEY" 2>/dev/null); then
            local http_code="${response: -3}"
            if [[ "$http_code" =~ ^[23][0-9][0-9]$ ]]; then
                test_result "Plugin Update" "PASS" "Plugin updated successfully" "$duration"
            else
                test_result "Plugin Update" "FAIL" "Plugin update failed (HTTP $http_code)" "$duration"
            fi
        else
            test_result "Plugin Update" "FAIL" "Failed to call plugin update API" "$duration"
        fi

        # DELETE: Remove the test plugin
        if response=$(measure_time api_call "DELETE" "/rest/v1/plugins?id=eq.$plugin_id" "" "$SERVICE_ROLE_KEY" 2>/dev/null); then
            local http_code="${response: -3}"
            if [[ "$http_code" =~ ^[23][0-9][0-9]$ ]]; then
                test_result "Plugin Deletion" "PASS" "Plugin deleted successfully" "$duration"
                log_debug "Cleaned up test plugin: $plugin_id"
            else
                test_result "Plugin Deletion" "FAIL" "Plugin deletion failed (HTTP $http_code)" "$duration"
            fi
        else
            test_result "Plugin Deletion" "FAIL" "Failed to call plugin deletion API" "$duration"
        fi
    fi
}

# Test 6: Complex workflow integration
test_complex_workflow() {
    log_info "Testing complex workflow integration..."

    # This test simulates a complete user workflow:
    # 1. Create user
    # 2. Create tag
    # 3. Create post
    # 4. Associate post with tag
    # 5. Retrieve complete data
    # 6. Clean up

    local test_email="test.workflow.$(date +%s)@lifeboard.test"
    local test_password="TestPassword123!"
    local user_id=""
    local tag_id=""
    local post_id=""

    # Step 1: Create user
    local signup_data="{\"email\":\"$test_email\",\"password\":\"$test_password\"}"
    local response

    if response=$(api_call "POST" "/auth/v1/signup" "$signup_data" "$ANON_KEY" 2>/dev/null); then
        local http_code="${response: -3}"
        local response_body="${response%???}"

        if [[ "$http_code" =~ ^[23][0-9][0-9]$ ]]; then
            user_id=$(echo "$response_body" | jq -r '.user.id // empty' 2>/dev/null || echo "")
            log_debug "Workflow: Created user $user_id"
        else
            test_result "Complex Workflow" "FAIL" "Failed to create user for workflow test"
            return
        fi
    else
        test_result "Complex Workflow" "FAIL" "Failed to call signup API for workflow test"
        return
    fi

    # Step 2: Create tag
    local tag_data="{\"name\":\"workflow-tag-$(date +%s)\",\"color\":\"#00FF00\",\"description\":\"Workflow test tag\"}"

    if response=$(api_call "POST" "/rest/v1/tags" "$tag_data" "$SERVICE_ROLE_KEY" 2>/dev/null); then
        local http_code="${response: -3}"
        local response_body="${response%???}"

        if [[ "$http_code" =~ ^[23][0-9][0-9]$ ]]; then
            tag_id=$(echo "$response_body" | jq -r '.[0].id // .id // empty' 2>/dev/null || echo "")
            log_debug "Workflow: Created tag $tag_id"
        else
            test_result "Complex Workflow" "FAIL" "Failed to create tag for workflow test"
            return
        fi
    fi

    # Step 3: Create post
    local post_data="{\"user_id\":\"$user_id\",\"title\":\"Workflow Test Post\",\"content\":\"This post is part of a complex workflow test\",\"post_type\":\"manual\"}"

    if response=$(api_call "POST" "/rest/v1/posts" "$post_data" "$SERVICE_ROLE_KEY" 2>/dev/null); then
        local http_code="${response: -3}"
        local response_body="${response%???}"

        if [[ "$http_code" =~ ^[23][0-9][0-9]$ ]]; then
            post_id=$(echo "$response_body" | jq -r '.[0].id // .id // empty' 2>/dev/null || echo "")
            log_debug "Workflow: Created post $post_id"
        else
            test_result "Complex Workflow" "FAIL" "Failed to create post for workflow test"
            return
        fi
    fi

    # Step 4: Associate post with tag (if post_tags table exists)
    if [[ -n "$post_id" && -n "$tag_id" ]]; then
        local association_data="{\"post_id\":\"$post_id\",\"tag_id\":\"$tag_id\"}"

        # This might fail if post_tags table doesn't exist, which is okay
        api_call "POST" "/rest/v1/post_tags" "$association_data" "$SERVICE_ROLE_KEY" >/dev/null 2>&1
        log_debug "Workflow: Attempted to associate post with tag"
    fi

    # Step 5: Verify complete workflow
    local duration
    if response=$(measure_time api_call "GET" "/rest/v1/posts?id=eq.$post_id&select=*,users(email)" "" "$SERVICE_ROLE_KEY" 2>/dev/null); then
        local http_code="${response: -3}"
        local response_body="${response%???}"

        if [[ "$http_code" =~ ^[23][0-9][0-9]$ ]]; then
            local post_count=$(echo "$response_body" | jq '. | length' 2>/dev/null || echo "0")
            if [[ "$post_count" == "1" ]]; then
                test_result "Complex Workflow" "PASS" "Complete workflow executed successfully" "$duration"
            else
                test_result "Complex Workflow" "FAIL" "Workflow verification failed - post not found" "$duration"
            fi
        else
            test_result "Complex Workflow" "FAIL" "Workflow verification failed (HTTP $http_code)" "$duration"
        fi
    else
        test_result "Complex Workflow" "FAIL" "Failed to verify workflow" "$duration"
    fi

    # Step 6: Clean up
    if [[ -n "$post_id" ]]; then
        api_call "DELETE" "/rest/v1/posts?id=eq.$post_id" "" "$SERVICE_ROLE_KEY" >/dev/null 2>&1
        log_debug "Workflow: Cleaned up post $post_id"
    fi

    if [[ -n "$tag_id" ]]; then
        api_call "DELETE" "/rest/v1/tags?id=eq.$tag_id" "" "$SERVICE_ROLE_KEY" >/dev/null 2>&1
        log_debug "Workflow: Cleaned up tag $tag_id"
    fi

    if [[ -n "$user_id" ]]; then
        api_call "DELETE" "/auth/v1/admin/users/$user_id" "" "$SERVICE_ROLE_KEY" >/dev/null 2>&1
        log_debug "Workflow: Cleaned up user $user_id"
    fi
}

# Main execution
main() {
    echo -e "${BLUE}=== Lifeboard CRUD API Integration Tests ===${NC}"
    echo -e "${BLUE}Started at: $(date)${NC}"
    echo ""

    # Check prerequisites
    if [[ -z "$ANON_KEY" || -z "$SERVICE_ROLE_KEY" ]]; then
        echo -e "${RED}ERROR: API keys not configured. Please check .env.local file.${NC}"
        log_error "API keys not configured in environment"
        exit 1
    fi

    log_info "Starting Phase 5 CRUD API integration test suite"
    log_info "API URL: $API_URL"

    # Run test suites
    test_api_connectivity
    test_user_crud_operations
    test_tags_crud_operations
    test_posts_crud_operations
    test_plugins_crud_operations
    test_complex_workflow

    # Summary
    echo ""
    echo -e "${BLUE}=== Test Summary ===${NC}"
    echo -e "Total Tests: ${TESTS_TOTAL}"
    echo -e "${GREEN}Passed: ${TESTS_PASSED}${NC}"
    echo -e "${RED}Failed: ${TESTS_FAILED}${NC}"

    if [[ $TESTS_FAILED -eq 0 ]]; then
        echo ""
        echo -e "${GREEN}✓ All CRUD API integration tests passed!${NC}"
        echo -e "${GREEN}✓ Create, Read, Update, Delete operations working${NC}"
        echo -e "${GREEN}✓ User management workflows functional${NC}"
        echo -e "${GREEN}✓ Complex workflows executing successfully${NC}"
        log_success "All CRUD API integration tests completed successfully"
        exit 0
    else
        echo ""
        echo -e "${RED}✗ Some CRUD API tests failed. API may not be fully functional.${NC}"
        echo -e "${YELLOW}Please review failed tests and check API endpoints before proceeding${NC}"
        log_error "Some CRUD API integration tests failed"
        exit 1
    fi
}

# Execute main function
main "$@"
