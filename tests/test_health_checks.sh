#!/bin/bash

# Test: Health Checks and Service Monitoring
# Description: Validates that all services are healthy and responding correctly
# Author: AI Assistant
# Created: 2025-07-02
# Phase: 5 - Health & Tests

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/.." && pwd)"
LOG_DIR="${PROJECT_ROOT}/logs"
LOG_FILE="${LOG_DIR}/health_checks_$(date +%Y%m%d_%H%M%S).log"

# Load environment variables
if [[ -f "${PROJECT_ROOT}/.env.local" ]]; then
    set -a
    source "${PROJECT_ROOT}/.env.local"
    set +a
fi

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Ensure log directory exists
mkdir -p "${LOG_DIR}"

# Logging function with structured format
log() {
    local level="$1"
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%dT%H:%M:%S.000Z')
    local component="health_checks"
    local session_id="$(date +%s)_$$"

    # Structured JSON log format
    echo "{\"timestamp\":\"${timestamp}\",\"level\":\"${level}\",\"component\":\"${component}\",\"session_id\":\"${session_id}\",\"message\":\"${message}\"}" | tee -a "${LOG_FILE}"
}

log_info() { log "INFO" "$@"; }
log_warn() { log "WARN" "$@"; }
log_error() { log "ERROR" "$@"; }
log_success() { log "SUCCESS" "$@"; }
log_debug() { log "DEBUG" "$@"; }

# Test counters
TESTS_TOTAL=0
TESTS_PASSED=0
TESTS_FAILED=0

# Test result tracking
test_result() {
    local test_name="$1"
    local result="$2"
    local message="$3"
    local duration="${4:-N/A}"

    TESTS_TOTAL=$((TESTS_TOTAL + 1))

    if [[ "$result" == "PASS" ]]; then
        TESTS_PASSED=$((TESTS_PASSED + 1))
        echo -e "${GREEN}✓ PASS${NC}: $test_name - $message ${duration:+(${duration}ms)}"
        log_success "TEST PASS: $test_name - $message - Duration: ${duration}ms"
    else
        TESTS_FAILED=$((TESTS_FAILED + 1))
        echo -e "${RED}✗ FAIL${NC}: $test_name - $message ${duration:+(${duration}ms)}"
        log_error "TEST FAIL: $test_name - $message - Duration: ${duration}ms"
    fi
}

# Utility function to measure command execution time
measure_time() {
    local start_time=$(date +%s%3N)
    "$@"
    local exit_code=$?
    local end_time=$(date +%s%3N)
    local duration=$((end_time - start_time))
    echo "$duration"
    return $exit_code
}

# Test 1: Docker health check status validation
test_docker_health_status() {
    log_info "Testing Docker container health status..."

    if ! command -v docker >/dev/null 2>&1; then
        test_result "Docker Health Status" "SKIP" "Docker not available"
        return
    fi

    # Expected healthy services
    local expected_services=("db" "auth" "realtime" "rest" "storage" "studio")
    local healthy_services=()
    local unhealthy_services=()

    for service in "${expected_services[@]}"; do
        local health_status=$(docker inspect --format='{{.State.Health.Status}}' "lifeboard-${service}-1" 2>/dev/null || echo "not_found")

        case "$health_status" in
            "healthy")
                healthy_services+=("$service")
                log_debug "Service $service is healthy"
                ;;
            "unhealthy")
                unhealthy_services+=("$service")
                log_warn "Service $service is unhealthy"
                ;;
            "starting")
                log_warn "Service $service is still starting"
                unhealthy_services+=("$service")
                ;;
            "not_found")
                log_warn "Service $service container not found"
                unhealthy_services+=("$service")
                ;;
        esac
    done

    local healthy_count=${#healthy_services[@]}
    local total_expected=${#expected_services[@]}

    if [[ $healthy_count -eq $total_expected ]]; then
        test_result "Docker Health Status" "PASS" "All $total_expected services are healthy"
    else
        local unhealthy_list="${unhealthy_services[*]:-none}"
        test_result "Docker Health Status" "FAIL" "Only $healthy_count/$total_expected services healthy. Unhealthy: $unhealthy_list"
    fi
}

# Test 2: Database connectivity and health
test_database_health() {
    log_info "Testing database connectivity and health..."

    local db_host="${POSTGRES_HOST:-localhost}"
    local db_port="${POSTGRES_PORT:-5432}"
    local db_user="${POSTGRES_USER:-supabase_admin}"
    local db_name="${POSTGRES_DB:-postgres}"
    local db_password="${POSTGRES_PASSWORD}"

    if [[ -z "$db_password" ]]; then
        test_result "Database Health" "FAIL" "Database password not set in environment"
        return
    fi

    # Test basic connectivity
    local duration
    if duration=$(measure_time timeout 10s psql -h "$db_host" -p "$db_port" -U "$db_user" -d "$db_name" -c "SELECT 1;" >/dev/null 2>&1); then
        test_result "Database Connectivity" "PASS" "Database connection successful" "$duration"
    else
        test_result "Database Connectivity" "FAIL" "Database connection failed" "$duration"
        return
    fi

    # Test database health metrics
    local health_query="
    SELECT
        'connections' as metric,
        count(*) as value
    FROM pg_stat_activity
    UNION ALL
    SELECT
        'database_size' as metric,
        pg_size_pretty(pg_database_size('$db_name'))::text as value
    UNION ALL
    SELECT
        'uptime' as metric,
        extract(epoch from now() - pg_postmaster_start_time())::text as value;
    "

    if duration=$(measure_time timeout 10s psql -h "$db_host" -p "$db_port" -U "$db_user" -d "$db_name" -c "$health_query" >/dev/null 2>&1); then
        test_result "Database Health Metrics" "PASS" "Health metrics retrieved successfully" "$duration"
    else
        test_result "Database Health Metrics" "FAIL" "Failed to retrieve health metrics" "$duration"
    fi
}

# Test 3: API endpoint health checks
test_api_endpoints_health() {
    log_info "Testing API endpoint health..."

    local base_url="${SUPABASE_PUBLIC_URL:-http://localhost:8810}"

    # Test REST API health
    local duration
    if duration=$(measure_time timeout 10s curl -sf "$base_url/" >/dev/null 2>&1); then
        test_result "REST API Health" "PASS" "REST API responding" "$duration"
    else
        test_result "REST API Health" "FAIL" "REST API not responding" "$duration"
    fi

    # Test authentication endpoint
    if duration=$(measure_time timeout 10s curl -sf "$base_url/auth/v1/settings" >/dev/null 2>&1); then
        test_result "Auth API Health" "PASS" "Auth API responding" "$duration"
    else
        test_result "Auth API Health" "FAIL" "Auth API not responding" "$duration"
    fi

    # Test realtime endpoint (WebSocket upgrade endpoint)
    if duration=$(measure_time timeout 10s curl -sf "$base_url/realtime/v1/" >/dev/null 2>&1); then
        test_result "Realtime API Health" "PASS" "Realtime API responding" "$duration"
    else
        test_result "Realtime API Health" "FAIL" "Realtime API not responding" "$duration"
    fi

    # Test storage endpoint
    if duration=$(measure_time timeout 10s curl -sf "$base_url/storage/v1/healthcheck" >/dev/null 2>&1); then
        test_result "Storage API Health" "PASS" "Storage API responding" "$duration"
    else
        test_result "Storage API Health" "FAIL" "Storage API not responding" "$duration"
    fi
}

# Test 4: Service dependency chain validation
test_service_dependencies() {
    log_info "Testing service dependency chain..."

    # Test that services are started in correct order and dependencies are met
    local services_order=("db" "auth" "rest" "realtime" "storage" "studio")
    local dependency_valid=true

    for service in "${services_order[@]}"; do
        local container_name="lifeboard-${service}-1"
        local start_time=$(docker inspect --format='{{.State.StartedAt}}' "$container_name" 2>/dev/null || echo "")

        if [[ -n "$start_time" ]]; then
            log_debug "Service $service started at: $start_time"
        else
            log_warn "Could not get start time for service: $service"
            dependency_valid=false
        fi
    done

    # Check if all required services are running
    local running_count=$(docker ps --filter "label=com.docker.compose.project=lifeboard" --format "{{.Names}}" | wc -l)

    if [[ $running_count -ge 6 ]] && $dependency_valid; then
        test_result "Service Dependencies" "PASS" "All services started in correct dependency order ($running_count services running)"
    else
        test_result "Service Dependencies" "FAIL" "Service dependency issues detected ($running_count services running)"
    fi
}

# Test 5: Resource usage monitoring
test_resource_usage() {
    log_info "Testing resource usage and limits..."

    local total_memory_mb=0
    local total_cpu_percent=0
    local container_count=0

    # Get resource usage for all containers
    while IFS= read -r line; do
        if [[ -n "$line" ]]; then
            container_count=$((container_count + 1))
            local memory_usage=$(echo "$line" | awk '{print $4}' | sed 's/MiB//')
            local cpu_percent=$(echo "$line" | awk '{print $3}' | sed 's/%//')

            total_memory_mb=$(echo "$total_memory_mb + $memory_usage" | bc -l 2>/dev/null || echo "$total_memory_mb")
            total_cpu_percent=$(echo "$total_cpu_percent + $cpu_percent" | bc -l 2>/dev/null || echo "$total_cpu_percent")
        fi
    done < <(docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}" --filter "label=com.docker.compose.project=lifeboard" 2>/dev/null | tail -n +2)

    if [[ $container_count -gt 0 ]]; then
        local avg_cpu=$(echo "scale=2; $total_cpu_percent / $container_count" | bc -l 2>/dev/null || echo "0")
        test_result "Resource Usage" "PASS" "Monitoring $container_count containers - Total Memory: ${total_memory_mb}MB, Avg CPU: ${avg_cpu}%"

        # Log resource usage for monitoring
        log_info "Resource usage: containers=$container_count, total_memory=${total_memory_mb}MB, avg_cpu=${avg_cpu}%"
    else
        test_result "Resource Usage" "FAIL" "No containers found for resource monitoring"
    fi
}

# Test 6: Health check timeout validation
test_health_check_timeouts() {
    log_info "Testing health check timeout configurations..."

    local services=("db" "auth" "realtime" "rest" "storage" "studio")
    local timeout_valid=true

    for service in "${services[@]}"; do
        local container_name="lifeboard-${service}-1"
        local health_config=$(docker inspect --format='{{json .Config.Healthcheck}}' "$container_name" 2>/dev/null || echo "{}")

        if [[ "$health_config" != "{}" ]] && [[ "$health_config" != "null" ]]; then
            log_debug "Service $service has health check configured"
        else
            log_warn "Service $service missing health check configuration"
            timeout_valid=false
        fi
    done

    if $timeout_valid; then
        test_result "Health Check Timeouts" "PASS" "All services have health check configurations"
    else
        test_result "Health Check Timeouts" "FAIL" "Some services missing health check configurations"
    fi
}

# Test 7: Service recovery testing
test_service_recovery() {
    log_info "Testing service recovery capabilities..."

    # This test checks if services can recover from brief interruptions
    # For safety, we'll just verify the restart policies are configured

    local services=("db" "auth" "realtime" "rest" "storage" "studio")
    local recovery_ready=true

    for service in "${services[@]}"; do
        local container_name="lifeboard-${service}-1"
        local restart_policy=$(docker inspect --format='{{.HostConfig.RestartPolicy.Name}}' "$container_name" 2>/dev/null || echo "no")

        if [[ "$restart_policy" != "no" ]]; then
            log_debug "Service $service has restart policy: $restart_policy"
        else
            log_warn "Service $service has no restart policy configured"
            recovery_ready=false
        fi
    done

    if $recovery_ready; then
        test_result "Service Recovery" "PASS" "All services have appropriate restart policies"
    else
        test_result "Service Recovery" "FAIL" "Some services lack restart policies"
    fi
}

# Main execution
main() {
    echo -e "${BLUE}=== Lifeboard Health Checks & Service Monitoring ===${NC}"
    echo -e "${BLUE}Started at: $(date)${NC}"
    echo ""

    log_info "Starting Phase 5 health checks and service monitoring test suite"

    # Run test suites
    test_docker_health_status
    test_database_health
    test_api_endpoints_health
    test_service_dependencies
    test_resource_usage
    test_health_check_timeouts
    test_service_recovery

    # Summary
    echo ""
    echo -e "${BLUE}=== Test Summary ===${NC}"
    echo -e "Total Tests: ${TESTS_TOTAL}"
    echo -e "${GREEN}Passed: ${TESTS_PASSED}${NC}"
    echo -e "${RED}Failed: ${TESTS_FAILED}${NC}"

    # Health metrics summary
    echo ""
    echo -e "${BLUE}=== Health Status Summary ===${NC}"
    if command -v docker >/dev/null 2>&1; then
        echo -e "${YELLOW}Container Health Status:${NC}"
        docker ps --filter "label=com.docker.compose.project=lifeboard" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" || true
    fi

    if [[ $TESTS_FAILED -eq 0 ]]; then
        echo ""
        echo -e "${GREEN}✓ All health checks passed!${NC}"
        echo -e "${GREEN}✓ All services are healthy and responsive${NC}"
        echo -e "${GREEN}✓ Resource usage within acceptable limits${NC}"
        echo -e "${GREEN}✓ Service dependencies properly configured${NC}"
        log_success "All health checks and service monitoring tests completed successfully"
        exit 0
    else
        echo ""
        echo -e "${RED}✗ Some health checks failed. System may not be fully operational.${NC}"
        echo -e "${YELLOW}Please review failed tests and check service status before proceeding${NC}"
        log_error "Some health checks and service monitoring tests failed"
        exit 1
    fi
}

# Execute main function
main "$@"
