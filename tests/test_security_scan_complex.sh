#!/bin/bash

# Test: Security Scanning and Analysis
# Description: Scans codebase for security vulnerabilities, leaked secrets, and misconfigurations
# Author: AI Assistant
# Created: 2025-07-01

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/.." && pwd)"
LOG_DIR="${PROJECT_ROOT}/logs"
LOG_FILE="${LOG_DIR}/security_scan_$(date +%Y%m%d_%H%M%S).log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Ensure log directory exists
mkdir -p "${LOG_DIR}"

# Logging function
log() {
    local level="$1"
    shift
    local message="$*"
    local timestamp
    timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${timestamp} [${level}] ${message}" | tee -a "${LOG_FILE}"
}

log_info() { log "INFO" "$@"; }
log_warn() { log "WARN" "$@"; }
log_error() { log "ERROR" "$@"; }
log_success() { log "SUCCESS" "$@"; }
log_critical() { log "CRITICAL" "$@"; }

# Security issue counters
CRITICAL_ISSUES=0
HIGH_ISSUES=0
MEDIUM_ISSUES=0
LOW_ISSUES=0
INFO_ISSUES=0

# Report security issue
report_issue() {
    local severity="$1"
    local category="$2"
    local description="$3"
    local file_path="$4"
    local line_number="${5:-N/A}"

    case "$severity" in
        "CRITICAL")
            CRITICAL_ISSUES=$((CRITICAL_ISSUES + 1))
            echo -e "${RED}🚨 CRITICAL${NC}: [$category] $description"
            ;;
        "HIGH")
            HIGH_ISSUES=$((HIGH_ISSUES + 1))
            echo -e "${RED}⚠️  HIGH${NC}: [$category] $description"
            ;;
        "MEDIUM")
            MEDIUM_ISSUES=$((MEDIUM_ISSUES + 1))
            echo -e "${YELLOW}⚠️  MEDIUM${NC}: [$category] $description"
            ;;
        "LOW")
            LOW_ISSUES=$((LOW_ISSUES + 1))
            echo -e "${BLUE}ℹ️  LOW${NC}: [$category] $description"
            ;;
        "INFO")
            INFO_ISSUES=$((INFO_ISSUES + 1))
            echo -e "${PURPLE}ℹ️  INFO${NC}: [$category] $description"
            ;;
    esac

    if [[ "$file_path" != "N/A" ]]; then
        echo "    📁 File: $file_path"
        if [[ "$line_number" != "N/A" ]]; then
            echo "    📍 Line: $line_number"
        fi
    fi
    echo ""

    log "$severity" "[$category] $description - File: $file_path - Line: $line_number"
}

# Scan for hardcoded secrets
scan_hardcoded_secrets() {
    log_info "Scanning for hardcoded secrets..."

    # Define secret patterns with their risk levels
    declare -A secret_patterns=(
        ["password\\s*[:=]\\s*['\"]?[a-zA-Z0-9!@#\$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>\\/?]{8,}"]="HIGH:Hardcoded Password"
        ["api[_-]?key\s*[:=]\s*['\"]?[a-zA-Z0-9]{20,}"]="HIGH:API Key"
        ["secret[_-]?key\s*[:=]\s*['\"]?[a-zA-Z0-9]{20,}"]="HIGH:Secret Key"
        ["private[_-]?key\s*[:=]"]="CRITICAL:Private Key"
        ["token\s*[:=]\s*['\"]?[a-zA-Z0-9]{20,}"]="MEDIUM:Token"
        ["DATABASE_URL\s*[:=]\s*['\"]?postgresql://.*:[^@]*@"]="HIGH:Database URL with Password"
        ["mysql://.*:[^@]*@"]="HIGH:MySQL URL with Password"
        ["mongodb://.*:[^@]*@"]="HIGH:MongoDB URL with Password"
        ["redis://.*:[^@]*@"]="MEDIUM:Redis URL with Password"
        ["aws[_-]?access[_-]?key[_-]?id\s*[:=]"]="CRITICAL:AWS Access Key"
        ["aws[_-]?secret[_-]?access[_-]?key\s*[:=]"]="CRITICAL:AWS Secret Key"
        ["github[_-]?token\s*[:=]"]="HIGH:GitHub Token"
        ["slack[_-]?token\s*[:=]"]="MEDIUM:Slack Token"
        ["jwt[_-]?secret\s*[:=]\s*['\"]?[a-zA-Z0-9]{32,}"]="HIGH:JWT Secret"
    )

    # Files to scan (exclude .env files as they're expected to contain secrets)
    local files_to_scan=(
        "*.js" "*.ts" "*.jsx" "*.tsx" "*.py" "*.go" "*.java" "*.php"
        "*.rb" "*.cs" "*.cpp" "*.c" "*.sh" "*.bash" "*.zsh"
        "*.yml" "*.yaml" "*.json" "*.xml" "*.toml" "*.ini"
        "*.md" "*.txt" "*.conf" "*.config"
        "Dockerfile*" "docker-compose*" "*.dockerfile"
    )

    for pattern in "${!secret_patterns[@]}"; do
        local severity_category="${secret_patterns[$pattern]}"
        local severity="${severity_category%%:*}"
        local category="${severity_category##*:}"

        # Search in source files
        for file_pattern in "${files_to_scan[@]}"; do
            while IFS= read -r -d '' file; do
                # Skip binary files
                if file "$file" | grep -q "text\|empty"; then
                    local matches
                    matches=$(grep -n -i -E "$pattern" "$file" 2>/dev/null || true)
                    if [[ -n "$matches" ]]; then
                        while IFS= read -r match; do
                            local line_num
                            local line_content
                            line_num=$(echo "$match" | cut -d: -f1)
                            line_content=$(echo "$match" | cut -d: -f2-)
                            report_issue "$severity" "$category" "Potential hardcoded secret detected" "$file" "$line_num"
                        done <<< "$matches"
                    fi
                fi
            done < <(find "$PROJECT_ROOT" -name "$file_pattern" -type f -not -path "*/node_modules/*" -not -path "*/vendor/*" -not -path "*/.git/*" -not -path "*/logs/*" -print0 2>/dev/null || true)
        done
    done
}

# Scan for insecure configurations
scan_insecure_configurations() {
    log_info "Scanning for insecure configurations..."

    # Check Docker configurations
    local docker_compose_files=(
        "docker-compose.yml"
        "docker-compose.yaml"
        "docker-compose.override.yml"
        "docker-compose.override.yaml"
        "docker-compose.prod.yml"
        "docker-compose.dev.yml"
    )

    for compose_file in "${docker_compose_files[@]}"; do
        local file_path="${PROJECT_ROOT}/$compose_file"
        if [[ -f "$file_path" ]]; then
            # Check for privileged containers
            if grep -q "privileged:\s*true" "$file_path"; then
                report_issue "HIGH" "Docker Security" "Privileged container detected" "$file_path"
            fi

            # Check for host network mode
            if grep -q "network_mode:\s*host" "$file_path"; then
                report_issue "MEDIUM" "Docker Security" "Host network mode detected" "$file_path"
            fi

            # Check for bind mounts to sensitive directories
            local sensitive_mounts=("/etc" "/var" "/sys" "/proc" "/dev")
            for mount in "${sensitive_mounts[@]}"; do
                if grep -q "- $mount:" "$file_path"; then
                    report_issue "HIGH" "Docker Security" "Sensitive directory bind mount: $mount" "$file_path"
                fi
            done

            # Check for default ports exposed
            if grep -q "80:80\|443:443\|22:22\|3306:3306\|5432:5432" "$file_path"; then
                report_issue "LOW" "Docker Security" "Standard port mapping detected (consider using non-standard ports)" "$file_path"
            fi
        fi
    done

    # Check Dockerfile security
    local dockerfiles=(
        "Dockerfile"
        "Dockerfile.dev"
        "Dockerfile.prod"
        "*.dockerfile"
    )

    for dockerfile_pattern in "${dockerfiles[@]}"; do
        while IFS= read -r -d '' dockerfile; do
            # Check for running as root
            if ! grep -q "USER\s" "$dockerfile"; then
                report_issue "MEDIUM" "Dockerfile Security" "Container runs as root (no USER directive)" "$dockerfile"
            fi

            # Check for ADD instead of COPY
            if grep -q "^ADD\s" "$dockerfile"; then
                report_issue "LOW" "Dockerfile Security" "ADD instruction used instead of COPY" "$dockerfile"
            fi

            # Check for latest tag
            if grep -q "FROM.*:latest" "$dockerfile"; then
                report_issue "MEDIUM" "Dockerfile Security" "Using :latest tag (unpinned base image)" "$dockerfile"
            fi

        done < <(find "$PROJECT_ROOT" -name "$dockerfile_pattern" -type f -print0 2>/dev/null || true)
    done
}

# Scan for file permissions issues
scan_file_permissions() {
    log_info "Scanning for file permission issues..."

    # Check for world-writable files
    while IFS= read -r -d '' file; do
        if [[ -f "$file" ]]; then
            local perms
            perms=$(stat -f "%Mp%Lp" "$file" 2>/dev/null || stat -c "%a" "$file" 2>/dev/null || echo "")
            if [[ "$perms" =~ ^.*[0-9][0-9][2367]$ ]]; then
                report_issue "HIGH" "File Permissions" "World-writable file detected" "$file"
            fi
        fi
    done < <(find "$PROJECT_ROOT" -type f -not -path "*/.git/*" -not -path "*/logs/*" -print0 2>/dev/null || true)

    # Check for overly permissive script files
    while IFS= read -r -d '' script; do
        if [[ -f "$script" ]]; then
            local perms
            perms=$(stat -f "%Mp%Lp" "$script" 2>/dev/null || stat -c "%a" "$script" 2>/dev/null || echo "")
            if [[ "$perms" =~ ^.*[0-9][4567][4567]$ ]]; then
                report_issue "MEDIUM" "File Permissions" "Script file with excessive permissions" "$script"
            fi
        fi
    done < <(find "$PROJECT_ROOT" -name "*.sh" -o -name "*.bash" -o -name "*.zsh" -type f -print0 2>/dev/null || true)
}

# Check environment file security
check_env_file_security() {
    log_info "Checking environment file security..."

    local env_files=(
        ".env"
        ".env.local"
        ".env.development"
        ".env.production"
        ".env.test"
        ".env.example"
    )

    for env_file in "${env_files[@]}"; do
        local file_path="${PROJECT_ROOT}/$env_file"
        if [[ -f "$file_path" ]]; then
            # Check file permissions
            local perms
            perms=$(stat -f "%Mp%Lp" "$file_path" 2>/dev/null || stat -c "%a" "$file_path" 2>/dev/null || echo "")
            if [[ "$perms" =~ ^.*[0-9][4567][4567]$ ]]; then
                report_issue "HIGH" "Environment Security" "Environment file readable by others" "$file_path"
            fi

            # Check if tracked by git (except .env.example)
            if [[ "$env_file" != ".env.example" ]] && git ls-files --error-unmatch "$file_path" >/dev/null 2>&1; then
                report_issue "CRITICAL" "Environment Security" "Environment file tracked by git" "$file_path"
            fi

            # Check for weak secrets in .env files
            if [[ "$env_file" != ".env.example" ]]; then
                # Check for weak passwords
                local weak_passwords=("password" "123456" "admin" "test" "demo" "secret")
                for weak_pass in "${weak_passwords[@]}"; do
                    if grep -i "=$weak_pass\|=\"$weak_pass\"\|='$weak_pass'" "$file_path" >/dev/null 2>&1; then
                        report_issue "HIGH" "Weak Credentials" "Weak password detected: $weak_pass" "$file_path"
                    fi
                done

                # Check for short JWT secrets
                local jwt_secret
                jwt_secret=$(grep "JWT_SECRET=" "$file_path" 2>/dev/null | cut -d'=' -f2 | tr -d '"' || echo "")
                if [[ -n "$jwt_secret" && ${#jwt_secret} -lt 32 ]]; then
                    report_issue "HIGH" "Weak Credentials" "JWT secret too short (< 32 chars)" "$file_path"
                fi
            fi
        fi
    done
}

# Check for dependencies vulnerabilities
check_dependency_security() {
    log_info "Checking for dependency security issues..."

    # Check for package files
    local package_files=(
        "package.json"
        "package-lock.json"
        "yarn.lock"
        "requirements.txt"
        "Pipfile"
        "Pipfile.lock"
        "go.mod"
        "go.sum"
        "composer.json"
        "composer.lock"
        "Gemfile"
        "Gemfile.lock"
    )

    for package_file in "${package_files[@]}"; do
        local file_path="${PROJECT_ROOT}/$package_file"
        if [[ -f "$file_path" ]]; then
            # Check for development dependencies in production files
            if [[ "$package_file" == "package.json" ]]; then
                if grep -q "devDependencies" "$file_path" && grep -q "\"dev\":\s*false" "$file_path"; then
                    report_issue "MEDIUM" "Dependency Security" "Development dependencies may be included in production" "$file_path"
                fi
            fi

            # Check for commonly vulnerable packages (basic check)
            local vulnerable_patterns=(
                "lodash.*[\"']:[\"']\s*[0-3]\."
                "moment.*[\"']:[\"']\s*[0-1]\."
                "jquery.*[\"']:[\"']\s*[0-2]\."
                "express.*[\"']:[\"']\s*[0-3]\."
            )

            for pattern in "${vulnerable_patterns[@]}"; do
                if grep -E "$pattern" "$file_path" >/dev/null 2>&1; then
                    local package_name
                    package_name=$(echo "$pattern" | cut -d'.' -f1)
                    report_issue "MEDIUM" "Dependency Security" "Potentially outdated package: $package_name" "$file_path"
                fi
            done
        fi
    done

    # Suggest running vulnerability scanners
    if [[ -f "${PROJECT_ROOT}/package.json" ]]; then
        if command -v npm >/dev/null 2>&1; then
            report_issue "INFO" "Security Tools" "Consider running 'npm audit' for Node.js vulnerability scanning" "N/A"
        fi
    fi

    if [[ -f "${PROJECT_ROOT}/requirements.txt" ]] || [[ -f "${PROJECT_ROOT}/Pipfile" ]]; then
        report_issue "INFO" "Security Tools" "Consider using 'safety' or 'bandit' for Python security scanning" "N/A"
    fi
}

# Check for SSL/TLS configuration issues
check_ssl_tls_config() {
    log_info "Checking SSL/TLS configuration..."

    # Check for HTTP URLs in production configs
    local config_files=(
        "*.yml" "*.yaml" "*.json" "*.conf" "*.config"
        "docker-compose*.yml" "docker-compose*.yaml"
    )

    for pattern in "${config_files[@]}"; do
        while IFS= read -r -d '' file; do
            if grep -E "http://[^/]*\.(com|org|net|io|dev)" "$file" >/dev/null 2>&1; then
                local matches
                matches=$(grep -n "http://[^/]*\.(com|org|net|io|dev)" "$file")
                while IFS= read -r match; do
                    local line_num
                    line_num=$(echo "$match" | cut -d: -f1)
                    report_issue "MEDIUM" "SSL/TLS Security" "HTTP URL in production config (should use HTTPS)" "$file" "$line_num"
                done <<< "$matches"
            fi
        done < <(find "$PROJECT_ROOT" -name "$pattern" -type f -not -path "*/.git/*" -not -path "*/logs/*" -print0 2>/dev/null || true)
    done
}

# Generate security report
generate_security_report() {
    local total_issues=$((CRITICAL_ISSUES + HIGH_ISSUES + MEDIUM_ISSUES + LOW_ISSUES + INFO_ISSUES))

    echo ""
    echo -e "${BLUE}=== Security Scan Summary ===${NC}"
    echo -e "🚨 Critical Issues: ${RED}${CRITICAL_ISSUES}${NC}"
    echo -e "⚠️  High Issues: ${RED}${HIGH_ISSUES}${NC}"
    echo -e "⚠️  Medium Issues: ${YELLOW}${MEDIUM_ISSUES}${NC}"
    echo -e "ℹ️  Low Issues: ${BLUE}${LOW_ISSUES}${NC}"
    echo -e "ℹ️  Info Items: ${PURPLE}${INFO_ISSUES}${NC}"
    echo -e "📊 Total Issues: ${total_issues}"
    echo ""

    # Security score calculation
    local security_score=100
    security_score=$((security_score - (CRITICAL_ISSUES * 20)))
    security_score=$((security_score - (HIGH_ISSUES * 10)))
    security_score=$((security_score - (MEDIUM_ISSUES * 5)))
    security_score=$((security_score - (LOW_ISSUES * 2)))

    if [[ $security_score -lt 0 ]]; then
        security_score=0
    fi

    echo -e "🔒 Security Score: ${security_score}/100"

    if [[ $security_score -ge 90 ]]; then
        echo -e "${GREEN}✅ Excellent security posture${NC}"
    elif [[ $security_score -ge 75 ]]; then
        echo -e "${YELLOW}⚠️  Good security, with room for improvement${NC}"
    elif [[ $security_score -ge 50 ]]; then
        echo -e "${YELLOW}⚠️  Moderate security concerns${NC}"
    else
        echo -e "${RED}🚨 Significant security issues detected${NC}"
    fi

    echo ""
    log_info "Security scan completed. Score: $security_score/100, Total issues: $total_issues"

    # Return appropriate exit code
    if [[ $CRITICAL_ISSUES -gt 0 ]]; then
        return 2
    elif [[ $HIGH_ISSUES -gt 0 ]]; then
        return 1
    else
        return 0
    fi
}

# Main execution
main() {
    echo -e "${BLUE}=== Lifeboard Security Scan ===${NC}"
    echo -e "${BLUE}Started at: $(date)${NC}"
    echo ""

    log_info "Starting comprehensive security scan"

    # Run security scans
    scan_hardcoded_secrets
    scan_insecure_configurations
    scan_file_permissions
    check_env_file_security
    check_dependency_security
    check_ssl_tls_config

    # Generate final report
    generate_security_report
}

# Execute main function
main "$@"
