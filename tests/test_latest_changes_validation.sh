#!/bin/bash

# Quick Validation Test for Latest Changes
# Tests the most recent developments and overall system health
# Focuses on Phase 10 M4 implementation and code quality improvements

# shellcheck disable=SC2317
set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Test configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
LOGS_DIR="$PROJECT_ROOT/logs"

# Test counters
TESTS_RUN=0
TESTS_PASSED=0
TESTS_FAILED=0

# Logging setup
setup_logging() {
    local timestamp
    timestamp=$(date '+%Y%m%d_%H%M%S')
    mkdir -p "$LOGS_DIR"
    LOG_FILE="$LOGS_DIR/latest_changes_validation_${timestamp}.log"
    echo "Latest changes validation started at $(date)" > "$LOG_FILE"
}

log_info() {
    echo -e "${BLUE}[INFO]${NC} $*" | tee -a "$LOG_FILE"
}

log_success() {
    echo -e "${GREEN}[PASS]${NC} $*" | tee -a "$LOG_FILE"
}

log_error() {
    echo -e "${RED}[FAIL]${NC} $*" | tee -a "$LOG_FILE"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $*" | tee -a "$LOG_FILE"
}

# Test execution framework
run_test() {
    local test_name="$1"
    local test_function="$2"

    echo -e "\n${BLUE}Testing: $test_name${NC}"
    TESTS_RUN=$((TESTS_RUN + 1))

    if "$test_function"; then
        log_success "$test_name"
        TESTS_PASSED=$((TESTS_PASSED + 1))
        return 0
    else
        log_error "$test_name"
        TESTS_FAILED=$((TESTS_FAILED + 1))
        return 1
    fi
}

# Test: Latest Phase 10 M4 Implementation
test_m4_implementation() {
    log_info "Validating Phase 10 M4 implementation"

    # Check core M4 files
    local m4_files=(
        "$PROJECT_ROOT/desktop/src/ui/RibbonManager.js"
        "$PROJECT_ROOT/desktop/src/ui/ModalManager.js"
        "$PROJECT_ROOT/desktop/src/ui/CommandPaletteUI.js"
        "$PROJECT_ROOT/tests/test_plugin_ui_m4.sh"
        "$PROJECT_ROOT/supporting_documents/Development Phases/Phase_10_M4_Implementation_Summary.md"
    )

    for file in "${m4_files[@]}"; do
        if [[ ! -f "$file" ]]; then
            log_error "M4 file missing: $(basename "$file")"
            return 1
        fi
    done

    # Check for key M4 features
    if ! grep -q "class RibbonManager" "$PROJECT_ROOT/desktop/src/ui/RibbonManager.js"; then
        log_error "RibbonManager class not properly implemented"
        return 1
    fi

    if ! grep -q "class ModalManager" "$PROJECT_ROOT/desktop/src/ui/ModalManager.js"; then
        log_error "ModalManager class not properly implemented"
        return 1
    fi

    log_info "✓ All M4 core files present and properly structured"
    return 0
}

# Test: Code Smell Infrastructure
test_code_smell_infrastructure() {
    log_info "Validating code smell infrastructure"

    # Check infrastructure files
    local infra_files=(
        "$PROJECT_ROOT/tools/code_smell_detector.py"
        "$PROJECT_ROOT/.pre-commit-config-codesmell.yaml"
        "$PROJECT_ROOT/.github/workflows/code-smell-detection.yml"
        "$PROJECT_ROOT/tests/test_code_smell_infrastructure.sh"
    )

    for file in "${infra_files[@]}"; do
        if [[ ! -f "$file" ]]; then
            log_error "Code smell infrastructure missing: $(basename "$file")"
            return 1
        fi
    done

    # Test Python detector syntax if Python is available
    if command -v python3 >/dev/null 2>&1; then
        if ! python3 -m py_compile "$PROJECT_ROOT/tools/code_smell_detector.py"; then
            log_error "Code smell detector has syntax errors"
            return 1
        fi
    fi

    log_info "✓ Code smell infrastructure is properly set up"
    return 0
}

# Test: Shellcheck Compliance
test_shellcheck_fixes() {
    log_info "Validating shellcheck compliance improvements"

    if ! command -v shellcheck >/dev/null 2>&1; then
        log_warn "Shellcheck not available, skipping compliance check"
        return 0
    fi

    # Check key shell scripts for compliance
    local shell_scripts=(
        "$SCRIPT_DIR/run_all_tests.sh"
        "$SCRIPT_DIR/test_code_smell_infrastructure.sh"
        "$SCRIPT_DIR/test_plugin_ui_m4.sh"
    )

    local issues_found=0
    for script in "${shell_scripts[@]}"; do
        if [[ -f "$script" ]]; then
            if ! shellcheck "$script" >/dev/null 2>&1; then
                log_error "Shellcheck issues in: $(basename "$script")"
                issues_found=$((issues_found + 1))
            fi
        fi
    done

    if [[ $issues_found -gt 0 ]]; then
        log_error "$issues_found shell scripts have shellcheck issues"
        return 1
    fi

    log_info "✓ Shell scripts pass shellcheck validation"
    return 0
}

# Test: Test Suite Coverage
test_suite_coverage() {
    log_info "Validating test suite coverage for latest changes"

    # Check for required test files
    local test_files=(
        "$SCRIPT_DIR/test_plugin_ui_m4.sh"
        "$SCRIPT_DIR/test_plugin_ui_integration_m4.sh"
        "$SCRIPT_DIR/test_ui_performance_m4.sh"
        "$SCRIPT_DIR/test_code_smell_infrastructure.sh"
        "$SCRIPT_DIR/run_all_tests.sh"
    )

    for test_file in "${test_files[@]}"; do
        if [[ ! -f "$test_file" ]]; then
            log_error "Test file missing: $(basename "$test_file")"
            return 1
        fi

        # Check if executable
        if [[ ! -x "$test_file" ]]; then
            log_error "Test file not executable: $(basename "$test_file")"
            return 1
        fi
    done

    # Check if main test runner includes new tests
    if ! grep -q "test_plugin_ui_m4.sh" "$SCRIPT_DIR/run_all_tests.sh"; then
        log_error "New M4 tests not integrated into main test runner"
        return 1
    fi

    log_info "✓ Test suite coverage is comprehensive and up-to-date"
    return 0
}

# Test: Documentation Updates
test_documentation() {
    log_info "Validating documentation for latest changes"

    # Check for M4 documentation
    local m4_doc="$PROJECT_ROOT/supporting_documents/Development Phases/Phase_10_M4_Implementation_Summary.md"
    if [[ ! -f "$m4_doc" ]]; then
        log_error "M4 implementation documentation missing"
        return 1
    fi

    # Check documentation completeness
    local required_sections=(
        "Executive Summary"
        "Architecture Implementation"
        "Plugin API Integration"
        "Testing"
        "Files Created/Modified"
    )

    for section in "${required_sections[@]}"; do
        if ! grep -q "$section" "$m4_doc"; then
            log_error "Missing documentation section: $section"
            return 1
        fi
    done

    log_info "✓ Documentation is complete and up-to-date"
    return 0
}

# Test: Git Repository State
test_git_state() {
    log_info "Validating git repository state"

    cd "$PROJECT_ROOT"

    # Check if we're in a git repository
    if ! git rev-parse --git-dir >/dev/null 2>&1; then
        log_error "Not in a git repository"
        return 1
    fi

    # Check for recent commits (latest changes should be committed)
    local recent_commits
    recent_commits=$(git --no-pager log --oneline -5)

    if ! echo "$recent_commits" | grep -q "M4\|UI\|plugin\|smell"; then
        log_warn "Recent commits might not reflect latest changes"
    fi

    # Check for clean working directory (optional)
    if [[ -n "$(git status --porcelain)" ]]; then
        log_info "Working directory has uncommitted changes (normal during development)"
    fi

    log_info "✓ Git repository state is acceptable"
    return 0
}

# Test: Basic Functionality Smoke Test
test_basic_functionality() {
    log_info "Running basic functionality smoke test"

    # Check if key directories exist
    local key_dirs=(
        "$PROJECT_ROOT/desktop/src"
        "$PROJECT_ROOT/tests"
        "$PROJECT_ROOT/supporting_documents"
        "$PROJECT_ROOT/tools"
        "$PROJECT_ROOT/logs"
    )

    for dir in "${key_dirs[@]}"; do
        if [[ ! -d "$dir" ]]; then
            log_error "Key directory missing: $(basename "$dir")"
            return 1
        fi
    done

    # Test if Node.js modules can be loaded (if Node.js available)
    if command -v node >/dev/null 2>&1; then
        local test_script="/tmp/smoke_test.js"
        cat > "$test_script" << 'EOF'
try {
    const path = require('path');
    const projectRoot = process.argv[2];

    // Try to load a UI component
    const RibbonManager = require(path.join(projectRoot, 'desktop/src/ui/RibbonManager.js'));
    const manager = new RibbonManager();

    console.log('✓ Basic module loading works');
    process.exit(0);
} catch (error) {
    console.error('✗ Module loading failed:', error.message);
    process.exit(1);
}
EOF

        if ! node "$test_script" "$PROJECT_ROOT" >/dev/null 2>&1; then
            log_error "Basic Node.js module loading failed"
            rm -f "$test_script"
            return 1
        fi

        rm -f "$test_script"
    fi

    log_info "✓ Basic functionality smoke test passed"
    return 0
}

# Generate validation report
generate_validation_report() {
    local pass_rate
    if [[ $TESTS_RUN -gt 0 ]]; then
        pass_rate=$(( (TESTS_PASSED * 100) / TESTS_RUN ))
    else
        pass_rate=0
    fi

    echo -e "\n${BLUE}════════════════════════════════════════════════════════════════${NC}"
    echo -e "${BLUE}                  Latest Changes Validation Report                  ${NC}"
    echo -e "${BLUE}════════════════════════════════════════════════════════════════${NC}"

    echo -e "\n${PURPLE}Validation Summary:${NC}"
    echo -e "  Tests Run:      $TESTS_RUN"
    echo -e "  Tests Passed:   ${GREEN}$TESTS_PASSED${NC}"
    echo -e "  Tests Failed:   ${RED}$TESTS_FAILED${NC}"
    echo -e "  Pass Rate:      ${pass_rate}%"

    echo -e "\n${PURPLE}Areas Validated:${NC}"
    echo -e "  ✓ Phase 10 M4 UI Implementation"
    echo -e "  ✓ Code Smell Infrastructure"
    echo -e "  ✓ Shellcheck Compliance"
    echo -e "  ✓ Test Suite Coverage"
    echo -e "  ✓ Documentation Updates"
    echo -e "  ✓ Git Repository State"
    echo -e "  ✓ Basic Functionality"

    if [[ $TESTS_FAILED -eq 0 ]]; then
        echo -e "\n${GREEN}🎉 ALL VALIDATIONS PASSED! 🎉${NC}"
        echo -e "${GREEN}✓ Latest changes are properly implemented${NC}"
        echo -e "${GREEN}✓ Code quality standards maintained${NC}"
        echo -e "${GREEN}✓ Test coverage is comprehensive${NC}"
        echo -e "${GREEN}✓ Documentation is up-to-date${NC}"
        echo -e "\n${BLUE}System is ready for production use!${NC}"
    else
        echo -e "\n${RED}❌ Some validations failed${NC}"
        echo -e "${YELLOW}Please address the issues above before proceeding${NC}"
        echo -e "${BLUE}Check log file: $LOG_FILE${NC}"
    fi
}

# Main execution
main() {
    echo -e "${PURPLE}Starting Latest Changes Validation${NC}"
    echo -e "${BLUE}Validating: M4 UI, Code Smell Infrastructure, Quality Improvements${NC}"
    echo "=================================================================="

    setup_logging

    # Run all validation tests
    run_test "Phase 10 M4 Implementation" test_m4_implementation
    run_test "Code Smell Infrastructure" test_code_smell_infrastructure
    run_test "Shellcheck Compliance" test_shellcheck_fixes
    run_test "Test Suite Coverage" test_suite_coverage
    run_test "Documentation Updates" test_documentation
    run_test "Git Repository State" test_git_state
    run_test "Basic Functionality" test_basic_functionality

    # Generate report
    generate_validation_report

    # Exit with appropriate code
    if [[ $TESTS_FAILED -eq 0 ]]; then
        exit 0
    else
        exit 1
    fi
}

# Execute main function
main "$@"
