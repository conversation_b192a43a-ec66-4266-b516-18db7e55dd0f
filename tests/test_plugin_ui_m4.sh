#!/bin/bash

# Test suite for Phase 10 Milestone M4 - UI Features
# Tests ribbon icons, modals, command palette UI, and integration

# Test suite with graceful error handling

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test counters
TESTS_RUN=0
TESTS_PASSED=0
TESTS_FAILED=0

# Helper functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[PASS]${NC} $1"
    ((TESTS_PASSED++))
}

log_error() {
    echo -e "${RED}[FAIL]${NC} $1"
    ((TESTS_FAILED++))
}

log_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

run_test() {
    ((TESTS_RUN++))
    echo -e "\n${BLUE}Test $TESTS_RUN:${NC} $1"
}

# Change to project root
cd "$(dirname "$0")/.." || exit 1

log_info "Starting Phase 10 M4 UI Feature Tests"
log_info "Testing: Ribbon Icons, Modals, Command Palette UI"

# Test 1: Verify M4 UI files exist
run_test "Check M4 UI implementation files"

FILES_TO_CHECK=(
    "desktop/src/ui/RibbonManager.js"
    "desktop/src/ui/ModalManager.js"
    "desktop/src/ui/CommandPaletteUI.js"
    "desktop/types/plugin-api.d.ts"
)

for file in "${FILES_TO_CHECK[@]}"; do
    if [ -f "$file" ]; then
        log_success "Found: $file"
    else
        log_error "Missing: $file"
    fi
done

# Test 2: Verify TypeScript definitions include M4 features
run_test "Check TypeScript definitions for M4 UI features"

if grep -q "addRibbonIcon" desktop/types/plugin-api.d.ts; then
    log_success "RibbonIcon API found in type definitions"
else
    log_error "RibbonIcon API missing from type definitions"
fi

if grep -q "showModal" desktop/types/plugin-api.d.ts; then
    log_success "Modal API found in type definitions"
else
    log_error "Modal API missing from type definitions"
fi

if grep -q "ModalConfig" desktop/types/plugin-api.d.ts; then
    log_success "ModalConfig interface found in type definitions"
else
    log_error "ModalConfig interface missing from type definitions"
fi

# Test 3: Check plugin manager integration
run_test "Verify plugin manager M4 integration"

if grep -q "RibbonManager" desktop/src/plugin-manager.js; then
    log_success "RibbonManager integrated in plugin manager"
else
    log_error "RibbonManager missing from plugin manager"
fi

if grep -q "ModalManager" desktop/src/plugin-manager.js; then
    log_success "ModalManager integrated in plugin manager"
else
    log_error "ModalManager missing from plugin manager"
fi

if grep -q "CommandPaletteUI" desktop/src/plugin-manager.js; then
    log_success "CommandPaletteUI integrated in plugin manager"
else
    log_error "CommandPaletteUI missing from plugin manager"
fi

# Test 4: Verify IPC handlers for M4 features
run_test "Check IPC handlers for M4 UI features"

IPC_CHANNELS=(
    "ribbon:list"
    "ribbon:click"
    "modal:list"
    "modal:close"
    "command-palette:show"
    "command-palette:hide"
    "ui:stats"
)

for channel in "${IPC_CHANNELS[@]}"; do
    if grep -q "$channel" desktop/src/main.js; then
        log_success "IPC handler found: $channel"
    else
        log_error "IPC handler missing: $channel"
    fi
done

# Test 5: Check preload.js API exposure
run_test "Verify preload.js exposes M4 UI APIs"

PRELOAD_APIS=(
    "ribbon:"
    "modal:"
    "commandPalette:"
    "ui:"
)

for api in "${PRELOAD_APIS[@]}"; do
    if grep -q "$api" desktop/src/preload.js; then
        log_success "Preload API found: $api"
    else
        log_error "Preload API missing: $api"
    fi
done

# Test 6: Validate demo plugin M4 usage
run_test "Check demo plugin uses M4 features"

if grep -q "addRibbonIcon" desktop/plugins/limitless/main.js; then
    log_success "Demo plugin uses ribbon icons"
else
    log_error "Demo plugin missing ribbon icon usage"
fi

if grep -q "showModal" desktop/plugins/limitless/main.js; then
    log_success "Demo plugin uses modals"
else
    log_error "Demo plugin missing modal usage"
fi

if grep -q "setMetadata" desktop/plugins/limitless/main.js; then
    log_success "Demo plugin sets command metadata"
else
    log_error "Demo plugin missing command metadata"
fi

# Test 7: Syntax validation for JavaScript files
run_test "Syntax validation for M4 JavaScript files"

JS_FILES=(
    "desktop/src/ui/RibbonManager.js"
    "desktop/src/ui/ModalManager.js"
    "desktop/src/ui/CommandPaletteUI.js"
)

for js_file in "${JS_FILES[@]}"; do
    if node -c "$js_file" 2>/dev/null; then
        log_success "Valid syntax: $js_file"
    else
        log_error "Syntax error in: $js_file"
    fi
done

# Test 8: Check for proper error handling
run_test "Verify error handling in M4 UI components"

ERROR_PATTERNS=(
    "try.*catch"
    "throw new Error"
    "log.error"
)

for js_file in "${JS_FILES[@]}"; do
    for pattern in "${ERROR_PATTERNS[@]}"; do
        if grep -q "$pattern" "$js_file"; then
            log_success "Error handling found in: $(basename "$js_file")"
            break
        fi
    done
done

# Test 9: Check for logging implementation
run_test "Verify logging in M4 UI components"

for js_file in "${JS_FILES[@]}"; do
    if grep -q "log\." "$js_file"; then
        log_success "Logging implemented in: $(basename "$js_file")"
    else
        log_error "Missing logging in: $(basename "$js_file")"
    fi
done

# Test 10: Validate required methods exist
run_test "Check required methods in UI managers"

# RibbonManager methods
RIBBON_METHODS=(
    "addRibbonIcon"
    "removeRibbonIcon"
    "executeIconCallback"
    "getAllRibbonIcons"
    "cleanup"
)

for method in "${RIBBON_METHODS[@]}"; do
    if grep -q "$method" desktop/src/ui/RibbonManager.js; then
        log_success "RibbonManager method: $method"
    else
        log_error "Missing RibbonManager method: $method"
    fi
done

# ModalManager methods
MODAL_METHODS=(
    "showModal"
    "closeModal"
    "validateModalConfig"
    "getOpenModals"
    "cleanup"
)

for method in "${MODAL_METHODS[@]}"; do
    if grep -q "$method" desktop/src/ui/ModalManager.js; then
        log_success "ModalManager method: $method"
    else
        log_error "Missing ModalManager method: $method"
    fi
done

# Test 11: Check security measures
run_test "Verify security measures in M4 implementation"

# Check for input sanitization
if grep -q "sanitize" desktop/src/ui/ModalManager.js; then
    log_success "Input sanitization found in ModalManager"
else
    log_error "Missing input sanitization in ModalManager"
fi

# Check for permission validation
if grep -q "permissions" desktop/src/plugin-manager.js; then
    log_success "Permission validation found"
else
    log_error "Missing permission validation"
fi

# Test 12: Verify plugin cleanup for UI elements
run_test "Check plugin cleanup for UI elements"

if grep -q "cleanupPluginUI" desktop/src/plugin-manager.js; then
    log_success "Plugin UI cleanup method found"
else
    log_error "Missing plugin UI cleanup method"
fi

if grep -q "removePluginIcons" desktop/src/plugin-manager.js; then
    log_success "Plugin icon cleanup found"
else
    log_error "Missing plugin icon cleanup"
fi

if grep -q "closePluginModals" desktop/src/plugin-manager.js; then
    log_success "Plugin modal cleanup found"
else
    log_error "Missing plugin modal cleanup"
fi

# Test 13: Basic smoke test if Node.js is available
run_test "Basic smoke test for M4 components"

if command -v node &> /dev/null; then
    # Test RibbonManager instantiation
    if node -e "
        const RibbonManager = require('./desktop/src/ui/RibbonManager.js');
        const manager = new RibbonManager();
        console.log('RibbonManager instantiated successfully');
    " 2>/dev/null; then
        log_success "RibbonManager instantiation test passed"
    else
        log_error "RibbonManager instantiation test failed"
    fi

    # Test ModalManager instantiation
    if node -e "
        const ModalManager = require('./desktop/src/ui/ModalManager.js');
        const manager = new ModalManager();
        console.log('ModalManager instantiated successfully');
    " 2>/dev/null; then
        log_success "ModalManager instantiation test passed"
    else
        log_error "ModalManager instantiation test failed"
    fi
else
    log_warning "Node.js not available, skipping smoke tests"
fi

# Test 14: Check dependencies
run_test "Verify M4 dependencies"

if grep -q "electron-log" desktop/package.json; then
    log_success "electron-log dependency found"
else
    log_error "Missing electron-log dependency"
fi

if grep -q "semver" desktop/package.json; then
    log_success "semver dependency found"
else
    log_error "Missing semver dependency"
fi

# Test Summary
echo -e "\n${BLUE}========================================${NC}"
echo -e "${BLUE}Phase 10 M4 Test Summary${NC}"
echo -e "${BLUE}========================================${NC}"
echo -e "Tests Run:    ${TESTS_RUN}"
echo -e "Tests Passed: ${GREEN}${TESTS_PASSED}${NC}"
echo -e "Tests Failed: ${RED}${TESTS_FAILED}${NC}"

if [ $TESTS_FAILED -eq 0 ]; then
    echo -e "\n${GREEN}🎉 All M4 UI tests passed! Ready for implementation.${NC}"
    echo -e "${GREEN}✅ Ribbon Icons system implemented${NC}"
    echo -e "${GREEN}✅ Modal framework implemented${NC}"
    echo -e "${GREEN}✅ Enhanced Command Palette implemented${NC}"
    echo -e "${GREEN}✅ Plugin API integration complete${NC}"
    exit 0
else
    echo -e "\n${RED}❌ Some M4 tests failed. Review implementation.${NC}"
    exit 1
fi
