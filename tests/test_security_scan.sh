#!/bin/bash

# Test: Security Scanning and Analysis (Simplified)
# Description: Scans codebase for basic security vulnerabilities and misconfigurations
# Author: AI Assistant
# Created: 2025-07-01

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/.." && pwd)"
LOG_DIR="${PROJECT_ROOT}/logs"
LOG_FILE="${LOG_DIR}/security_scan_$(date +%Y%m%d_%H%M%S).log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Ensure log directory exists
mkdir -p "${LOG_DIR}"

# Logging function
log() {
    local level="$1"
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${timestamp} [${level}] ${message}" | tee -a "${LOG_FILE}"
}

log_info() { log "INFO" "$@"; }
log_warn() { log "WARN" "$@"; }
log_error() { log "ERROR" "$@"; }
log_success() { log "SUCCESS" "$@"; }

# Security issue counters
CRITICAL_ISSUES=0
HIGH_ISSUES=0
MEDIUM_ISSUES=0
LOW_ISSUES=0
INFO_ISSUES=0

# Report security issue
report_issue() {
    local severity="$1"
    local category="$2"
    local description="$3"
    local file_path="${4:-N/A}"
    local line_number="${5:-N/A}"

    case "$severity" in
        "CRITICAL")
            CRITICAL_ISSUES=$((CRITICAL_ISSUES + 1))
            echo -e "${RED}🚨 CRITICAL${NC}: [$category] $description"
            ;;
        "HIGH")
            HIGH_ISSUES=$((HIGH_ISSUES + 1))
            echo -e "${RED}⚠️  HIGH${NC}: [$category] $description"
            ;;
        "MEDIUM")
            MEDIUM_ISSUES=$((MEDIUM_ISSUES + 1))
            echo -e "${YELLOW}⚠️  MEDIUM${NC}: [$category] $description"
            ;;
        "LOW")
            LOW_ISSUES=$((LOW_ISSUES + 1))
            echo -e "${BLUE}ℹ️  LOW${NC}: [$category] $description"
            ;;
        "INFO")
            INFO_ISSUES=$((INFO_ISSUES + 1))
            echo -e "${PURPLE}ℹ️  INFO${NC}: [$category] $description"
            ;;
    esac

    if [[ "$file_path" != "N/A" ]]; then
        echo "    📁 File: $file_path"
        if [[ "$line_number" != "N/A" ]]; then
            echo "    📍 Line: $line_number"
        fi
    fi
    echo ""

    log "$severity" "[$category] $description - File: $file_path - Line: $line_number"
}

# Check for basic hardcoded secrets
scan_basic_secrets() {
    log_info "Scanning for basic hardcoded secrets..."

    # Check for common secret patterns in source files
    if find "$PROJECT_ROOT" -type f \( -name "*.js" -o -name "*.ts" -o -name "*.py" -o -name "*.go" -o -name "*.java" -o -name "*.php" \) ! -path "*/.git/*" ! -path "*/node_modules/*" ! -path "*/logs/*" -exec grep -l "password.*=" {} + 2>/dev/null | head -5; then
        report_issue "MEDIUM" "Potential Secrets" "Password assignments found in source code"
    fi

    # Check for API keys in source code
    if find "$PROJECT_ROOT" -type f \( -name "*.js" -o -name "*.ts" -o -name "*.py" -o -name "*.go" \) ! -path "*/.git/*" ! -path "*/node_modules/*" ! -path "*/logs/*" -exec grep -l "api.*key.*=" {} + 2>/dev/null | head -3; then
        report_issue "HIGH" "Potential Secrets" "API key assignments found in source code"
    fi
}

# Check Docker configurations
check_docker_security() {
    log_info "Checking Docker security configurations..."

    local docker_compose="$PROJECT_ROOT/docker-compose.yml"
    if [[ -f "$docker_compose" ]]; then
        # Check for privileged containers
        if grep -q "privileged.*true" "$docker_compose"; then
            report_issue "HIGH" "Docker Security" "Privileged container detected" "$docker_compose"
        fi

        # Check for host network mode
        if grep -q "network_mode.*host" "$docker_compose"; then
            report_issue "MEDIUM" "Docker Security" "Host network mode detected" "$docker_compose"
        fi

        # Check for standard port mappings
        if grep -E "ports:" -A 5 "$docker_compose" | grep -E "(80:80|443:443|22:22|3306:3306|5432:5432)"; then
            report_issue "LOW" "Docker Security" "Standard port mappings detected" "$docker_compose"
        fi
    fi
}

# Check environment file security
check_env_security() {
    log_info "Checking environment file security..."

    # Check if .env files are properly protected
    local env_files=(".env" ".env.local" ".env.production")

    for env_file in "${env_files[@]}"; do
        local file_path="$PROJECT_ROOT/$env_file"
        if [[ -f "$file_path" ]]; then
            # Check if tracked by git (except .env.example)
            if [[ "$env_file" != ".env.example" ]] && git ls-files --error-unmatch "$file_path" >/dev/null 2>&1; then
                report_issue "CRITICAL" "Environment Security" "Environment file tracked by git: $env_file" "$file_path"
            fi

            # Check file permissions
            local perms=$(stat -f "%Mp%Lp" "$file_path" 2>/dev/null || stat -c "%a" "$file_path" 2>/dev/null || echo "644")
            if [[ "$perms" =~ 64[4-7]$ ]] || [[ "$perms" =~ 66[4-7]$ ]]; then
                report_issue "MEDIUM" "Environment Security" "Environment file readable by others: $env_file" "$file_path"
            fi
        fi
    done

    # Check .gitignore protection
    if [[ -f "$PROJECT_ROOT/.gitignore" ]]; then
        if ! grep -q "\.env\.local" "$PROJECT_ROOT/.gitignore"; then
            report_issue "HIGH" "Environment Security" ".env.local not in .gitignore"
        fi
    else
        report_issue "HIGH" "Environment Security" "No .gitignore file found"
    fi
}

# Check file permissions
check_file_permissions() {
    log_info "Checking file permissions..."

    # Check for world-writable files
    local writable_files=$(find "$PROJECT_ROOT" -type f -perm +002 ! -path "*/.git/*" ! -path "*/logs/*" 2>/dev/null | head -5)
    if [[ -n "$writable_files" ]]; then
        report_issue "HIGH" "File Permissions" "World-writable files detected"
    fi

    # Check script permissions
    local script_files=$(find "$PROJECT_ROOT" -name "*.sh" -type f ! -path "*/.git/*" 2>/dev/null)
    for script in $script_files; do
        if [[ -f "$script" ]]; then
            local perms=$(stat -f "%Mp%Lp" "$script" 2>/dev/null || stat -c "%a" "$script" 2>/dev/null || echo "755")
            if [[ "$perms" =~ 77[7]$ ]]; then
                report_issue "MEDIUM" "File Permissions" "Script file with excessive permissions" "$script"
            fi
        fi
    done
}

# Check for dependency security
check_dependencies() {
    log_info "Checking dependency security..."

    # Check for package.json
    if [[ -f "$PROJECT_ROOT/package.json" ]]; then
        report_issue "INFO" "Security Tools" "Consider running 'npm audit' for Node.js vulnerability scanning"

        # Basic check for outdated packages
        if grep -E "(lodash.*[\"']:.*[\"'][0-3]\.|moment.*[\"']:.*[\"'][0-1]\.)" "$PROJECT_ROOT/package.json" >/dev/null 2>&1; then
            report_issue "MEDIUM" "Dependency Security" "Potentially outdated packages detected in package.json"
        fi
    fi

    # Check for Python requirements
    if [[ -f "$PROJECT_ROOT/requirements.txt" ]]; then
        report_issue "INFO" "Security Tools" "Consider using 'safety' for Python vulnerability scanning"
    fi
}

# Check SSL/TLS configurations
check_ssl_config() {
    log_info "Checking SSL/TLS configurations..."

    # Check for HTTP URLs in configuration files
    local config_files=$(find "$PROJECT_ROOT" -name "*.yml" -o -name "*.yaml" -o -name "*.json" ! -path "*/.git/*" ! -path "*/node_modules/*" 2>/dev/null)

    for config_file in $config_files; do
        if [[ -f "$config_file" ]] && grep -E "http://[^/]*\.(com|org|net|io|dev)" "$config_file" >/dev/null 2>&1; then
            report_issue "MEDIUM" "SSL/TLS Security" "HTTP URLs found in config file (should use HTTPS)" "$config_file"
        fi
    done
}

# Generate security report
generate_security_report() {
    local total_issues=$((CRITICAL_ISSUES + HIGH_ISSUES + MEDIUM_ISSUES + LOW_ISSUES + INFO_ISSUES))

    echo ""
    echo -e "${BLUE}=== Security Scan Summary ===${NC}"
    echo -e "🚨 Critical Issues: ${RED}${CRITICAL_ISSUES}${NC}"
    echo -e "⚠️  High Issues: ${RED}${HIGH_ISSUES}${NC}"
    echo -e "⚠️  Medium Issues: ${YELLOW}${MEDIUM_ISSUES}${NC}"
    echo -e "ℹ️  Low Issues: ${BLUE}${LOW_ISSUES}${NC}"
    echo -e "ℹ️  Info Items: ${PURPLE}${INFO_ISSUES}${NC}"
    echo -e "📊 Total Issues: ${total_issues}"
    echo ""

    # Security score calculation
    local security_score=100
    security_score=$((security_score - (CRITICAL_ISSUES * 20)))
    security_score=$((security_score - (HIGH_ISSUES * 10)))
    security_score=$((security_score - (MEDIUM_ISSUES * 5)))
    security_score=$((security_score - (LOW_ISSUES * 2)))

    if [[ $security_score -lt 0 ]]; then
        security_score=0
    fi

    echo -e "🔒 Security Score: ${security_score}/100"

    if [[ $security_score -ge 90 ]]; then
        echo -e "${GREEN}✅ Excellent security posture${NC}"
    elif [[ $security_score -ge 75 ]]; then
        echo -e "${YELLOW}⚠️  Good security, with room for improvement${NC}"
    elif [[ $security_score -ge 50 ]]; then
        echo -e "${YELLOW}⚠️  Moderate security concerns${NC}"
    else
        echo -e "${RED}🚨 Significant security issues detected${NC}"
    fi

    echo ""
    log_info "Security scan completed. Score: $security_score/100, Total issues: $total_issues"

    # Return appropriate exit code
    if [[ $CRITICAL_ISSUES -gt 0 ]]; then
        return 2
    elif [[ $HIGH_ISSUES -gt 0 ]]; then
        return 1
    else
        return 0
    fi
}

# Main execution
main() {
    echo -e "${BLUE}=== Lifeboard Security Scan ===${NC}"
    echo -e "${BLUE}Started at: $(date)${NC}"
    echo ""

    log_info "Starting security scan"

    # Run security checks
    scan_basic_secrets
    check_docker_security
    check_env_security
    check_file_permissions
    check_dependencies
    check_ssl_config

    # Generate final report
    generate_security_report
}

# Execute main function
main "$@"
