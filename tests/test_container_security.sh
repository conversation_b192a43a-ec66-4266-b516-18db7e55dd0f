#!/bin/bash
# Phase 2 Container Security Test
# Purpose: Verify container security configuration
# Usage: ./tests/test_container_security.sh

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}=== Phase 2: Container Security Tests ===${NC}"
echo "Testing container security configuration..."
echo

# Test 1: Verify no-new-privileges is set
echo -e "${YELLOW}Test 1: Checking no-new-privileges flag...${NC}"
CONTAINERS=$(docker compose -p lifeboard ps --services)
FAILURES=0

for container in $CONTAINERS; do
    CONTAINER_NAME="lifeboard-${container}-1"
    if docker inspect "$CONTAINER_NAME" >/dev/null 2>&1; then
        NO_NEW_PRIVS=$(docker inspect "$CONTAINER_NAME" --format='{{.HostConfig.SecurityOpt}}' 2>/dev/null | grep -o "no-new-privileges:true" || echo "")
        if [[ -n "$NO_NEW_PRIVS" ]]; then
            echo -e "${GREEN}✓ $container: no-new-privileges enabled${NC}"
        else
            echo -e "${RED}✗ $container: no-new-privileges NOT enabled${NC}"
            FAILURES=$((FAILURES + 1))
        fi
    else
        echo -e "${YELLOW}! $container: container not running${NC}"
    fi
done

# Test 2: Verify capabilities are dropped
echo -e "${YELLOW}Test 2: Checking dropped capabilities...${NC}"
for container in $CONTAINERS; do
    CONTAINER_NAME="lifeboard-${container}-1"
    if docker inspect "$CONTAINER_NAME" >/dev/null 2>&1; then
        CAP_DROP=$(docker inspect "$CONTAINER_NAME" --format='{{.HostConfig.CapDrop}}' 2>/dev/null)
        if [[ "$CAP_DROP" == *"ALL"* ]]; then
            echo -e "${GREEN}✓ $container: ALL capabilities dropped${NC}"
        else
            echo -e "${RED}✗ $container: capabilities not properly dropped ($CAP_DROP)${NC}"
            FAILURES=$((FAILURES + 1))
        fi
    fi
done

# Test 3: Verify network isolation
echo -e "${YELLOW}Test 3: Checking network isolation...${NC}"
NETWORK_NAME="lifeboard_net"
NETWORK_EXISTS=$(docker network ls --filter name="$NETWORK_NAME" --format "{{.Name}}" | grep -x "$NETWORK_NAME" || echo "")

if [[ -n "$NETWORK_EXISTS" ]]; then
    echo -e "${GREEN}✓ Isolated network '$NETWORK_NAME' exists${NC}"

    # Check that containers are on the isolated network
    for container in $CONTAINERS; do
        CONTAINER_NAME="lifeboard-${container}-1"
        if docker inspect "$CONTAINER_NAME" >/dev/null 2>&1; then
            NETWORKS=$(docker inspect "$CONTAINER_NAME" --format='{{range $net, $conf := .NetworkSettings.Networks}}{{$net}} {{end}}')
            if [[ "$NETWORKS" == *"$NETWORK_NAME"* ]]; then
                echo -e "${GREEN}✓ $container: connected to isolated network${NC}"
            else
                echo -e "${RED}✗ $container: not on isolated network (networks: $NETWORKS)${NC}"
                FAILURES=$((FAILURES + 1))
            fi
        fi
    done
else
    echo -e "${RED}✗ Isolated network '$NETWORK_NAME' does not exist${NC}"
    FAILURES=$((FAILURES + 1))
fi

# Test 4: Verify port mappings are restricted
echo -e "${YELLOW}Test 4: Checking port exposure...${NC}"
EXPOSED_PORTS=$(docker compose -p lifeboard ps --format "table {{.Service}}\t{{.Ports}}" | grep -v "SERVICE" | grep -v "^$")

# Count how many services have exposed ports
EXPOSED_COUNT=$(echo "$EXPOSED_PORTS" | grep -c "0.0.0.0" || echo "0")

echo "Services with exposed ports:"
echo "$EXPOSED_PORTS"

# For development environment, allow up to 3 services to be exposed:
# - db (database access)
# - rest (API access)
# - webui (web interface access)
if [[ $EXPOSED_COUNT -le 3 ]]; then  # Allow db, rest, and webui to be exposed in development
    echo -e "${GREEN}✓ Port exposure is acceptable for development environment ($EXPOSED_COUNT services exposed)${NC}"
else
    echo -e "${RED}✗ Too many services exposed ($EXPOSED_COUNT services exposed)${NC}"
    FAILURES=$((FAILURES + 1))
fi

# Test 5: Verify high-range port usage
echo -e "${YELLOW}Test 5: Checking high-range port usage...${NC}"
LOW_PORTS=$(echo "$EXPOSED_PORTS" | grep -E ":[0-9]{1,3}->" | grep -v ":[5-9][0-9][0-9][0-9]" || echo "")

if [[ -z "$LOW_PORTS" ]]; then
    echo -e "${GREEN}✓ All exposed ports use high-range numbers${NC}"
else
    echo -e "${RED}✗ Some services use low-range ports:${NC}"
    echo "$LOW_PORTS"
    FAILURES=$((FAILURES + 1))
fi

# Summary
echo
echo -e "${YELLOW}=== Phase 2 Security Test Summary ===${NC}"
if [[ $FAILURES -eq 0 ]]; then
    echo -e "${GREEN}All container security tests passed!${NC}"
    echo -e "${GREEN}✓ Network isolation configured${NC}"
    echo -e "${GREEN}✓ Container security hardening applied${NC}"
    echo -e "${GREEN}✓ Port exposure minimized${NC}"
    exit 0
else
    echo -e "${RED}$FAILURES security test(s) failed${NC}"
    echo "Please review container configuration and security settings"
    exit 1
fi
