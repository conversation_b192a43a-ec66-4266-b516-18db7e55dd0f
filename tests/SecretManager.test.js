
/**
 * SecretManager Unit and Integration Tests
 */
const fs = require('fs');
const path = require('path');
const os = require('os');
const { secretManager } = require('../desktop/core/secretManager/SecretManager');
const cryptoUtils = require('../desktop/core/pluginAPI/cryptoUtilities');
const supabaseClient = require('../desktop/core/supabase/client');

// Mock dependencies
const mockFileSystem = new Map();

jest.mock('fs', () => ({
    ...jest.requireActual('fs'),
    promises: {
        readFile: jest.fn().mockImplementation((path) => {
            if (mockFileSystem.has(path)) {
                return Promise.resolve(mockFileSystem.get(path));
            }
            return Promise.reject(new Error('ENOENT: no such file or directory'));
        }),
        writeFile: jest.fn().mockImplementation((path, data) => {
            mockFileSystem.set(path, data);
            return Promise.resolve();
        }),
        access: jest.fn().mockImplementation((path) => {
            if (mockFileSystem.has(path)) {
                return Promise.resolve();
            }
            return Promise.reject(new Error('ENOENT: no such file or directory'));
        }),
        mkdtemp: jest.requireActual('fs').promises.mkdtemp,
        rm: jest.requireActual('fs').promises.rm,
    },
}));

jest.mock('../desktop/core/logger/CoreLogger', () => ({
    factory: jest.fn(() => ({
        DEBUG: jest.fn(),
        INFO: jest.fn(),
        WARN: jest.fn(),
        ERROR: jest.fn(),
        getStats: jest.fn(() => ({
            startTimer: jest.fn(() => ({
                stop: jest.fn(),
            })),
        })),
    })),
}));


jest.mock('../desktop/core/pluginAPI/cryptoUtilities', () => ({
    encryptString: jest.fn().mockImplementation(str => `encrypted_${str}`),
    decryptString: jest.fn().mockImplementation(str => str.replace('encrypted_', '')),
}));

// Additional mock setup to ensure it's working
const originalCryptoUtils = require('../desktop/core/pluginAPI/cryptoUtilities');

jest.mock('../desktop/core/supabase/client', () => ({
    getClient: jest.fn(),
    isAuthenticated: jest.fn(),
}));

describe('SecretManager Tests', () => {
    let tempDir;
    const mockSupabaseRpc = jest.fn();

    beforeEach(async () => {
        tempDir = await fs.promises.mkdtemp(path.join(os.tmpdir(), 'secretmanager-test-'));

        // Reset mocks before each test
        jest.clearAllMocks();
        mockFileSystem.clear();

        // Reset and reconfigure crypto mocks
        cryptoUtils.encryptString.mockImplementation(str => `encrypted_${str}`);
        cryptoUtils.decryptString.mockImplementation(str => str.replace('encrypted_', ''));

        // Reset the SecretManager singleton
        secretManager.localStoragePath = path.join(tempDir, 'secure_storage.json');
        secretManager.initialized = false;
        secretManager.localCache = new Map();

        // Mock Supabase client
        supabaseClient.getClient.mockReturnValue({
            rpc: mockSupabaseRpc,
        });

        // Initialize SecretManager
        await secretManager.initialize();
    });

    afterEach(async () => {
        // Clean up temporary directory
        try {
            await fs.promises.access(tempDir);
            await fs.promises.rm(tempDir, { recursive: true, force: true });
        } catch (error) {
            // Directory doesn't exist, ignore
        }
    });

    describe('Local Storage Operations', () => {
        beforeEach(() => {
            supabaseClient.isAuthenticated.mockResolvedValue(false);
        });

        test('should store an API key to local storage', async () => {
            const pluginId = 'test-plugin';
            const keyName = 'apiKey';
            const keyValue = '12345';

            await secretManager.storeAPIKey(pluginId, keyName, keyValue);

            expect(fs.promises.writeFile).toHaveBeenCalledWith(
                secretManager.localStoragePath,
                expect.stringContaining(`"encrypted_${keyValue}"`)
            );
            expect(cryptoUtils.encryptString).toHaveBeenCalledWith(keyValue);
        });

        test('should retrieve an API key from local storage', async () => {
            const pluginId = 'test-plugin';
            const keyName = 'apiKey';
            const keyValue = '12345';

            // Simulate existing local storage
            const encryptedValue = cryptoUtils.encryptString(keyValue);
            const storageKey = `${pluginId}_${keyName}`;
            const fileContent = JSON.stringify({ [storageKey]: encryptedValue });
            fs.promises.readFile.mockResolvedValue(fileContent);

            // Force re-initialization to load the mock data
            secretManager.initialized = false;
            await secretManager.initialize();

            const retrievedKey = await secretManager.retrieveAPIKey(pluginId, keyName);

            expect(retrievedKey).toBe(keyValue);
            expect(cryptoUtils.decryptString).toHaveBeenCalledWith(encryptedValue);
        });

        test('should update an existing API key in local storage', async () => {
            const pluginId = 'test-plugin';
            const keyName = 'apiKey';
            const initialValue = 'initial-key';
            const updatedValue = 'updated-key';

            await secretManager.storeAPIKey(pluginId, keyName, initialValue);
            const result = await secretManager.updateAPIKey(pluginId, keyName, updatedValue);

            expect(result).toBe(true);
            const finalContent = JSON.parse(fs.promises.writeFile.mock.calls[1][1]);
            expect(finalContent[`${pluginId}_${keyName}`]).toBe(`encrypted_${updatedValue}`);
        });

        test('should delete an API key from local storage', async () => {
            const pluginId = 'test-plugin';
            const keyName = 'apiKey';
            const value = 'key-to-delete';

            await secretManager.storeAPIKey(pluginId, keyName, value);
            const result = await secretManager.deleteAPIKey(pluginId, keyName);

            expect(result).toBe(true);
            const finalContent = JSON.parse(fs.promises.writeFile.mock.calls[1][1]);
            expect(finalContent[`${pluginId}_${keyName}`]).toBeUndefined();
        });
    });

    describe('Database Operations', () => {
        beforeEach(() => {
            supabaseClient.isAuthenticated.mockResolvedValue(true);
        });

        test('should store an API key to the database', async () => {
            const pluginId = 'db-plugin';
            const keyName = 'db-key';
            const keyValue = 'db-secret';
            mockSupabaseRpc.mockResolvedValue({ data: 'some-uuid', error: null });

            await secretManager.storeAPIKey(pluginId, keyName, keyValue);

            expect(mockSupabaseRpc).toHaveBeenCalledWith('sp_create_api_key', {
                p_plugin_id: pluginId,
                p_key_name: keyName,
                p_raw_key: keyValue,
            });
            expect(fs.promises.writeFile).not.toHaveBeenCalled();
        });

        test('should retrieve an API key from the database', async () => {
            const pluginId = 'db-plugin';
            const keyName = 'db-key';
            const decryptedValue = 'db-secret-retrieved';
            mockSupabaseRpc.mockResolvedValue({
                data: [{ key_name: keyName, decrypted_key: decryptedValue }],
                error: null,
            });

            const result = await secretManager.retrieveAPIKey(pluginId, keyName);

            expect(mockSupabaseRpc).toHaveBeenCalledWith('sp_get_api_keys', {
                p_plugin_id: pluginId,
            });
            expect(result).toBe(decryptedValue);
        });

        test('should update an API key in the database', async () => {
             const pluginId = 'db-plugin';
            const keyName = 'db-key-update';
            const newValue = 'new-db-secret';
            const keyId = 'key-uuid';

            // Mock retrieval to find the key's ID
            mockSupabaseRpc.mockResolvedValueOnce({
                data: [{ id: keyId, key_name: keyName, decrypted_key: 'old-value' }],
                error: null,
            });
            // Mock update rpc call
            mockSupabaseRpc.mockResolvedValueOnce({ data: true, error: null });

            const result = await secretManager.updateAPIKey(pluginId, keyName, newValue);

            expect(result).toBe(true);
            expect(mockSupabaseRpc).toHaveBeenCalledWith('sp_update_api_key', {
                p_api_key_id: keyId,
                p_raw_key: newValue,
            });
        });

        test('should delete an API key from the database', async () => {
            const pluginId = 'db-plugin-delete';
            const keyName = 'db-key-delete';
            const keyId = 'key-uuid-delete';

            // Mock retrieval to find the key's ID
            mockSupabaseRpc.mockResolvedValueOnce({
                data: [{ id: keyId, key_name: keyName, decrypted_key: 'some-value' }],
                error: null,
            });
            // Mock delete rpc call
            mockSupabaseRpc.mockResolvedValueOnce({ data: true, error: null });

            const result = await secretManager.deleteAPIKey(pluginId, keyName);

            expect(result).toBe(true);
            expect(mockSupabaseRpc).toHaveBeenCalledWith('sp_delete_api_key', {
                p_api_key_id: keyId,
            });
        });
    });

    describe('Fallback Mechanism', () => {
         beforeEach(() => {
            supabaseClient.isAuthenticated.mockResolvedValue(true);
        });

        test('should fall back to local storage if database storage fails', async () => {
            const pluginId = 'fallback-plugin';
            const keyName = 'fallback-key';
            const keyValue = 'fallback-secret';

            // Simulate database failure
            mockSupabaseRpc.mockResolvedValue({ data: null, error: new Error('DB connection failed') });

            await secretManager.storeAPIKey(pluginId, keyName, keyValue);

            expect(mockSupabaseRpc).toHaveBeenCalledWith('sp_create_api_key', expect.any(Object));
            // Check if it fell back to local storage
            expect(fs.promises.writeFile).toHaveBeenCalledWith(
                secretManager.localStoragePath,
                expect.stringContaining(`encrypted_${keyValue}`)
            );
        });

        test('should fall back to local storage if database retrieval fails', async () => {
            const pluginId = 'fallback-retrieve';
            const keyName = 'fallback-key';
            const keyValue = 'local-secret';

            // Simulate database failure
            mockSupabaseRpc.mockResolvedValue({ data: null, error: new Error('DB read error') });

            // Set up local storage as a backup
            const encryptedValue = cryptoUtils.encryptString(keyValue);
            const storageKey = `${pluginId}_${keyName}`;
            const fileContent = JSON.stringify({ [storageKey]: encryptedValue });
            fs.promises.readFile.mockResolvedValue(fileContent);

            // Force re-initialization to load the mock data
            secretManager.initialized = false;
            await secretManager.initialize();

            const result = await secretManager.retrieveAPIKey(pluginId, keyName);

            expect(mockSupabaseRpc).toHaveBeenCalledWith('sp_get_api_keys', expect.any(Object));
            expect(result).toBe(keyValue);
        });
    });
});
