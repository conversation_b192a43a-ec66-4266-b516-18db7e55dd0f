-- Migration Smoke Test
-- Purpose: Verify all tables exist and basic structure is correct
-- Usage: Run after applying migrations to ensure schema is properly created

-- Test 1: Verify all core tables exist
DO $$
DECLARE
    missing_tables TEXT[];
    expected_tables TEXT[] := ARRAY['posts', 'tags', 'post_tags', 'plugins', 'plugin_logs', 'user_preferences', 'app_logs'];
    tbl_name TEXT;
BEGIN
    -- Check users table in auth schema
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.tables
WHERE information_schema.tables.table_name = 'users' AND table_schema = 'auth'
    ) THEN
        missing_tables := array_append(missing_tables, 'auth.users');
    END IF;

    -- Check other tables in public schema
    FOREACH tbl_name IN ARRAY expected_tables
    LOOP
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.tables t
            WHERE t.table_name = tbl_name AND t.table_schema = 'public'
        ) THEN
            missing_tables := array_append(missing_tables, tbl_name);
        END IF;
    END LOOP;

    IF array_length(missing_tables, 1) > 0 THEN
        RAISE EXCEPTION 'Missing tables: %', array_to_string(missing_tables, ', ');
    ELSE
        RAISE NOTICE 'SUCCESS: All core tables exist';
    END IF;
END $$;

-- Test 2: Verify all indexes exist
DO $$
DECLARE
    missing_indexes TEXT[];
    expected_indexes TEXT[] := ARRAY[
        'idx_posts_user_id', 'idx_posts_created_at', 'idx_posts_source_plugin',
        'idx_tags_user_id', 'idx_post_tags_post_id', 'idx_plugins_user_id',
        'idx_app_logs_created_at', 'idx_app_logs_level'
    ];
    index_name TEXT;
BEGIN
    FOREACH index_name IN ARRAY expected_indexes
    LOOP
        IF NOT EXISTS (
            SELECT 1 FROM pg_indexes
            WHERE indexname = index_name AND schemaname = 'public'
        ) THEN
            missing_indexes := array_append(missing_indexes, index_name);
        END IF;
    END LOOP;

    IF array_length(missing_indexes, 1) > 0 THEN
        RAISE EXCEPTION 'Missing indexes: %', array_to_string(missing_indexes, ', ');
    ELSE
        RAISE NOTICE 'SUCCESS: All required indexes exist';
    END IF;
END $$;

-- Test 3: Verify RLS is enabled on all tables
DO $$
DECLARE
    rls_disabled_tables TEXT[];
    table_name TEXT;
    rls_enabled BOOLEAN;
BEGIN
    FOR table_name IN SELECT tablename FROM pg_tables WHERE schemaname = 'public' AND tablename IN ('posts', 'tags', 'post_tags', 'plugins', 'plugin_logs', 'user_preferences')
    LOOP
        SELECT relrowsecurity INTO rls_enabled
        FROM pg_class
        WHERE relname = table_name;

        IF NOT rls_enabled THEN
            rls_disabled_tables := array_append(rls_disabled_tables, table_name);
        END IF;
    END LOOP;

    IF array_length(rls_disabled_tables, 1) > 0 THEN
        RAISE EXCEPTION 'RLS not enabled on tables: %', array_to_string(rls_disabled_tables, ', ');
    ELSE
        RAISE NOTICE 'SUCCESS: RLS enabled on all required tables';
    END IF;
END $$;

-- Test 4: Verify essential foreign key constraints exist
DO $$
DECLARE
    constraint_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO constraint_count
    FROM information_schema.table_constraints
    WHERE constraint_type = 'FOREIGN KEY'
    AND table_schema = 'public'
AND table_name IN ('posts', 'tags', 'post_tags', 'plugins', 'plugin_logs', 'user_preferences');

    IF constraint_count < 7 THEN  -- Expecting at least 7 FK constraints
        RAISE EXCEPTION 'Insufficient foreign key constraints found: % (expected at least 7)', constraint_count;
    ELSE
        RAISE NOTICE 'SUCCESS: Foreign key constraints properly configured (% found)', constraint_count;
    END IF;
END $$;

-- Test 5: Verify functions and triggers exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.routines
        WHERE routine_name = 'update_updated_at_column' AND routine_schema = 'public'
    ) THEN
        RAISE EXCEPTION 'Missing function: update_updated_at_column';
    END IF;

    IF (SELECT COUNT(*) FROM information_schema.triggers WHERE trigger_name LIKE '%update%updated_at%') < 3 THEN
        RAISE EXCEPTION 'Missing updated_at triggers';
    END IF;

    RAISE NOTICE 'SUCCESS: Functions and triggers properly configured';
END $$;

-- Test 6: Verify essential columns exist with correct types
DO $$
DECLARE
    test_passed BOOLEAN := true;
BEGIN
    -- Check posts table structure
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'posts' AND column_name = 'content' AND is_nullable = 'NO'
    ) THEN
        RAISE EXCEPTION 'Posts table missing required content column or it allows NULL';
    END IF;

    -- Check plugins table structure
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'plugins' AND column_name = 'configuration' AND data_type = 'jsonb'
    ) THEN
        RAISE EXCEPTION 'Plugins table missing configuration JSONB column';
    END IF;

    -- Check app_logs table structure
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'app_logs' AND column_name = 'level'
    ) THEN
        RAISE EXCEPTION 'App_logs table missing level column';
    END IF;

    RAISE NOTICE 'SUCCESS: Essential columns exist with correct types';
END $$;

-- Insert test completion log
INSERT INTO app_logs (level, service, message, context) VALUES
    ('INFO', 'test', 'Migration smoke test completed successfully',
     jsonb_build_object(
         'test_type', 'migration_smoke',
         'status', 'passed',
         'timestamp', NOW()::text
     ));
