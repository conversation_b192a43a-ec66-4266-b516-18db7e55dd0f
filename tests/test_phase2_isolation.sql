-- Phase 2 Container & Network Isolation Test
-- Purpose: Verify network isolation and security configuration
-- Usage: Run after applying Phase 2 configuration changes

-- Test 1: Verify database connectivity within the isolated network
DO $$
BEGIN
    -- This test runs inside the database container, confirming it's accessible
    IF (SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public') = 0 THEN
        RAISE EXCEPTION 'Database schema appears empty - network isolation may be blocking access';
    ELSE
        RAISE NOTICE 'SUCCESS: Database accessible within isolated network';
    END IF;
END $$;

-- Test 2: Verify core tables exist (ensuring migration worked with isolation)
DO $$
DECLARE
    table_count INTEGER;
    expected_tables TEXT[] := ARRAY['users', 'posts', 'tags', 'plugins', 'app_logs'];
    missing_tables TEXT[];
    tbl_name TEXT;
BEGIN
    FOREACH tbl_name IN ARRAY expected_tables
    LOOP
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.tables ist
            WHERE ist.table_name = tbl_name AND ist.table_schema = 'public'
        ) THEN
            missing_tables := array_append(missing_tables, tbl_name);
        END IF;
    END LOOP;

    IF array_length(missing_tables, 1) > 0 THEN
        RAISE EXCEPTION 'Missing tables in isolated environment: %', array_to_string(missing_tables, ', ');
    ELSE
        RAISE NOTICE 'SUCCESS: All core tables accessible in isolated network';
    END IF;
END $$;

-- Test 3: Verify data persistence across container restarts
DO $$
DECLARE
    user_count INTEGER;
    tag_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO user_count FROM users;
    SELECT COUNT(*) INTO tag_count FROM tags;

    IF user_count = 0 THEN
        RAISE EXCEPTION 'No users found - data may not be persisting across container restarts';
    END IF;

    IF tag_count < 5 THEN
        RAISE EXCEPTION 'Insufficient tags found (%) - data persistence issue', tag_count;
    END IF;

    RAISE NOTICE 'SUCCESS: Data persisted correctly (% users, % tags)', user_count, tag_count;
END $$;

-- Test 4: Verify logging functionality works in isolated environment
DO $$
DECLARE
    log_count_before INTEGER;
    log_count_after INTEGER;
BEGIN
    SELECT COUNT(*) INTO log_count_before FROM app_logs;

    -- Insert a test log entry
    INSERT INTO app_logs (level, service, message, context) VALUES
        ('INFO', 'isolation_test', 'Phase 2 network isolation test executed',
         ('{"test_phase": "2", "isolation": "verified", "timestamp": "' || NOW()::text || '"}')::jsonb);

    SELECT COUNT(*) INTO log_count_after FROM app_logs;

    IF log_count_after <= log_count_before THEN
        RAISE EXCEPTION 'Logging not working properly in isolated environment';
    ELSE
        RAISE NOTICE 'SUCCESS: Logging functional in isolated network (% total logs)', log_count_after;
    END IF;
END $$;

-- Test 5: Verify database performance is not degraded by isolation
DO $$
DECLARE
    start_time TIMESTAMP;
    end_time TIMESTAMP;
    duration_ms INTEGER;
BEGIN
    start_time := clock_timestamp();

    -- Perform a moderately complex query
    PERFORM COUNT(*) FROM posts p
    JOIN tags t ON t.user_id = p.user_id
    WHERE p.created_at > NOW() - INTERVAL '1 year';

    end_time := clock_timestamp();
    duration_ms := EXTRACT(milliseconds FROM (end_time - start_time));

    IF duration_ms > 1000 THEN  -- More than 1 second is concerning
        RAISE WARNING 'Query performance may be degraded: %ms', duration_ms;
    ELSE
        RAISE NOTICE 'SUCCESS: Database performance acceptable (%ms)', duration_ms;
    END IF;
END $$;

-- Insert test completion log
INSERT INTO app_logs (level, service, message, context) VALUES
    ('INFO', 'test', 'Phase 2 isolation test completed successfully',
     ('{"test_type": "phase2_isolation", "status": "passed", "timestamp": "' || NOW()::text || '"}')::jsonb);
