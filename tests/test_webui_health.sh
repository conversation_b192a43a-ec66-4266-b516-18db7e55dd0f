#!/bin/bash
# Web UI Health Check Test
# Phase 8: Minimal Web UI Infrastructure
# Purpose: Validate web UI service health, accessibility, and logging

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test configuration
WEBUI_PORT=9820
WEBUI_URL="http://localhost:${WEBUI_PORT}"
WEBUI_HEALTH_URL="${WEBUI_URL}/health"
LOG_DIR="./logs/webui"
TEST_SESSION_ID="webui_test_$(date +%s)"
TIMESTAMP=$(date -u +"%Y%m%d_%H%M%S")
LOG_FILE="${LOG_DIR}/webui_test_${TIMESTAMP}.log"

# Test tracking
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0
TEST_RESULTS=()

# Logging function
log_test_event() {
    local level=$1
    local component=$2
    local message=$3
    local extra_data=${4:-"{}"}

    local log_entry=$(cat <<EOF
{
    "timestamp": "$(date -u +"%Y-%m-%dT%H:%M:%S.%3NZ")",
    "level": "${level}",
    "component": "${component}",
    "message": "${message}",
    "session_id": "${TEST_SESSION_ID}",
    "test_phase": "webui_health",
    "extra_data": ${extra_data}
}
EOF
)
    echo "$log_entry" >> "$LOG_FILE"
}

# Test execution function
run_test() {
    local test_name=$1
    local test_function=$2

    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    echo -e "${YELLOW}Running: ${test_name}${NC}"
    log_test_event "info" "test_runner" "Starting test: ${test_name}"

    if $test_function; then
        echo -e "${GREEN}✓ ${test_name} PASSED${NC}"
        PASSED_TESTS=$((PASSED_TESTS + 1))
        TEST_RESULTS+=("✅ ${test_name} - PASSED")
        log_test_event "info" "test_runner" "Test passed: ${test_name}"
        return 0
    else
        echo -e "${RED}✗ ${test_name} FAILED${NC}"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        TEST_RESULTS+=("❌ ${test_name} - FAILED")
        log_test_event "error" "test_runner" "Test failed: ${test_name}"
        return 1
    fi
}

# Test 1: Check if webui directory and files exist
test_webui_files() {
    log_test_event "debug" "test_webui_files" "Checking webui directory structure"

    if [ ! -d "./webui" ]; then
        log_test_event "error" "test_webui_files" "webui directory not found"
        return 1
    fi

    if [ ! -f "./webui/index.html" ]; then
        log_test_event "error" "test_webui_files" "index.html not found"
        return 1
    fi

    log_test_event "info" "test_webui_files" "All webui files present and valid"
    return 0
}

# Test 2: Check if docker-compose.web-ui.yml exists and is valid
test_docker_compose_config() {
    log_test_event "debug" "test_docker_compose_config" "Validating docker-compose configuration"

    if [ ! -f "./docker-compose.web-ui.yml" ]; then
        log_test_event "error" "test_docker_compose_config" "docker-compose.web-ui.yml not found"
        return 1
    fi

    # Check if it contains webui service
    if ! grep -q "webui:" "./docker-compose.web-ui.yml"; then
        log_test_event "error" "test_docker_compose_config" "webui service not found in compose file"
        return 1
    fi

    # Check if it uses nginx:alpine
    if ! grep -q "nginx:alpine" "./docker-compose.web-ui.yml"; then
        log_test_event "error" "test_docker_compose_config" "nginx:alpine image not specified"
        return 1
    fi

    # Check if port 9820 is configured
    if ! grep -q "9820:80" "./docker-compose.web-ui.yml"; then
        log_test_event "error" "test_docker_compose_config" "Port 9820 mapping not found"
        return 1
    fi

    log_test_event "info" "test_docker_compose_config" "Docker compose configuration valid"
    return 0
}

# Test 3: Check if nginx configuration exists
test_nginx_config() {
    log_test_event "debug" "test_nginx_config" "Checking nginx configuration"

    if [ ! -f "./config/nginx/nginx.conf" ]; then
        log_test_event "error" "test_nginx_config" "nginx.conf not found"
        return 1
    fi

    # Check if it contains JSON logging format
    if ! grep -q "json_combined" "./config/nginx/nginx.conf"; then
        log_test_event "error" "test_nginx_config" "JSON logging format not configured"
        return 1
    fi

    log_test_event "info" "test_nginx_config" "Nginx configuration valid"
    return 0
}

# Test 4: Check if logging directory exists
test_logging_setup() {
    log_test_event "debug" "test_logging_setup" "Validating logging directory structure"

    if [ ! -d "$LOG_DIR" ]; then
        log_test_event "error" "test_logging_setup" "Logging directory $LOG_DIR not found"
        return 1
    fi

    # Test if we can write to log directory
    if ! touch "${LOG_DIR}/test_write.tmp" 2>/dev/null; then
        log_test_event "error" "test_logging_setup" "Cannot write to logging directory"
        return 1
    fi
    rm -f "${LOG_DIR}/test_write.tmp"

    log_test_event "info" "test_logging_setup" "Logging setup valid"
    return 0
}

# Test 5: Check if webui service is running (if docker is available)
test_webui_service_availability() {
    log_test_event "debug" "test_webui_service" "Checking if webui service is accessible"

    # Check if service is running by testing the port
    if command -v curl >/dev/null 2>&1; then
        # Try to connect with a short timeout
        if curl -s --connect-timeout 5 --max-time 10 "$WEBUI_URL" >/dev/null 2>&1; then
            log_test_event "info" "test_webui_service" "Web UI service is accessible at $WEBUI_URL"
            return 0
        else
            log_test_event "warning" "test_webui_service" "Web UI service not accessible (may not be running)"
            # This is a warning, not a failure, as the service might not be started
            return 0
        fi
    else
        log_test_event "warning" "test_webui_service" "curl not available, skipping service connectivity test"
        return 0
    fi
}

# Test 6: Validate compose file syntax
test_compose_syntax() {
    log_test_event "debug" "test_compose_syntax" "Validating Docker Compose syntax"

    if command -v docker >/dev/null 2>&1 && command -v docker-compose >/dev/null 2>&1; then
        if docker-compose -f docker-compose.web-ui.yml config >/dev/null 2>&1; then
            log_test_event "info" "test_compose_syntax" "Docker Compose syntax valid"
            return 0
        else
            log_test_event "error" "test_compose_syntax" "Docker Compose syntax validation failed"
            return 1
        fi
    else
        log_test_event "warning" "test_compose_syntax" "Docker/docker-compose not available, skipping syntax validation"
        return 0
    fi
}

# Test 7: Security configuration validation
test_security_config() {
    log_test_event "debug" "test_security_config" "Validating security configuration"

    # Check for security hardening in compose file
    if ! grep -q "no-new-privileges:true" "./docker-compose.web-ui.yml"; then
        log_test_event "error" "test_security_config" "Security hardening (no-new-privileges) not configured"
        return 1
    fi

    # Check for capability drops
    if ! grep -q "cap_drop:" "./docker-compose.web-ui.yml"; then
        log_test_event "error" "test_security_config" "Capability drops not configured"
        return 1
    fi

    # Check for read-only filesystem
    if ! grep -q "read_only: true" "./docker-compose.web-ui.yml"; then
        log_test_event "error" "test_security_config" "Read-only filesystem not configured"
        return 1
    fi

    log_test_event "info" "test_security_config" "Security configuration valid"
    return 0
}

# Main execution
echo -e "${BLUE}============================================${NC}"
echo -e "${BLUE}  Web UI Health Check Test Suite${NC}"
echo -e "${BLUE}  Phase 8: Minimal Web UI Infrastructure${NC}"
echo -e "${BLUE}============================================${NC}"
echo "Testing web UI infrastructure..."
echo "Session ID: $TEST_SESSION_ID"
echo "Log file: $LOG_FILE"
echo

# Ensure log directory exists
mkdir -p "$LOG_DIR"

# Initialize log file
log_test_event "info" "test_suite" "Starting Web UI health check test suite"

# Run all tests
run_test "Web UI Files Structure" test_webui_files
run_test "Docker Compose Configuration" test_docker_compose_config
run_test "Nginx Configuration" test_nginx_config
run_test "Logging Setup" test_logging_setup
run_test "Web UI Service Availability" test_webui_service_availability
run_test "Compose File Syntax" test_compose_syntax
run_test "Security Configuration" test_security_config

echo
echo -e "${BLUE}============================================${NC}"
echo -e "${BLUE}  Web UI Test Results Summary${NC}"
echo -e "${BLUE}============================================${NC}"

for result in "${TEST_RESULTS[@]}"; do
    echo -e "$result"
done

echo
echo -e "${BLUE}Overall Results:${NC}"
echo -e "Total Tests: $TOTAL_TESTS"
echo -e "${GREEN}Passed: $PASSED_TESTS${NC}"
echo -e "${RED}Failed: $FAILED_TESTS${NC}"

log_test_event "info" "test_suite" "Test suite completed" "{\"total_tests\": $TOTAL_TESTS, \"passed\": $PASSED_TESTS, \"failed\": $FAILED_TESTS}"

if [ $FAILED_TESTS -eq 0 ]; then
    echo -e "${GREEN}🎉 ALL WEB UI TESTS PASSED! 🎉${NC}"
    echo -e "${GREEN}✓ Web UI infrastructure ready${NC}"
    echo -e "${GREEN}✓ Docker configuration valid${NC}"
    echo -e "${GREEN}✓ Nginx configuration valid${NC}"
    echo -e "${GREEN}✓ Logging setup complete${NC}"
    echo -e "${GREEN}✓ Security hardening applied${NC}"
    echo
    echo -e "${BLUE}Web UI is ready for deployment!${NC}"
    echo -e "${BLUE}Start with: docker compose --profile webui up${NC}"
    echo -e "${BLUE}Access at: http://localhost:9820${NC}"
    exit 0
else
    PASS_RATE=$(( (PASSED_TESTS * 100) / TOTAL_TESTS ))
    echo -e "${RED}❌ Some web UI tests failed (${PASS_RATE}% pass rate)${NC}"
    echo -e "${YELLOW}Please review failed tests and fix issues before proceeding${NC}"
    exit 1
fi
