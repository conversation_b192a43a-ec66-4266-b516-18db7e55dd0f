#!/bin/bash

# test_observability_logging_plugin.sh
# CI Test for CoreLogger functionality and plugin logging integration
#
# This test verifies that plugins are generating sufficient DEBUG logs
# as required by the Logging Infrastructure Reboot plan

set -e

echo "🧪 Testing Observability Logging - Plugin Integration"
echo "====================================================="

# Configuration
DESKTOP_DIR="/Users/<USER>/code/lifeboard-supabase/desktop"
LOGS_DIR="/Users/<USER>/code/lifeboard-supabase/logs"
TEST_TIMEOUT=30
MIN_DEBUG_LOGS=10

# Cleanup function
cleanup() {
    echo "🧹 Cleaning up test environment..."
    if [ -n "$APP_PID" ]; then
        kill $APP_PID 2>/dev/null || true
        wait $APP_PID 2>/dev/null || true
    fi

    # Clean test log files
    rm -f "$LOGS_DIR"/test-*.log

    echo "✅ Cleanup completed"
}

# Set trap for cleanup on exit
trap cleanup EXIT

# Function to check if CoreLogger is properly integrated
check_corelogger_integration() {
    echo "🔍 Checking CoreLogger integration..."

    # Check if CoreLogger exists
    if [ ! -f "$DESKTOP_DIR/core/logger/CoreLogger.js" ]; then
        echo "❌ CoreLogger module not found"
        return 1
    fi

    # Check if PluginAPI exposes logger
    if [ ! -f "$DESKTOP_DIR/core/pluginAPI/index.js" ]; then
        echo "❌ PluginAPI module not found"
        return 1
    fi

    # Check if getLogger function exists in PluginAPI
    if ! grep -q "getLogger" "$DESKTOP_DIR/core/pluginAPI/index.js"; then
        echo "❌ getLogger function not found in PluginAPI"
        return 1
    fi

    echo "✅ CoreLogger integration verified"
    return 0
}

# Function to check plugin manager integration
check_plugin_manager_integration() {
    echo "🔧 Checking Plugin Manager CoreLogger integration..."

    if [ ! -f "$DESKTOP_DIR/src/plugin-manager.js" ]; then
        echo "❌ Plugin Manager not found"
        return 1
    fi

    # Check if plugin-manager imports CoreLogger
    if ! grep -q "CoreLogger" "$DESKTOP_DIR/src/plugin-manager.js"; then
        echo "❌ Plugin Manager does not import CoreLogger"
        return 1
    fi

    # Check if plugin-manager creates CoreLogger instance
    if ! grep -q "createLogger" "$DESKTOP_DIR/src/plugin-manager.js"; then
        echo "❌ Plugin Manager does not create CoreLogger instance"
        return 1
    fi

    echo "✅ Plugin Manager CoreLogger integration verified"
    return 0
}

# Function to check main.js integration
check_main_integration() {
    echo "🎯 Checking Main Process CoreLogger integration..."

    if [ ! -f "$DESKTOP_DIR/src/main.js" ]; then
        echo "❌ Main process file not found"
        return 1
    fi

    # Check if main.js imports CoreLogger instead of electron-log
    if grep -q "electron-log" "$DESKTOP_DIR/src/main.js"; then
        echo "❌ Main process still using electron-log instead of CoreLogger"
        return 1
    fi

    if ! grep -q "CoreLogger" "$DESKTOP_DIR/src/main.js"; then
        echo "❌ Main process does not import CoreLogger"
        return 1
    fi

    echo "✅ Main Process CoreLogger integration verified"
    return 0
}

# Function to ensure logs directory exists
setup_logs_directory() {
    echo "📁 Setting up logs directory..."

    if [ ! -d "$LOGS_DIR" ]; then
        mkdir -p "$LOGS_DIR"
        echo "✅ Created logs directory: $LOGS_DIR"
    else
        echo "✅ Logs directory exists: $LOGS_DIR"
    fi

    # Set proper permissions (as specified in plan)
    chmod 700 "$LOGS_DIR"
    echo "✅ Set logs directory permissions to 700"
}

# Function to start desktop app in test mode
start_desktop_app() {
    echo "🚀 Starting desktop application for testing..."

    cd "$DESKTOP_DIR"

    # Set environment variable for test mode
    export NODE_ENV=test
    export LIFEBOARD_LOG_LEVEL=DEBUG

    # Start the app in background
    npm run electron-dev > /dev/null 2>&1 &
    APP_PID=$!

    echo "✅ Desktop app started (PID: $APP_PID)"

    # Wait for app to initialize
    echo "⏳ Waiting for app initialization..."
    sleep 10

    # Check if process is still running
    if ! kill -0 $APP_PID 2>/dev/null; then
        echo "❌ Desktop app failed to start or crashed"
        return 1
    fi

    echo "✅ Desktop app is running"
    return 0
}

# Function to wait for log generation
wait_for_logs() {
    echo "⏳ Waiting for log generation (${TEST_TIMEOUT}s timeout)..."

    local start_time=$(date +%s)
    local current_time

    while true; do
        current_time=$(date +%s)
        elapsed=$((current_time - start_time))

        if [ $elapsed -ge $TEST_TIMEOUT ]; then
            echo "⏰ Test timeout reached (${TEST_TIMEOUT}s)"
            break
        fi

        # Check if any log files exist
        if ls "$LOGS_DIR"/*.log >/dev/null 2>&1; then
            echo "✅ Log files detected"
            break
        fi

        sleep 2
    done
}

# Function to analyze generated logs
analyze_logs() {
    echo "📊 Analyzing generated logs..."

    local today=$(date +%Y-%m-%d)
    local debug_count=0
    local total_count=0

    # Count logs by examining all log files from today
    for log_file in "$LOGS_DIR"/${today}-*.log; do
        if [ -f "$log_file" ]; then
            echo "📄 Analyzing log file: $(basename "$log_file")"

            # Count total log entries
            local file_total=$(wc -l < "$log_file" 2>/dev/null || echo "0")
            total_count=$((total_count + file_total))

            # Count DEBUG level logs
            local file_debug=$(grep -c '"level":"DEBUG"' "$log_file" 2>/dev/null || echo "0")
            debug_count=$((debug_count + file_debug))

            echo "  📈 Total entries: $file_total"
            echo "  🐛 DEBUG entries: $file_debug"
        fi
    done

    echo ""
    echo "📊 Log Analysis Summary:"
    echo "  📝 Total log entries: $total_count"
    echo "  🐛 DEBUG log entries: $debug_count"
    echo "  🎯 Required DEBUG logs: $MIN_DEBUG_LOGS"

    # Verify minimum DEBUG logs requirement
    if [ $debug_count -ge $MIN_DEBUG_LOGS ]; then
        echo "✅ DEBUG logging requirement met ($debug_count >= $MIN_DEBUG_LOGS)"
        return 0
    else
        echo "❌ Insufficient DEBUG logs generated ($debug_count < $MIN_DEBUG_LOGS)"
        return 1
    fi
}

# Function to validate log format
validate_log_format() {
    echo "🔍 Validating log format (JSON schema compliance)..."

    local today=$(date +%Y-%m-%d)
    local format_valid=true

    for log_file in "$LOGS_DIR"/${today}-*.log; do
        if [ -f "$log_file" ]; then
            echo "📋 Checking format of: $(basename "$log_file")"

            # Read first few lines and validate JSON format
            while IFS= read -r line; do
                if [ -n "$line" ]; then
                    # Check if it's valid JSON
                    if ! echo "$line" | jq empty >/dev/null 2>&1; then
                        echo "❌ Invalid JSON format found: $line"
                        format_valid=false
                        break
                    fi

                    # Check for required schema fields
                    local has_ts=$(echo "$line" | jq -e '.ts' >/dev/null 2>&1 && echo "true" || echo "false")
                    local has_level=$(echo "$line" | jq -e '.level' >/dev/null 2>&1 && echo "true" || echo "false")
                    local has_component=$(echo "$line" | jq -e '.component' >/dev/null 2>&1 && echo "true" || echo "false")
                    local has_msg=$(echo "$line" | jq -e '.msg' >/dev/null 2>&1 && echo "true" || echo "false")

                    if [ "$has_ts" = "false" ] || [ "$has_level" = "false" ] || [ "$has_component" = "false" ] || [ "$has_msg" = "false" ]; then
                        echo "❌ Missing required schema fields in: $line"
                        format_valid=false
                        break
                    fi
                fi
            done < <(head -5 "$log_file")
        fi
    done

    if [ "$format_valid" = "true" ]; then
        echo "✅ Log format validation passed"
        return 0
    else
        echo "❌ Log format validation failed"
        return 1
    fi
}

# Function to check plugin-specific logging
check_plugin_logging() {
    echo "🔌 Checking plugin-specific logging..."

    local today=$(date +%Y-%m-%d)
    local plugin_logs_found=false

    # Look for plugin component logs
    for log_file in "$LOGS_DIR"/${today}-*.log; do
        if [ -f "$log_file" ]; then
            # Check for plugin component entries
            if grep -q '"component":"plugin:' "$log_file" 2>/dev/null; then
                plugin_logs_found=true
                local plugin_count=$(grep -c '"component":"plugin:' "$log_file" 2>/dev/null || echo "0")
                echo "✅ Found $plugin_count plugin log entries in $(basename "$log_file")"
            fi
        fi
    done

    if [ "$plugin_logs_found" = "true" ]; then
        echo "✅ Plugin logging verification passed"
        return 0
    else
        echo "⚠️  No plugin-specific log entries found (this may be normal if no plugins are active)"
        return 0
    fi
}

# Main test execution
main() {
    echo "🎬 Starting Observability Logging Test Suite"
    echo ""

    # Check dependencies
    if ! command -v jq &> /dev/null; then
        echo "❌ jq is required for log format validation"
        echo "📦 Install with: brew install jq"
        exit 1
    fi

    # Run integration checks
    check_corelogger_integration || exit 1
    echo ""

    check_plugin_manager_integration || exit 1
    echo ""

    check_main_integration || exit 1
    echo ""

    # Setup environment
    setup_logs_directory || exit 1
    echo ""

    # Start application and test logging
    start_desktop_app || exit 1
    echo ""

    wait_for_logs
    echo ""

    # Analyze results
    analyze_logs || exit 1
    echo ""

    validate_log_format || exit 1
    echo ""

    check_plugin_logging
    echo ""

    echo "🎉 Observability Logging Test Suite Completed Successfully!"
    echo ""
    echo "📋 Test Results Summary:"
    echo "✅ CoreLogger integration verified"
    echo "✅ Plugin Manager integration verified"
    echo "✅ Main Process integration verified"
    echo "✅ Log directory setup completed"
    echo "✅ Desktop application launched successfully"
    echo "✅ Minimum DEBUG logs requirement met"
    echo "✅ JSON log format validation passed"
    echo "✅ Plugin logging functionality verified"
    echo ""
    echo "🚀 Ready for production deployment!"
}

# Execute main function
main "$@"
