/**
 * CryptoUtilities Unit Tests
 *
 * Tests for encryption and decryption functionality used by SecretManager
 */

const { encryptString, decryptString } = require('../desktop/core/pluginAPI/cryptoUtilities');

describe('CryptoUtilities Tests', () => {
    const testKeys = [
        'simple-key',
        'complex-key-with-special-chars!@#$%^&*()',
        'very-long-key-that-contains-lots-of-characters-and-numbers-123456789',
        'unicode-key-with-emojis-🔐🔑🛡️',
        '',
        'key with spaces',
        'key\nwith\nnewlines',
        'key\twith\ttabs',
        JSON.stringify({ complex: 'object', with: ['arrays', 'and', 'nested', { objects: true }] })
    ];

    describe('Encryption and Decryption', () => {
        test.each(testKeys)('should encrypt and decrypt: %s', (testKey) => {
            const encrypted = encryptString(testKey);
            const decrypted = decryptString(encrypted);

            expect(decrypted).toBe(testKey);
            expect(encrypted).not.toBe(testKey);
            expect(encrypted).toBeDefined();
            expect(encrypted.length).toBeGreaterThan(0);
        });

        test('should produce different encrypted values for the same input', () => {
            const testKey = 'test-key-for-randomness';
            const encrypted1 = encryptString(testKey);
            const encrypted2 = encryptString(testKey);

            // Due to crypto randomness, encrypted values should be different
            expect(encrypted1).not.toBe(encrypted2);

            // But both should decrypt to the same value
            expect(decryptString(encrypted1)).toBe(testKey);
            expect(decryptString(encrypted2)).toBe(testKey);
        });

        test('should handle empty string', () => {
            const emptyString = '';
            const encrypted = encryptString(emptyString);
            const decrypted = decryptString(encrypted);

            expect(decrypted).toBe(emptyString);
        });

        test('should handle null and undefined gracefully', () => {
            expect(() => encryptString(null)).toThrow();
            expect(() => encryptString(undefined)).toThrow();
            expect(() => decryptString(null)).toThrow();
            expect(() => decryptString(undefined)).toThrow();
        });
    });

    describe('Environment Variable Handling', () => {
        let originalEnv;

        beforeEach(() => {
            originalEnv = process.env.VAULT_ENC_KEY;
        });

        afterEach(() => {
            if (originalEnv) {
                process.env.VAULT_ENC_KEY = originalEnv;
            } else {
                delete process.env.VAULT_ENC_KEY;
            }
        });

        test('should use custom encryption key from environment variable', () => {
            process.env.VAULT_ENC_KEY = 'custom-encryption-key';

            // Re-require the module to pick up the new environment variable
            jest.resetModules();
            const { encryptString, decryptString } = require('../desktop/core/pluginAPI/cryptoUtilities');

            const testKey = 'test-with-custom-key';
            const encrypted = encryptString(testKey);
            const decrypted = decryptString(encrypted);

            expect(decrypted).toBe(testKey);
        });

        test('should use default key when environment variable is not set', () => {
            delete process.env.VAULT_ENC_KEY;

            // Re-require the module to pick up the change
            jest.resetModules();
            const { encryptString, decryptString } = require('../desktop/core/pluginAPI/cryptoUtilities');

            const testKey = 'test-with-default-key';
            const encrypted = encryptString(testKey);
            const decrypted = decryptString(encrypted);

            expect(decrypted).toBe(testKey);
        });
    });

    describe('Error Handling', () => {
        test('should throw error when decrypting invalid encrypted data', () => {
            expect(() => decryptString('invalid-encrypted-data')).toThrow();
        });

        test('should throw error when decrypting with wrong key', () => {
            const originalKey = process.env.VAULT_ENC_KEY;
            process.env.VAULT_ENC_KEY = 'original-key';

            jest.resetModules();
            let { encryptString } = require('../desktop/core/pluginAPI/cryptoUtilities');

            const testKey = 'test-key';
            const encrypted = encryptString(testKey);

            // Change the key
            process.env.VAULT_ENC_KEY = 'different-key';

            jest.resetModules();
            const { decryptString } = require('../desktop/core/pluginAPI/cryptoUtilities');

            expect(() => decryptString(encrypted)).toThrow();

            // Restore original key
            if (originalKey) {
                process.env.VAULT_ENC_KEY = originalKey;
            } else {
                delete process.env.VAULT_ENC_KEY;
            }
        });
    });

    describe('Performance and Security', () => {
        test('should encrypt and decrypt large strings efficiently', () => {
            const largeString = 'x'.repeat(10000);

            const startTime = Date.now();
            const encrypted = encryptString(largeString);
            const decrypted = decryptString(encrypted);
            const endTime = Date.now();

            expect(decrypted).toBe(largeString);
            expect(endTime - startTime).toBeLessThan(1000); // Should complete within 1 second
        });

        test('should produce encrypted output that is not obviously related to input', () => {
            const testKey = 'predictable-input-123';
            const encrypted = encryptString(testKey);

            // Encrypted value should not contain the original string
            expect(encrypted).not.toContain(testKey);
            expect(encrypted).not.toContain('predictable');
            expect(encrypted).not.toContain('123');
        });
    });
});
