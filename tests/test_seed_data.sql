-- Seed Data Test
-- Purpose: Verify seed data was inserted correctly and minimum data exists
-- Usage: Run after seed scripts to ensure basic data is available

-- Test 1: Verify minimum tag count
DO $$
DECLARE
    tag_count INTEGER;
    expected_min_tags INTEGER := 10;
BEGIN
    SELECT COUNT(*) INTO tag_count FROM tags;

    IF tag_count < expected_min_tags THEN
        RAISE EXCEPTION 'Insufficient tags: % found, expected at least %', tag_count, expected_min_tags;
    ELSE
        RAISE NOTICE 'SUCCESS: Found % tags (expected at least %)', tag_count, expected_min_tags;
    END IF;
END $$;

-- Test 2: Verify minimum plugin count
DO $$
DECLARE
    plugin_count INTEGER;
    expected_min_plugins INTEGER := 3;
BEGIN
    SELECT COUNT(*) INTO plugin_count FROM plugins;

    IF plugin_count < expected_min_plugins THEN
        RAISE EXCEPTION 'Insufficient plugins: % found, expected at least %', plugin_count, expected_min_plugins;
    ELSE
        RAISE NOTICE 'SUCCESS: Found % plugins (expected at least %)', plugin_count, expected_min_plugins;
    END IF;
END $$;

-- Test 3: Verify essential tags exist
DO $$
DECLARE
    missing_tags TEXT[];
    essential_tags TEXT[] := ARRAY['reflection', 'growth', 'gratitude', 'relationships', 'health'];
    tag_name TEXT;
BEGIN
    FOREACH tag_name IN ARRAY essential_tags
    LOOP
        IF NOT EXISTS (SELECT 1 FROM tags WHERE name = tag_name) THEN
            missing_tags := array_append(missing_tags, tag_name);
        END IF;
    END LOOP;

    IF array_length(missing_tags, 1) > 0 THEN
        RAISE EXCEPTION 'Missing essential tags: %', array_to_string(missing_tags, ', ');
    ELSE
        RAISE NOTICE 'SUCCESS: All essential tags exist';
    END IF;
END $$;

-- Test 4: Verify essential plugins exist
DO $$
DECLARE
    missing_plugins TEXT[];
    essential_plugins TEXT[] := ARRAY['manual-journal', 'location-tracker', 'spotify-integration'];
    plugin_name TEXT;
BEGIN
    FOREACH plugin_name IN ARRAY essential_plugins
    LOOP
        IF NOT EXISTS (SELECT 1 FROM plugins WHERE name = plugin_name) THEN
            missing_plugins := array_append(missing_plugins, plugin_name);
        END IF;
    END LOOP;

    IF array_length(missing_plugins, 1) > 0 THEN
        RAISE EXCEPTION 'Missing essential plugins: %', array_to_string(missing_plugins, ', ');
    ELSE
        RAISE NOTICE 'SUCCESS: All essential plugins exist';
    END IF;
END $$;

-- Test 5: Verify plugin configurations are valid JSON
DO $$
DECLARE
    invalid_config_count INTEGER;
BEGIN
    -- Try to parse all plugin configurations as JSON
    SELECT COUNT(*) INTO invalid_config_count
    FROM plugins
    WHERE (configuration IS NULL OR NOT (configuration::text)::json IS NOT NULL);

    IF invalid_config_count > 0 THEN
        RAISE EXCEPTION 'Found % plugins with invalid JSON configuration', invalid_config_count;
    ELSE
        RAISE NOTICE 'SUCCESS: All plugin configurations contain valid JSON';
    END IF;
END $$;

-- Test 6: Verify tag colors are valid hex codes
DO $$
DECLARE
    invalid_color_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO invalid_color_count
    FROM tags
    WHERE (color IS NULL OR NOT color ~ '^#[0-9A-Fa-f]{6}$');

    IF invalid_color_count > 0 THEN
        RAISE EXCEPTION 'Found % tags with invalid color hex codes', invalid_color_count;
    ELSE
        RAISE NOTICE 'SUCCESS: All tag colors are valid hex codes';
    END IF;
END $$;

-- Test 7: Verify default user preferences template exists
DO $$
DECLARE
    pref_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO pref_count
    FROM user_preferences;

    IF pref_count = 0 THEN
        RAISE EXCEPTION 'No default user preferences template found';
    ELSE
        RAISE NOTICE 'SUCCESS: Default user preferences template exists';
    END IF;
END $$;

-- Test 8: Verify app logs contain seeding entries
DO $$
DECLARE
    seed_log_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO seed_log_count
    FROM app_logs
    WHERE service = 'database'
    AND message LIKE '%seed%';

    IF seed_log_count = 0 THEN
        RAISE EXCEPTION 'No seeding log entries found in app_logs';
    ELSE
        RAISE NOTICE 'SUCCESS: Found % seeding log entries', seed_log_count;
    END IF;
END $$;

-- Test 9: Verify data integrity - no orphaned references
DO $$
DECLARE
    orphaned_count INTEGER;
BEGIN
    -- Check for orphaned post_tags (should be 0 since we only have templates)
    SELECT COUNT(*) INTO orphaned_count
    FROM post_tags pt
    LEFT JOIN posts p ON pt.post_id = p.id
    LEFT JOIN tags t ON pt.tag_id = t.id
    WHERE p.id IS NULL OR t.id IS NULL;

    IF orphaned_count > 0 THEN
        RAISE EXCEPTION 'Found % orphaned post_tag relationships', orphaned_count;
    ELSE
        RAISE NOTICE 'SUCCESS: No orphaned relationships found';
    END IF;
END $$;

-- Insert test completion log
INSERT INTO app_logs (level, service, message, context) VALUES
    ('INFO', 'test', 'Seed data test completed successfully',
     jsonb_build_object(
         'test_type', 'seed_data',
         'status', 'passed',
         'timestamp', NOW()::text
     ));
