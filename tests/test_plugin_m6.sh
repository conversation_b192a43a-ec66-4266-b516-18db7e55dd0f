#!/bin/bash

# Test Plugin M6: Marketplace & Package Management Implementation
# Tests the comprehensive M6 implementation including:
# - MarketplaceManager with plugin discovery and installation
# - PackageManager with ZIP creation and signing
# - CLI tool for plugin operations
# - Plugin Manager integration with marketplace features
# - IPC communication for marketplace operations

set -e

echo "🧪 Starting Plugin M6 Marketplace Implementation Tests"
echo "====================================================="

# Navigate to desktop directory
cd "$(dirname "$0")/../desktop"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test counters
TESTS_RUN=0
TESTS_PASSED=0
TESTS_FAILED=0

# Function to run a test
run_test() {
    local test_name="$1"
    local test_command="$2"

    echo -e "${BLUE}Testing: $test_name${NC}"
    TESTS_RUN=$((TESTS_RUN + 1))

    if eval "$test_command"; then
        echo -e "${GREEN}✓ PASS: $test_name${NC}"
        TESTS_PASSED=$((TESTS_PASSED + 1))
    else
        echo -e "${RED}✗ FAIL: $test_name${NC}"
        TESTS_FAILED=$((TESTS_FAILED + 1))
    fi
    echo
}

# Function to check file exists
check_file() {
    if [ -f "$1" ]; then
        return 0
    else
        echo "File not found: $1"
        return 1
    fi
}

# Function to check directory exists
check_directory() {
    if [ -d "$1" ]; then
        return 0
    else
        echo "Directory not found: $1"
        return 1
    fi
}

# Function to check if class/function exists in file
check_class_exists() {
    local file="$1"
    local class_name="$2"

    if grep -q "class $class_name" "$file"; then
        return 0
    else
        echo "Class $class_name not found in $file"
        return 1
    fi
}

# Function to check if method exists in file
check_method_exists() {
    local file="$1"
    local method_name="$2"

    if grep -q "$method_name" "$file"; then
        return 0
    else
        echo "Method $method_name not found in $file"
        return 1
    fi
}

# Function to check package.json dependencies
check_dependency() {
    local dependency="$1"

    if grep -q "\"$dependency\"" "package.json"; then
        return 0
    else
        echo "Dependency $dependency not found in package.json"
        return 1
    fi
}

echo "🔍 Phase 1: M6 File Structure and Dependencies"
echo "=============================================="

# Test 1: M6 Marketplace Infrastructure Files
run_test "Marketplace directory exists" "check_directory 'src/marketplace'"
run_test "MarketplaceManager exists" "check_file 'src/marketplace/MarketplaceManager.js'"
run_test "PackageManager exists" "check_file 'src/marketplace/PackageManager.js'"

# Test 2: M6 CLI Infrastructure Files
run_test "CLI directory exists" "check_directory 'src/cli'"
run_test "Plugin CLI exists" "check_file 'src/cli/plugin-cli.js'"
run_test "CLI is executable" "test -x 'src/cli/plugin-cli.js'"

# Test 3: M6 UI Files
run_test "Marketplace UI exists" "check_file '../webui/marketplace.html'"

# Test 4: M6 Dependencies
run_test "yauzl dependency" "check_dependency 'yauzl'"
run_test "archiver dependency" "check_dependency 'archiver'"
run_test "commander dependency" "check_dependency 'commander'"
run_test "chalk dependency" "check_dependency 'chalk'"
run_test "inquirer dependency" "check_dependency 'inquirer'"
run_test "ora dependency" "check_dependency 'ora'"

# Test 5: CLI bin configuration
run_test "CLI bin configured" "grep -q 'lifeboard-plugin' 'package.json'"

echo "🔍 Phase 2: M6 Marketplace Classes Structure"
echo "============================================"

# Test 6: MarketplaceManager Class Structure
run_test "MarketplaceManager class exists" "check_class_exists 'src/marketplace/MarketplaceManager.js' 'MarketplaceManager'"
run_test "MarketplaceManager has initialize method" "check_method_exists 'src/marketplace/MarketplaceManager.js' 'initialize'"
run_test "MarketplaceManager has getRegistry method" "check_method_exists 'src/marketplace/MarketplaceManager.js' 'getRegistry'"
run_test "MarketplaceManager has searchPlugins method" "check_method_exists 'src/marketplace/MarketplaceManager.js' 'searchPlugins'"
run_test "MarketplaceManager has installPlugin method" "check_method_exists 'src/marketplace/MarketplaceManager.js' 'installPlugin'"
run_test "MarketplaceManager has uninstallPlugin method" "check_method_exists 'src/marketplace/MarketplaceManager.js' 'uninstallPlugin'"
run_test "MarketplaceManager has checkForUpdates method" "check_method_exists 'src/marketplace/MarketplaceManager.js' 'checkForUpdates'"

# Test 7: PackageManager Class Structure
run_test "PackageManager class exists" "check_class_exists 'src/marketplace/PackageManager.js' 'PackageManager'"
run_test "PackageManager has createPackage method" "check_method_exists 'src/marketplace/PackageManager.js' 'createPackage'"
run_test "PackageManager has verifyPackage method" "check_method_exists 'src/marketplace/PackageManager.js' 'verifyPackage'"
run_test "PackageManager has generateRegistryEntry method" "check_method_exists 'src/marketplace/PackageManager.js' 'generateRegistryEntry'"
run_test "PackageManager has _signPackage method" "check_method_exists 'src/marketplace/PackageManager.js' '_signPackage'"
run_test "PackageManager has _verifySignature method" "check_method_exists 'src/marketplace/PackageManager.js' '_verifySignature'"

# Test 8: CLI Class Structure
run_test "PluginCLI class exists" "check_class_exists 'src/cli/plugin-cli.js' 'PluginCLI'"
run_test "CLI has searchCommand method" "check_method_exists 'src/cli/plugin-cli.js' 'searchCommand'"
run_test "CLI has installCommand method" "check_method_exists 'src/cli/plugin-cli.js' 'installCommand'"
run_test "CLI has uninstallCommand method" "check_method_exists 'src/cli/plugin-cli.js' 'uninstallCommand'"
run_test "CLI has packageCommand method" "check_method_exists 'src/cli/plugin-cli.js' 'packageCommand'"

echo "🔍 Phase 3: M6 Plugin Manager Integration"
echo "========================================="

# Test 9: Plugin Manager M6 Imports
run_test "Plugin Manager imports MarketplaceManager" "grep -q 'MarketplaceManager' 'src/plugin-manager.js'"
run_test "Plugin Manager imports PackageManager" "grep -q 'PackageManager' 'src/plugin-manager.js'"

# Test 10: Plugin Manager M6 Components
run_test "Plugin Manager has marketplaceManager property" "grep -q 'this.marketplaceManager' 'src/plugin-manager.js'"
run_test "Plugin Manager has packageManager property" "grep -q 'this.packageManager' 'src/plugin-manager.js'"

# Test 11: Plugin Manager M6 Methods
run_test "Plugin Manager has searchMarketplace method" "check_method_exists 'src/plugin-manager.js' 'searchMarketplace'"
run_test "Plugin Manager has installFromMarketplace method" "check_method_exists 'src/plugin-manager.js' 'installFromMarketplace'"
run_test "Plugin Manager has uninstallPlugin method" "check_method_exists 'src/plugin-manager.js' 'uninstallPlugin'"
run_test "Plugin Manager has checkForUpdates method" "check_method_exists 'src/plugin-manager.js' 'checkForUpdates'"
run_test "Plugin Manager has createPackage method" "check_method_exists 'src/plugin-manager.js' 'createPackage'"
run_test "Plugin Manager has verifyPackage method" "check_method_exists 'src/plugin-manager.js' 'verifyPackage'"

echo "🔍 Phase 4: M6 IPC Handlers"
echo "==========================="

# Test 12: Main.js M6 IPC Handlers
run_test "Main.js has marketplace:search handler" "grep -q 'marketplace:search' 'src/main.js'"
run_test "Main.js has marketplace:install handler" "grep -q 'marketplace:install' 'src/main.js'"
run_test "Main.js has marketplace:uninstall handler" "grep -q 'marketplace:uninstall' 'src/main.js'"
run_test "Main.js has marketplace:check-updates handler" "grep -q 'marketplace:check-updates' 'src/main.js'"
run_test "Main.js has package:create handler" "grep -q 'package:create' 'src/main.js'"
run_test "Main.js has package:verify handler" "grep -q 'package:verify' 'src/main.js'"

# Test 13: Preload.js M6 API Exposure
run_test "Preload.js has marketplace channels" "grep -q 'marketplace:search' 'src/preload.js'"
run_test "Preload.js has marketplace API" "grep -q 'marketplace:' 'src/preload.js'"
run_test "Preload.js has packages API" "grep -q 'packages:' 'src/preload.js'"
run_test "Preload.js has marketplace object" "grep -q 'marketplace: {' 'src/preload.js'"

echo "🔍 Phase 5: M6 JavaScript Syntax Validation"
echo "==========================================="

# Test 14: Syntax Check All M6 Files
run_test "MarketplaceManager syntax valid" "node -c 'src/marketplace/MarketplaceManager.js'"
run_test "PackageManager syntax valid" "node -c 'src/marketplace/PackageManager.js'"
run_test "Plugin CLI syntax valid" "node -c 'src/cli/plugin-cli.js'"

# Test 15: Integration Files Still Valid
run_test "Plugin Manager syntax valid" "node -c 'src/plugin-manager.js'"
run_test "Main.js syntax valid" "node -c 'src/main.js'"
run_test "Preload.js syntax valid" "node -c 'src/preload.js'"

echo "🔍 Phase 6: M6 Marketplace Features"
echo "==================================="

# Test 16: MarketplaceManager Features
run_test "MarketplaceManager has registry caching" "grep -q 'registryCache' 'src/marketplace/MarketplaceManager.js'"
run_test "MarketplaceManager has download verification" "grep -q 'verifyPackage' 'src/marketplace/MarketplaceManager.js'"
run_test "MarketplaceManager has installation tracking" "grep -q 'activeInstallations' 'src/marketplace/MarketplaceManager.js'"
run_test "MarketplaceManager has search filtering" "grep -q 'searchPlugins' 'src/marketplace/MarketplaceManager.js'"

# Test 17: PackageManager Features
run_test "PackageManager has ZIP creation" "grep -q 'archiver' 'src/marketplace/PackageManager.js'"
run_test "PackageManager has hash calculation" "grep -q 'sha256' 'src/marketplace/PackageManager.js'"
run_test "PackageManager has signature support" "grep -q 'signPackage' 'src/marketplace/PackageManager.js'"
run_test "PackageManager has metadata generation" "grep -q 'generateRegistryEntry' 'src/marketplace/PackageManager.js'"

# Test 18: CLI Features
run_test "CLI has search command" "grep -q 'command.*search' 'src/cli/plugin-cli.js'"
run_test "CLI has install command" "grep -q 'command.*install' 'src/cli/plugin-cli.js'"
run_test "CLI has package command" "grep -q 'command.*package' 'src/cli/plugin-cli.js'"
run_test "CLI has interactive prompts" "grep -q 'inquirer' 'src/cli/plugin-cli.js'"

echo "🔍 Phase 7: M6 UI Integration"
echo "============================="

# Test 19: Marketplace UI Features
run_test "Marketplace UI exists" "check_file '../webui/marketplace.html'"
run_test "Marketplace UI has search functionality" "grep -q 'searchInput' '../webui/marketplace.html'"
run_test "Marketplace UI has plugin cards" "grep -q 'plugin-card' '../webui/marketplace.html'"
run_test "Marketplace UI has installation progress" "grep -q 'installation-progress' '../webui/marketplace.html'"
run_test "Marketplace UI has marketplace API calls" "grep -q 'window.lifeboard.marketplace' '../webui/marketplace.html'"

echo "🔍 Phase 8: M6 Demo Plugin Integration"
echo "======================================"

# Test 20: Enhanced Demo Plugin Support (if Limitless exists)
if [ -f "plugins/limitless/main.js" ]; then
    run_test "Limitless plugin ready for marketplace" "grep -q 'version' 'plugins/limitless/manifest.json'"
else
    echo -e "${YELLOW}⚠ Limitless plugin not found - skipping demo integration tests${NC}"
    TESTS_RUN=$((TESTS_RUN + 1))
fi

echo "🔍 Phase 9: M6 Error Handling and Security"
echo "=========================================="

# Test 21: Security Features
run_test "MarketplaceManager has download size limits" "grep -q 'maxDownloadSize' 'src/marketplace/MarketplaceManager.js'"
run_test "MarketplaceManager has timeout protection" "grep -q 'downloadTimeout' 'src/marketplace/MarketplaceManager.js'"
run_test "PackageManager has signature verification" "grep -q 'verifySignature' 'src/marketplace/PackageManager.js'"

# Test 22: Error Handling
run_test "MarketplaceManager has try-catch blocks" "grep -q 'try {' 'src/marketplace/MarketplaceManager.js'"
run_test "PackageManager has error handling" "grep -q 'catch.*error' 'src/marketplace/PackageManager.js'"
run_test "CLI has error handling" "grep -q 'catch.*error' 'src/cli/plugin-cli.js'"

echo "🔍 Phase 10: M6 Integration Testing"
echo "==================================="

# Test 23: Component Integration
run_test "Plugin Manager can create marketplace instance" "grep -q 'new MarketplaceManager' 'src/plugin-manager.js'"
run_test "Plugin Manager can create package instance" "grep -q 'new PackageManager' 'src/plugin-manager.js'"

# Test 24: API Integration
run_test "IPC handlers use plugin manager methods" "grep -q 'this.pluginManager.*searchMarketplace' 'src/main.js'"
run_test "Preload exposes marketplace APIs" "grep -q 'ipcRenderer.invoke.*marketplace' 'src/preload.js'"

echo ""
echo "📊 Test Results Summary:"
echo "========================"
echo -e "Total Tests: ${BLUE}$TESTS_RUN${NC}"
echo -e "Passed: ${GREEN}$TESTS_PASSED${NC}"
echo -e "Failed: ${RED}$TESTS_FAILED${NC}"

if [ $TESTS_FAILED -eq 0 ]; then
    echo -e "\n${GREEN}🎉 All M6 tests passed! Marketplace & Package Management implementation is complete.${NC}"
    echo ""
    echo "M6 Features Validated:"
    echo "• ✅ MarketplaceManager with plugin discovery and installation"
    echo "• ✅ PackageManager with ZIP creation and digital signing"
    echo "• ✅ CLI tool for command-line plugin operations"
    echo "• ✅ Plugin Manager integration with marketplace features"
    echo "• ✅ IPC communication for marketplace operations"
    echo "• ✅ Marketplace UI for plugin browsing and installation"
    echo "• ✅ Security features including signature verification"
    echo "• ✅ Error handling and validation throughout"
    echo ""
    echo "🚀 M6 implementation is ready for use!"
    echo ""
    echo "Next Steps:"
    echo "• Test the CLI: ./src/cli/plugin-cli.js search"
    echo "• Open marketplace UI: open ../webui/marketplace.html"
    echo "• Run electron app: npm run electron-dev"
    exit 0
else
    echo -e "\n${RED}❌ Some M6 tests failed. Please review the implementation.${NC}"
    exit 1
fi
