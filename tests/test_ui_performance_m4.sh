#!/bin/bash

# Test Suite for Phase 10 M4 - UI Performance and Stress Testing
# Tests UI responsiveness, memory management, and performance under load
# Validates latest UI framework performance characteristics

# shellcheck disable=SC2317,SC2034
set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Test configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
LOGS_DIR="$PROJECT_ROOT/logs"
DESKTOP_DIR="$PROJECT_ROOT/desktop"

# Performance test parameters
MAX_ICONS=50
MAX_MODALS=10
STRESS_ITERATIONS=100
MEMORY_THRESHOLD_MB=100

# Test counters and timing
TESTS_RUN=0
TESTS_PASSED=0
TESTS_FAILED=0
START_TIME=$(date +%s)

# Logging setup with performance focus
setup_logging() {
    local timestamp
    timestamp=$(date '+%Y%m%d_%H%M%S')
    mkdir -p "$LOGS_DIR"
    LOG_FILE="$LOGS_DIR/ui_performance_m4_${timestamp}.log"
    PERF_LOG="$LOGS_DIR/performance_metrics_${timestamp}.log"

    echo "Performance test run started at $(date)" > "$LOG_FILE"
    echo "timestamp,test_name,duration_ms,memory_mb,cpu_percent" > "$PERF_LOG"
}

# Enhanced logging with performance metrics
log() {
    local level="$1"
    shift
    local message="$*"
    local timestamp
    timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "[$timestamp] [$level] $message" | tee -a "$LOG_FILE"
}

log_info() { log "INFO" "$@"; }
log_warn() { log "WARN" "$@"; }
log_error() { log "ERROR" "$@"; }
log_success() { log "SUCCESS" "$@"; }
log_debug() { log "DEBUG" "$@"; }

# Performance measurement utilities
measure_performance() {
    local test_name="$1"
    local start_time_ms
    local end_time_ms
    local duration_ms
    local memory_mb=0
    local cpu_percent=0

    start_time_ms=$(node -e "console.log(Date.now())" 2>/dev/null || echo "0")

    # Run the test
    shift
    "$@"
    local exit_code=$?

    end_time_ms=$(node -e "console.log(Date.now())" 2>/dev/null || echo "0")
    duration_ms=$((end_time_ms - start_time_ms))

    # Get memory usage if possible (macOS specific)
    if command -v ps >/dev/null 2>&1; then
        memory_mb=$(ps -o rss= -p $$ 2>/dev/null | awk '{print int($1/1024)}' || echo "0")
    fi

    # Log performance metrics
    echo "$start_time_ms,$test_name,$duration_ms,$memory_mb,$cpu_percent" >> "$PERF_LOG"

    log_debug "Performance: $test_name - ${duration_ms}ms, ${memory_mb}MB"
    return $exit_code
}

# Test execution with performance monitoring
run_test() {
    local test_name="$1"
    local test_function="$2"
    local start_time
    local end_time
    local duration

    echo -e "\n${BLUE}═══ Performance Test $((TESTS_RUN + 1)): $test_name ═══${NC}"
    log_info "Starting performance test: $test_name"
    start_time=$(date +%s)

    TESTS_RUN=$((TESTS_RUN + 1))

    if measure_performance "$test_name" "$test_function"; then
        end_time=$(date +%s)
        duration=$((end_time - start_time))
        echo -e "${GREEN}✓ PASSED: $test_name (${duration}s)${NC}"
        log_success "Performance test passed: $test_name in ${duration}s"
        TESTS_PASSED=$((TESTS_PASSED + 1))
        return 0
    else
        end_time=$(date +%s)
        duration=$((end_time - start_time))
        echo -e "${RED}✗ FAILED: $test_name (${duration}s)${NC}"
        log_error "Performance test failed: $test_name after ${duration}s"
        TESTS_FAILED=$((TESTS_FAILED + 1))
        return 1
    fi
}

# Test: Ribbon Icon Performance Under Load
test_ribbon_performance() {
    log_info "Testing ribbon icon performance under load"

    if ! command -v node >/dev/null 2>&1; then
        log_warn "Node.js not available, skipping ribbon performance test"
        return 0
    fi

    # Create performance test script for ribbon icons
    local test_script="/tmp/ribbon_perf_test.js"
    cat > "$test_script" << EOF
const path = require('path');
const projectRoot = process.argv[2];
const maxIcons = parseInt(process.argv[3]);

try {
    const RibbonManager = require(path.join(projectRoot, 'desktop/src/ui/RibbonManager.js'));
    const ribbonManager = new RibbonManager();

    console.log('Testing ribbon icon performance with', maxIcons, 'icons');

    const startTime = Date.now();
    const iconIds = [];

    // Add icons rapidly
    for (let i = 0; i < maxIcons; i++) {
        const iconId = ribbonManager.addRibbonIcon(
            'test-plugin',
            'icon-' + i,
            'Test Icon ' + i,
            () => console.log('Icon', i, 'clicked')
        );
        iconIds.push(iconId);

        if (i % 10 === 0) {
            process.stdout.write('.');
        }
    }

    const addTime = Date.now() - startTime;
    console.log('\nAdded', maxIcons, 'icons in', addTime, 'ms');

    // Test retrieval performance
    const retrievalStart = Date.now();
    const allIcons = ribbonManager.getAllRibbonIcons();
    const retrievalTime = Date.now() - retrievalStart;

    console.log('Retrieved', allIcons.length, 'icons in', retrievalTime, 'ms');

    // Test removal performance
    const removalStart = Date.now();
    const removedCount = ribbonManager.removePluginIcons('test-plugin');
    const removalTime = Date.now() - removalStart;

    console.log('Removed', removedCount, 'icons in', removalTime, 'ms');

    // Performance thresholds (adjust based on requirements)
    if (addTime > 1000) {
        console.error('Icon addition too slow:', addTime, 'ms');
        process.exit(1);
    }

    if (retrievalTime > 100) {
        console.error('Icon retrieval too slow:', retrievalTime, 'ms');
        process.exit(1);
    }

    if (removalTime > 500) {
        console.error('Icon removal too slow:', removalTime, 'ms');
        process.exit(1);
    }

    console.log('✓ Ribbon performance test passed');
    process.exit(0);

} catch (error) {
    console.error('✗ Ribbon performance test failed:', error.message);
    process.exit(1);
}
EOF

    if ! node "$test_script" "$PROJECT_ROOT" "$MAX_ICONS" 2>/dev/null; then
        log_error "Ribbon icon performance test failed"
        rm -f "$test_script"
        return 1
    fi

    rm -f "$test_script"
    log_info "Ribbon icon performance test passed"
    return 0
}

# Test: Modal Manager Performance and Memory
test_modal_performance() {
    log_info "Testing modal manager performance and memory usage"

    if ! command -v node >/dev/null 2>&1; then
        log_warn "Node.js not available, skipping modal performance test"
        return 0
    fi

    # Create performance test script for modals
    local test_script="/tmp/modal_perf_test.js"
    cat > "$test_script" << EOF
const path = require('path');
const projectRoot = process.argv[2];
const maxModals = parseInt(process.argv[3]);

try {
    const ModalManager = require(path.join(projectRoot, 'desktop/src/ui/ModalManager.js'));
    const modalManager = new ModalManager();

    console.log('Testing modal performance with', maxModals, 'modals');

    const startTime = Date.now();
    const modalIds = [];

    // Create modals rapidly
    for (let i = 0; i < maxModals; i++) {
        try {
            const modalId = modalManager.showModal('test-plugin', {
                title: 'Test Modal ' + i,
                content: 'This is test modal content for modal ' + i,
                width: 400,
                height: 300,
                buttons: [
                    { text: 'OK', variant: 'primary', callback: () => {} },
                    { text: 'Cancel', variant: 'secondary', callback: () => {} }
                ]
            });
            modalIds.push(modalId);
        } catch (error) {
            if (error.message.includes('Maximum number of modals')) {
                console.log('Hit modal limit at', i, 'modals (expected)');
                break;
            } else {
                throw error;
            }
        }
    }

    const addTime = Date.now() - startTime;
    console.log('Created', modalIds.length, 'modals in', addTime, 'ms');

    // Test modal state queries
    const queryStart = Date.now();
    const openModals = modalManager.getOpenModals();
    const pluginModals = modalManager.getPluginModals('test-plugin');
    const queryTime = Date.now() - queryStart;

    console.log('Queried modal state in', queryTime, 'ms');
    console.log('Open modals:', openModals.length, 'Plugin modals:', pluginModals.length);

    // Test bulk closure
    const closeStart = Date.now();
    const closedCount = modalManager.closePluginModals('test-plugin');
    const closeTime = Date.now() - closeStart;

    console.log('Closed', closedCount, 'modals in', closeTime, 'ms');

    // Test cleanup
    const cleanupStart = Date.now();
    modalManager.cleanup();
    const cleanupTime = Date.now() - cleanupStart;

    console.log('Cleanup completed in', cleanupTime, 'ms');

    // Performance thresholds
    if (addTime > 2000) {
        console.error('Modal creation too slow:', addTime, 'ms');
        process.exit(1);
    }

    if (queryTime > 50) {
        console.error('Modal querying too slow:', queryTime, 'ms');
        process.exit(1);
    }

    if (closeTime > 500) {
        console.error('Modal closure too slow:', closeTime, 'ms');
        process.exit(1);
    }

    console.log('✓ Modal performance test passed');
    process.exit(0);

} catch (error) {
    console.error('✗ Modal performance test failed:', error.message);
    process.exit(1);
}
EOF

    if ! node "$test_script" "$PROJECT_ROOT" "$MAX_MODALS" 2>/dev/null; then
        log_error "Modal manager performance test failed"
        rm -f "$test_script"
        return 1
    fi

    rm -f "$test_script"
    log_info "Modal manager performance test passed"
    return 0
}

# Test: Command Palette Search Performance
test_command_palette_performance() {
    log_info "Testing command palette search performance"

    if ! command -v node >/dev/null 2>&1; then
        log_warn "Node.js not available, skipping command palette performance test"
        return 0
    fi

    # Create command palette performance test
    local test_script="/tmp/command_palette_perf_test.js"
    cat > "$test_script" << EOF
const path = require('path');
const projectRoot = process.argv[2];

try {
    // Mock CommandPalette for testing
    const mockCommandPalette = {
        listCommands: () => {
            const commands = [];
            for (let i = 0; i < 1000; i++) {
                commands.push({
                    id: 'plugin' + (i % 10) + ':command-' + i,
                    handler: () => {}
                });
            }
            return commands;
        },
        executeCommand: (id) => true
    };

    const CommandPaletteUI = require(path.join(projectRoot, 'desktop/src/ui/CommandPaletteUI.js'));
    const commandPaletteUI = new CommandPaletteUI(mockCommandPalette);

    console.log('Testing command palette performance with 1000 commands');

    // Add metadata for all commands
    const metadataStart = Date.now();
    for (let i = 0; i < 1000; i++) {
        commandPaletteUI.setCommandMetadata('plugin' + (i % 10) + ':command-' + i, {
            category: 'Category ' + (i % 20),
            description: 'Test command ' + i + ' description',
            icon: 'icon-' + (i % 15),
            tags: ['tag' + (i % 5), 'test', 'command'],
            hotkey: i < 26 ? 'Cmd+Shift+' + String.fromCharCode(65 + i) : ''
        });
    }
    const metadataTime = Date.now() - metadataStart;
    console.log('Set metadata for 1000 commands in', metadataTime, 'ms');

    // Test search performance
    const searchQueries = ['test', 'command', 'plugin', 'category', 'tag'];
    let totalSearchTime = 0;

    for (const query of searchQueries) {
        const searchStart = Date.now();
        commandPaletteUI.updateSearch(query);
        const commands = commandPaletteUI.getDisplayCommands();
        const searchTime = Date.now() - searchStart;
        totalSearchTime += searchTime;

        console.log('Search "' + query + '" found', commands.length, 'results in', searchTime, 'ms');
    }

    console.log('Total search time:', totalSearchTime, 'ms');

    // Test category filtering performance
    const categoryStart = Date.now();
    commandPaletteUI.filterByCategory('Category 5');
    const categoryCommands = commandPaletteUI.getDisplayCommands();
    const categoryTime = Date.now() - categoryStart;

    console.log('Category filter found', categoryCommands.length, 'commands in', categoryTime, 'ms');

    // Test navigation performance
    const navStart = Date.now();
    for (let i = 0; i < 50; i++) {
        commandPaletteUI.selectNext();
    }
    for (let i = 0; i < 25; i++) {
        commandPaletteUI.selectPrevious();
    }
    const navTime = Date.now() - navStart;

    console.log('Navigation (75 moves) completed in', navTime, 'ms');

    // Test cleanup performance
    const cleanupStart = Date.now();
    commandPaletteUI.cleanup();
    const cleanupTime = Date.now() - cleanupStart;

    console.log('Cleanup completed in', cleanupTime, 'ms');

    // Performance thresholds
    if (metadataTime > 1500) {
        console.error('Metadata setting too slow:', metadataTime, 'ms');
        process.exit(1);
    }

    if (totalSearchTime > 500) {
        console.error('Search performance too slow:', totalSearchTime, 'ms');
        process.exit(1);
    }

    if (categoryTime > 100) {
        console.error('Category filtering too slow:', categoryTime, 'ms');
        process.exit(1);
    }

    if (navTime > 100) {
        console.error('Navigation too slow:', navTime, 'ms');
        process.exit(1);
    }

    console.log('✓ Command palette performance test passed');
    process.exit(0);

} catch (error) {
    console.error('✗ Command palette performance test failed:', error.message);
    process.exit(1);
}
EOF

    if ! node "$test_script" "$PROJECT_ROOT" 2>/dev/null; then
        log_error "Command palette performance test failed"
        rm -f "$test_script"
        return 1
    fi

    rm -f "$test_script"
    log_info "Command palette performance test passed"
    return 0
}

# Test: Memory Leak Detection
test_memory_leaks() {
    log_info "Testing for memory leaks in UI components"

    if ! command -v node >/dev/null 2>&1; then
        log_warn "Node.js not available, skipping memory leak test"
        return 0
    fi

    # Create memory leak detection test
    local test_script="/tmp/memory_leak_test.js"
    cat > "$test_script" << EOF
const path = require('path');
const projectRoot = process.argv[2];

try {
    const RibbonManager = require(path.join(projectRoot, 'desktop/src/ui/RibbonManager.js'));
    const ModalManager = require(path.join(projectRoot, 'desktop/src/ui/ModalManager.js'));

    console.log('Testing for memory leaks in UI components');

    // Initial memory measurement
    const initialMemory = process.memoryUsage().heapUsed;
    console.log('Initial memory:', Math.round(initialMemory / 1024 / 1024), 'MB');

    // Create and destroy UI components repeatedly
    for (let cycle = 0; cycle < 10; cycle++) {
        const ribbonManager = new RibbonManager();
        const modalManager = new ModalManager();

        // Add many UI elements
        for (let i = 0; i < 100; i++) {
            ribbonManager.addRibbonIcon('test-plugin', 'icon-' + i, 'Test', () => {});

            try {
                modalManager.showModal('test-plugin', {
                    title: 'Test Modal ' + i,
                    content: 'Test content'
                });
            } catch (e) {
                // Expected when hitting modal limits
                break;
            }
        }

        // Clean up
        ribbonManager.cleanup();
        modalManager.cleanup();

        // Force garbage collection if available
        if (global.gc) {
            global.gc();
        }

        const currentMemory = process.memoryUsage().heapUsed;
        const memoryMB = Math.round(currentMemory / 1024 / 1024);
        console.log('Cycle', cycle + 1, 'memory:', memoryMB, 'MB');

        // Check for excessive memory growth
        if (currentMemory > initialMemory * 3) {
            console.error('Potential memory leak detected. Memory grew from',
                Math.round(initialMemory / 1024 / 1024), 'MB to', memoryMB, 'MB');
            process.exit(1);
        }
    }

    const finalMemory = process.memoryUsage().heapUsed;
    const memoryGrowth = finalMemory - initialMemory;
    const growthMB = Math.round(memoryGrowth / 1024 / 1024);

    console.log('Final memory:', Math.round(finalMemory / 1024 / 1024), 'MB');
    console.log('Memory growth:', growthMB, 'MB');

    // Allow some memory growth but flag excessive growth
    if (growthMB > 50) {
        console.error('Excessive memory growth detected:', growthMB, 'MB');
        process.exit(1);
    }

    console.log('✓ Memory leak test passed');
    process.exit(0);

} catch (error) {
    console.error('✗ Memory leak test failed:', error.message);
    process.exit(1);
}
EOF

    if ! node --expose-gc "$test_script" "$PROJECT_ROOT" 2>/dev/null; then
        # Try without --expose-gc flag
        if ! node "$test_script" "$PROJECT_ROOT" 2>/dev/null; then
            log_error "Memory leak test failed"
            rm -f "$test_script"
            return 1
        fi
    fi

    rm -f "$test_script"
    log_info "Memory leak test passed"
    return 0
}

# Test: Concurrent Operations Stress Test
test_concurrent_operations() {
    log_info "Testing concurrent UI operations under stress"

    if ! command -v node >/dev/null 2>&1; then
        log_warn "Node.js not available, skipping concurrent operations test"
        return 0
    fi

    # Create concurrent operations test
    local test_script="/tmp/concurrent_ops_test.js"
    cat > "$test_script" << EOF
const path = require('path');
const projectRoot = process.argv[2];

try {
    const RibbonManager = require(path.join(projectRoot, 'desktop/src/ui/RibbonManager.js'));
    const ModalManager = require(path.join(projectRoot, 'desktop/src/ui/ModalManager.js'));

    console.log('Testing concurrent UI operations');

    const ribbonManager = new RibbonManager();
    const modalManager = new ModalManager();

    const startTime = Date.now();
    const operations = [];

    // Simulate rapid concurrent operations
    for (let i = 0; i < 200; i++) {
        operations.push(
            new Promise((resolve) => {
                setTimeout(() => {
                    try {
                        // Random UI operations
                        const operation = i % 4;

                        switch (operation) {
                            case 0:
                                // Add ribbon icon
                                const iconId = ribbonManager.addRibbonIcon(
                                    'plugin-' + (i % 5),
                                    'icon-' + i,
                                    'Title ' + i,
                                    () => {}
                                );
                                resolve({ type: 'icon-add', id: iconId });
                                break;

                            case 1:
                                // Remove random icon
                                const icons = ribbonManager.getAllRibbonIcons();
                                if (icons.length > 0) {
                                    const success = ribbonManager.removeRibbonIcon(icons[0].id);
                                    resolve({ type: 'icon-remove', success });
                                } else {
                                    resolve({ type: 'icon-remove', success: false });
                                }
                                break;

                            case 2:
                                // Create modal
                                try {
                                    const modalId = modalManager.showModal('plugin-' + (i % 3), {
                                        title: 'Modal ' + i,
                                        content: 'Content'
                                    });
                                    resolve({ type: 'modal-create', id: modalId });
                                } catch (e) {
                                    resolve({ type: 'modal-create', error: e.message });
                                }
                                break;

                            case 3:
                                // Close random modal
                                const modals = modalManager.getOpenModals();
                                if (modals.length > 0) {
                                    const success = modalManager.closeModal(modals[0].id);
                                    resolve({ type: 'modal-close', success });
                                } else {
                                    resolve({ type: 'modal-close', success: false });
                                }
                                break;
                        }
                    } catch (error) {
                        resolve({ type: 'error', message: error.message });
                    }
                }, Math.random() * 100); // Random timing
            })
        );
    }

    // Wait for all operations to complete
    Promise.all(operations).then(results => {
        const endTime = Date.now();
        const duration = endTime - startTime;

        console.log('Completed 200 concurrent operations in', duration, 'ms');

        // Count operation types
        const counts = {};
        let errorCount = 0;

        results.forEach(result => {
            if (result.type === 'error') {
                errorCount++;
            } else {
                counts[result.type] = (counts[result.type] || 0) + 1;
            }
        });

        console.log('Operation counts:', counts);
        console.log('Errors:', errorCount);

        // Final state check
        const finalIcons = ribbonManager.getAllRibbonIcons();
        const finalModals = modalManager.getOpenModals();

        console.log('Final state - Icons:', finalIcons.length, 'Modals:', finalModals.length);

        // Cleanup
        ribbonManager.cleanup();
        modalManager.cleanup();

        // Performance thresholds
        if (duration > 5000) {
            console.error('Concurrent operations too slow:', duration, 'ms');
            process.exit(1);
        }

        if (errorCount > 20) {
            console.error('Too many errors in concurrent operations:', errorCount);
            process.exit(1);
        }

        console.log('✓ Concurrent operations stress test passed');
        process.exit(0);
    }).catch(error => {
        console.error('✗ Concurrent operations test failed:', error.message);
        process.exit(1);
    });

} catch (error) {
    console.error('✗ Concurrent operations test failed:', error.message);
    process.exit(1);
}
EOF

    if ! timeout 30 node "$test_script" "$PROJECT_ROOT" 2>/dev/null; then
        log_error "Concurrent operations test failed or timed out"
        rm -f "$test_script"
        return 1
    fi

    rm -f "$test_script"
    log_info "Concurrent operations stress test passed"
    return 0
}

# Generate performance report
generate_performance_report() {
    local end_time
    local total_duration
    local pass_rate

    end_time=$(date +%s)
    total_duration=$((end_time - START_TIME))

    if [[ $TESTS_RUN -gt 0 ]]; then
        pass_rate=$(( (TESTS_PASSED * 100) / TESTS_RUN ))
    else
        pass_rate=0
    fi

    echo -e "\n${BLUE}════════════════════════════════════════════════════════════════${NC}"
    echo -e "${BLUE}                    M4 UI Performance Test Report                    ${NC}"
    echo -e "${BLUE}════════════════════════════════════════════════════════════════${NC}"

    echo -e "\n${CYAN}Performance Test Summary:${NC}"
    echo -e "  Total Tests Run:     $TESTS_RUN"
    echo -e "  ${GREEN}Tests Passed:        $TESTS_PASSED${NC}"
    echo -e "  ${RED}Tests Failed:         $TESTS_FAILED${NC}"
    echo -e "  Pass Rate:           ${pass_rate}%"
    echo -e "  Total Duration:      ${total_duration}s"

    echo -e "\n${CYAN}Performance Test Coverage:${NC}"
    echo -e "  ✓ Ribbon Icon Load Testing ($MAX_ICONS icons)"
    echo -e "  ✓ Modal Manager Performance ($MAX_MODALS modals)"
    echo -e "  ✓ Command Palette Search (1000 commands)"
    echo -e "  ✓ Memory Leak Detection (10 cycles)"
    echo -e "  ✓ Concurrent Operations (200 operations)"

    echo -e "\n${CYAN}Performance Metrics:${NC}"
    if [[ -f "$PERF_LOG" ]]; then
        echo -e "  Performance log: $PERF_LOG"
        echo -e "  Detailed metrics available in log file"
    fi

    if [[ $TESTS_FAILED -eq 0 ]]; then
        echo -e "\n${GREEN}🚀 ALL PERFORMANCE TESTS PASSED! 🚀${NC}"
        echo -e "${GREEN}✓ UI components perform well under load${NC}"
        echo -e "${GREEN}✓ Memory management is efficient${NC}"
        echo -e "${GREEN}✓ No performance regressions detected${NC}"
        echo -e "${GREEN}✓ Concurrent operations handled properly${NC}"
        echo -e "\n${BLUE}UI framework is performance-ready for production!${NC}"
    else
        echo -e "\n${RED}⚠️  Some performance tests failed${NC}"
        echo -e "${YELLOW}Performance issues detected - review and optimize${NC}"
        echo -e "${CYAN}Check detailed logs: $LOG_FILE${NC}"
    fi

    # Log final summary
    log_info "Performance test suite completed. Duration: ${total_duration}s, Pass rate: ${pass_rate}%"
}

# Main test execution
main() {
    echo -e "${PURPLE}Starting Phase 10 M4 UI Performance Test Suite${NC}"
    echo -e "${BLUE}Testing: Load handling, memory efficiency, responsiveness${NC}"
    echo "=================================================================="

    setup_logging
    log_info "M4 UI Performance test suite started"

    # Run all performance tests
    run_test "Ribbon Icon Performance" test_ribbon_performance
    run_test "Modal Manager Performance" test_modal_performance
    run_test "Command Palette Performance" test_command_palette_performance
    run_test "Memory Leak Detection" test_memory_leaks
    run_test "Concurrent Operations Stress" test_concurrent_operations

    # Generate comprehensive performance report
    generate_performance_report

    # Exit with appropriate code
    if [[ $TESTS_FAILED -eq 0 ]]; then
        exit 0
    else
        exit 1
    fi
}

# Execute main function
main "$@"
