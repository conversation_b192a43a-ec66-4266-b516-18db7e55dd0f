-- Edge Cases and Stress Test Suite
-- Purpose: Test boundary conditions, stress scenarios, and error handling
-- Usage: Run after core functionality tests to verify robustness

-- Test 1: Boundary Value Testing
DO $$
DECLARE
    test_user_id UUID := '888e8400-e29b-41d4-a716-************';
    max_content TEXT;
    min_content TEXT;
    zero_length_tag TEXT := '';
    max_length_tag TEXT;
BEGIN
    -- Setup test user
    INSERT INTO auth.users (id, email, encrypted_password, created_at, updated_at, role, aud)
    VALUES (test_user_id, '<EMAIL>', 'test_password_hash', NOW(), NOW(), 'authenticated', 'authenticated')
    ON CONFLICT DO NOTHING;

    -- Test maximum reasonable content length (1MB)
    max_content := repeat('X', 1048576);

    BEGIN
        INSERT INTO posts (content, user_id) VALUES (max_content, test_user_id);
        DELETE FROM posts WHERE content = max_content;
        RAISE NOTICE 'Large content test: PASSED (1MB content)';
    EXCEPTION
        WHEN OTHERS THEN
            RAISE WARNING 'Large content test: FAILED - %', SQLERRM;
    END;

    -- Test minimum content (single character)
    min_content := 'X';
    INSERT INTO posts (content, user_id) VALUES (min_content, test_user_id);
    DELETE FROM posts WHERE content = min_content;

    -- Test maximum tag name length (approaching 100 char limit)
    max_length_tag := repeat('T', 95);
    INSERT INTO tags (name, user_id) VALUES (max_length_tag, test_user_id);
    DELETE FROM tags WHERE name = max_length_tag;

    -- Test edge case: tag name exactly at limit
    max_length_tag := repeat('T', 100);
    INSERT INTO tags (name, user_id) VALUES (max_length_tag, test_user_id);
    DELETE FROM tags WHERE name = max_length_tag;

    RAISE NOTICE 'SUCCESS: Boundary value testing completed';

    -- Cleanup
    DELETE FROM auth.users WHERE id = test_user_id;
END $$;

-- Test 2: Concurrent Operations Simulation
DO $$
DECLARE
    test_user_id UUID := '888e8400-e29b-41d4-a716-************';
    post_id UUID;
    i INTEGER;
    tag_ids UUID[];
    start_time TIMESTAMP;
    end_time TIMESTAMP;
    duration_ms INTEGER;
BEGIN
    -- Setup
    INSERT INTO auth.users (id, email, encrypted_password, created_at, updated_at, role, aud) VALUES (test_user_id, '<EMAIL>', 'test_password_hash', NOW(), NOW(), 'authenticated', 'authenticated')
    ON CONFLICT DO NOTHING;

    INSERT INTO posts (title, content, user_id)
    VALUES ('Concurrent Test Post', 'Testing concurrent operations', test_user_id)
    RETURNING id INTO post_id;

    start_time := clock_timestamp();

    -- Simulate rapid tag creation and association
    FOR i IN 1..50 LOOP
        DECLARE
            tag_id UUID;
        BEGIN
            INSERT INTO tags (name, user_id)
            VALUES ('concurrent-tag-' || i, test_user_id)
            RETURNING id INTO tag_id;

            tag_ids := array_append(tag_ids, tag_id);

            -- Associate with post
            INSERT INTO post_tags (post_id, tag_id) VALUES (post_id, tag_id);

        EXCEPTION
            WHEN OTHERS THEN
                RAISE WARNING 'Concurrent operation failed at iteration %: %', i, SQLERRM;
        END;
    END LOOP;

    end_time := clock_timestamp();
    duration_ms := EXTRACT(milliseconds FROM (end_time - start_time));

    -- Verify all operations completed
    IF array_length(tag_ids, 1) = 50 THEN
        RAISE NOTICE 'SUCCESS: Concurrent operations test passed (50 operations in %ms)', duration_ms;
    ELSE
        RAISE WARNING 'Concurrent operations test: Only % of 50 operations completed', array_length(tag_ids, 1);
    END IF;

    -- Cleanup
    DELETE FROM auth.users WHERE id = test_user_id;
END $$;

-- Test 3: Invalid Data Type Handling
DO $$
DECLARE
    test_user_id UUID := '888e8400-e29b-41d4-a716-************';
    invalid_json TEXT := '{"invalid": json malformed}';
    plugin_id UUID;
BEGIN
    -- Setup
    INSERT INTO auth.users (id, email, encrypted_password, created_at, updated_at, role, aud) VALUES (test_user_id, '<EMAIL>', 'test_password_hash', NOW(), NOW(), 'authenticated', 'authenticated')
    ON CONFLICT DO NOTHING;

    -- Test invalid JSON in plugin configuration
    BEGIN
        INSERT INTO plugins (name, version, configuration, user_id)
        VALUES ('invalid-json-plugin', '1.0.0', invalid_json, test_user_id);
        RAISE EXCEPTION 'Invalid JSON was accepted - this should not happen';
    EXCEPTION
        WHEN invalid_text_representation THEN
            RAISE NOTICE 'SUCCESS: Invalid JSON properly rejected';
        WHEN OTHERS THEN
            RAISE WARNING 'Unexpected error handling invalid JSON: %', SQLERRM;
    END;

    -- Test extremely nested JSON (potential DoS)
    DECLARE
        nested_json TEXT := '{"a":{"b":{"c":{"d":{"e":{"f":{"g":{"h":{"i":{"j":"deep"}}}}}}}}}';
    BEGIN
        INSERT INTO plugins (name, version, configuration, user_id)
        VALUES ('deep-json-plugin', '1.0.0', nested_json, test_user_id)
        RETURNING id INTO plugin_id;

        -- Verify we can query nested values
        IF (SELECT configuration->'a'->'b'->'c'->'d'->'e'->'f'->'g'->'h'->'i'->>'j' FROM plugins WHERE id = plugin_id) = 'deep' THEN
            RAISE NOTICE 'SUCCESS: Deep JSON nesting handled correctly';
        END IF;

        DELETE FROM plugins WHERE id = plugin_id;
    EXCEPTION
        WHEN OTHERS THEN
            RAISE WARNING 'Deep JSON nesting failed: %', SQLERRM;
    END;

    -- Cleanup
    DELETE FROM auth.users WHERE id = test_user_id;
END $$;

-- Test 4: Special Characters and Unicode
DO $$
DECLARE
    test_user_id UUID := '888e8400-e29b-41d4-a716-************';
    unicode_content TEXT := 'Unicode test: 你好世界 🌟 café naïve résumé';
    special_chars TEXT := E'Special chars: \t\n\r\'\"\\\/\b\f';
    emoji_tag TEXT := '📱💻🚀';
    post_id UUID;
    tag_id UUID;
BEGIN
    -- Setup
    INSERT INTO auth.users (id, email, encrypted_password, created_at, updated_at, role, aud) VALUES (test_user_id, '<EMAIL>', 'test_password_hash', NOW(), NOW(), 'authenticated', 'authenticated')
    ON CONFLICT DO NOTHING;

    -- Test Unicode content
    INSERT INTO posts (title, content, user_id)
    VALUES ('Unicode Test', unicode_content, test_user_id)
    RETURNING id INTO post_id;

    -- Verify Unicode was stored correctly
    IF (SELECT content FROM posts WHERE id = post_id) = unicode_content THEN
        RAISE NOTICE 'SUCCESS: Unicode content stored and retrieved correctly';
    ELSE
        RAISE WARNING 'Unicode content corruption detected';
    END IF;

    -- Test special characters in tags
    INSERT INTO tags (name, user_id) VALUES (special_chars, test_user_id)
    RETURNING id INTO tag_id;

    -- Test emoji in tag names
    BEGIN
        INSERT INTO tags (name, user_id) VALUES (emoji_tag, test_user_id);
        RAISE NOTICE 'SUCCESS: Emoji tag names supported';
        DELETE FROM tags WHERE name = emoji_tag;
    EXCEPTION
        WHEN OTHERS THEN
            RAISE WARNING 'Emoji tag names not supported: %', SQLERRM;
    END;

    -- Cleanup
    DELETE FROM auth.users WHERE id = test_user_id;
END $$;

-- Test 5: Memory and Resource Stress Test
DO $$
DECLARE
    test_user_id UUID := '888e8400-e29b-41d4-a716-************';
    batch_size INTEGER := 100;
    i INTEGER;
    start_time TIMESTAMP;
    end_time TIMESTAMP;
    duration_ms INTEGER;
    insert_count INTEGER := 0;
BEGIN
    -- Setup
    INSERT INTO auth.users (id, email, encrypted_password, created_at, updated_at, role, aud) VALUES (test_user_id, '<EMAIL>', 'test_password_hash', NOW(), NOW(), 'authenticated', 'authenticated')
    ON CONFLICT DO NOTHING;

    start_time := clock_timestamp();

    -- Stress test: Insert many records quickly
    FOR i IN 1..batch_size LOOP
        BEGIN
            INSERT INTO posts (title, content, user_id)
            VALUES (
                'Stress Test Post ' || i,
                'This is stress test content for post number ' || i || '. ' || repeat('Content ', 100),
                test_user_id
            );
            insert_count := insert_count + 1;
        EXCEPTION
            WHEN OTHERS THEN
                RAISE WARNING 'Stress test insert failed at %: %', i, SQLERRM;
                EXIT;
        END;
    END LOOP;

    end_time := clock_timestamp();
    duration_ms := EXTRACT(milliseconds FROM (end_time - start_time));

    IF insert_count = batch_size THEN
        RAISE NOTICE 'SUCCESS: Stress test completed (% inserts in %ms, avg %ms per insert)',
            insert_count, duration_ms, round(duration_ms::numeric / batch_size, 2);
    ELSE
        RAISE WARNING 'Stress test incomplete: %/% inserts completed', insert_count, batch_size;
    END IF;

    -- Test bulk delete performance
    start_time := clock_timestamp();
    DELETE FROM posts WHERE user_id = test_user_id;
    end_time := clock_timestamp();
    duration_ms := EXTRACT(milliseconds FROM (end_time - start_time));

    RAISE NOTICE 'Bulk delete performance: %ms for % records', duration_ms, insert_count;

    -- Cleanup
    DELETE FROM auth.users WHERE id = test_user_id;
END $$;

-- Test 6: Transaction Rollback Scenarios
DO $$
DECLARE
    test_user_id UUID := '888e8400-e29b-41d4-a716-************';
    post_count_before INTEGER;
    post_count_after INTEGER;
BEGIN
    -- Setup
    INSERT INTO auth.users (id, email, encrypted_password, created_at, updated_at, role, aud) VALUES (test_user_id, '<EMAIL>', 'test_password_hash', NOW(), NOW(), 'authenticated', 'authenticated')
    ON CONFLICT DO NOTHING;

    SELECT COUNT(*) INTO post_count_before FROM posts WHERE user_id = test_user_id;

    -- Test transaction rollback on constraint violation
    BEGIN
        BEGIN
            INSERT INTO posts (title, content, user_id) VALUES ('Test 1', 'Content 1', test_user_id);
            INSERT INTO posts (title, content, user_id) VALUES ('Test 2', 'Content 2', test_user_id);
            -- This should fail and rollback the transaction
            INSERT INTO posts (title, content, user_id) VALUES ('Test 3', NULL, test_user_id);
        EXCEPTION
            WHEN not_null_violation THEN
                ROLLBACK;
                RAISE NOTICE 'Transaction correctly rolled back on constraint violation';
        END;
    EXCEPTION
        WHEN OTHERS THEN
            RAISE WARNING 'Unexpected transaction behavior: %', SQLERRM;
    END;

    SELECT COUNT(*) INTO post_count_after FROM posts WHERE user_id = test_user_id;

    IF post_count_after = post_count_before THEN
        RAISE NOTICE 'SUCCESS: Transaction rollback working correctly';
    ELSE
        RAISE WARNING 'Transaction rollback failed: % posts before, % after', post_count_before, post_count_after;
    END IF;

    -- Cleanup
    DELETE FROM auth.users WHERE id = test_user_id;
END $$;

-- Test 7: Database Constraint Edge Cases
DO $$
DECLARE
    test_user_id UUID := '888e8400-e29b-41d4-a716-************';
    duplicate_count INTEGER;
BEGIN
    -- Setup
    INSERT INTO auth.users (id, email, encrypted_password, created_at, updated_at, role, aud) VALUES (test_user_id, '<EMAIL>', 'test_password_hash', NOW(), NOW(), 'authenticated', 'authenticated')
    ON CONFLICT DO NOTHING;

    -- Test case sensitivity in unique constraints
    INSERT INTO tags (name, user_id) VALUES ('CaseSensitive', test_user_id);

    BEGIN
        INSERT INTO tags (name, user_id) VALUES ('casesensitive', test_user_id);
        RAISE NOTICE 'Tag uniqueness is case-sensitive (both tags created)';
        DELETE FROM tags WHERE name IN ('CaseSensitive', 'casesensitive') AND user_id = test_user_id;
    EXCEPTION
        WHEN unique_violation THEN
            RAISE NOTICE 'Tag uniqueness is case-insensitive (duplicate prevented)';
            DELETE FROM tags WHERE name = 'CaseSensitive' AND user_id = test_user_id;
    END;

    -- Test whitespace handling in constraints
    INSERT INTO tags (name, user_id) VALUES ('  spaced  ', test_user_id);

    BEGIN
        INSERT INTO tags (name, user_id) VALUES ('spaced', test_user_id);
        RAISE NOTICE 'Whitespace in tag names is preserved (both tags created)';
        DELETE FROM tags WHERE name IN ('  spaced  ', 'spaced') AND user_id = test_user_id;
    EXCEPTION
        WHEN unique_violation THEN
            RAISE NOTICE 'Whitespace is trimmed in tag uniqueness checks';
            DELETE FROM tags WHERE name = '  spaced  ' AND user_id = test_user_id;
    END;

    -- Cleanup
    DELETE FROM auth.users WHERE id = test_user_id;

    RAISE NOTICE 'SUCCESS: Database constraint edge cases tested';
END $$;

-- Log edge cases test completion
INSERT INTO app_logs (level, service, message, context) VALUES
    ('INFO', 'test', 'Edge cases and stress test suite completed',
     ('{"test_type": "edge_cases", "status": "passed", "tests_run": 7, "timestamp": "' || NOW()::text || '"}')::jsonb);
