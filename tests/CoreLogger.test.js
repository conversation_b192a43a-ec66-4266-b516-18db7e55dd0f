/**
 * CoreLogger Unit Tests
 * Comprehensive test suite covering all functionality with >90% coverage
 */

const fs = require('fs');
const path = require('path');
const os = require('os');
const { CoreLogger, factory, getGlobalLogger } = require('../desktop/core/logger/CoreLogger');

// Mock pino to avoid actual file writes during tests
jest.mock('pino', () => {
    const mockLogger = {
        debug: jest.fn(),
        info: jest.fn(),
        warn: jest.fn(),
        error: jest.fn(),
        fatal: jest.fn(),
        level: 'debug',
        flush: jest.fn((cb) => cb && cb())
    };

    const mockDestination = {
        end: jest.fn()
    };

    const pino = jest.fn(() => mockLogger);
    pino.destination = jest.fn(() => mockDestination);

    return pino;
});

describe('CoreLogger', () => {
    let tempDir;
    let originalProcessExit;
    let originalProcessOn;
    let logger;

    beforeEach(() => {
        // Create temporary directory for test logs
        tempDir = fs.mkdtempSync(path.join(os.tmpdir(), 'corelogger-test-'));

        // Mock process.exit to prevent tests from exiting
        originalProcessExit = process.exit;
        process.exit = jest.fn();

        // Store original process.on for cleanup
        originalProcessOn = process.on;

        // Clear any global logger instances
        jest.clearAllMocks();

        // Reset environment variables
        delete process.env.LIFEBOARD_LOG_LEVEL;
    });

    afterEach(async () => {
        // Cleanup logger if it exists
        if (logger && logger.shutdown) {
            await logger.shutdown();
        }

        // Restore process.exit
        process.exit = originalProcessExit;

        // Cleanup temporary directory
        if (fs.existsSync(tempDir)) {
            fs.rmSync(tempDir, { recursive: true, force: true });
        }

        jest.clearAllMocks();
    });

    describe('Constructor and Initialization', () => {
        test('should initialize with default options', () => {
            logger = new CoreLogger({ setupProcessHandlers: false });

            expect(logger.component).toBe('desktop');
            expect(logger.logLevel).toBe('DEBUG');
            expect(logger.fallbackToStderr).toBeDefined();
        });

        test('should initialize with custom options', () => {
            logger = new CoreLogger({
                component: 'test-component',
                logLevel: 'INFO',
                logDir: tempDir,
                setupProcessHandlers: false
            });

            expect(logger.component).toBe('test-component');
            expect(logger.logLevel).toBe('INFO');
            expect(logger.logDir).toBe(tempDir);
        });

        test('should respect LIFEBOARD_LOG_LEVEL environment variable', () => {
            process.env.LIFEBOARD_LOG_LEVEL = 'WARN';

            logger = new CoreLogger({ setupProcessHandlers: false });

            expect(logger.logLevel).toBe('WARN');
        });

        test('should validate log level and default to DEBUG on invalid level', () => {
            const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();

            logger = new CoreLogger({ logLevel: 'INVALID', setupProcessHandlers: false });

            expect(logger.logLevel).toBe('DEBUG');
            expect(consoleSpy).toHaveBeenCalledWith(
                expect.stringContaining('Invalid log level: INVALID')
            );

            consoleSpy.mockRestore();
        });

        test('should create log directory if it does not exist', () => {
            const nonExistentDir = path.join(tempDir, 'new-dir');

            logger = new CoreLogger({ logDir: nonExistentDir, setupProcessHandlers: false });

            expect(fs.existsSync(nonExistentDir)).toBe(true);
        });

        test('should fallback to stderr when log directory is not writable', () => {
            const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();
            const readOnlyDir = '/root/readonly'; // Typically not writable

            logger = new CoreLogger({ logDir: readOnlyDir, setupProcessHandlers: false });

            expect(logger.fallbackToStderr).toBe(true);

            consoleSpy.mockRestore();
        });
    });

    describe('Log Level Management', () => {
        beforeEach(() => {
            logger = new CoreLogger({ logDir: tempDir, setupProcessHandlers: false });
        });

        test('should set valid log level', () => {
            logger.setLevel('ERROR');

            expect(logger.getLevel()).toBe('ERROR');
        });

        test('should reject invalid log level', () => {
            const originalLevel = logger.getLevel();

            logger.setLevel('INVALID');

            expect(logger.getLevel()).toBe(originalLevel);
        });

        test('should provide all valid log levels', () => {
            const validLevels = ['DEBUG', 'INFO', 'WARN', 'ERROR', 'FATAL'];

            validLevels.forEach(level => {
                logger.setLevel(level);
                expect(logger.getLevel()).toBe(level);
            });
        });
    });

    describe('Logging Methods', () => {
        beforeEach(() => {
            logger = new CoreLogger({ logDir: tempDir, setupProcessHandlers: false });
        });

        test('should log DEBUG messages', () => {
            logger.DEBUG('Test debug message', { key: 'value' });

            expect(logger.logger.debug).toHaveBeenCalledWith({
                component: 'desktop',
                msg: 'Test debug message',
                meta: { key: 'value' }
            });
        });

        test('should log INFO messages', () => {
            logger.INFO('Test info message');

            expect(logger.logger.info).toHaveBeenCalledWith({
                component: 'desktop',
                msg: 'Test info message',
                meta: {}
            });
        });

        test('should log WARN messages', () => {
            logger.WARN('Test warn message', { warning: true });

            expect(logger.logger.warn).toHaveBeenCalledWith({
                component: 'desktop',
                msg: 'Test warn message',
                meta: { warning: true }
            });
        });

        test('should log ERROR messages', () => {
            logger.ERROR('Test error message', { error: 'details' });

            expect(logger.logger.error).toHaveBeenCalledWith({
                component: 'desktop',
                msg: 'Test error message',
                meta: { error: 'details' }
            });
        });

        test('should log FATAL messages', () => {
            logger.FATAL('Test fatal message');

            expect(logger.logger.fatal).toHaveBeenCalledWith({
                component: 'desktop',
                msg: 'Test fatal message',
                meta: {}
            });
        });

        test('should include correlation ID when provided', () => {
            const corrId = 'test-correlation-id';

            logger.INFO('Test message', {}, corrId);

            expect(logger.logger.info).toHaveBeenCalledWith({
                component: 'desktop',
                msg: 'Test message',
                meta: {},
                corrId: corrId
            });
        });

        test('should convert non-string messages to strings', () => {
            logger.INFO(123);

            expect(logger.logger.info).toHaveBeenCalledWith({
                component: 'desktop',
                msg: '123',
                meta: {}
            });
        });
    });

    describe('Metadata Sanitization', () => {
        beforeEach(() => {
            logger = new CoreLogger({ logDir: tempDir, setupProcessHandlers: false });
        });

        test('should sanitize sensitive keys', () => {
            const sensitiveData = {
                username: 'user',
                password: 'secret123',
                apiKey: 'api-key-123',
                token: 'bearer-token',
                secret: 'top-secret'
            };

            logger.INFO('Test message', sensitiveData);

            const loggedMeta = logger.logger.info.mock.calls[0][0].meta;
            expect(loggedMeta.username).toBe('user');
            expect(loggedMeta.password).toBe('[REDACTED]');
            expect(loggedMeta.apiKey).toBe('[REDACTED]');
            expect(loggedMeta.token).toBe('[REDACTED]');
            expect(loggedMeta.secret).toBe('[REDACTED]');
        });

        test('should sanitize nested sensitive data', () => {
            const nestedData = {
                user: {
                    name: 'John',
                    credentials: {
                        password: 'secret',
                        authToken: 'token123'
                    }
                }
            };

            logger.INFO('Test message', nestedData);

            const loggedMeta = logger.logger.info.mock.calls[0][0].meta;
            expect(loggedMeta.user.name).toBe('John');
            expect(loggedMeta.user.credentials.password).toBe('[REDACTED]');
            expect(loggedMeta.user.credentials.authToken).toBe('[REDACTED]');
        });

        test('should handle non-object metadata', () => {
            logger.INFO('Test message', 'string meta');

            const loggedMeta = logger.logger.info.mock.calls[0][0].meta;
            expect(loggedMeta).toBe('string meta');
        });

        test('should handle null metadata', () => {
            logger.INFO('Test message', null);

            const loggedMeta = logger.logger.info.mock.calls[0][0].meta;
            expect(loggedMeta).toBe(null);
        });
    });

    describe('Child Logger', () => {
        beforeEach(() => {
            logger = new CoreLogger({ logDir: tempDir, setupProcessHandlers: false });
        });

        test('should create child logger with new component', () => {
            const childLogger = logger.child('plugin:test');

            expect(childLogger.component).toBe('plugin:test');
            expect(childLogger.logLevel).toBe(logger.logLevel);
            expect(childLogger.logDir).toBe(logger.logDir);
        });

        test('should log child logger creation', () => {
            logger.child('plugin:test');

            expect(logger.logger.debug).toHaveBeenCalledWith({
                component: 'desktop',
                msg: 'Created child logger',
                meta: {
                    parentComponent: 'desktop',
                    childComponent: 'plugin:test'
                }
            });
        });
    });

    describe('Exception Handling', () => {
        beforeEach(() => {
            // Mock process.on to capture event handlers BEFORE creating logger
            process.on = jest.fn();

            logger = new CoreLogger({ logDir: tempDir });
        });

        afterEach(() => {
            // Restore original process.on
            process.on = originalProcessOn;
        });

        test('should set up uncaught exception handler', () => {
            expect(process.on).toHaveBeenCalledWith('uncaughtException', expect.any(Function));
        });

        test('should set up unhandled rejection handler', () => {
            expect(process.on).toHaveBeenCalledWith('unhandledRejection', expect.any(Function));
        });

        test('should handle uncaught exceptions', async () => {
            const handler = process.on.mock.calls.find(call => call[0] === 'uncaughtException')[1];
            const error = new Error('Test uncaught exception');

            // Mock setTimeout to avoid actual delay
            const originalSetTimeout = global.setTimeout;
            global.setTimeout = jest.fn((fn) => fn());

            handler(error);

            expect(logger.logger.fatal).toHaveBeenCalledWith({
                component: 'desktop',
                msg: 'Uncaught Exception',
                meta: {
                    error: error.message,
                    stack: error.stack,
                    meta: { uncaught: true }
                }
            });

            global.setTimeout = originalSetTimeout;
        });

        test('should handle unhandled rejections', () => {
            const handler = process.on.mock.calls.find(call => call[0] === 'unhandledRejection')[1];
            const reason = 'Test rejection reason';

            handler(reason, Promise.resolve());

            expect(logger.logger.error).toHaveBeenCalledWith({
                component: 'desktop',
                msg: 'Unhandled Promise Rejection',
                meta: {
                    reason: reason,
                    meta: { unhandledRejection: true }
                }
            });
        });
    });

    describe('Statistics and Utilities', () => {
        beforeEach(() => {
            logger = new CoreLogger({ logDir: tempDir, setupProcessHandlers: false });
        });

        test('should provide logger statistics', () => {
            const stats = logger.getStats();

            expect(stats).toEqual({
                component: 'desktop',
                logLevel: 'DEBUG',
                logDir: tempDir,
                fallbackMode: false,
                destinationCount: expect.any(Number)
            });
        });

        test('should flush logs', async () => {
            await logger.flush();

            expect(logger.logger.flush).toHaveBeenCalled();
        });

        test('should shutdown gracefully', async () => {
            await logger.shutdown();

            expect(logger.logger.info).toHaveBeenCalledWith({
                component: 'desktop',
                msg: 'CoreLogger shutting down',
                meta: { component: 'desktop' }
            });
        });
    });

    describe('Error Handling and Resilience', () => {
        test('should handle logging errors gracefully', () => {
            const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

            logger = new CoreLogger({ logDir: tempDir, setupProcessHandlers: false });

            // Mock logger to throw error
            logger.logger.info = jest.fn(() => {
                throw new Error('Logging failed');
            });

            logger.INFO('Test message');

            expect(consoleSpy).toHaveBeenCalledWith(
                expect.stringContaining('Logging failed')
            );

            consoleSpy.mockRestore();
        });

        test('should handle flush errors gracefully', async () => {
            logger = new CoreLogger({ logDir: tempDir, setupProcessHandlers: false });
            logger.logger.flush = null; // Remove flush method

            await expect(logger.flush()).resolves.toBeUndefined();
        });
    });

    describe('Factory Functions', () => {
        test('should create logger via factory', () => {
            const pluginLogger = factory('plugin:test');

            expect(pluginLogger.component).toBe('plugin:test');
        });

        test('should get global logger instance', () => {
            const globalLogger = getGlobalLogger();

            expect(globalLogger.component).toBe('desktop');
        });

        test('should reuse global logger instance', () => {
            const logger1 = getGlobalLogger();
            const logger2 = getGlobalLogger();

            expect(logger1).toBe(logger2);
        });
    });

    describe('Log Directory Detection', () => {
        test('should find project root with package.json', () => {
            // Create a temp directory structure
            const projectRoot = path.join(tempDir, 'project');
            fs.mkdirSync(projectRoot, { recursive: true });
            fs.writeFileSync(path.join(projectRoot, 'package.json'), '{}');

            // Change to subdirectory
            const subDir = path.join(projectRoot, 'src');
            fs.mkdirSync(subDir);

            const originalCwd = process.cwd();
            process.chdir(subDir);

            logger = new CoreLogger({ setupProcessHandlers: false });

            // Normalize paths for comparison to handle OS differences
            expect(fs.realpathSync(logger.logDir)).toBe(fs.realpathSync(path.join(projectRoot, 'logs')));

            process.chdir(originalCwd);
        });

        test('should find project root with docker-compose.yml', () => {
            // Create a temp directory structure
            const projectRoot = path.join(tempDir, 'project');
            fs.mkdirSync(projectRoot, { recursive: true });
            fs.writeFileSync(path.join(projectRoot, 'docker-compose.yml'), 'version: "3"');

            const originalCwd = process.cwd();
            process.chdir(projectRoot);

            logger = new CoreLogger({ setupProcessHandlers: false });

            // Normalize paths for comparison to handle OS differences
            expect(fs.realpathSync(logger.logDir)).toBe(fs.realpathSync(path.join(projectRoot, 'logs')));

            process.chdir(originalCwd);
        });
    });

    describe('Browser Environment Handling', () => {
        let originalWindow;

        beforeEach(() => {
            originalWindow = global.window;
        });

        afterEach(() => {
            global.window = originalWindow;
        });

        test('should handle browser environment setup', () => {
            // Mock browser environment
            const mockAddEventListener = jest.fn();
            global.window = {
                addEventListener: mockAddEventListener
            };

            logger = new CoreLogger({ logDir: tempDir });

            expect(mockAddEventListener).toHaveBeenCalledWith('error', expect.any(Function));
            expect(mockAddEventListener).toHaveBeenCalledWith('unhandledrejection', expect.any(Function));
        });

        test('should handle window errors', () => {
            const mockAddEventListener = jest.fn();
            global.window = {
                addEventListener: mockAddEventListener
            };

            logger = new CoreLogger({ logDir: tempDir });

            // Get the error handler
            const errorHandler = mockAddEventListener.mock.calls.find(
                call => call[0] === 'error'
            )[1];

            const mockEvent = {
                error: new Error('Window error'),
                filename: 'test.js',
                lineno: 10,
                colno: 5
            };

            errorHandler(mockEvent);

            expect(logger.logger.error).toHaveBeenCalledWith({
                component: 'desktop',
                msg: 'Window Error',
                meta: {
                    message: 'Window error',
                    filename: 'test.js',
                    lineno: 10,
                    colno: 5,
                    meta: { windowError: true }
                }
            });
        });
    });
});

describe('Integration Tests', () => {
    let tempDir;
    let logger;

    beforeEach(() => {
        tempDir = fs.mkdtempSync(path.join(os.tmpdir(), 'corelogger-integration-'));
    });

    afterEach(async () => {
        if (logger && logger.shutdown) {
            await logger.shutdown();
        }

        if (fs.existsSync(tempDir)) {
            fs.rmSync(tempDir, { recursive: true, force: true });
        }
    });

    test('should demonstrate complete logging workflow', async () => {
        // Create logger
        logger = new CoreLogger({
            component: 'integration-test',
            logLevel: 'DEBUG',
            logDir: tempDir,
            setupProcessHandlers: false
        });

        // Log various levels
        logger.DEBUG('Debug message', { debug: true });
        logger.INFO('Info message', { info: true });
        logger.WARN('Warning message', { warn: true });
        logger.ERROR('Error message', { error: true });

        // Create child logger
        const childLogger = logger.child('child-component');
        childLogger.INFO('Child logger message');

        // Change log level
        logger.setLevel('ERROR');
        logger.DEBUG('This should not appear'); // Below threshold
        logger.ERROR('This should appear');

        // Test statistics
        const stats = logger.getStats();
        expect(stats.component).toBe('integration-test');
        expect(stats.logLevel).toBe('ERROR');

        // Graceful shutdown
        await logger.shutdown();
    });
});
