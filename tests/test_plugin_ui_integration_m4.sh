#!/bin/bash

# Test Suite for Phase 10 M4 - Comprehensive UI Integration Testing
# Tests ribbon icons, modals, command palette integration, security, and performance
# Addresses latest changes in UI framework implementation

# shellcheck disable=SC2317
set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Test configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
LOGS_DIR="$PROJECT_ROOT/logs"
DESKTOP_DIR="$PROJECT_ROOT/desktop"

# Test counters and timing
TESTS_RUN=0
TESTS_PASSED=0
TESTS_FAILED=0
START_TIME=$(date +%s)

# Logging setup
setup_logging() {
    local timestamp
    timestamp=$(date '+%Y%m%d_%H%M%S')
    mkdir -p "$LOGS_DIR"
    LOG_FILE="$LOGS_DIR/plugin_ui_integration_m4_${timestamp}.log"
    echo "Test run started at $(date)" > "$LOG_FILE"
}

# Enhanced logging functions
log() {
    local level="$1"
    shift
    local message="$*"
    local timestamp
    timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "[$timestamp] [$level] $message" | tee -a "$LOG_FILE"
}

log_info() { log "INFO" "$@"; }
log_warn() { log "WARN" "$@"; }
log_error() { log "ERROR" "$@"; }
log_success() { log "SUCCESS" "$@"; }
log_debug() { log "DEBUG" "$@"; }

# Test execution with timing
run_test() {
    local test_name="$1"
    local test_function="$2"
    local start_time
    local end_time
    local duration

    echo -e "\n${BLUE}═══ Test $((TESTS_RUN + 1)): $test_name ═══${NC}"
    log_info "Starting test: $test_name"
    start_time=$(date +%s)

    TESTS_RUN=$((TESTS_RUN + 1))

    if "$test_function"; then
        end_time=$(date +%s)
        duration=$((end_time - start_time))
        echo -e "${GREEN}✓ PASSED: $test_name (${duration}s)${NC}"
        log_success "Test passed: $test_name in ${duration}s"
        TESTS_PASSED=$((TESTS_PASSED + 1))
        return 0
    else
        end_time=$(date +%s)
        duration=$((end_time - start_time))
        echo -e "${RED}✗ FAILED: $test_name (${duration}s)${NC}"
        log_error "Test failed: $test_name after ${duration}s"
        TESTS_FAILED=$((TESTS_FAILED + 1))
        return 1
    fi
}

# Test: UI Component Architecture Validation
test_ui_architecture() {
    log_info "Validating UI component architecture"

    # Check main UI component files exist with proper structure
    local ui_components=(
        "$DESKTOP_DIR/src/ui/RibbonManager.js"
        "$DESKTOP_DIR/src/ui/ModalManager.js"
        "$DESKTOP_DIR/src/ui/CommandPaletteUI.js"
    )

    for component in "${ui_components[@]}"; do
        if [[ ! -f "$component" ]]; then
            log_error "UI component missing: $component"
            return 1
        fi

        # Check for class declaration
        if ! grep -q "class.*{" "$component"; then
            log_error "No class declaration found in: $component"
            return 1
        fi

        # Check for constructor
        if ! grep -q "constructor(" "$component"; then
            log_error "No constructor found in: $component"
            return 1
        fi

        # Check for logging
        if ! grep -q "log\." "$component"; then
            log_error "No logging found in: $component"
            return 1
        fi

        log_debug "✓ Validated component: $(basename "$component")"
    done

    log_info "UI architecture validation passed"
    return 0
}

# Test: Security Input Sanitization
test_security_sanitization() {
    log_info "Testing security input sanitization"

    # Check ModalManager sanitization methods
    local modal_manager="$DESKTOP_DIR/src/ui/ModalManager.js"

    if [[ ! -f "$modal_manager" ]]; then
        log_error "ModalManager not found for security testing"
        return 1
    fi

    local security_patterns=(
        "sanitizeString"
        "sanitizeClassName"
        "clampNumber"
        "validateModalConfig"
        "validateButtons"
    )

    for pattern in "${security_patterns[@]}"; do
        if ! grep -q "$pattern" "$modal_manager"; then
            log_error "Security method missing: $pattern"
            return 1
        fi
        log_debug "✓ Found security method: $pattern"
    done

    # Check for HTML tag removal
    if ! grep -q "replace.*<.*>.*g" "$modal_manager"; then
        log_error "HTML tag sanitization pattern not found"
        return 1
    fi

    # Check for input validation
    if ! grep -q "typeof.*string" "$modal_manager"; then
        log_error "Type validation pattern not found"
        return 1
    fi

    log_info "Security sanitization validation passed"
    return 0
}

# Test: Plugin Manager Integration
test_plugin_manager_integration() {
    log_info "Testing plugin manager UI integration"

    local plugin_manager="$DESKTOP_DIR/src/plugin-manager.js"

    if [[ ! -f "$plugin_manager" ]]; then
        log_error "Plugin manager not found"
        return 1
    fi

    # Check for UI manager imports
    local ui_managers=(
        "RibbonManager"
        "ModalManager"
        "CommandPaletteUI"
    )

    for manager in "${ui_managers[@]}"; do
        if ! grep -q "$manager" "$plugin_manager"; then
            log_error "UI manager not integrated: $manager"
            return 1
        fi
        log_debug "✓ Found UI manager integration: $manager"
    done

    # Check for cleanup methods
    local cleanup_patterns=(
        "cleanupPluginUI"
        "removePluginIcons"
        "closePluginModals"
    )

    for pattern in "${cleanup_patterns[@]}"; do
        if ! grep -q "$pattern" "$plugin_manager"; then
            log_error "Cleanup method missing: $pattern"
            return 1
        fi
        log_debug "✓ Found cleanup method: $pattern"
    done

    log_info "Plugin manager integration validation passed"
    return 0
}

# Test: IPC Channel Implementation
test_ipc_channels() {
    log_info "Testing IPC channel implementation"

    local main_file="$DESKTOP_DIR/src/main.js"

    if [[ ! -f "$main_file" ]]; then
        log_error "Main process file not found"
        return 1
    fi

    # Check for required IPC channels
    local ipc_channels=(
        "ribbon:list"
        "ribbon:click"
        "modal:list"
        "modal:close"
        "command-palette:show"
        "command-palette:hide"
        "ui:stats"
    )

    for channel in "${ipc_channels[@]}"; do
        if ! grep -q "$channel" "$main_file"; then
            log_error "IPC channel missing: $channel"
            return 1
        fi
        log_debug "✓ Found IPC channel: $channel"
    done

    # Check for IPC handler registration
    if ! grep -q "ipcMain.handle\|ipcMain.on" "$main_file"; then
        log_error "No IPC handler registration found"
        return 1
    fi

    log_info "IPC channel implementation validation passed"
    return 0
}

# Test: Preload API Exposure
test_preload_api() {
    log_info "Testing preload API exposure"

    local preload_file="$DESKTOP_DIR/src/preload.js"

    if [[ ! -f "$preload_file" ]]; then
        log_error "Preload file not found"
        return 1
    fi

    # Check for API sections
    local api_sections=(
        "ribbon:"
        "modal:"
        "commandPalette:"
        "ui:"
    )

    for section in "${api_sections[@]}"; do
        if ! grep -q "$section" "$preload_file"; then
            log_error "API section missing: $section"
            return 1
        fi
        log_debug "✓ Found API section: $section"
    done

    # Check for contextBridge usage
    if ! grep -q "contextBridge" "$preload_file"; then
        log_error "contextBridge not found in preload"
        return 1
    fi

    # Check for security isolation
    if ! grep -q "nodeIntegration.*false\|contextIsolation.*true" "$DESKTOP_DIR"/*.js; then
        log_warn "Security isolation settings should be verified"
    fi

    log_info "Preload API exposure validation passed"
    return 0
}

# Test: TypeScript Definitions
test_typescript_definitions() {
    log_info "Testing TypeScript definitions"

    local types_file="$DESKTOP_DIR/types/plugin-api.d.ts"

    if [[ ! -f "$types_file" ]]; then
        log_error "TypeScript definitions not found"
        return 1
    fi

    # Check for interface definitions
    local interfaces=(
        "interface UI"
        "interface Commands"
        "ModalConfig"
        "CommandMetadata"
    )

    for interface in "${interfaces[@]}"; do
        if ! grep -q "$interface" "$types_file"; then
            log_error "Interface missing: $interface"
            return 1
        fi
        log_debug "✓ Found interface: $interface"
    done

    # Check for UI methods
    local ui_methods=(
        "addRibbonIcon"
        "removeRibbonIcon"
        "showModal"
        "closeModal"
    )

    for method in "${ui_methods[@]}"; do
        if ! grep -q "$method" "$types_file"; then
            log_error "UI method missing in types: $method"
            return 1
        fi
        log_debug "✓ Found UI method: $method"
    done

    log_info "TypeScript definitions validation passed"
    return 0
}

# Test: Demo Plugin Integration
test_demo_plugin() {
    log_info "Testing demo plugin M4 feature usage"

    local demo_plugin="$DESKTOP_DIR/plugins/limitless/main.js"

    if [[ ! -f "$demo_plugin" ]]; then
        log_error "Demo plugin not found"
        return 1
    fi

    # Check for M4 feature usage
    local m4_features=(
        "addRibbonIcon"
        "showModal"
        "setMetadata"
    )

    for feature in "${m4_features[@]}"; do
        if ! grep -q "$feature" "$demo_plugin"; then
            log_error "Demo plugin missing M4 feature: $feature"
            return 1
        fi
        log_debug "✓ Demo plugin uses: $feature"
    done

    # Check for proper API access
    if ! grep -q "api\.ui\." "$demo_plugin"; then
        log_error "Demo plugin not using API properly"
        return 1
    fi

    log_info "Demo plugin integration validation passed"
    return 0
}

# Test: Error Handling and Logging
test_error_handling() {
    log_info "Testing error handling and logging"

    local ui_files=(
        "$DESKTOP_DIR/src/ui/RibbonManager.js"
        "$DESKTOP_DIR/src/ui/ModalManager.js"
        "$DESKTOP_DIR/src/ui/CommandPaletteUI.js"
    )

    for file in "${ui_files[@]}"; do
        if [[ ! -f "$file" ]]; then
            log_error "UI file not found: $file"
            return 1
        fi

        # Check for try-catch blocks
        if ! grep -q "try.*{" "$file"; then
            log_error "No try-catch blocks found in: $(basename "$file")"
            return 1
        fi

        # Check for error logging
        if ! grep -q "log\.error\|log\.warn" "$file"; then
            log_error "No error logging found in: $(basename "$file")"
            return 1
        fi

        # Check for input validation
        if ! grep -q "throw new Error\|return false" "$file"; then
            log_error "No error throwing/handling found in: $(basename "$file")"
            return 1
        fi

        log_debug "✓ Error handling validated: $(basename "$file")"
    done

    log_info "Error handling validation passed"
    return 0
}

# Test: Performance and Memory Management
test_performance() {
    log_info "Testing performance and memory management"

    local ui_files=(
        "$DESKTOP_DIR/src/ui/RibbonManager.js"
        "$DESKTOP_DIR/src/ui/ModalManager.js"
        "$DESKTOP_DIR/src/ui/CommandPaletteUI.js"
    )

    for file in "${ui_files[@]}"; do
        # Check for cleanup methods
        if ! grep -q "cleanup\|clear\|delete" "$file"; then
            log_error "No cleanup methods found in: $(basename "$file")"
            return 1
        fi

        # Check for efficient data structures
        if ! grep -q "Map\|Set" "$file"; then
            log_warn "Consider using Map/Set for better performance in: $(basename "$file")"
        fi

        # Check for memory leak prevention
        if ! grep -q "delete\|clear\|null" "$file"; then
            log_error "No memory cleanup patterns found in: $(basename "$file")"
            return 1
        fi

        log_debug "✓ Performance patterns validated: $(basename "$file")"
    done

    # Check for limits and bounds checking
    if ! grep -q "maxModals\|limit\|clamp" "$DESKTOP_DIR/src/ui/ModalManager.js"; then
        log_error "No resource limits found in ModalManager"
        return 1
    fi

    log_info "Performance validation passed"
    return 0
}

# Test: Node.js Module Syntax Validation
test_syntax_validation() {
    log_info "Testing JavaScript syntax validation"

    if ! command -v node >/dev/null 2>&1; then
        log_warn "Node.js not available, skipping syntax validation"
        return 0
    fi

    local js_files=(
        "$DESKTOP_DIR/src/ui/RibbonManager.js"
        "$DESKTOP_DIR/src/ui/ModalManager.js"
        "$DESKTOP_DIR/src/ui/CommandPaletteUI.js"
        "$DESKTOP_DIR/src/plugin-manager.js"
    )

    for file in "${js_files[@]}"; do
        if [[ -f "$file" ]]; then
            if ! node -c "$file" 2>/dev/null; then
                log_error "Syntax error in: $(basename "$file")"
                return 1
            fi
            log_debug "✓ Syntax valid: $(basename "$file")"
        fi
    done

    log_info "Syntax validation passed"
    return 0
}

# Test: Dependencies and Package Configuration
test_dependencies() {
    log_info "Testing dependencies and package configuration"

    local package_file="$DESKTOP_DIR/package.json"

    if [[ ! -f "$package_file" ]]; then
        log_error "Package.json not found"
        return 1
    fi

    # Check for required dependencies
    local deps=(
        "electron"
        "electron-log"
    )

    for dep in "${deps[@]}"; do
        if ! grep -q "\"$dep\"" "$package_file"; then
            log_error "Dependency missing: $dep"
            return 1
        fi
        log_debug "✓ Found dependency: $dep"
    done

    # Check for proper main entry point
    if ! grep -q "\"main\".*\"src/main.js\"" "$package_file"; then
        log_error "Main entry point not properly configured"
        return 1
    fi

    log_info "Dependencies validation passed"
    return 0
}

# Test: Shellcheck Compliance for Test Scripts
test_shellcheck_compliance() {
    log_info "Testing shellcheck compliance for test scripts"

    if ! command -v shellcheck >/dev/null 2>&1; then
        log_warn "Shellcheck not available, skipping shellcheck tests"
        return 0
    fi

    local test_scripts=(
        "$SCRIPT_DIR/test_plugin_ui_m4.sh"
        "$SCRIPT_DIR/run_all_tests.sh"
        "$SCRIPT_DIR/test_code_smell_infrastructure.sh"
    )

    for script in "${test_scripts[@]}"; do
        if [[ -f "$script" ]]; then
            if ! shellcheck "$script" 2>/dev/null; then
                log_error "Shellcheck issues in: $(basename "$script")"
                return 1
            fi
            log_debug "✓ Shellcheck passed: $(basename "$script")"
        fi
    done

    log_info "Shellcheck compliance validation passed"
    return 0
}

# Test: Integration Smoke Test
test_integration_smoke() {
    log_info "Running integration smoke test"

    if ! command -v node >/dev/null 2>&1; then
        log_warn "Node.js not available, skipping smoke test"
        return 0
    fi

    # Test basic module loading
    local test_script="/tmp/ui_smoke_test.js"
    cat > "$test_script" << 'EOF'
try {
    const path = require('path');
    const fs = require('fs');

    // Test if UI modules can be loaded
    const projectRoot = process.argv[2];
    const RibbonManager = require(path.join(projectRoot, 'desktop/src/ui/RibbonManager.js'));
    const ModalManager = require(path.join(projectRoot, 'desktop/src/ui/ModalManager.js'));

    // Test instantiation
    const ribbonManager = new RibbonManager();
    const modalManager = new ModalManager();

    console.log('✓ UI modules loaded and instantiated successfully');
    process.exit(0);
} catch (error) {
    console.error('✗ Smoke test failed:', error.message);
    process.exit(1);
}
EOF

    if ! node "$test_script" "$PROJECT_ROOT" 2>/dev/null; then
        log_error "Integration smoke test failed"
        rm -f "$test_script"
        return 1
    fi

    rm -f "$test_script"
    log_info "Integration smoke test passed"
    return 0
}

# Test results summary and reporting
generate_test_report() {
    local end_time
    local total_duration
    local pass_rate

    end_time=$(date +%s)
    total_duration=$((end_time - START_TIME))

    if [[ $TESTS_RUN -gt 0 ]]; then
        pass_rate=$(( (TESTS_PASSED * 100) / TESTS_RUN ))
    else
        pass_rate=0
    fi

    echo -e "\n${BLUE}════════════════════════════════════════════════════════════════${NC}"
    echo -e "${BLUE}                    M4 UI Integration Test Report                    ${NC}"
    echo -e "${BLUE}════════════════════════════════════════════════════════════════${NC}"

    echo -e "\n${CYAN}Test Execution Summary:${NC}"
    echo -e "  Total Tests Run:     $TESTS_RUN"
    echo -e "  ${GREEN}Tests Passed:        $TESTS_PASSED${NC}"
    echo -e "  ${RED}Tests Failed:         $TESTS_FAILED${NC}"
    echo -e "  Pass Rate:           ${pass_rate}%"
    echo -e "  Total Duration:      ${total_duration}s"

    echo -e "\n${CYAN}Test Coverage Areas:${NC}"
    echo -e "  ✓ UI Architecture Validation"
    echo -e "  ✓ Security Input Sanitization"
    echo -e "  ✓ Plugin Manager Integration"
    echo -e "  ✓ IPC Channel Implementation"
    echo -e "  ✓ Preload API Exposure"
    echo -e "  ✓ TypeScript Definitions"
    echo -e "  ✓ Demo Plugin Integration"
    echo -e "  ✓ Error Handling & Logging"
    echo -e "  ✓ Performance & Memory Management"
    echo -e "  ✓ Syntax Validation"
    echo -e "  ✓ Dependencies Verification"
    echo -e "  ✓ Shellcheck Compliance"
    echo -e "  ✓ Integration Smoke Test"

    if [[ $TESTS_FAILED -eq 0 ]]; then
        echo -e "\n${GREEN}🎉 ALL INTEGRATION TESTS PASSED! 🎉${NC}"
        echo -e "${GREEN}✓ Phase 10 M4 UI integration is fully functional${NC}"
        echo -e "${GREEN}✓ Security measures are in place${NC}"
        echo -e "${GREEN}✓ Performance optimizations validated${NC}"
        echo -e "${GREEN}✓ Code quality standards met${NC}"
        echo -e "\n${BLUE}System is ready for M5 development!${NC}"
    else
        echo -e "\n${RED}❌ Some integration tests failed${NC}"
        echo -e "${YELLOW}Please review failed tests and fix issues before proceeding${NC}"
        echo -e "${CYAN}Check log file for details: $LOG_FILE${NC}"
    fi

    # Log final summary
    log_info "Test suite completed. Duration: ${total_duration}s, Pass rate: ${pass_rate}%"
}

# Main test execution
main() {
    echo -e "${PURPLE}Starting Phase 10 M4 UI Integration Test Suite${NC}"
    echo -e "${BLUE}Testing latest changes: UI components, security, performance${NC}"
    echo "=================================================================="

    setup_logging
    log_info "M4 UI Integration test suite started"

    # Run all integration tests
    run_test "UI Architecture Validation" test_ui_architecture
    run_test "Security Input Sanitization" test_security_sanitization
    run_test "Plugin Manager Integration" test_plugin_manager_integration
    run_test "IPC Channel Implementation" test_ipc_channels
    run_test "Preload API Exposure" test_preload_api
    run_test "TypeScript Definitions" test_typescript_definitions
    run_test "Demo Plugin Integration" test_demo_plugin
    run_test "Error Handling & Logging" test_error_handling
    run_test "Performance & Memory Mgmt" test_performance
    run_test "Syntax Validation" test_syntax_validation
    run_test "Dependencies Verification" test_dependencies
    run_test "Shellcheck Compliance" test_shellcheck_compliance
    run_test "Integration Smoke Test" test_integration_smoke

    # Generate comprehensive report
    generate_test_report

    # Exit with appropriate code
    if [[ $TESTS_FAILED -eq 0 ]]; then
        exit 0
    else
        exit 1
    fi
}

# Execute main function
main "$@"
