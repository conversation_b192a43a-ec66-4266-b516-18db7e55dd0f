#!/bin/bash
# Web UI Configuration Validation Test
# Phase 8: Minimal Web UI Infrastructure
# Purpose: Validate all configuration files syntax and completeness

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test configuration
LOG_DIR="./logs/webui"
TEST_SESSION_ID="webui_config_validation_$(date +%s)"
TIMESTAMP=$(date -u +"%Y%m%d_%H%M%S")
LOG_FILE="${LOG_DIR}/config_validation_${TIMESTAMP}.log"

# Test tracking
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0
TEST_RESULTS=()

# Logging function
log_test_event() {
    local level=$1
    local component=$2
    local message=$3
    local extra_data=${4:-"{}"}

    local log_entry
    log_entry=$(cat <<EOF
{
    "timestamp": "$(date -u +"%Y-%m-%dT%H:%M:%S.%3NZ")",
    "level": "${level}",
    "component": "${component}",
    "message": "${message}",
    "session_id": "${TEST_SESSION_ID}",
    "test_phase": "webui_config_validation",
    "extra_data": ${extra_data}
}
EOF
)
    echo "$log_entry" >> "$LOG_FILE"
}

# Test execution function
run_test() {
    local test_name=$1
    local test_function=$2

    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    echo -e "${YELLOW}Running: ${test_name}${NC}"
    log_test_event "info" "test_runner" "Starting test: ${test_name}"

    if $test_function; then
        echo -e "${GREEN}✓ ${test_name} PASSED${NC}"
        PASSED_TESTS=$((PASSED_TESTS + 1))
        TEST_RESULTS+=("✅ ${test_name} - PASSED")
        log_test_event "info" "test_runner" "Test passed: ${test_name}"
        return 0
    else
        echo -e "${RED}✗ ${test_name} FAILED${NC}"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        TEST_RESULTS+=("❌ ${test_name} - FAILED")
        log_test_event "error" "test_runner" "Test failed: ${test_name}"
        return 1
    fi
}

# Test 1: Validate HTML structure and content
test_html_validation() {
    log_test_event "debug" "html_validation" "Validating HTML structure and content"

    local html_file="./webui/index.html"

    if [ ! -f "$html_file" ]; then
        log_test_event "error" "html_validation" "HTML file not found: $html_file"
        return 1
    fi

    # Check for HTML5 doctype
    if ! grep -q '<!DOCTYPE html>' "$html_file"; then
        log_test_event "error" "html_validation" "HTML5 doctype not found"
        return 1
    fi

    # Check for required meta tags
    if ! grep -q 'charset="UTF-8"' "$html_file"; then
        log_test_event "error" "html_validation" "UTF-8 charset meta tag not found"
        return 1
    fi

    if ! grep -q 'name="viewport"' "$html_file"; then
        log_test_event "warning" "html_validation" "Viewport meta tag not found (mobile responsiveness)"
    fi

    # Check for title tag
    if ! grep -q '<title>' "$html_file"; then
        log_test_event "warning" "html_validation" "Title tag not found"
    fi

    # Check for proper HTML structure
    if ! grep -q '<html' "$html_file" || ! grep -q '</html>' "$html_file"; then
        log_test_event "error" "html_validation" "Proper HTML structure not found"
        return 1
    fi

    if ! grep -q '<head>' "$html_file" || ! grep -q '</head>' "$html_file"; then
        log_test_event "error" "html_validation" "Head section not found"
        return 1
    fi

    if ! grep -q '<body>' "$html_file" || ! grep -q '</body>' "$html_file"; then
        log_test_event "error" "html_validation" "Body section not found"
        return 1
    fi

    log_test_event "info" "html_validation" "HTML validation successful"
    return 0
}

# Test 2: Validate Docker Compose YAML syntax
test_docker_compose_yaml() {
    log_test_event "debug" "docker_compose_yaml" "Validating Docker Compose YAML syntax"

    local compose_file="./docker-compose.web-ui.yml"

    if [ ! -f "$compose_file" ]; then
        log_test_event "error" "docker_compose_yaml" "Docker Compose file not found: $compose_file"
        return 1
    fi

    # Check YAML syntax using Python
    if command -v python3 >/dev/null 2>&1; then
        if ! python3 -c "import yaml; yaml.safe_load(open('$compose_file'))" 2>/dev/null; then
            log_test_event "error" "docker_compose_yaml" "YAML syntax validation failed"
            return 1
        fi
        log_test_event "info" "docker_compose_yaml" "YAML syntax is valid"
    else
        log_test_event "warning" "docker_compose_yaml" "Python3 not available for YAML validation"
    fi

    # Check required compose version
    if ! grep -q "version:" "$compose_file"; then
        log_test_event "error" "docker_compose_yaml" "Docker Compose version not specified"
        return 1
    fi

    # Check if webui service is defined
    if ! grep -q "webui:" "$compose_file"; then
        log_test_event "error" "docker_compose_yaml" "webui service not defined"
        return 1
    fi

    # Check required service properties
    local required_properties=(
        "image:"
        "ports:"
        "volumes:"
        "networks:"
        "logging:"
        "security_opt:"
        "cap_drop:"
    )

    for prop in "${required_properties[@]}"; do
        if ! grep -q "$prop" "$compose_file"; then
            log_test_event "error" "docker_compose_yaml" "Required property '$prop' not found"
            return 1
        fi
    done

    log_test_event "info" "docker_compose_yaml" "Docker Compose configuration validation successful"
    return 0
}

# Test 3: Validate Nginx configuration syntax
test_nginx_config_syntax() {
    log_test_event "debug" "nginx_config_syntax" "Validating Nginx configuration syntax"

    local nginx_config="./config/nginx/nginx.conf"

    if [ ! -f "$nginx_config" ]; then
        log_test_event "error" "nginx_config_syntax" "Nginx config file not found: $nginx_config"
        return 1
    fi

    # Check for basic nginx structure
    if ! grep -q "events {" "$nginx_config"; then
        log_test_event "error" "nginx_config_syntax" "Events block not found in nginx config"
        return 1
    fi

    if ! grep -q "http {" "$nginx_config"; then
        log_test_event "error" "nginx_config_syntax" "HTTP block not found in nginx config"
        return 1
    fi

    if ! grep -q "server {" "$nginx_config"; then
        log_test_event "error" "nginx_config_syntax" "Server block not found in nginx config"
        return 1
    fi

    # Check for required directives
    local required_directives=(
        "listen"
        "server_name"
        "root"
        "index"
        "access_log"
        "error_log"
    )

    for directive in "${required_directives[@]}"; do
        if ! grep -q "$directive" "$nginx_config"; then
            log_test_event "error" "nginx_config_syntax" "Required directive '$directive' not found"
            return 1
        fi
    done

    # Check for JSON logging format
    if ! grep -q "json_combined" "$nginx_config"; then
        log_test_event "error" "nginx_config_syntax" "JSON logging format not configured"
        return 1
    fi

    # Check for security headers
    local security_headers=(
        "X-Frame-Options"
        "X-Content-Type-Options"
        "X-XSS-Protection"
        "Content-Security-Policy"
    )

    for header in "${security_headers[@]}"; do
        if ! grep -q "$header" "$nginx_config"; then
            log_test_event "warning" "nginx_config_syntax" "Security header '$header' not found"
        fi
    done

    # Test nginx syntax if nginx is available
    if command -v nginx >/dev/null 2>&1; then
        if nginx -t -c "$(pwd)/$nginx_config" >/dev/null 2>&1; then
            log_test_event "info" "nginx_config_syntax" "Nginx configuration syntax is valid"
        else
            log_test_event "warning" "nginx_config_syntax" "Nginx syntax check failed (may be due to missing includes)"
        fi
    else
        log_test_event "info" "nginx_config_syntax" "Nginx not available for syntax testing, structure validation passed"
    fi

    return 0
}

# Test 4: Validate security configuration completeness
test_security_config_completeness() {
    log_test_event "debug" "security_config" "Validating security configuration completeness"

    local compose_file="./docker-compose.web-ui.yml"

    # Check security options
    local security_checks=(
        "no-new-privileges:true"
        "cap_drop:"
        "cap_add:"
        "read_only: true"
        "tmpfs:"
    )

    local security_score=0
    local total_checks=${#security_checks[@]}

    for check in "${security_checks[@]}"; do
        if grep -q "$check" "$compose_file"; then
            log_test_event "info" "security_config" "Security check passed: $check"
            security_score=$((security_score + 1))
        else
            log_test_event "warning" "security_config" "Security check missing: $check"
        fi
    done

    # Check resource limits
    if grep -q "resources:" "$compose_file" && grep -q "limits:" "$compose_file"; then
        log_test_event "info" "security_config" "Resource limits configured"
        security_score=$((security_score + 1))
        total_checks=$((total_checks + 1))
    else
        log_test_event "warning" "security_config" "Resource limits not configured"
        total_checks=$((total_checks + 1))
    fi

    # Evaluate security score
    local pass_threshold=$((total_checks * 70 / 100))  # 70% threshold

    if [ "$security_score" -ge "$pass_threshold" ]; then
        log_test_event "info" "security_config" "Security configuration check passed ($security_score/$total_checks)"
        return 0
    else
        log_test_event "error" "security_config" "Security configuration insufficient ($security_score/$total_checks, need $pass_threshold)"
        return 1
    fi
}

# Test 5: Validate logging configuration
test_logging_config() {
    log_test_event "debug" "logging_config" "Validating logging configuration"

    local compose_file="./docker-compose.web-ui.yml"
    local nginx_config="./config/nginx/nginx.conf"

    # Check Docker logging configuration
    if ! grep -q "logging:" "$compose_file"; then
        log_test_event "error" "logging_config" "Docker logging configuration not found"
        return 1
    fi

    if ! grep -q "json-file" "$compose_file"; then
        log_test_event "error" "logging_config" "JSON file logging driver not configured"
        return 1
    fi

    if ! grep -q "max-size" "$compose_file"; then
        log_test_event "warning" "logging_config" "Log rotation max-size not configured"
    fi

    if ! grep -q "max-file" "$compose_file"; then
        log_test_event "warning" "logging_config" "Log rotation max-file not configured"
    fi

    # Check Nginx logging configuration
    if ! grep -q "access_log" "$nginx_config"; then
        log_test_event "error" "logging_config" "Nginx access log not configured"
        return 1
    fi

    if ! grep -q "error_log" "$nginx_config"; then
        log_test_event "error" "logging_config" "Nginx error log not configured"
        return 1
    fi

    # Check for structured logging format
    if ! grep -q '"timestamp"' "$nginx_config"; then
        log_test_event "error" "logging_config" "Structured logging timestamp field not found"
        return 1
    fi

    if ! grep -q '"level"' "$nginx_config"; then
        log_test_event "error" "logging_config" "Structured logging level field not found"
        return 1
    fi

    if ! grep -q '"component"' "$nginx_config"; then
        log_test_event "error" "logging_config" "Structured logging component field not found"
        return 1
    fi

    log_test_event "info" "logging_config" "Logging configuration validation successful"
    return 0
}

# Test 6: Validate port configuration
test_port_config() {
    log_test_event "debug" "port_config" "Validating port configuration"

    local compose_file="./docker-compose.web-ui.yml"

    # Check for high port usage (9820 as specified)
    if ! grep -q "9820:80" "$compose_file"; then
        log_test_event "error" "port_config" "Required high port 9820 not configured"
        return 1
    fi

    # Check if any low ports are used
    if grep -E "[0-9]{1,3}:[0-9]" "$compose_file" | grep -v "9820:80"; then
        log_test_event "warning" "port_config" "Additional port mappings found, ensure they follow high-port rules"
    fi

    log_test_event "info" "port_config" "Port configuration follows high-port rules"
    return 0
}

# Test 7: Validate volume and mount configuration
test_volume_config() {
    log_test_event "debug" "volume_config" "Validating volume and mount configuration"

    local compose_file="./docker-compose.web-ui.yml"

    # Check for required volume mounts
    if ! grep -q "./webui:/usr/share/nginx/html" "$compose_file"; then
        log_test_event "error" "volume_config" "Web UI content volume mount not found"
        return 1
    fi

    if ! grep -q "./logs/webui:/var/log/nginx" "$compose_file"; then
        log_test_event "error" "volume_config" "Nginx log volume mount not found"
        return 1
    fi

    if ! grep -q "./config/nginx/nginx.conf:/etc/nginx/nginx.conf" "$compose_file"; then
        log_test_event "error" "volume_config" "Nginx config volume mount not found"
        return 1
    fi

    # Check for read-only mounts where appropriate
    if ! grep -q ":ro" "$compose_file"; then
        log_test_event "warning" "volume_config" "No read-only volume mounts found"
    fi

    log_test_event "info" "volume_config" "Volume configuration validation successful"
    return 0
}

# Test 8: Validate network configuration
test_network_config() {
    log_test_event "debug" "network_config" "Validating network configuration"

    local compose_file="./docker-compose.web-ui.yml"

    # Check for network definition
    if ! grep -q "networks:" "$compose_file"; then
        log_test_event "error" "network_config" "Network configuration not found"
        return 1
    fi

    if ! grep -q "lifeboard_frontend" "$compose_file"; then
        log_test_event "error" "network_config" "Required frontend network not defined"
        return 1
    fi

    # Check for proper network driver
    if ! grep -q "driver: bridge" "$compose_file"; then
        log_test_event "warning" "network_config" "Network driver not explicitly set to bridge"
    fi

    # Check for network isolation
    if ! grep -q "name: lifeboard_frontend" "$compose_file"; then
        log_test_event "warning" "network_config" "Network name not explicitly set for isolation"
    fi

    log_test_event "info" "network_config" "Network configuration validation successful"
    return 0
}

# Main execution
echo -e "${BLUE}============================================${NC}"
echo -e "${BLUE}  Web UI Configuration Validation Suite${NC}"
echo -e "${BLUE}  Phase 8: Minimal Web UI Infrastructure${NC}"
echo -e "${BLUE}============================================${NC}"
echo "Validating all configuration files..."
echo "Session ID: $TEST_SESSION_ID"
echo "Log file: $LOG_FILE"
echo

# Ensure log directory exists
mkdir -p "$LOG_DIR"

# Initialize log file
log_test_event "info" "test_suite" "Starting Web UI configuration validation suite"

# Run all tests
run_test "HTML Structure Validation" test_html_validation
run_test "Docker Compose YAML Syntax" test_docker_compose_yaml
run_test "Nginx Configuration Syntax" test_nginx_config_syntax
run_test "Security Configuration Completeness" test_security_config_completeness
run_test "Logging Configuration" test_logging_config
run_test "Port Configuration" test_port_config
run_test "Volume Configuration" test_volume_config
run_test "Network Configuration" test_network_config

echo
echo -e "${BLUE}============================================${NC}"
echo -e "${BLUE}  Configuration Validation Results${NC}"
echo -e "${BLUE}============================================${NC}"

for result in "${TEST_RESULTS[@]}"; do
    echo -e "$result"
done

echo
echo -e "${BLUE}Overall Results:${NC}"
echo -e "Total Tests: $TOTAL_TESTS"
echo -e "${GREEN}Passed: $PASSED_TESTS${NC}"
echo -e "${RED}Failed: $FAILED_TESTS${NC}"

log_test_event "info" "test_suite" "Configuration validation completed" "{\"total_tests\": $TOTAL_TESTS, \"passed\": $PASSED_TESTS, \"failed\": $FAILED_TESTS}"

if [ $FAILED_TESTS -eq 0 ]; then
    echo -e "${GREEN}🎉 ALL CONFIGURATION VALIDATIONS PASSED! 🎉${NC}"
    echo -e "${GREEN}✓ HTML structure valid${NC}"
    echo -e "${GREEN}✓ Docker Compose YAML valid${NC}"
    echo -e "${GREEN}✓ Nginx configuration valid${NC}"
    echo -e "${GREEN}✓ Security configuration complete${NC}"
    echo -e "${GREEN}✓ Logging configuration proper${NC}"
    echo -e "${GREEN}✓ Port configuration follows rules${NC}"
    echo -e "${GREEN}✓ Volume configuration secure${NC}"
    echo -e "${GREEN}✓ Network configuration isolated${NC}"
    echo
    echo -e "${BLUE}All configurations are ready for deployment!${NC}"
    exit 0
else
    PASS_RATE=$(( (PASSED_TESTS * 100) / TOTAL_TESTS ))
    echo -e "${RED}❌ Some configuration validations failed (${PASS_RATE}% pass rate)${NC}"
    echo -e "${YELLOW}Please review failed validations and fix configuration issues${NC}"
    exit 1
fi
