#!/bin/bash
# Web UI Integration Test Suite
# Phase 8: Minimal Web UI Infrastructure
# Purpose: Test end-to-end web UI functionality with Docker Compose

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test configuration
WEBUI_PORT=9820
WEBUI_URL="http://localhost:${WEBUI_PORT}"
WEBUI_HEALTH_URL="${WEBUI_URL}/health"
COMPOSE_PROJECT="lifeboard-webui-test"
LOG_DIR="./logs/webui"
TEST_SESSION_ID="webui_integration_$(date +%s)"
TIMESTAMP=$(date -u +"%Y%m%d_%H%M%S")
LOG_FILE="${LOG_DIR}/integration_test_${TIMESTAMP}.log"

# Test tracking
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0
TEST_RESULTS=()
SERVICE_STARTED=false

# Cleanup function
cleanup() {
    if [ "$SERVICE_STARTED" = true ]; then
        echo -e "${YELLOW}Cleaning up test services...${NC}"
        docker compose -p "$COMPOSE_PROJECT" -f docker-compose.web-ui.yml down -v --remove-orphans >/dev/null 2>&1 || true
        docker network prune -f >/dev/null 2>&1 || true
    fi
}

# Set trap for cleanup
trap cleanup EXIT

# Logging function
log_test_event() {
    local level=$1
    local component=$2
    local message=$3
    local extra_data=${4:-"{}"}

    local log_entry
    log_entry=$(cat <<EOF
{
    "timestamp": "$(date -u +"%Y-%m-%dT%H:%M:%S.%3NZ")",
    "level": "${level}",
    "component": "${component}",
    "message": "${message}",
    "session_id": "${TEST_SESSION_ID}",
    "test_phase": "webui_integration",
    "extra_data": ${extra_data}
}
EOF
)
    echo "$log_entry" >> "$LOG_FILE"
}

# Test execution function
run_test() {
    local test_name=$1
    local test_function=$2

    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    echo -e "${YELLOW}Running: ${test_name}${NC}"
    log_test_event "info" "test_runner" "Starting test: ${test_name}"

    if $test_function; then
        echo -e "${GREEN}✓ ${test_name} PASSED${NC}"
        PASSED_TESTS=$((PASSED_TESTS + 1))
        TEST_RESULTS+=("✅ ${test_name} - PASSED")
        log_test_event "info" "test_runner" "Test passed: ${test_name}"
        return 0
    else
        echo -e "${RED}✗ ${test_name} FAILED${NC}"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        TEST_RESULTS+=("❌ ${test_name} - FAILED")
        log_test_event "error" "test_runner" "Test failed: ${test_name}"
        return 1
    fi
}

# Test 1: Start Web UI service
test_service_startup() {
    log_test_event "debug" "service_startup" "Starting web UI service"

    # Start the web UI service with profile
    if docker compose -p "$COMPOSE_PROJECT" -f docker-compose.web-ui.yml --profile webui up -d >/dev/null 2>&1; then
        SERVICE_STARTED=true
        log_test_event "info" "service_startup" "Web UI service started successfully"

        # Wait for service to be ready
        local max_attempts=30
        local attempt=0

        while [ "$attempt" -lt "$max_attempts" ]; do
            if docker compose -p "$COMPOSE_PROJECT" ps --services --filter "status=running" | grep -q "webui"; then
                log_test_event "info" "service_startup" "Web UI service is running"
                return 0
            fi
            sleep 1
            attempt=$((attempt + 1))
        done

        log_test_event "error" "service_startup" "Web UI service failed to start within timeout"
        return 1
    else
        log_test_event "error" "service_startup" "Failed to start web UI service"
        return 1
    fi
}

# Test 2: Test HTTP connectivity
test_http_connectivity() {
    log_test_event "debug" "http_connectivity" "Testing HTTP connectivity to web UI"

    local max_attempts=30
    local attempt=0

    while [ "$attempt" -lt "$max_attempts" ]; do
        if curl -s --connect-timeout 2 --max-time 5 "$WEBUI_URL" >/dev/null 2>&1; then
            log_test_event "info" "http_connectivity" "HTTP connectivity successful"
            return 0
        fi
        sleep 1
        attempt=$((attempt + 1))
    done

    log_test_event "error" "http_connectivity" "Failed to connect to web UI service"
    return 1
}

# Test 3: Validate welcome page content
test_welcome_page_content() {
    log_test_event "debug" "welcome_content" "Validating welcome page content"

    local response
    if response=$(curl -s --connect-timeout 5 --max-time 10 "$WEBUI_URL" 2>/dev/null); then
        # Check for Welcome text
        if echo "$response" | grep -q "Welcome"; then
            log_test_event "info" "welcome_content" "Welcome text found in response"

            # Check for proper HTML structure
            if echo "$response" | grep -q "<!DOCTYPE html>"; then
                log_test_event "info" "welcome_content" "Proper HTML5 doctype found"

                # Check for meta viewport tag (mobile responsive)
                if echo "$response" | grep -q 'name="viewport"'; then
                    log_test_event "info" "welcome_content" "Mobile responsive viewport meta tag found"
                    return 0
                else
                    log_test_event "warning" "welcome_content" "Viewport meta tag not found"
                    return 0  # Not critical for basic functionality
                fi
            else
                log_test_event "error" "welcome_content" "HTML5 doctype not found"
                return 1
            fi
        else
            log_test_event "error" "welcome_content" "Welcome text not found in response"
            return 1
        fi
    else
        log_test_event "error" "welcome_content" "Failed to retrieve welcome page"
        return 1
    fi
}

# Test 4: Test health check endpoint
test_health_endpoint() {
    log_test_event "debug" "health_endpoint" "Testing health check endpoint"

    local response
    local http_code

    if response=$(curl -s --connect-timeout 5 --max-time 10 -w "%{http_code}" "$WEBUI_HEALTH_URL" 2>/dev/null); then
        http_code=$(echo "$response" | tail -c 4)
        content=$(echo "$response" | head -c -4)

        if [ "$http_code" = "200" ]; then
            log_test_event "info" "health_endpoint" "Health endpoint returned 200 OK"

            # Check if response is JSON
            if echo "$content" | python3 -m json.tool >/dev/null 2>&1; then
                log_test_event "info" "health_endpoint" "Health endpoint returned valid JSON"

                # Check for status field
                if echo "$content" | grep -q '"status"'; then
                    log_test_event "info" "health_endpoint" "Health status field found"
                    return 0
                else
                    log_test_event "warning" "health_endpoint" "Health status field not found in JSON"
                    return 0  # Not critical
                fi
            else
                log_test_event "warning" "health_endpoint" "Health endpoint did not return valid JSON"
                return 0  # Not critical
            fi
        else
            log_test_event "error" "health_endpoint" "Health endpoint returned HTTP $http_code"
            return 1
        fi
    else
        log_test_event "error" "health_endpoint" "Failed to reach health endpoint"
        return 1
    fi
}

# Test 5: Test HTTP response headers
test_security_headers() {
    log_test_event "debug" "security_headers" "Testing security headers"

    local headers
    if headers=$(curl -s -I --connect-timeout 5 --max-time 10 "$WEBUI_URL" 2>/dev/null); then
        local security_score=0
        local total_checks=5

        # Check for X-Frame-Options
        if echo "$headers" | grep -qi "X-Frame-Options"; then
            log_test_event "info" "security_headers" "X-Frame-Options header found"
            security_score=$((security_score + 1))
        else
            log_test_event "warning" "security_headers" "X-Frame-Options header missing"
        fi

        # Check for X-Content-Type-Options
        if echo "$headers" | grep -qi "X-Content-Type-Options"; then
            log_test_event "info" "security_headers" "X-Content-Type-Options header found"
            security_score=$((security_score + 1))
        else
            log_test_event "warning" "security_headers" "X-Content-Type-Options header missing"
        fi

        # Check for X-XSS-Protection
        if echo "$headers" | grep -qi "X-XSS-Protection"; then
            log_test_event "info" "security_headers" "X-XSS-Protection header found"
            security_score=$((security_score + 1))
        else
            log_test_event "warning" "security_headers" "X-XSS-Protection header missing"
        fi

        # Check for Content-Security-Policy
        if echo "$headers" | grep -qi "Content-Security-Policy"; then
            log_test_event "info" "security_headers" "Content-Security-Policy header found"
            security_score=$((security_score + 1))
        else
            log_test_event "warning" "security_headers" "Content-Security-Policy header missing"
        fi

        # Check for server header hiding
        if echo "$headers" | grep -qi "Server: nginx"; then
            log_test_event "warning" "security_headers" "Server header reveals nginx version"
        else
            log_test_event "info" "security_headers" "Server header properly hidden"
            security_score=$((security_score + 1))
        fi

        # Security score evaluation
        if [ "$security_score" -ge 3 ]; then
            log_test_event "info" "security_headers" "Security headers check passed ($security_score/$total_checks)"
            return 0
        else
            log_test_event "warning" "security_headers" "Security headers check needs improvement ($security_score/$total_checks)"
            return 0  # Warning only, not critical for basic functionality
        fi
    else
        log_test_event "error" "security_headers" "Failed to retrieve headers"
        return 1
    fi
}

# Test 6: Test container security configuration
test_container_security() {
    log_test_event "debug" "container_security" "Testing container security configuration"

    local container_name
    if container_name=$(docker compose -p "$COMPOSE_PROJECT" ps -q webui 2>/dev/null); then
        if [ -n "$container_name" ]; then
            # Check for no-new-privileges
            local security_opts
            if security_opts=$(docker inspect "$container_name" --format='{{.HostConfig.SecurityOpt}}' 2>/dev/null); then
                if echo "$security_opts" | grep -q "no-new-privileges:true"; then
                    log_test_event "info" "container_security" "no-new-privileges security option enabled"
                else
                    log_test_event "error" "container_security" "no-new-privileges security option not enabled"
                    return 1
                fi
            fi

            # Check for dropped capabilities
            local cap_drops
            if cap_drops=$(docker inspect "$container_name" --format='{{.HostConfig.CapDrop}}' 2>/dev/null); then
                if echo "$cap_drops" | grep -q "ALL"; then
                    log_test_event "info" "container_security" "All capabilities properly dropped"
                else
                    log_test_event "warning" "container_security" "Capabilities not properly dropped: $cap_drops"
                fi
            fi

            # Check for read-only root filesystem
            local readonly_root
            if readonly_root=$(docker inspect "$container_name" --format='{{.HostConfig.ReadonlyRootfs}}' 2>/dev/null); then
                if [ "$readonly_root" = "true" ]; then
                    log_test_event "info" "container_security" "Read-only root filesystem enabled"
                else
                    log_test_event "warning" "container_security" "Read-only root filesystem not enabled"
                fi
            fi

            return 0
        else
            log_test_event "error" "container_security" "Web UI container not found"
            return 1
        fi
    else
        log_test_event "error" "container_security" "Failed to get container information"
        return 1
    fi
}

# Test 7: Test logging functionality
test_logging_functionality() {
    log_test_event "debug" "logging_functionality" "Testing logging functionality"

    # Make a request to generate access logs
    curl -s "$WEBUI_URL" >/dev/null 2>&1

    # Wait a moment for logs to be written
    sleep 2

    # Check if nginx logs are being generated
    local container_name
    if container_name=$(docker compose -p "$COMPOSE_PROJECT" ps -q webui 2>/dev/null); then
        if [ -n "$container_name" ]; then
            # Check access logs
            local access_logs
            if access_logs=$(docker exec "$container_name" ls -la /var/log/nginx/ 2>/dev/null | grep access); then
                log_test_event "info" "logging_functionality" "Nginx access logs found"

                # Check if logs contain JSON format (look for timestamp field)
                local log_content
                if log_content=$(docker exec "$container_name" cat /var/log/nginx/access.log 2>/dev/null | tail -1); then
                    if echo "$log_content" | grep -q '"timestamp"'; then
                        log_test_event "info" "logging_functionality" "JSON formatted logs confirmed"
                        return 0
                    else
                        log_test_event "warning" "logging_functionality" "Logs may not be in JSON format"
                        return 0  # Warning only
                    fi
                else
                    log_test_event "warning" "logging_functionality" "Could not read log content"
                    return 0  # Warning only
                fi
            else
                log_test_event "warning" "logging_functionality" "Nginx access logs not found"
                return 0  # Warning only
            fi
        else
            log_test_event "error" "logging_functionality" "Container not found for log testing"
            return 1
        fi
    else
        log_test_event "error" "logging_functionality" "Failed to get container for log testing"
        return 1
    fi
}

# Prerequisites check
check_prerequisites() {
    echo -e "${YELLOW}Checking prerequisites...${NC}"

    # Check if Docker is available
    if ! command -v docker >/dev/null 2>&1; then
        echo -e "${RED}Error: Docker is not installed or not in PATH${NC}"
        exit 1
    fi

    # Check if Docker Compose is available
    if ! command -v docker-compose >/dev/null 2>&1 && ! docker compose version >/dev/null 2>&1; then
        echo -e "${RED}Error: Docker Compose is not available${NC}"
        exit 1
    fi

    # Check if curl is available
    if ! command -v curl >/dev/null 2>&1; then
        echo -e "${RED}Error: curl is not installed${NC}"
        exit 1
    fi

    echo -e "${GREEN}✓ All prerequisites met${NC}"
}

# Main execution
echo -e "${BLUE}============================================${NC}"
echo -e "${BLUE}  Web UI Integration Test Suite${NC}"
echo -e "${BLUE}  Phase 8: Minimal Web UI Infrastructure${NC}"
echo -e "${BLUE}============================================${NC}"
echo "Testing end-to-end web UI functionality..."
echo "Session ID: $TEST_SESSION_ID"
echo "Log file: $LOG_FILE"
echo

# Check prerequisites
check_prerequisites

# Ensure log directory exists
mkdir -p "$LOG_DIR"

# Initialize log file
log_test_event "info" "test_suite" "Starting Web UI integration test suite"

# Run all tests
run_test "Service Startup" test_service_startup
run_test "HTTP Connectivity" test_http_connectivity
run_test "Welcome Page Content" test_welcome_page_content
run_test "Health Check Endpoint" test_health_endpoint
run_test "Security Headers" test_security_headers
run_test "Container Security" test_container_security
run_test "Logging Functionality" test_logging_functionality

echo
echo -e "${BLUE}============================================${NC}"
echo -e "${BLUE}  Integration Test Results Summary${NC}"
echo -e "${BLUE}============================================${NC}"

for result in "${TEST_RESULTS[@]}"; do
    echo -e "$result"
done

echo
echo -e "${BLUE}Overall Results:${NC}"
echo -e "Total Tests: $TOTAL_TESTS"
echo -e "${GREEN}Passed: $PASSED_TESTS${NC}"
echo -e "${RED}Failed: $FAILED_TESTS${NC}"

log_test_event "info" "test_suite" "Integration test suite completed" "{\"total_tests\": $TOTAL_TESTS, \"passed\": $PASSED_TESTS, \"failed\": $FAILED_TESTS}"

if [ $FAILED_TESTS -eq 0 ]; then
    echo -e "${GREEN}🎉 ALL WEB UI INTEGRATION TESTS PASSED! 🎉${NC}"
    echo -e "${GREEN}✓ Web UI service starts successfully${NC}"
    echo -e "${GREEN}✓ HTTP connectivity working${NC}"
    echo -e "${GREEN}✓ Welcome page content valid${NC}"
    echo -e "${GREEN}✓ Health check endpoint functional${NC}"
    echo -e "${GREEN}✓ Security headers configured${NC}"
    echo -e "${GREEN}✓ Container security hardened${NC}"
    echo -e "${GREEN}✓ Logging functionality operational${NC}"
    echo
    echo -e "${BLUE}Web UI integration test successful!${NC}"
    echo -e "${BLUE}Service is accessible at: http://localhost:9820${NC}"
    exit 0
else
    PASS_RATE=$(( (PASSED_TESTS * 100) / TOTAL_TESTS ))
    echo -e "${RED}❌ Some integration tests failed (${PASS_RATE}% pass rate)${NC}"
    echo -e "${YELLOW}Please review failed tests and fix issues before proceeding${NC}"
    exit 1
fi
