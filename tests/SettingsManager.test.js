// SettingsManager.test.js
// Unit tests for SettingsManager with edge cases: no file, malformed JSON, schema violation

const fs = require('fs');
const path = require('path');
const os = require('os');
const SettingsManager = require('../desktop/src/SettingsManager');

// Mock electron-log to avoid actual logging during tests
jest.mock('electron-log', () => ({
  info: jest.fn(),
  debug: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
  transports: {
    file: {
      level: 'info'
    }
  }
}));

/**
 * Comprehensive test suite for SettingsManager
 * Tests edge cases including missing files, malformed JSON, and schema validation failures
 */
describe('SettingsManager', () => {
  let settingsManager;
  let testDir;
  let originalCwd;

  beforeEach(() => {
    // Create a temporary directory for testing
    testDir = fs.mkdtempSync(path.join(os.tmpdir(), 'settings-manager-test-'));
    originalCwd = process.cwd();

    // Mock the base directory to use our test directory
    jest.spyOn(SettingsManager.prototype, 'getAppDataDirectory').mockReturnValue(testDir);

    settingsManager = new SettingsManager();
    settingsManager.clearAllCache();
  });

  afterEach(() => {
    // Clean up test directory
    if (fs.existsSync(testDir)) {
      fs.rmSync(testDir, { recursive: true, force: true });
    }

    // Restore original working directory
    process.chdir(originalCwd);

    // Clear all mocks
    jest.clearAllMocks();
  });

  describe('Constructor and Initialization', () => {
    test('should initialize with empty cache', () => {
      expect(settingsManager.getCacheSize()).toBe(0);
    });

    test('should create base directory if it does not exist', () => {
      // The directory should exist after initialization
      expect(fs.existsSync(testDir)).toBe(true);
    });
  });

  describe('load() method', () => {
    test('should throw error for invalid plugin ID', () => {
      expect(() => settingsManager.load(null)).toThrow('Plugin ID must be a non-empty string');
      expect(() => settingsManager.load('')).toThrow('Plugin ID must be a non-empty string');
      expect(() => settingsManager.load(123)).toThrow('Plugin ID must be a non-empty string');
    });

    test('should return empty object when settings file does not exist', () => {
      const result = settingsManager.load('nonexistent-plugin');
      expect(result).toEqual({});
    });

    test('should apply defaults when no settings file exists', () => {
      const schema = {
        type: 'object',
        properties: {
          theme: { type: 'string', default: 'dark' },
          notifications: { type: 'boolean', default: true }
        }
      };

      const result = settingsManager.load('test-plugin', schema);
      expect(result).toEqual({
        theme: 'dark',
        notifications: true
      });
    });

    test('should load existing settings successfully', () => {
      const pluginId = 'test-plugin';
      const testSettings = { theme: 'light', notifications: false };

      // Create plugin directory and settings file
      const pluginDir = path.join(testDir, pluginId);
      fs.mkdirSync(pluginDir, { recursive: true });
      fs.writeFileSync(path.join(pluginDir, 'settings.json'), JSON.stringify(testSettings));

      const result = settingsManager.load(pluginId);
      expect(result).toEqual(testSettings);
    });

    test('should handle empty settings file gracefully', () => {
      const pluginId = 'test-plugin';

      // Create plugin directory and empty settings file
      const pluginDir = path.join(testDir, pluginId);
      fs.mkdirSync(pluginDir, { recursive: true });
      fs.writeFileSync(path.join(pluginDir, 'settings.json'), '');

      const result = settingsManager.load(pluginId);
      expect(result).toEqual({});
    });

    test('should handle whitespace-only settings file gracefully', () => {
      const pluginId = 'test-plugin';

      // Create plugin directory and whitespace-only settings file
      const pluginDir = path.join(testDir, pluginId);
      fs.mkdirSync(pluginDir, { recursive: true });
      fs.writeFileSync(path.join(pluginDir, 'settings.json'), '   \n\t  ');

      const result = settingsManager.load(pluginId);
      expect(result).toEqual({});
    });

    test('should throw error for malformed JSON', () => {
      const pluginId = 'test-plugin';
      const malformedJson = '{ "theme": "dark", "notifications": true'; // Missing closing brace

      // Create plugin directory and malformed JSON file
      const pluginDir = path.join(testDir, pluginId);
      fs.mkdirSync(pluginDir, { recursive: true });
      fs.writeFileSync(path.join(pluginDir, 'settings.json'), malformedJson);

      expect(() => settingsManager.load(pluginId)).toThrow('Malformed JSON in settings file');
    });

    test('should throw error for invalid JSON syntax', () => {
      const pluginId = 'test-plugin';
      const invalidJson = 'not json at all';

      // Create plugin directory and invalid JSON file
      const pluginDir = path.join(testDir, pluginId);
      fs.mkdirSync(pluginDir, { recursive: true });
      fs.writeFileSync(path.join(pluginDir, 'settings.json'), invalidJson);

      expect(() => settingsManager.load(pluginId)).toThrow('Malformed JSON in settings file');
    });

    test('should merge defaults with existing settings', () => {
      const pluginId = 'test-plugin';
      const existingSettings = { theme: 'light' };
      const schema = {
        type: 'object',
        properties: {
          theme: { type: 'string', default: 'dark' },
          notifications: { type: 'boolean', default: true },
          language: { type: 'string', default: 'en' }
        }
      };

      // Create plugin directory and partial settings file
      const pluginDir = path.join(testDir, pluginId);
      fs.mkdirSync(pluginDir, { recursive: true });
      fs.writeFileSync(path.join(pluginDir, 'settings.json'), JSON.stringify(existingSettings));

      const result = settingsManager.load(pluginId, schema);
      expect(result).toEqual({
        theme: 'light', // Existing value preserved
        notifications: true, // Default applied
        language: 'en' // Default applied
      });
    });

    test('should fail validation for schema violations', () => {
      const pluginId = 'test-plugin';
      const invalidSettings = { theme: 123, notifications: 'invalid' }; // Wrong types
      const schema = {
        type: 'object',
        properties: {
          theme: { type: 'string' },
          notifications: { type: 'boolean' }
        },
        required: ['theme', 'notifications']
      };

      // Create plugin directory and invalid settings file
      const pluginDir = path.join(testDir, pluginId);
      fs.mkdirSync(pluginDir, { recursive: true });
      fs.writeFileSync(path.join(pluginDir, 'settings.json'), JSON.stringify(invalidSettings));

      expect(() => settingsManager.load(pluginId, schema)).toThrow('Settings validation failed against provided schema');
    });

    test('should pass validation for valid schema', () => {
      const pluginId = 'test-plugin';
      const validSettings = { theme: 'dark', notifications: true };
      const schema = {
        type: 'object',
        properties: {
          theme: { type: 'string' },
          notifications: { type: 'boolean' }
        },
        required: ['theme', 'notifications']
      };

      // Create plugin directory and valid settings file
      const pluginDir = path.join(testDir, pluginId);
      fs.mkdirSync(pluginDir, { recursive: true });
      fs.writeFileSync(path.join(pluginDir, 'settings.json'), JSON.stringify(validSettings));

      const result = settingsManager.load(pluginId, schema);
      expect(result).toEqual(validSettings);
    });

    test('should cache loaded settings', () => {
      const pluginId = 'test-plugin';
      const testSettings = { theme: 'light' };

      // Create plugin directory and settings file
      const pluginDir = path.join(testDir, pluginId);
      fs.mkdirSync(pluginDir, { recursive: true });
      fs.writeFileSync(path.join(pluginDir, 'settings.json'), JSON.stringify(testSettings));

      settingsManager.load(pluginId);
      expect(settingsManager.getCacheSize()).toBe(1);
    });
  });

  describe('save() method', () => {
    test('should throw error for invalid plugin ID', () => {
      expect(settingsManager.save(null, {})).toBe(false);
      expect(settingsManager.save('', {})).toBe(false);
      expect(settingsManager.save(123, {})).toBe(false);
    });

    test('should throw error for invalid data', () => {
      expect(settingsManager.save('test-plugin', null)).toBe(false);
      expect(settingsManager.save('test-plugin', 'not an object')).toBe(false);
      expect(settingsManager.save('test-plugin', 123)).toBe(false);
    });

    test('should save settings successfully', () => {
      const pluginId = 'test-plugin';
      const testSettings = { theme: 'dark', notifications: false };

      const result = settingsManager.save(pluginId, testSettings);
      expect(result).toBe(true);

      // Verify file was created
      const settingsPath = path.join(testDir, pluginId, 'settings.json');
      expect(fs.existsSync(settingsPath)).toBe(true);

      // Verify content
      const savedContent = JSON.parse(fs.readFileSync(settingsPath, 'utf8'));
      expect(savedContent).toEqual(testSettings);
    });

    test('should create plugin directory if it does not exist', () => {
      const pluginId = 'new-plugin';
      const testSettings = { theme: 'light' };

      const result = settingsManager.save(pluginId, testSettings);
      expect(result).toBe(true);

      // Verify directory was created
      const pluginDir = path.join(testDir, pluginId);
      expect(fs.existsSync(pluginDir)).toBe(true);
    });

    test('should fail validation and not save for schema violations', () => {
      const pluginId = 'test-plugin';
      const invalidSettings = { theme: 123 }; // Wrong type
      const schema = {
        type: 'object',
        properties: {
          theme: { type: 'string' }
        }
      };

      const result = settingsManager.save(pluginId, invalidSettings, schema);
      expect(result).toBe(false);

      // Verify file was not created
      const settingsPath = path.join(testDir, pluginId, 'settings.json');
      expect(fs.existsSync(settingsPath)).toBe(false);
    });

    test('should save successfully with valid schema', () => {
      const pluginId = 'test-plugin';
      const validSettings = { theme: 'dark', notifications: true };
      const schema = {
        type: 'object',
        properties: {
          theme: { type: 'string' },
          notifications: { type: 'boolean' }
        }
      };

      const result = settingsManager.save(pluginId, validSettings, schema);
      expect(result).toBe(true);

      // Verify content
      const settingsPath = path.join(testDir, pluginId, 'settings.json');
      const savedContent = JSON.parse(fs.readFileSync(settingsPath, 'utf8'));
      expect(savedContent).toEqual(validSettings);
    });

    test('should update cache when saving', () => {
      const pluginId = 'test-plugin';
      const testSettings = { theme: 'light' };

      settingsManager.save(pluginId, testSettings);
      expect(settingsManager.getCacheSize()).toBe(1);
    });

    test('should handle file system errors gracefully', () => {
      const pluginId = 'test-plugin';
      const testSettings = { theme: 'light' };

      // Mock fs.writeFileSync to throw an error
      const originalWriteFileSync = fs.writeFileSync;
      fs.writeFileSync = jest.fn(() => {
        throw new Error('File system error');
      });

      const result = settingsManager.save(pluginId, testSettings);
      expect(result).toBe(false);

      // Restore original function
      fs.writeFileSync = originalWriteFileSync;
    });
  });

  describe('Cache Management', () => {
    test('should clear cache for specific plugin', () => {
      const pluginId = 'test-plugin';
      const testSettings = { theme: 'light' };

      settingsManager.save(pluginId, testSettings);
      expect(settingsManager.getCacheSize()).toBe(1);

      settingsManager.clearCache(pluginId);
      expect(settingsManager.getCacheSize()).toBe(0);
    });

    test('should clear all cache', () => {
      settingsManager.save('plugin1', { theme: 'light' });
      settingsManager.save('plugin2', { theme: 'dark' });
      expect(settingsManager.getCacheSize()).toBe(2);

      settingsManager.clearAllCache();
      expect(settingsManager.getCacheSize()).toBe(0);
    });
  });

  describe('Utility Methods', () => {
    test('should check if settings file exists', () => {
      const pluginId = 'test-plugin';

      // Initially should not exist
      expect(settingsManager.hasSettings(pluginId)).toBe(false);

      // Create settings file
      const testSettings = { theme: 'light' };
      settingsManager.save(pluginId, testSettings);

      // Now should exist
      expect(settingsManager.hasSettings(pluginId)).toBe(true);
    });

    test('should delete settings file', () => {
      const pluginId = 'test-plugin';
      const testSettings = { theme: 'light' };

      // Create settings file
      settingsManager.save(pluginId, testSettings);
      expect(settingsManager.hasSettings(pluginId)).toBe(true);

      // Delete settings
      const result = settingsManager.deleteSettings(pluginId);
      expect(result).toBe(true);
      expect(settingsManager.hasSettings(pluginId)).toBe(false);
    });

    test('should handle deletion of non-existent file', () => {
      const pluginId = 'nonexistent-plugin';

      const result = settingsManager.deleteSettings(pluginId);
      expect(result).toBe(true); // Should succeed even if file doesn't exist
    });

    test('should handle file system errors in hasSettings', () => {
      const pluginId = 'test-plugin';

      // Mock getSettingsFilePath to throw an error
      const originalGetSettingsFilePath = settingsManager.getSettingsFilePath;
      settingsManager.getSettingsFilePath = jest.fn(() => {
        throw new Error('File system error');
      });

      const result = settingsManager.hasSettings(pluginId);
      expect(result).toBe(false);

      // Restore original function
      settingsManager.getSettingsFilePath = originalGetSettingsFilePath;
    });
  });

  describe('Cross-platform AppData Directory', () => {
    test('should handle Windows AppData path', () => {
      const originalPlatform = process.platform;
      const originalEnv = process.env;

      // Clear existing mock and create a new instance
      jest.restoreAllMocks();

      // Mock Windows environment
      Object.defineProperty(process, 'platform', { value: 'win32' });
      process.env = { ...originalEnv, APPDATA: 'C:\\Users\\<USER>\\AppData\\Roaming' };

      const manager = new SettingsManager();
      const appDataPath = manager.getAppDataDirectory();

      // Check that the path contains the expected components
      expect(appDataPath).toContain('AppData');
      expect(appDataPath).toContain('Roaming');
      expect(appDataPath).toContain('lifeboard');
      expect(appDataPath).toContain('plugins');
      expect(appDataPath).toMatch(/[Cc]:/); // Should contain C: drive

      // Restore original values
      Object.defineProperty(process, 'platform', { value: originalPlatform });
      process.env = originalEnv;

      // Re-establish the mock for other tests
      jest.spyOn(SettingsManager.prototype, 'getAppDataDirectory').mockReturnValue(testDir);
    });

    test('should handle macOS AppData path', () => {
      const originalPlatform = process.platform;

      // Clear existing mock and create a new instance
      jest.restoreAllMocks();

      // Mock macOS environment
      Object.defineProperty(process, 'platform', { value: 'darwin' });

      const manager = new SettingsManager();
      const appDataPath = manager.getAppDataDirectory();

      expect(appDataPath).toContain('Library/Application Support/lifeboard/plugins');

      // Restore original value
      Object.defineProperty(process, 'platform', { value: originalPlatform });

      // Re-establish the mock for other tests
      jest.spyOn(SettingsManager.prototype, 'getAppDataDirectory').mockReturnValue(testDir);
    });

    test('should handle Linux AppData path', () => {
      const originalPlatform = process.platform;
      const originalEnv = process.env;

      // Clear existing mock and create a new instance
      jest.restoreAllMocks();

      // Mock Linux environment with a path that exists
      Object.defineProperty(process, 'platform', { value: 'linux' });
      const testConfigDir = fs.mkdtempSync(path.join(os.tmpdir(), 'linux-config-test-'));
      process.env = { ...originalEnv, XDG_CONFIG_HOME: testConfigDir };

      const manager = new SettingsManager();
      const appDataPath = manager.getAppDataDirectory();

      expect(appDataPath).toBe(path.join(testConfigDir, 'lifeboard', 'plugins'));

      // Clean up test directory
      if (fs.existsSync(testConfigDir)) {
        fs.rmSync(testConfigDir, { recursive: true, force: true });
      }

      // Restore original values
      Object.defineProperty(process, 'platform', { value: originalPlatform });
      process.env = originalEnv;

      // Re-establish the mock for other tests
      jest.spyOn(SettingsManager.prototype, 'getAppDataDirectory').mockReturnValue(testDir);
    });
  });

  describe('Schema Validation Edge Cases', () => {
    test('should handle complex nested schema', () => {
      const pluginId = 'test-plugin';
      const complexSettings = {
        ui: {
          theme: 'dark',
          sidebar: {
            width: 300,
            collapsed: false
          }
        },
        features: ['feature1', 'feature2']
      };

      const complexSchema = {
        type: 'object',
        properties: {
          ui: {
            type: 'object',
            properties: {
              theme: { type: 'string' },
              sidebar: {
                type: 'object',
                properties: {
                  width: { type: 'number' },
                  collapsed: { type: 'boolean' }
                }
              }
            }
          },
          features: {
            type: 'array',
            items: { type: 'string' }
          }
        }
      };

      const result = settingsManager.save(pluginId, complexSettings, complexSchema);
      expect(result).toBe(true);

      const loadedSettings = settingsManager.load(pluginId, complexSchema);
      expect(loadedSettings).toEqual(complexSettings);
    });

    test('should handle schema with required fields', () => {
      const pluginId = 'test-plugin';
      const incompleteSettings = { theme: 'dark' }; // Missing required field
      const schema = {
        type: 'object',
        properties: {
          theme: { type: 'string' },
          notifications: { type: 'boolean' }
        },
        required: ['theme', 'notifications']
      };

      const result = settingsManager.save(pluginId, incompleteSettings, schema);
      expect(result).toBe(false);
    });

    test('should handle schema with additional properties removal', () => {
      const pluginId = 'test-plugin';
      const settingsWithExtra = {
        theme: 'dark',
        notifications: true,
        extraField: 'should be removed'
      };
      const schema = {
        type: 'object',
        properties: {
          theme: { type: 'string' },
          notifications: { type: 'boolean' }
        },
        additionalProperties: false
      };

      const result = settingsManager.save(pluginId, settingsWithExtra, schema);
      expect(result).toBe(true);

      // The extra field should be removed during validation
      const loadedSettings = settingsManager.load(pluginId, schema);
      expect(loadedSettings).not.toHaveProperty('extraField');
    });
  });
});
