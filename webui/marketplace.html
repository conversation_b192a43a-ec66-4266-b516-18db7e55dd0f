<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Plugin Marketplace - Lifeboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header .subtitle {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .marketplace-content {
            padding: 30px;
        }

        .marketplace-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            flex-wrap: wrap;
            gap: 20px;
        }

        .search-container {
            flex: 1;
            max-width: 500px;
            position: relative;
        }

        .search-input {
            width: 100%;
            padding: 12px 40px 12px 16px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            outline: none;
            transition: border-color 0.3s ease;
        }

        .search-input:focus {
            border-color: #4facfe;
        }

        .search-icon {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: #666;
        }

        .filters {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .filter-btn {
            padding: 8px 16px;
            border: 2px solid #e1e5e9;
            background: white;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .filter-btn:hover {
            border-color: #4facfe;
            background: #f8f9fa;
        }

        .filter-btn.active {
            background: #4facfe;
            color: white;
            border-color: #4facfe;
        }

        .marketplace-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 12px;
            text-align: center;
        }

        .stat-number {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 0.9em;
            opacity: 0.9;
        }

        .plugins-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 25px;
            margin-top: 30px;
        }

        .plugin-card {
            background: white;
            border: 1px solid #e1e5e9;
            border-radius: 12px;
            overflow: hidden;
            transition: all 0.3s ease;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        }

        .plugin-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 20px rgba(0, 0, 0, 0.1);
            border-color: #4facfe;
        }

        .plugin-header {
            padding: 20px;
            border-bottom: 1px solid #f0f0f0;
        }

        .plugin-title {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 10px;
        }

        .plugin-name {
            font-size: 1.3em;
            font-weight: 600;
            color: #333;
        }

        .plugin-version {
            background: #e3f2fd;
            color: #1976d2;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: 500;
        }

        .plugin-author {
            color: #666;
            font-size: 0.9em;
            margin-bottom: 10px;
        }

        .plugin-description {
            color: #555;
            line-height: 1.5;
            margin-bottom: 15px;
        }

        .plugin-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 6px;
            margin-bottom: 15px;
        }

        .tag {
            background: #f0f0f0;
            color: #666;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 0.75em;
        }

        .plugin-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.8em;
            color: #666;
        }

        .verified-badge {
            display: inline-flex;
            align-items: center;
            gap: 4px;
            background: #4caf50;
            color: white;
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 0.7em;
        }

        .plugin-actions {
            padding: 20px;
            background: #f8f9fa;
            display: flex;
            gap: 10px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            flex: 1;
        }

        .btn-primary {
            background: #4facfe;
            color: white;
        }

        .btn-primary:hover {
            background: #3d8bfe;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #545b62;
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn-success:hover {
            background: #218838;
        }

        .btn-warning {
            background: #ffc107;
            color: #212529;
        }

        .btn-warning:hover {
            background: #e0a800;
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .loading {
            text-align: center;
            padding: 50px;
            color: #666;
        }

        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #4facfe;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .no-results {
            text-align: center;
            padding: 50px;
            color: #666;
        }

        .no-results-icon {
            font-size: 4em;
            margin-bottom: 20px;
            opacity: 0.3;
        }

        .installation-progress {
            margin-top: 10px;
        }

        .progress-bar {
            background: #e9ecef;
            border-radius: 10px;
            height: 8px;
            overflow: hidden;
        }

        .progress-fill {
            background: linear-gradient(90deg, #4facfe, #00f2fe);
            height: 100%;
            transition: width 0.3s ease;
            border-radius: 10px;
        }

        .progress-text {
            font-size: 0.8em;
            color: #666;
            margin-top: 5px;
        }

        .toast {
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            border: 1px solid #e1e5e9;
            border-radius: 8px;
            padding: 16px;
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
            z-index: 1000;
            transform: translateX(400px);
            transition: transform 0.3s ease;
        }

        .toast.show {
            transform: translateX(0);
        }

        .toast.success {
            border-left: 4px solid #28a745;
        }

        .toast.error {
            border-left: 4px solid #dc3545;
        }

        .toast.warning {
            border-left: 4px solid #ffc107;
        }

        @media (max-width: 768px) {
            .marketplace-header {
                flex-direction: column;
                align-items: stretch;
            }

            .search-container {
                max-width: none;
            }

            .plugins-grid {
                grid-template-columns: 1fr;
            }

            .marketplace-stats {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏪 Plugin Marketplace</h1>
            <div class="subtitle">Discover and install plugins to enhance your Lifeboard experience</div>
        </div>

        <div class="marketplace-content">
            <div class="marketplace-header">
                <div class="search-container">
                    <input type="text" class="search-input" placeholder="Search plugins..." id="searchInput">
                    <span class="search-icon">🔍</span>
                </div>

                <div class="filters">
                    <button class="filter-btn active" data-filter="all">All</button>
                    <button class="filter-btn" data-filter="verified">Verified</button>
                    <button class="filter-btn" data-filter="category:ai">AI</button>
                    <button class="filter-btn" data-filter="category:productivity">Productivity</button>
                    <button class="filter-btn" data-filter="category:utility">Utility</button>
                </div>
            </div>

            <div class="marketplace-stats" id="marketplaceStats">
                <div class="stat-card">
                    <div class="stat-number" id="totalPlugins">-</div>
                    <div class="stat-label">Total Plugins</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="verifiedPlugins">-</div>
                    <div class="stat-label">Verified</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="installedPlugins">-</div>
                    <div class="stat-label">Installed</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="updatesAvailable">-</div>
                    <div class="stat-label">Updates Available</div>
                </div>
            </div>

            <div id="pluginsContainer">
                <div class="loading">
                    <div class="spinner"></div>
                    <div>Loading marketplace...</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        class MarketplaceUI {
            constructor() {
                this.plugins = [];
                this.filteredPlugins = [];
                this.currentFilter = 'all';
                this.currentSearch = '';
                this.installationStatus = new Map();

                this.init();
            }

            async init() {
                this.setupEventListeners();
                await this.loadMarketplaceData();
                this.startInstallationStatusPolling();
            }

            setupEventListeners() {
                // Search input
                const searchInput = document.getElementById('searchInput');
                searchInput.addEventListener('input', (e) => {
                    this.currentSearch = e.target.value.toLowerCase();
                    this.filterAndDisplayPlugins();
                });

                // Filter buttons
                document.querySelectorAll('.filter-btn').forEach(btn => {
                    btn.addEventListener('click', (e) => {
                        document.querySelectorAll('.filter-btn').forEach(b => b.classList.remove('active'));
                        e.target.classList.add('active');
                        this.currentFilter = e.target.dataset.filter;
                        this.filterAndDisplayPlugins();
                    });
                });

                // Marketplace event listeners
                if (window.lifeboard && window.lifeboard.marketplace) {
                    window.lifeboard.marketplace.onInstallationStarted((data) => {
                        this.handleInstallationStarted(data);
                    });

                    window.lifeboard.marketplace.onInstallationProgress((data) => {
                        this.handleInstallationProgress(data);
                    });

                    window.lifeboard.marketplace.onInstallationCompleted((data) => {
                        this.handleInstallationCompleted(data);
                    });

                    window.lifeboard.marketplace.onInstallationFailed((data) => {
                        this.handleInstallationFailed(data);
                    });

                    window.lifeboard.marketplace.onUninstallationCompleted((data) => {
                        this.handleUninstallationCompleted(data);
                    });
                }
            }

            async loadMarketplaceData() {
                try {
                    // Load marketplace stats and plugins
                    const [stats, plugins] = await Promise.all([
                        this.getMarketplaceStats(),
                        this.searchPlugins('', {})
                    ]);

                    this.displayStats(stats);
                    this.plugins = plugins;
                    this.filterAndDisplayPlugins();
                } catch (error) {
                    console.error('Failed to load marketplace data:', error);
                    this.showError('Failed to load marketplace data');
                }
            }

            async getMarketplaceStats() {
                if (window.lifeboard && window.lifeboard.marketplace) {
                    return await window.lifeboard.marketplace.getStats();
                } else {
                    // Fallback for demo
                    return {
                        totalPlugins: 12,
                        verifiedPlugins: 8,
                        installedPlugins: 3,
                        updatesAvailable: 1
                    };
                }
            }

            async searchPlugins(query, filters) {
                if (window.lifeboard && window.lifeboard.marketplace) {
                    return await window.lifeboard.marketplace.search(query, filters);
                } else {
                    // Demo data for testing
                    return [
                        {
                            id: 'limitless',
                            name: 'Limitless AI Assistant',
                            version: '1.2.0',
                            description: 'Advanced AI assistant for enhanced productivity and automation',
                            author: 'Lifeboard Team',
                            category: 'ai',
                            tags: ['ai', 'assistant', 'productivity'],
                            verified: true,
                            downloadCount: 1250,
                            size: 2048000,
                            lastUpdated: '2025-01-15',
                            isInstalled: true,
                            installedVersion: '1.1.0',
                            hasUpdate: true
                        },
                        {
                            id: 'task-manager',
                            name: 'Advanced Task Manager',
                            version: '2.0.1',
                            description: 'Comprehensive task management with calendar integration',
                            author: 'TaskFlow Inc',
                            category: 'productivity',
                            tags: ['tasks', 'calendar', 'planning'],
                            verified: true,
                            downloadCount: 890,
                            size: 1536000,
                            lastUpdated: '2025-01-10',
                            isInstalled: false
                        },
                        {
                            id: 'weather-widget',
                            name: 'Weather Dashboard',
                            version: '1.0.3',
                            description: 'Beautiful weather widget with forecasts and alerts',
                            author: 'WeatherCorp',
                            category: 'utility',
                            tags: ['weather', 'widget', 'forecast'],
                            verified: false,
                            downloadCount: 456,
                            size: 512000,
                            lastUpdated: '2025-01-05',
                            isInstalled: false
                        }
                    ];
                }
            }

            displayStats(stats) {
                document.getElementById('totalPlugins').textContent = stats.totalPlugins || 0;
                document.getElementById('verifiedPlugins').textContent = stats.verifiedPlugins || 0;
                document.getElementById('installedPlugins').textContent = stats.installedPlugins || 0;
                document.getElementById('updatesAvailable').textContent = stats.updatesAvailable || 0;
            }

            filterAndDisplayPlugins() {
                this.filteredPlugins = this.plugins.filter(plugin => {
                    // Search filter
                    if (this.currentSearch) {
                        const searchMatch = plugin.name.toLowerCase().includes(this.currentSearch) ||
                                          plugin.description.toLowerCase().includes(this.currentSearch) ||
                                          plugin.tags?.some(tag => tag.toLowerCase().includes(this.currentSearch));
                        if (!searchMatch) return false;
                    }

                    // Category/type filter
                    if (this.currentFilter === 'all') {
                        return true;
                    } else if (this.currentFilter === 'verified') {
                        return plugin.verified;
                    } else if (this.currentFilter.startsWith('category:')) {
                        const category = this.currentFilter.split(':')[1];
                        return plugin.category === category;
                    }

                    return true;
                });

                this.displayPlugins();
            }

            displayPlugins() {
                const container = document.getElementById('pluginsContainer');

                if (this.filteredPlugins.length === 0) {
                    container.innerHTML = `
                        <div class="no-results">
                            <div class="no-results-icon">🔍</div>
                            <h3>No plugins found</h3>
                            <p>Try adjusting your search or filters</p>
                        </div>
                    `;
                    return;
                }

                const pluginsGrid = document.createElement('div');
                pluginsGrid.className = 'plugins-grid';

                this.filteredPlugins.forEach(plugin => {
                    const pluginCard = this.createPluginCard(plugin);
                    pluginsGrid.appendChild(pluginCard);
                });

                container.innerHTML = '';
                container.appendChild(pluginsGrid);
            }

            createPluginCard(plugin) {
                const card = document.createElement('div');
                card.className = 'plugin-card';
                card.innerHTML = `
                    <div class="plugin-header">
                        <div class="plugin-title">
                            <div class="plugin-name">${plugin.name}</div>
                            <div class="plugin-version">v${plugin.version}</div>
                        </div>
                        <div class="plugin-author">by ${plugin.author}</div>
                        <div class="plugin-description">${plugin.description}</div>
                        <div class="plugin-tags">
                            ${plugin.tags?.map(tag => `<span class="tag">${tag}</span>`).join('') || ''}
                        </div>
                        <div class="plugin-meta">
                            <div>
                                ${plugin.verified ? '<span class="verified-badge">✓ Verified</span>' : ''}
                                ${plugin.downloadCount ? `${plugin.downloadCount} downloads` : ''}
                            </div>
                            <div>${this.formatSize(plugin.size)}</div>
                        </div>
                    </div>
                    <div class="plugin-actions">
                        ${this.getPluginActions(plugin)}
                    </div>
                `;

                // Add event listeners
                this.addPluginCardEventListeners(card, plugin);
                return card;
            }

            getPluginActions(plugin) {
                const status = this.installationStatus.get(plugin.id);

                if (status) {
                    return `
                        <div class="installation-progress">
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: ${status.progress}%"></div>
                            </div>
                            <div class="progress-text">${status.status}... ${status.progress}%</div>
                        </div>
                    `;
                }

                if (plugin.isInstalled) {
                    if (plugin.hasUpdate) {
                        return `
                            <button class="btn btn-warning" data-action="update" data-plugin-id="${plugin.id}">
                                Update to v${plugin.version}
                            </button>
                            <button class="btn btn-secondary" data-action="uninstall" data-plugin-id="${plugin.id}">
                                Uninstall
                            </button>
                        `;
                    } else {
                        return `
                            <button class="btn btn-success" disabled>
                                ✓ Installed
                            </button>
                            <button class="btn btn-secondary" data-action="uninstall" data-plugin-id="${plugin.id}">
                                Uninstall
                            </button>
                        `;
                    }
                } else {
                    return `
                        <button class="btn btn-primary" data-action="install" data-plugin-id="${plugin.id}">
                            Install
                        </button>
                        <button class="btn btn-secondary" data-action="details" data-plugin-id="${plugin.id}">
                            Details
                        </button>
                    `;
                }
            }

            addPluginCardEventListeners(card, plugin) {
                card.querySelectorAll('button[data-action]').forEach(btn => {
                    btn.addEventListener('click', async (e) => {
                        const action = e.target.dataset.action;
                        const pluginId = e.target.dataset.pluginId;

                        try {
                            switch (action) {
                                case 'install':
                                    await this.installPlugin(pluginId);
                                    break;
                                case 'update':
                                    await this.installPlugin(pluginId, { force: true });
                                    break;
                                case 'uninstall':
                                    await this.uninstallPlugin(pluginId);
                                    break;
                                case 'details':
                                    await this.showPluginDetails(pluginId);
                                    break;
                            }
                        } catch (error) {
                            console.error(`Failed to ${action} plugin:`, error);
                            this.showError(`Failed to ${action} plugin: ${error.message}`);
                        }
                    });
                });
            }

            async installPlugin(pluginId, options = {}) {
                if (window.lifeboard && window.lifeboard.marketplace) {
                    const result = await window.lifeboard.marketplace.install(pluginId, options);
                    if (result.success) {
                        this.showSuccess(`Plugin ${pluginId} installed successfully`);
                        await this.loadMarketplaceData(); // Refresh data
                    } else {
                        throw new Error(result.error);
                    }
                } else {
                    // Demo mode
                    this.showSuccess(`Demo: Plugin ${pluginId} would be installed`);
                }
            }

            async uninstallPlugin(pluginId) {
                if (window.lifeboard && window.lifeboard.marketplace) {
                    const result = await window.lifeboard.marketplace.uninstall(pluginId, {});
                    if (result.success) {
                        this.showSuccess(`Plugin ${pluginId} uninstalled successfully`);
                        await this.loadMarketplaceData(); // Refresh data
                    } else {
                        throw new Error(result.error);
                    }
                } else {
                    // Demo mode
                    this.showSuccess(`Demo: Plugin ${pluginId} would be uninstalled`);
                }
            }

            async showPluginDetails(pluginId) {
                if (window.lifeboard && window.lifeboard.marketplace) {
                    const details = await window.lifeboard.marketplace.getPluginDetails(pluginId);
                    // TODO: Show details modal
                    console.log('Plugin details:', details);
                } else {
                    console.log('Demo: Would show details for plugin:', pluginId);
                }
            }

            handleInstallationStarted(data) {
                this.installationStatus.set(data.pluginId, {
                    status: 'starting',
                    progress: 0
                });
                this.displayPlugins(); // Refresh display
            }

            handleInstallationProgress(data) {
                this.installationStatus.set(data.pluginId, {
                    status: data.status,
                    progress: data.progress
                });
                this.displayPlugins(); // Refresh display
            }

            handleInstallationCompleted(data) {
                this.installationStatus.delete(data.pluginId);
                this.showSuccess(`Plugin ${data.pluginId} installed successfully`);
                this.loadMarketplaceData(); // Refresh data
            }

            handleInstallationFailed(data) {
                this.installationStatus.delete(data.pluginId);
                this.showError(`Installation failed: ${data.error}`);
                this.displayPlugins(); // Refresh display
            }

            handleUninstallationCompleted(data) {
                this.showSuccess(`Plugin ${data.pluginId} uninstalled successfully`);
                this.loadMarketplaceData(); // Refresh data
            }

            startInstallationStatusPolling() {
                // Poll for installation status updates
                setInterval(async () => {
                    for (const [pluginId] of this.installationStatus) {
                        if (window.lifeboard && window.lifeboard.marketplace) {
                            try {
                                const status = await window.lifeboard.marketplace.getInstallationStatus(pluginId);
                                if (status) {
                                    this.installationStatus.set(pluginId, status);
                                } else {
                                    this.installationStatus.delete(pluginId);
                                }
                            } catch (error) {
                                console.error('Failed to get installation status:', error);
                            }
                        }
                    }
                }, 1000);
            }

            formatSize(bytes) {
                if (!bytes) return 'Unknown size';
                const sizes = ['B', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(1024));
                return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
            }

            showToast(message, type = 'info') {
                const toast = document.createElement('div');
                toast.className = `toast ${type}`;
                toast.innerHTML = `
                    <div style="font-weight: 500; margin-bottom: 4px;">${type.toUpperCase()}</div>
                    <div>${message}</div>
                `;

                document.body.appendChild(toast);

                setTimeout(() => toast.classList.add('show'), 100);
                setTimeout(() => {
                    toast.classList.remove('show');
                    setTimeout(() => document.body.removeChild(toast), 300);
                }, 3000);
            }

            showSuccess(message) {
                this.showToast(message, 'success');
            }

            showError(message) {
                this.showToast(message, 'error');
            }

            showWarning(message) {
                this.showToast(message, 'warning');
            }
        }

        // Initialize marketplace UI when page loads
        document.addEventListener('DOMContentLoaded', () => {
            new MarketplaceUI();
        });
    </script>
</body>
</html>
