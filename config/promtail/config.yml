server:
  http_listen_port: 9080
  grpc_listen_port: 0

positions:
  filename: /tmp/positions.yaml

clients:
  - url: http://loki:3100/loki/api/v1/push

scrape_configs:
  # Scrape logs from Lifeboard services
  - job_name: lifeboard-logs
    static_configs:
      - targets:
          - localhost
        labels:
          job: lifeboard
          __path__: /var/log/lifeboard/**/*.log

  # Scrape PostgreSQL logs
  - job_name: postgres-logs
    static_configs:
      - targets:
          - localhost
        labels:
          job: postgres
          service: database
          __path__: /var/log/lifeboard/postgres/*.log
    pipeline_stages:
      - regex:
          expression: '^(?P<timestamp>\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3}) (?P<level>[A-Z]+): (?P<message>.*)'
      - timestamp:
          source: timestamp
          format: '2006-01-02 15:04:05.000'
      - labels:
          level:

  # Scrape Auth service logs
  - job_name: auth-logs
    static_configs:
      - targets:
          - localhost
        labels:
          job: gotrue
          service: auth
          __path__: /var/log/lifeboard/auth/*.log
    pipeline_stages:
      - json:
          expressions:
            level: level
            message: msg
            timestamp: time
      - timestamp:
          source: timestamp
          format: RFC3339
      - labels:
          level:

  # Scrape Realtime service logs
  - job_name: realtime-logs
    static_configs:
      - targets:
          - localhost
        labels:
          job: realtime
          service: websocket
          __path__: /var/log/lifeboard/realtime/*.log
    pipeline_stages:
      - json:
          expressions:
            level: level
            message: msg
            timestamp: time
      - timestamp:
          source: timestamp
          format: RFC3339
      - labels:
          level:

  # Scrape PostgREST API logs
  - job_name: rest-logs
    static_configs:
      - targets:
          - localhost
        labels:
          job: postgrest
          service: api
          __path__: /var/log/lifeboard/rest/*.log
    pipeline_stages:
      - regex:
          expression: '^(?P<timestamp>\d{2}\/[A-Za-z]{3}\/\d{4}:\d{2}:\d{2}:\d{2}) \[(?P<level>[A-Z]+)\] (?P<message>.*)'
      - timestamp:
          source: timestamp
          format: '02/Jan/2006:15:04:05'
      - labels:
          level:

  # Scrape Storage API logs
  - job_name: storage-logs
    static_configs:
      - targets:
          - localhost
        labels:
          job: storage
          service: api
          __path__: /var/log/lifeboard/storage/*.log
    pipeline_stages:
      - json:
          expressions:
            level: level
            message: msg
            timestamp: time
      - timestamp:
          source: timestamp
          format: RFC3339
      - labels:
          level:

  # Scrape Studio UI logs
  - job_name: studio-logs
    static_configs:
      - targets:
          - localhost
        labels:
          job: studio
          service: ui
          __path__: /var/log/lifeboard/studio/*.log
    pipeline_stages:
      - json:
          expressions:
            level: level
            message: msg
            timestamp: time
      - timestamp:
          source: timestamp
          format: RFC3339
      - labels:
          level:
