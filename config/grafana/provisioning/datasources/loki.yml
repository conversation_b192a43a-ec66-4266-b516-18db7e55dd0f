apiVersion: 1

datasources:
  - name: Loki
    type: loki
    access: proxy
    url: http://loki:3100
    isDefault: true
    editable: true
    jsonData:
      maxLines: 1000
      derivedFields:
        - datasourceUid: loki
          matcherRegex: "traceID=(\\w+)"
          name: TraceID
          url: "$${__value.raw}"
        - datasourceUid: loki
          matcherRegex: "user_id=(\\w+)"
          name: UserID
          url: "$${__value.raw}"
