# Nginx Configuration for Lifeboard Web UI
# Phase 8: Minimal Web UI Infrastructure
# Purpose: Serve static files with proper logging and security

user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log error;
pid /var/run/nginx.pid;

events {
    worker_connections 1024;
    use epoll;
    multi_accept on;
}

http {
    # Basic settings
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # Logging format - JSON structured for analysis
    log_format json_combined escape=json '
    {
        "timestamp": "$time_iso8601",
        "level": "info",
        "component": "webui",
        "message": "HTTP Request",
        "remote_addr": "$remote_addr",
        "remote_user": "$remote_user",
        "request": "$request",
        "status": $status,
        "body_bytes_sent": $body_bytes_sent,
        "http_referer": "$http_referer",
        "http_user_agent": "$http_user_agent",
        "request_time": $request_time,
        "upstream_response_time": "$upstream_response_time",
        "session_id": "$request_id"
    }';

    # Access log with JSON format
    access_log /var/log/nginx/access.log json_combined;

    # Performance settings
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;

    # Security headers
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Content-Security-Policy "default-src 'self'; style-src 'self' 'unsafe-inline'; script-src 'self'; img-src 'self' data:" always;

    # Hide nginx version
    server_tokens off;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/xml+rss
        application/json;

    # Rate limiting
    limit_req_zone $binary_remote_addr zone=webui:10m rate=10r/s;

    server {
        listen 80;
        server_name localhost;
        root /usr/share/nginx/html;
        index index.html;

        # Apply rate limiting
        limit_req zone=webui burst=20 nodelay;

        # Security settings
        location / {
            try_files $uri $uri/ /index.html;

            # Cache control for static files
            location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg)$ {
                expires 1y;
                add_header Cache-Control "public, immutable";
            }

            # HTML files - no cache
            location ~* \.html$ {
                expires -1;
                add_header Cache-Control "no-cache, no-store, must-revalidate";
            }
        }

        # Health check endpoint
        location /health {
            access_log off;
            return 200 '{"status":"healthy","component":"webui","timestamp":"$time_iso8601"}';
            add_header Content-Type application/json;
        }

        # Favicon handling
        location = /favicon.ico {
            log_not_found off;
            access_log off;
            return 204;
        }

        # Deny access to hidden files
        location ~ /\. {
            deny all;
            access_log off;
            log_not_found off;
        }

        # Error pages
        error_page 404 /index.html;
        error_page 500 502 503 504 /index.html;
    }
}
