---
type: "always_apply"
---

Always add verbose debug logging to every service, class and method.  Seek opportunity to add logging and add it.

logs will be located in /logs at the root of the project.  logs will not be located in any other location

logs will be datetime stamped in naming convention

- Key Instrumentation Points  
  - User actions (post creation, edits)  
  - AI decisions (embedding generation, tag assignment)  
  - Workflow triggers (n8n nodes, function invocations)
- BACKEND API CALLS AND RESPONSES
- MIDDLE LAYER API CALLS AND RESPONSES
- FRONT END  API CALLS AND RESPONSES
