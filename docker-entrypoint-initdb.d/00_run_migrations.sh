#!/usr/bin/env bash
# 00_run_migrations.sh
# Sequentially applies SQL migrations located in the migrations directory.
# Files are executed in lexicographic order (00_, 01_, ...), ensuring that
# schema definitions precede seed data and role grants.
# Any error will halt the initialization thanks to ON_ERROR_STOP=1.

set -euo pipefail

for f in /docker-entrypoint-initdb.d/migrations/*.sql; do
  echo "Running migration: $f"
  psql -v ON_ERROR_STOP=1 -f "$f"
  echo "Finished migration: $f"
  echo "---"
done
