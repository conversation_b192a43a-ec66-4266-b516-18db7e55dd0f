-- =============================================================================
-- Supabase Role Bootstrap Script
-- =============================================================================
-- This script creates the fundamental roles required for Supabase operation:
-- - postgres: Superuser role for database administration
-- - authenticator: Role that <PERSON><PERSON><PERSON> uses to connect and switch roles
-- - anon: Anonymous/unauthenticated user role
-- - service_role: Service role for backend operations with elevated privileges
--
-- Security considerations:
-- - Passwords are pulled from environment variables for security
-- - authenticator role has NOINHERIT to prevent privilege escalation
-- - Proper role switching permissions are granted
-- =============================================================================

-- Create postgres role (superuser for database administration)
-- This role is used for database administration tasks
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'postgres') THEN
        -- Create postgres role with LOGIN capability and superuser privileges
        EXECUTE format('CREATE ROLE postgres LOGIN SUPERUSER CREATEDB CREATEROLE REPLICATION BYPASSRLS PASSWORD %L',
                      coalesce(current_setting('custom.db_password_postgres', true), 'postgres_default_password'));
        RAISE NOTICE 'Created postgres role with LOGIN privileges';
    ELSE
        RAISE NOTICE 'postgres role already exists, skipping creation';
    END IF;
END
$$;

-- Create authenticator role (used by GoTrue to authenticate and switch roles)
-- This role must have LOGIN but should not inherit privileges by default
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'authenticator') THEN
        -- Create authenticator role with LOGIN but NOINHERIT for security
        EXECUTE format('CREATE ROLE authenticator LOGIN NOINHERIT PASSWORD %L',
                      coalesce(current_setting('custom.db_password_authenticator', true), 'authenticator_default_password'));
        RAISE NOTICE 'Created authenticator role with LOGIN and NOINHERIT privileges';
    ELSE
        RAISE NOTICE 'authenticator role already exists, skipping creation';
    END IF;
END
$$;

-- Create anon role (anonymous/unauthenticated user role)
-- This role represents unauthenticated users and should have limited privileges
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'anon') THEN
        -- Create anon role without LOGIN (it's switched to via authenticator)
        EXECUTE format('CREATE ROLE anon NOLOGIN PASSWORD %L',
                      coalesce(current_setting('custom.db_password_anon', true), 'anon_default_password'));
        RAISE NOTICE 'Created anon role with NOLOGIN privileges';
    ELSE
        RAISE NOTICE 'anon role already exists, skipping creation';
    END IF;
END
$$;

-- Create service_role (service role for backend operations)
-- This role is used for backend services that need elevated privileges
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'service_role') THEN
        -- Create service_role with NOLOGIN (accessed via authenticator role switching)
        EXECUTE format('CREATE ROLE service_role NOLOGIN BYPASSRLS PASSWORD %L',
                      coalesce(current_setting('custom.db_password_service_role', true), 'service_role_default_password'));
        RAISE NOTICE 'Created service_role with NOLOGIN and BYPASSRLS privileges';
    ELSE
        RAISE NOTICE 'service_role role already exists, skipping creation';
    END IF;
END
$$;

-- Grant role switching permissions to authenticator
-- This allows the authenticator role to switch into anon and service_role
DO $$
BEGIN
    -- Grant anon role to authenticator for role switching
    IF EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'anon') AND
       EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'authenticator') THEN
        GRANT anon TO authenticator;
        RAISE NOTICE 'Granted anon role to authenticator for role switching';
    END IF;

    -- Grant service_role to authenticator for role switching
    IF EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'service_role') AND
       EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'authenticator') THEN
        GRANT service_role TO authenticator;
        RAISE NOTICE 'Granted service_role to authenticator for role switching';
    END IF;
END
$$;

-- Create authenticated role (for authenticated users)
-- This role represents authenticated users with standard privileges
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'authenticated') THEN
        -- Create authenticated role without LOGIN (accessed via role switching)
        EXECUTE format('CREATE ROLE authenticated NOLOGIN PASSWORD %L',
                      coalesce(current_setting('custom.db_password_authenticated', true), 'authenticated_default_password'));
        RAISE NOTICE 'Created authenticated role with NOLOGIN privileges';
    ELSE
        RAISE NOTICE 'authenticated role already exists, skipping creation';
    END IF;

    -- Grant authenticated role to authenticator for role switching
    IF EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'authenticated') AND
       EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'authenticator') THEN
        GRANT authenticated TO authenticator;
        RAISE NOTICE 'Granted authenticated role to authenticator for role switching';
    END IF;
END
$$;

-- =============================================================================
-- Role Summary:
-- - postgres: Superuser for database administration (LOGIN enabled)
-- - authenticator: Authentication role used by GoTrue (LOGIN enabled, NOINHERIT)
-- - anon: Anonymous user role (NOLOGIN, accessed via role switching)
-- - authenticated: Authenticated user role (NOLOGIN, accessed via role switching)
-- - service_role: Service role for backend operations (NOLOGIN, BYPASSRLS)
--
-- Role Switching:
-- - authenticator can switch to: anon, authenticated, service_role
-- - This enables GoTrue to authenticate as authenticator and then switch to
--   appropriate roles based on JWT claims
-- =============================================================================
