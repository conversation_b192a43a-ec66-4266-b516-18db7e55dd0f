-- =============================================================================
-- Supabase Auth Schema Bootstrap Script
-- =============================================================================
-- This script creates the complete auth schema infrastructure for Supabase
-- including all tables, functions, types, triggers, and RLS policies that
-- mirror Supabase's open-source auth implementation.
--
-- The script is idempotent and can be run multiple times safely.
-- All objects are created with proper ownership and permissions.
-- =============================================================================

-- Create the auth schema
CREATE SCHEMA IF NOT EXISTS auth;

-- Set search path to include auth schema
SET search_path TO auth, public, pg_catalog;

-- =============================================================================
-- EXTENSIONS
-- =============================================================================

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA extensions;
CREATE EXTENSION IF NOT EXISTS "pgcrypto" WITH SCHEMA extensions;

-- =============================================================================
-- ENUMS AND COMPOSITE TYPES
-- =============================================================================

-- Authentication factor type enumeration
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'factor_type') THEN
        CREATE TYPE auth.factor_type AS ENUM ('totp', 'webauthn');
    END IF;
END $$;

-- Authentication factor status enumeration
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'factor_status') THEN
        CREATE TYPE auth.factor_status AS ENUM ('unverified', 'verified');
    END IF;
END $$;

-- Authentication level enumeration
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'aal_level') THEN
        CREATE TYPE auth.aal_level AS ENUM ('aal1', 'aal2', 'aal3');
    END IF;
END $$;

-- Code challenge method enumeration
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'code_challenge_method') THEN
        CREATE TYPE auth.code_challenge_method AS ENUM ('s256', 'plain');
    END IF;
END $$;

-- =============================================================================
-- CORE TABLES
-- =============================================================================

-- Users table - core user information
CREATE TABLE IF NOT EXISTS auth.users (
    instance_id uuid,
    id uuid NOT NULL DEFAULT gen_random_uuid(),
    aud character varying(255),
    role character varying(255),
    email character varying(255),
    encrypted_password character varying(255),
    email_confirmed_at timestamp with time zone,
    invited_at timestamp with time zone,
    confirmation_token character varying(255),
    confirmation_sent_at timestamp with time zone,
    recovery_token character varying(255),
    recovery_sent_at timestamp with time zone,
    email_change_token_new character varying(255),
    email_change character varying(255),
    email_change_sent_at timestamp with time zone,
    last_sign_in_at timestamp with time zone,
    raw_app_meta_data jsonb,
    raw_user_meta_data jsonb,
    is_super_admin boolean,
    created_at timestamp with time zone,
    updated_at timestamp with time zone,
    phone text UNIQUE,
    phone_confirmed_at timestamp with time zone,
    phone_change text DEFAULT ''::text,
    phone_change_token character varying(255) DEFAULT ''::character varying,
    phone_change_sent_at timestamp with time zone,
    confirmed_at timestamp with time zone GENERATED ALWAYS AS (LEAST(email_confirmed_at, phone_confirmed_at)) STORED,
    email_change_token_current character varying(255) DEFAULT ''::character varying,
    email_change_confirm_status smallint DEFAULT 0,
    banned_until timestamp with time zone,
    reauthentication_token character varying(255) DEFAULT ''::character varying,
    reauthentication_sent_at timestamp with time zone,
    is_sso_user boolean NOT NULL DEFAULT false,
    deleted_at timestamp with time zone,
    is_anonymous boolean NOT NULL DEFAULT false,

    CONSTRAINT users_pkey PRIMARY KEY (id),
    CONSTRAINT users_phone_key UNIQUE (phone)
);

-- Refresh tokens table - manages JWT refresh tokens
CREATE TABLE IF NOT EXISTS auth.refresh_tokens (
    instance_id uuid,
    id bigserial,
    token character varying(255),
    user_id character varying(255),
    revoked boolean,
    created_at timestamp with time zone,
    updated_at timestamp with time zone,
    parent character varying(255),
    session_id uuid,

    CONSTRAINT refresh_tokens_pkey PRIMARY KEY (id),
    CONSTRAINT refresh_tokens_token_unique UNIQUE (token)
);

-- Audit log entries table
CREATE TABLE IF NOT EXISTS auth.audit_log_entries (
    instance_id uuid,
    id uuid NOT NULL DEFAULT gen_random_uuid(),
    payload json,
    created_at timestamp with time zone,
    ip_address character varying(64) NOT NULL DEFAULT ''::character varying,

    CONSTRAINT audit_log_entries_pkey PRIMARY KEY (id)
);

-- Identity linking table for external providers
CREATE TABLE IF NOT EXISTS auth.identities (
    provider_id text NOT NULL,
    user_id uuid NOT NULL,
    identity_data jsonb NOT NULL,
    provider text NOT NULL,
    last_sign_in_at timestamp with time zone,
    created_at timestamp with time zone,
    updated_at timestamp with time zone,
    email text GENERATED ALWAYS AS (lower((identity_data ->> 'email'::text))) STORED,
    id uuid NOT NULL DEFAULT gen_random_uuid(),

    CONSTRAINT identities_pkey PRIMARY KEY (id),
    CONSTRAINT identities_provider_id_provider_unique UNIQUE (provider_id, provider)
);

-- Multi-factor authentication factors
CREATE TABLE IF NOT EXISTS auth.mfa_factors (
    id uuid NOT NULL DEFAULT gen_random_uuid(),
    user_id uuid NOT NULL,
    friendly_name text,
    factor_type auth.factor_type NOT NULL,
    status auth.factor_status NOT NULL,
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL,
    secret text,
    phone text,
    last_challenged_at timestamp with time zone,

    CONSTRAINT mfa_factors_pkey PRIMARY KEY (id)
);

-- MFA challenges table
CREATE TABLE IF NOT EXISTS auth.mfa_challenges (
    id uuid NOT NULL DEFAULT gen_random_uuid(),
    factor_id uuid NOT NULL,
    created_at timestamp with time zone NOT NULL,
    verified_at timestamp with time zone,
    ip_address inet NOT NULL,
    otp_code text,
    web_authn_session_data jsonb,

    CONSTRAINT mfa_challenges_pkey PRIMARY KEY (id)
);

-- MFA audit log entries
CREATE TABLE IF NOT EXISTS auth.mfa_amr_claims (
    session_id uuid NOT NULL,
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL,
    authentication_method text NOT NULL,
    id uuid NOT NULL DEFAULT gen_random_uuid(),

    CONSTRAINT amr_id_pk PRIMARY KEY (id),
    CONSTRAINT mfa_amr_claims_session_id_authentication_method_pkey UNIQUE (session_id, authentication_method)
);

-- SSO providers table
CREATE TABLE IF NOT EXISTS auth.sso_providers (
    id uuid NOT NULL DEFAULT gen_random_uuid(),
    resource_id text,
    created_at timestamp with time zone,
    updated_at timestamp with time zone,

    CONSTRAINT sso_providers_pkey PRIMARY KEY (id)
);

-- SSO domains table
CREATE TABLE IF NOT EXISTS auth.sso_domains (
    id uuid NOT NULL DEFAULT gen_random_uuid(),
    sso_provider_id uuid NOT NULL,
    domain text NOT NULL,
    created_at timestamp with time zone,
    updated_at timestamp with time zone,

    CONSTRAINT sso_domains_pkey PRIMARY KEY (id)
);

-- SAML relay states
CREATE TABLE IF NOT EXISTS auth.saml_relay_states (
    id uuid NOT NULL DEFAULT gen_random_uuid(),
    sso_provider_id uuid NOT NULL,
    request_id text NOT NULL,
    for_email text,
    redirect_to text,
    created_at timestamp with time zone,
    updated_at timestamp with time zone,
    flow_state_id uuid,

    CONSTRAINT saml_relay_states_pkey PRIMARY KEY (id)
);

-- Flow state management
CREATE TABLE IF NOT EXISTS auth.flow_state (
    id uuid NOT NULL DEFAULT gen_random_uuid(),
    user_id uuid,
    auth_code text NOT NULL,
    code_challenge_method auth.code_challenge_method NOT NULL,
    code_challenge text NOT NULL,
    provider_type text NOT NULL,
    provider_access_token text,
    provider_refresh_token text,
    created_at timestamp with time zone,
    updated_at timestamp with time zone,
    authentication_method text NOT NULL,
    auth_code_issued_at timestamp with time zone,

    CONSTRAINT flow_state_pkey PRIMARY KEY (id)
);

-- User sessions table
CREATE TABLE IF NOT EXISTS auth.sessions (
    id uuid NOT NULL DEFAULT gen_random_uuid(),
    user_id uuid NOT NULL,
    created_at timestamp with time zone,
    updated_at timestamp with time zone,
    factor_id uuid,
    aal auth.aal_level,
    not_after timestamp with time zone,
    refreshed_at timestamp without time zone,
    user_agent text,
    ip inet,
    tag text,

    CONSTRAINT sessions_pkey PRIMARY KEY (id)
);

-- One-time passwords
CREATE TABLE IF NOT EXISTS auth.one_time_tokens (
    id uuid NOT NULL DEFAULT gen_random_uuid(),
    user_id uuid NOT NULL,
    token_type text NOT NULL,
    token_hash text NOT NULL,
    relates_to text NOT NULL,
    created_at timestamp without time zone NOT NULL DEFAULT now(),
    updated_at timestamp without time zone NOT NULL DEFAULT now(),

    CONSTRAINT one_time_tokens_pkey PRIMARY KEY (id)
);

-- =============================================================================
-- INDEXES FOR PERFORMANCE
-- =============================================================================

-- Users table indexes
CREATE INDEX IF NOT EXISTS users_instance_id_idx ON auth.users USING btree (instance_id);
CREATE INDEX IF NOT EXISTS users_instance_id_email_idx ON auth.users USING btree (instance_id, lower((email)::text));
CREATE UNIQUE INDEX IF NOT EXISTS users_email_partial_key ON auth.users USING btree (email) WHERE (deleted_at IS NULL);
CREATE INDEX IF NOT EXISTS users_is_anonymous_idx ON auth.users USING btree (is_anonymous);

-- Refresh tokens indexes
CREATE INDEX IF NOT EXISTS refresh_tokens_instance_id_idx ON auth.refresh_tokens USING btree (instance_id);
CREATE INDEX IF NOT EXISTS refresh_tokens_instance_id_user_id_idx ON auth.refresh_tokens USING btree (instance_id, user_id);
CREATE INDEX IF NOT EXISTS refresh_tokens_parent_idx ON auth.refresh_tokens USING btree (parent);
CREATE INDEX IF NOT EXISTS refresh_tokens_session_id_revoked_idx ON auth.refresh_tokens USING btree (session_id, revoked);

-- Audit log indexes
CREATE INDEX IF NOT EXISTS audit_logs_instance_id_idx ON auth.audit_log_entries USING btree (instance_id);

-- Identities indexes
CREATE INDEX IF NOT EXISTS identities_user_id_idx ON auth.identities USING btree (user_id);
CREATE INDEX IF NOT EXISTS identities_email_idx ON auth.identities USING btree (email text_pattern_ops) WHERE (email IS NOT NULL);

-- MFA factors indexes
CREATE INDEX IF NOT EXISTS factor_id_created_at_idx ON auth.mfa_factors USING btree (user_id, created_at);
CREATE INDEX IF NOT EXISTS mfa_factors_user_friendly_name_unique ON auth.mfa_factors USING btree (friendly_name, user_id) WHERE (TRIM(BOTH FROM friendly_name) <> ''::text);

-- MFA challenges indexes
CREATE INDEX IF NOT EXISTS mfa_challenge_created_at_idx ON auth.mfa_challenges USING btree (created_at DESC);

-- Sessions indexes
CREATE INDEX IF NOT EXISTS user_id_created_at_idx ON auth.sessions USING btree (user_id, created_at);
CREATE INDEX IF NOT EXISTS sessions_not_after_idx ON auth.sessions USING btree (not_after DESC);

-- SSO indexes
CREATE INDEX IF NOT EXISTS sso_domains_domain_idx ON auth.sso_domains USING btree (lower(domain));
CREATE INDEX IF NOT EXISTS sso_domains_sso_provider_id_idx ON auth.sso_domains USING btree (sso_provider_id);

-- SAML relay states indexes
CREATE INDEX IF NOT EXISTS saml_relay_states_sso_provider_id_idx ON auth.saml_relay_states USING btree (sso_provider_id);
CREATE INDEX IF NOT EXISTS saml_relay_states_for_email_idx ON auth.saml_relay_states USING btree (for_email);

-- Flow state indexes
CREATE INDEX IF NOT EXISTS flow_state_created_at_idx ON auth.flow_state USING btree (created_at DESC);
CREATE INDEX IF NOT EXISTS idx_auth_code ON auth.flow_state USING btree (auth_code);
CREATE INDEX IF NOT EXISTS idx_user_id_auth_method ON auth.flow_state USING btree (user_id, authentication_method);

-- One-time tokens indexes
CREATE UNIQUE INDEX IF NOT EXISTS one_time_tokens_user_id_token_type_key ON auth.one_time_tokens USING btree (user_id, token_type);
CREATE INDEX IF NOT EXISTS one_time_tokens_relates_to_hash_idx ON auth.one_time_tokens USING hash (relates_to);
CREATE INDEX IF NOT EXISTS one_time_tokens_token_hash_hash_idx ON auth.one_time_tokens USING hash (token_hash);

-- =============================================================================
-- FOREIGN KEY CONSTRAINTS
-- =============================================================================

-- Refresh tokens to users
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'refresh_tokens_user_id_fkey'
        AND table_schema = 'auth'
        AND table_name = 'refresh_tokens'
    ) THEN
        ALTER TABLE auth.refresh_tokens
        ADD CONSTRAINT refresh_tokens_user_id_fkey
        FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;
    END IF;
END $$;

-- Identities to users
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'identities_user_id_fkey'
        AND table_schema = 'auth'
        AND table_name = 'identities'
    ) THEN
        ALTER TABLE auth.identities
        ADD CONSTRAINT identities_user_id_fkey
        FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;
    END IF;
END $$;

-- MFA factors to users
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'mfa_factors_user_id_fkey'
        AND table_schema = 'auth'
        AND table_name = 'mfa_factors'
    ) THEN
        ALTER TABLE auth.mfa_factors
        ADD CONSTRAINT mfa_factors_user_id_fkey
        FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;
    END IF;
END $$;

-- MFA challenges to factors
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'mfa_challenges_auth_factor_id_fkey'
        AND table_schema = 'auth'
        AND table_name = 'mfa_challenges'
    ) THEN
        ALTER TABLE auth.mfa_challenges
        ADD CONSTRAINT mfa_challenges_auth_factor_id_fkey
        FOREIGN KEY (factor_id) REFERENCES auth.mfa_factors(id) ON DELETE CASCADE;
    END IF;
END $$;

-- Sessions to users
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'sessions_user_id_fkey'
        AND table_schema = 'auth'
        AND table_name = 'sessions'
    ) THEN
        ALTER TABLE auth.sessions
        ADD CONSTRAINT sessions_user_id_fkey
        FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;
    END IF;
END $$;

-- Sessions to refresh tokens
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'refresh_tokens_session_id_fkey'
        AND table_schema = 'auth'
        AND table_name = 'refresh_tokens'
    ) THEN
        ALTER TABLE auth.refresh_tokens
        ADD CONSTRAINT refresh_tokens_session_id_fkey
        FOREIGN KEY (session_id) REFERENCES auth.sessions(id) ON DELETE CASCADE;
    END IF;
END $$;

-- One-time tokens to users
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'one_time_tokens_user_id_fkey'
        AND table_schema = 'auth'
        AND table_name = 'one_time_tokens'
    ) THEN
        ALTER TABLE auth.one_time_tokens
        ADD CONSTRAINT one_time_tokens_user_id_fkey
        FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;
    END IF;
END $$;

-- SSO domains to providers
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'sso_domains_sso_provider_id_fkey'
        AND table_schema = 'auth'
        AND table_name = 'sso_domains'
    ) THEN
        ALTER TABLE auth.sso_domains
        ADD CONSTRAINT sso_domains_sso_provider_id_fkey
        FOREIGN KEY (sso_provider_id) REFERENCES auth.sso_providers(id) ON DELETE CASCADE;
    END IF;
END $$;

-- SAML relay states to SSO providers
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'saml_relay_states_sso_provider_id_fkey'
        AND table_schema = 'auth'
        AND table_name = 'saml_relay_states'
    ) THEN
        ALTER TABLE auth.saml_relay_states
        ADD CONSTRAINT saml_relay_states_sso_provider_id_fkey
        FOREIGN KEY (sso_provider_id) REFERENCES auth.sso_providers(id) ON DELETE CASCADE;
    END IF;
END $$;

-- Flow state to users
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'flow_state_user_id_fkey'
        AND table_schema = 'auth'
        AND table_name = 'flow_state'
    ) THEN
        ALTER TABLE auth.flow_state
        ADD CONSTRAINT flow_state_user_id_fkey
        FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;
    END IF;
END $$;

-- SAML relay states to flow state
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'saml_relay_states_flow_state_id_fkey'
        AND table_schema = 'auth'
        AND table_name = 'saml_relay_states'
    ) THEN
        ALTER TABLE auth.saml_relay_states
        ADD CONSTRAINT saml_relay_states_flow_state_id_fkey
        FOREIGN KEY (flow_state_id) REFERENCES auth.flow_state(id) ON DELETE CASCADE;
    END IF;
END $$;

-- MFA AMR claims to sessions
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'mfa_amr_claims_session_id_fkey'
        AND table_schema = 'auth'
        AND table_name = 'mfa_amr_claims'
    ) THEN
        ALTER TABLE auth.mfa_amr_claims
        ADD CONSTRAINT mfa_amr_claims_session_id_fkey
        FOREIGN KEY (session_id) REFERENCES auth.sessions(id) ON DELETE CASCADE;
    END IF;
END $$;

-- =============================================================================
-- SECURITY DEFINER FUNCTIONS
-- =============================================================================

-- Function to get current user ID from JWT
CREATE OR REPLACE FUNCTION auth.uid()
RETURNS uuid
LANGUAGE sql
SECURITY DEFINER
SET search_path = auth, pg_temp
AS $$
    SELECT nullif(current_setting('request.jwt.claims', true)::json->>'sub', '')::uuid;
$$;

-- Function to get current JWT
CREATE OR REPLACE FUNCTION auth.jwt()
RETURNS jsonb
LANGUAGE sql
SECURITY DEFINER
SET search_path = auth, pg_temp
AS $$
    SELECT coalesce(
        nullif(current_setting('request.jwt.claims', true), ''),
        nullif(current_setting('request.jwt', true), '')
    )::jsonb;
$$;

-- Function to get current user's role
CREATE OR REPLACE FUNCTION auth.role()
RETURNS text
LANGUAGE sql
SECURITY DEFINER
SET search_path = auth, pg_temp
AS $$
    SELECT nullif(current_setting('request.jwt.claims', true)::json->>'role', '')::text;
$$;

-- Function to get current user's email
CREATE OR REPLACE FUNCTION auth.email()
RETURNS text
LANGUAGE sql
SECURITY DEFINER
SET search_path = auth, pg_temp
AS $$
    SELECT nullif(current_setting('request.jwt.claims', true)::json->>'email', '')::text;
$$;

-- =============================================================================
-- TRIGGERS FOR AUTOMATIC TIMESTAMPS
-- =============================================================================

-- Generic trigger function for updated_at timestamps
CREATE OR REPLACE FUNCTION auth.trigger_set_timestamp()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = auth, pg_temp
AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$;

-- Apply updated_at triggers to relevant tables
DO $$
DECLARE
    table_name text;
    trigger_name text;
BEGIN
    FOR table_name IN
        SELECT t.table_name
        FROM information_schema.tables t
        JOIN information_schema.columns c ON t.table_name = c.table_name
        WHERE t.table_schema = 'auth'
        AND c.column_name = 'updated_at'
        AND t.table_type = 'BASE TABLE'
    LOOP
        trigger_name := 'set_' || table_name || '_updated_at';

        IF NOT EXISTS (
            SELECT 1 FROM information_schema.triggers
            WHERE trigger_name = trigger_name AND event_object_table = table_name
        ) THEN
            EXECUTE format('
                CREATE TRIGGER %I
                BEFORE UPDATE ON auth.%I
                FOR EACH ROW
                EXECUTE FUNCTION auth.trigger_set_timestamp();
            ', trigger_name, table_name);
        END IF;
    END LOOP;
END $$;

-- =============================================================================
-- ROW LEVEL SECURITY POLICIES
-- =============================================================================

-- Enable RLS on all auth tables
ALTER TABLE auth.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE auth.refresh_tokens ENABLE ROW LEVEL SECURITY;
ALTER TABLE auth.audit_log_entries ENABLE ROW LEVEL SECURITY;
ALTER TABLE auth.identities ENABLE ROW LEVEL SECURITY;
ALTER TABLE auth.mfa_factors ENABLE ROW LEVEL SECURITY;
ALTER TABLE auth.mfa_challenges ENABLE ROW LEVEL SECURITY;
ALTER TABLE auth.mfa_amr_claims ENABLE ROW LEVEL SECURITY;
ALTER TABLE auth.sso_providers ENABLE ROW LEVEL SECURITY;
ALTER TABLE auth.sso_domains ENABLE ROW LEVEL SECURITY;
ALTER TABLE auth.saml_relay_states ENABLE ROW LEVEL SECURITY;
ALTER TABLE auth.flow_state ENABLE ROW LEVEL SECURITY;
ALTER TABLE auth.sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE auth.one_time_tokens ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for auth.users
DO $$
BEGIN
    -- Service role can manage all users
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies
        WHERE tablename = 'users' AND policyname = 'service_role_can_manage_users'
    ) THEN
        CREATE POLICY service_role_can_manage_users ON auth.users
        FOR ALL USING (auth.role() = 'service_role');
    END IF;

    -- Users can view and update their own profile
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies
        WHERE tablename = 'users' AND policyname = 'users_can_view_own_profile'
    ) THEN
        CREATE POLICY users_can_view_own_profile ON auth.users
        FOR SELECT USING (auth.uid() = id);
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM pg_policies
        WHERE tablename = 'users' AND policyname = 'users_can_update_own_profile'
    ) THEN
        CREATE POLICY users_can_update_own_profile ON auth.users
        FOR UPDATE USING (auth.uid() = id);
    END IF;
END $$;

-- Create RLS policies for auth.sessions
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies
        WHERE tablename = 'sessions' AND policyname = 'users_can_view_own_sessions'
    ) THEN
        CREATE POLICY users_can_view_own_sessions ON auth.sessions
        FOR SELECT USING (auth.uid() = user_id);
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM pg_policies
        WHERE tablename = 'sessions' AND policyname = 'service_role_can_manage_sessions'
    ) THEN
        CREATE POLICY service_role_can_manage_sessions ON auth.sessions
        FOR ALL USING (auth.role() = 'service_role');
    END IF;
END $$;

-- Create RLS policies for auth.mfa_factors
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies
        WHERE tablename = 'mfa_factors' AND policyname = 'users_can_manage_own_mfa_factors'
    ) THEN
        CREATE POLICY users_can_manage_own_mfa_factors ON auth.mfa_factors
        FOR ALL USING (auth.uid() = user_id);
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM pg_policies
        WHERE tablename = 'mfa_factors' AND policyname = 'service_role_can_manage_mfa_factors'
    ) THEN
        CREATE POLICY service_role_can_manage_mfa_factors ON auth.mfa_factors
        FOR ALL USING (auth.role() = 'service_role');
    END IF;
END $$;

-- Apply service_role policies to remaining tables
DO $$
DECLARE
    table_name text;
    policy_name text;
BEGIN
    FOR table_name IN
        SELECT t.table_name
        FROM information_schema.tables t
        WHERE t.table_schema = 'auth'
        AND t.table_type = 'BASE TABLE'
        AND t.table_name NOT IN ('users', 'sessions', 'mfa_factors')
    LOOP
        policy_name := 'service_role_can_manage_' || table_name;

        IF NOT EXISTS (
            SELECT 1 FROM pg_policies
            WHERE tablename = table_name AND policyname = policy_name
        ) THEN
            EXECUTE format('
                CREATE POLICY %I ON auth.%I
                FOR ALL USING (auth.role() = ''service_role'');
            ', policy_name, table_name);
        END IF;
    END LOOP;
END $$;

-- =============================================================================
-- OWNERSHIP AND PERMISSIONS
-- =============================================================================

-- Set ownership of auth schema and all objects to postgres
ALTER SCHEMA auth OWNER TO postgres;

-- Set ownership of all tables, sequences, and functions
DO $$
DECLARE
    obj_name text;
BEGIN
    -- Tables
    FOR obj_name IN
        SELECT tablename FROM pg_tables WHERE schemaname = 'auth'
    LOOP
        EXECUTE format('ALTER TABLE auth.%I OWNER TO postgres;', obj_name);
    END LOOP;

    -- Sequences
    FOR obj_name IN
        SELECT sequencename FROM pg_sequences WHERE schemaname = 'auth'
    LOOP
        EXECUTE format('ALTER SEQUENCE auth.%I OWNER TO postgres;', obj_name);
    END LOOP;

    -- Functions
    FOR obj_name IN
        SELECT routine_name FROM information_schema.routines
        WHERE routine_schema = 'auth' AND routine_type = 'FUNCTION'
    LOOP
        EXECUTE format('ALTER FUNCTION auth.%I() OWNER TO postgres;', obj_name);
    END LOOP;

    -- Types
    FOR obj_name IN
        SELECT typname FROM pg_type t
        JOIN pg_namespace n ON t.typnamespace = n.oid
        WHERE n.nspname = 'auth' AND t.typtype = 'e'
    LOOP
        EXECUTE format('ALTER TYPE auth.%I OWNER TO postgres;', obj_name);
    END LOOP;
END $$;

-- Grant schema usage to auth roles
GRANT USAGE ON SCHEMA auth TO anon, authenticated, service_role;

-- Grant table permissions
-- anon role: SELECT and INSERT on limited tables
GRANT SELECT, INSERT ON auth.users TO anon;
GRANT SELECT, INSERT ON auth.sessions TO anon;
GRANT SELECT, INSERT ON auth.refresh_tokens TO anon;
GRANT SELECT, INSERT ON auth.audit_log_entries TO anon;

-- authenticated role: broader permissions for authenticated users
GRANT SELECT, INSERT, UPDATE, DELETE ON auth.users TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON auth.sessions TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON auth.mfa_factors TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON auth.mfa_challenges TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON auth.refresh_tokens TO authenticated;
GRANT SELECT, INSERT ON auth.audit_log_entries TO authenticated;
GRANT SELECT ON auth.identities TO authenticated;

-- service_role: full permissions on all tables
GRANT ALL ON ALL TABLES IN SCHEMA auth TO service_role;
GRANT ALL ON ALL SEQUENCES IN SCHEMA auth TO service_role;
GRANT ALL ON ALL FUNCTIONS IN SCHEMA auth TO service_role;

-- Grant sequence usage for auto-incrementing columns
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA auth TO anon, authenticated, service_role;

-- Revoke all permissions from PUBLIC
REVOKE ALL ON SCHEMA auth FROM PUBLIC;
REVOKE ALL ON ALL TABLES IN SCHEMA auth FROM PUBLIC;
REVOKE ALL ON ALL SEQUENCES IN SCHEMA auth FROM PUBLIC;
REVOKE ALL ON ALL FUNCTIONS IN SCHEMA auth FROM PUBLIC;

-- =============================================================================
-- SCHEMA VALIDATION AND SUMMARY
-- =============================================================================

-- Raise notices about created objects
DO $$
DECLARE
    table_count int;
    function_count int;
    policy_count int;
BEGIN
    SELECT COUNT(*) INTO table_count FROM information_schema.tables WHERE table_schema = 'auth';
    SELECT COUNT(*) INTO function_count FROM information_schema.routines WHERE routine_schema = 'auth';
    SELECT COUNT(*) INTO policy_count FROM pg_policies WHERE schemaname = 'auth';

    RAISE NOTICE '=============================================================================';
    RAISE NOTICE 'Auth schema bootstrap completed successfully';
    RAISE NOTICE '=============================================================================';
    RAISE NOTICE 'Created % tables in auth schema', table_count;
    RAISE NOTICE 'Created % functions in auth schema', function_count;
    RAISE NOTICE 'Created % RLS policies in auth schema', policy_count;
    RAISE NOTICE 'Set ownership to postgres and configured role permissions';
    RAISE NOTICE 'All auth tables have Row Level Security enabled';
    RAISE NOTICE '=============================================================================';
END $$;

-- Reset search path
RESET search_path;
