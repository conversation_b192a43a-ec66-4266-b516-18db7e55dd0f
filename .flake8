# .flake8 configuration for the Lifeboard project
# Ensures consistent style and discovery of potential Python issues

[flake8]
# Maximum allowed line length for code
max-line-length = 120

# Built-in errors and warnings that are ignored
ignore =
    # Missing or incorrect module, class or function docstrings
    D100, D101, D102, D103, D104, D105, D107,
    # Invalid first argument for method
    E305,
    # Invalid assignment regex
    E226, E241, E242,

# Exclude specific directories and files from checks
exclude =
    .git,
    __pycache__,
    build,
    dist,
    node_modules,
    .venv,
    .tox,
    .mypy_cache,
    .pytest_cache,
    .coverage,
    .nox,
    .history,
    .eslintcache,
    .vscode,

# Configure built-in extensions and plugins
extend-ignore =
    F403,  # Import * used; in some cases this is acceptable
    F401,  # Module imported but unused; sometimes acceptable in modules
    F405,  # Name may be undefined; okay for dynamic import environments
